import { AbstractControl, FormArray, FormGroup } from "@angular/forms";
import { EBidCommand, EBidHistoryService, EBidHistoryType, HistoryDefaultMessages, MessageInfo } from "../data-access/ebid-history.service";
import { moveItemInArray } from "@angular/cdk/drag-drop";
import { EBidSection, EBidSectionNames, ValueChangeBaseCommand } from "./ebid";

export enum UnitPriceSettings {
	NORMAL = "NORMAL",
	FIXED = "FIXED",
	PERCENTAGE = "PERCENTAGE",
	MAX = "MAX",
	MIN = "MIN"
}

export interface BidFormSection extends EBidSection {
	Forms: BidForm[];
}

export interface BidForm {
	Title: string;
	FormId: string;
	FormTotal: number;
	CountTowardsTotal: boolean;
	Sections: BidSection[];
}

export interface BaseBidItem {
	BidItemId: string;
	Description: string;
	CustomId1: string;
	CustomId2: string | null;
	Unit: string;
	UnitPrice: number | null;
	Qty: number;
	ExtensionPrice: number | null;
	IsQtyEditable: boolean;
	NotRequired: boolean;
	UnitPriceSetting: string | null;
	BidderPrice: number | null;
	IsDisabled: boolean;
}

export interface BidItem extends BaseBidItem {
	Alternates: AlternateBidItem[];
}

export interface AlternateBidItem extends BaseBidItem {
	AlternateId: string;
}

export interface BidSection {
	SectionId: string;
	Title: string;
	Total: number;
	BidItems: BidItem[];
}


export class EBidFomInfoCommand {
	formId: string;
	constructor(formId: string) {
		this.formId = formId;
	}

	getForm(bidFormSectionFormGroup: FormGroup): { idx: number, form: FormGroup } {
		const forms = bidFormSectionFormGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		const form = forms.at(formIndex) as FormGroup;

		return { idx: formIndex, form: form };
	}
}

export class EBidSectionInfoCommand extends EBidFomInfoCommand {
	sectionId: string;
	constructor(formId: string, sectionId: string) {
		super(formId);
		this.sectionId = sectionId;
	}

	getSection(form: FormGroup): { idx: number, section: FormGroup } {
		const sections = form.get('Sections') as FormArray;
		const sectionIndex = sections.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);
		const section = sections.at(sectionIndex) as FormGroup;

		return { idx: sectionIndex, section: section };
	}
}

export class EBidBidItemInfoCommand extends EBidSectionInfoCommand {
	bidItemId: string;
	constructor(formId: string, sectionId: string, bidItemId: string) {
		super(formId, sectionId);
		this.bidItemId = bidItemId;
	}

	getBidItem(section: FormGroup): { idx: number, bidItem: FormGroup } {
		const bidItems = section.get('BidItems') as FormArray;
		const bidItemIndex = bidItems.controls.findIndex((bidItem: AbstractControl) => bidItem.get('BidItemId')?.value === this.bidItemId);
		const bidItem = bidItems.at(bidItemIndex) as FormGroup;
		return { idx: bidItemIndex, bidItem: bidItem };
	}



}

export class EBidAddFormCommand implements EBidCommand {

	bidFormGroup: FormGroup
	bidFormSectionGroup: FormGroup;
	idx: number | null;
	formId: string;

	constructor(bidFormSectionGroup: FormGroup, bidFormGroup: FormGroup, idx: number | null = null, formId: string) {
		this.bidFormSectionGroup = bidFormSectionGroup;
		this.bidFormGroup = bidFormGroup;
		this.idx = idx;
		this.formId = formId;
	}

	execute(): void {
		const forms = this.bidFormSectionGroup.get('Forms') as FormArray;
		var hasForm = forms.controls.find((formControl: AbstractControl) => formControl.get('FormId')?.value === this.formId);
		if (!hasForm) {
			if (this.idx !== null) {
				forms.insert(this.idx, this.bidFormGroup);
			} else {
				forms.push(this.bidFormGroup);
			}
		}

		forms.markAsDirty();
		forms.markAsTouched();
		forms.updateValueAndValidity();
	}

	undo(): void {
		const forms = this.bidFormSectionGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		forms.removeAt(formIndex);

		forms.markAsDirty();
		forms.markAsTouched();
		forms.updateValueAndValidity();
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}
}

export class EBidDeleteFormCommand implements EBidCommand {

	bidFormGroup: FormGroup | null = null;
	bidFormSectionGroup: FormGroup;
	formId: string;
	idx: number | null = null;

	constructor(bidFormSectionGroup: FormGroup, formId: string) {
		this.bidFormSectionGroup = bidFormSectionGroup;
		this.formId = formId;
	}

	execute(): void {
		const forms = this.bidFormSectionGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			this.bidFormGroup = forms.at(formIndex) as FormGroup;
			this.idx = formIndex;
			forms.removeAt(formIndex);

			forms.markAsDirty();
			forms.markAsTouched();
			forms.updateValueAndValidity();
		}
	}

	undo(): void {
		const forms = this.bidFormSectionGroup.get('Forms') as FormArray;
		var hasForm = forms.controls.find((formControl: AbstractControl) => formControl.get('FormId')?.value === this.formId);
		if (!hasForm) {
			if (this.idx !== null) {
				forms.insert(this.idx, this.bidFormGroup);
			} else {
				forms.push(this.bidFormGroup);
			}

			forms.markAsDirty();
			forms.markAsTouched();
			forms.updateValueAndValidity();

			this.idx = null;
		} else {
			console.log(`Form already exists in formgroup - ${this.formId}`);
		}
	}

	getInfo<Type>(info: any): Type {
		const cInfo = this.bidFormGroup?.value as BidForm;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed the bid form <b>${cInfo?.Title} </b>`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added the bid form <b>${cInfo?.Title} </b>`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}

export class EBidAddFormSectionCommand implements EBidCommand {

	bidSectionFormGroup: FormGroup
	bidFormSectionFormGroup: FormGroup;
	idx: number | null;
	formId: string;
	sectionId: string;

	constructor(bidFormSectionFormGroup: FormGroup, bidSectionFormGroup: FormGroup, idx: number | null = null, formId: string, sectionId: string) {
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.bidSectionFormGroup = bidSectionFormGroup;
		this.idx = idx;
		this.formId = formId;
		this.sectionId = sectionId;
	}

	execute(): void {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;

		// Find the specific form by formId
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const formGroup = forms.at(formIndex) as FormGroup;

			// Access the sections array within the specific form
			const sections = formGroup.get('Sections') as FormArray;

			//var sectionForm = this.getBidSection(section.Title, formId, section.SectionId, section.Total, this.getBidItemArray(formId, section.SectionId, section.BidItems));

			if (this.idx) {
				sections.insert(this.idx, this.bidSectionFormGroup)
			} else {
				sections.push(this.bidSectionFormGroup);
			}

			forms.markAsDirty();
			forms.markAsTouched();
			forms.updateValueAndValidity();
		} else {
			console.warn(`Form not found in formgroup - ${this.formId}`);
		}
	}

	undo(): void {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const formGroup = forms.at(formIndex) as FormGroup;
			const sections = formGroup.get('Sections') as FormArray;
			const sectionIndex = sections.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);
			if (sectionIndex !== -1) {
				sections.removeAt(sectionIndex);

				forms.markAsDirty();
				forms.markAsTouched();
				forms.updateValueAndValidity();
			} else {
				console.warn(`Section not found in formgroup - ${this.formId} section - ${this.sectionId}`);
			}


		}
	}

	getInfo<Type>(info: any): Type {
		const cInfo = this.bidSectionFormGroup?.value as BidForm;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added a bid form section`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed a bid from section`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}


export class EBidDeleteFormSectionCommand implements EBidCommand {

	bidSectionFormGroup: FormGroup | null = null;
	bidFormSectionFormGroup: FormGroup;
	idx: number | null = null;
	formId: string;
	sectionId: string;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionId: string) {
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.formId = formId;
		this.sectionId = sectionId;
	}

	execute(): void {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const formGroup = forms.at(formIndex) as FormGroup;
			const sections = formGroup.get('Sections') as FormArray;
			const sectionIndex = sections.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);
			if (sectionIndex !== -1) {
				this.bidSectionFormGroup = sections.at(sectionIndex) as FormGroup;
				sections.removeAt(sectionIndex);
				this.idx = sectionIndex;

				forms.markAsDirty();
				forms.markAsTouched();
				forms.updateValueAndValidity();
			} else {
				console.log(`Section not found in formgroup - ${this.formId} section - ${this.sectionId}`);
			}
		}

	}

	undo(): void {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;

		// Find the specific form by formId
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const formGroup = forms.at(formIndex) as FormGroup;

			// Access the sections array within the specific form
			const sections = formGroup.get('Sections') as FormArray;

			if (this.idx) {
				sections.insert(this.idx, this.bidSectionFormGroup)
			} else {
				sections.push(this.bidSectionFormGroup);
			}
			sections.markAsDirty();
			sections.markAsTouched();
			sections.updateValueAndValidity();
			// this.bidSectionFormGroup = null;
			this.idx = null;
		} else {
			console.warn(`Form not found in formgroup - ${this.formId}`);
		}


	}
	getInfo<Type>(info: any): Type {
		const cInfo = this.bidSectionFormGroup?.value as BidForm;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed the bid form section <b>${cInfo?.Title || '<i>No Title </i>'} </b>`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added the bid from section <b>${cInfo?.Title || '<i>No Title </i>'} </b>`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}


export class EBidAddFormSectionBidItemCommand implements EBidCommand {

	bidItemFormGroup: FormGroup
	bidFormSectionFormGroup: FormGroup;
	idx: number | null;
	formId: string;
	sectionId: string;
	bidItemId: string;

	constructor(bidFormSectionFormGroup: FormGroup, bidItemFormGroup: FormGroup, idx: number | null = null, formId: string, sectionId: string, bidItemId: string) {
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.bidItemFormGroup = bidItemFormGroup;
		this.idx = idx;
		this.formId = formId;
		this.sectionId = sectionId;
		this.bidItemId = bidItemId;
	}

	execute(): void {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const formGroup = forms.at(formIndex) as FormGroup;
			const sections = formGroup.get('Sections') as FormArray;
			const sectionIndex = sections.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);
			if (sectionIndex !== -1) {
				var section = sections.at(sectionIndex) as FormGroup;
				var bidItems = section.get('BidItems') as FormArray;

				if (this.idx !== null) {
					bidItems.insert(this.idx, this.bidItemFormGroup);
				} else {
					bidItems.push(this.bidItemFormGroup);
				}

				bidItems.markAsDirty();
				bidItems.markAsTouched();
				bidItems.updateValueAndValidity();
			}
		}
	}

	undo(): void {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const formGroup = forms.at(formIndex) as FormGroup;
			const sections = formGroup.get('Sections') as FormArray;
			const sectionIndex = sections.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);
			if (sectionIndex !== -1) {
				var section = sections.at(sectionIndex) as FormGroup;
				var bidItems = section.get('BidItems') as FormArray;

				var bidItemIdx = bidItems.controls.findIndex((bidItem: AbstractControl) => bidItem.get("BidItemId")?.value === this.bidItemId);

				if (bidItemIdx !== -1) {
					bidItems.removeAt(bidItemIdx);

					bidItems.markAsDirty();
					bidItems.markAsTouched();
					bidItems.updateValueAndValidity();
				} else {
					console.log(`Bid Item ${this.bidItemId} not found in section ${this.sectionId} of form ${this.formId}`);
				}
			}
		}
	}

	getInfo<Type>(info: any): Type {
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added a new Bid Item`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed a Bid Item`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}


export class EBidDeleteFormSectionBidItemCommand extends EBidBidItemInfoCommand implements EBidCommand {

	bidItemFormGroup: FormGroup | null = null;
	bidFormSectionFormGroup: FormGroup;
	idx: number | null = null;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionId: string, bidItemId: string) {
		super(formId, sectionId, bidItemId);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
	}

	execute(): void {
		const formInfo = this.getInfoForBidItem();
		if (formInfo && formInfo.bidItem) {
			var bidItems = formInfo.section.get('BidItems') as FormArray;

			if (formInfo.bidItemIdx !== -1) {
				this.idx = formInfo.bidItemIdx;
				this.bidItemFormGroup = formInfo.bidItem;
				bidItems.removeAt(formInfo.bidItemIdx);

				bidItems.markAsDirty();
				bidItems.markAsTouched();
				bidItems.updateValueAndValidity();
			} else {
				console.log(`Bid Item not found in formgroup - ${this.bidItemId}`);
			}
		}
	}

	undo(): void {
		const formInfo = this.getInfoForBidItem();
		if (formInfo && formInfo.form && formInfo.section) {
			var bidItems = formInfo.section.get('BidItems') as FormArray;

			if (this.idx !== null) {
				bidItems.insert(this.idx, this.bidItemFormGroup);
			} else {
				bidItems.push(this.bidItemFormGroup);
			}

			bidItems.markAsDirty();
			bidItems.markAsTouched();
			bidItems.updateValueAndValidity();

			this.idx = null;
		}
	}



	private getInfoForBidItem(): { formIdx: number, form: FormGroup, sectionIdx: number, section: FormGroup, bidItemIdx: number, bidItem: FormGroup } {
		const form = this.getForm(this.bidFormSectionFormGroup);
		const section = this.getSection(form.form);
		const bidItem = this.getBidItem(section.section);
		return { formIdx: form.idx, form: form.form, sectionIdx: section.idx, section: section.section, bidItemIdx: bidItem.idx, bidItem: bidItem.bidItem };
	}

	getInfo<Type>(info: any): Type {
		const biInfo = this.getInfoForBidItem();
		const bidFormTitle = biInfo.form.get('Title')?.value;
		const bidSectionTitle = biInfo.section?.get('Title')?.value;
		const bidItemDescription = biInfo.bidItem?.get('Description')?.value;
		const descInfo = `<b> Bid Item: ${bidItemDescription}</b> in <b>Section: ${bidSectionTitle}</b> of <b>Form: ${bidFormTitle}</b>`;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed a Bid Item - ${descInfo}`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added a Bid Item - ${descInfo}`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}
}

export class EBidDisableFormSectionBidItemCommand extends EBidBidItemInfoCommand implements EBidCommand {

	bidItemFormGroup: FormGroup | null = null;
	bidFormSectionFormGroup: FormGroup;
	idx: number | null = null;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionId: string, bidItemId: string) {
		super(formId, sectionId, bidItemId);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
	}

	execute(): void {
		const formInfo = this.getInfoForBidItem();
		if (formInfo && formInfo.bidItem) {
			var bidItems = formInfo.section.get('BidItems') as FormArray;

			if (formInfo.bidItemIdx !== -1) {
				this.idx = formInfo.bidItemIdx;
				this.bidItemFormGroup = formInfo.bidItem;
				const isDiabled = formInfo.bidItem.get('IsDisabled')?.value;
				formInfo.bidItem.patchValue({ IsDisabled: !isDiabled });
				
				bidItems.markAsDirty();
				bidItems.markAsTouched();
				bidItems.updateValueAndValidity();
			} else {
				console.log(`Bid Item not found in formgroup - ${this.bidItemId}`);
			}
		}
	}

	undo(): void {
		const formInfo = this.getInfoForBidItem();
		if (formInfo && formInfo.form && formInfo.section) {
			var bidItems = formInfo.section.get('BidItems') as FormArray;

			const isDiabled = formInfo.bidItem.get('IsDisabled')?.value;
			formInfo.bidItem.patchValue({ IsDisabled: !isDiabled });

			bidItems.markAsDirty();
			bidItems.markAsTouched();
			bidItems.updateValueAndValidity();

			this.idx = null;
		}
	}



	private getInfoForBidItem(): { formIdx: number, form: FormGroup, sectionIdx: number, section: FormGroup, bidItemIdx: number, bidItem: FormGroup } {
		const form = this.getForm(this.bidFormSectionFormGroup);
		const section = this.getSection(form.form);
		const bidItem = this.getBidItem(section.section);
		return { formIdx: form.idx, form: form.form, sectionIdx: section.idx, section: section.section, bidItemIdx: bidItem.idx, bidItem: bidItem.bidItem };
	}

	getInfo<Type>(info: any): Type {
		const biInfo = this.getInfoForBidItem();
		const bidFormTitle = biInfo.form.get('Title')?.value;
		const bidSectionTitle = biInfo.section?.get('Title')?.value;
		const bidItemDescription = biInfo.bidItem?.get('Description')?.value;
		const descInfo = `<b> Bid Item: ${bidItemDescription}</b> in <b>Section: ${bidSectionTitle}</b> of <b>Form: ${bidFormTitle}</b>`;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed a Bid Item - ${descInfo}`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added a Bid Item - ${descInfo}`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}
}
export class EBidDisableBidItemAlternateCommand extends EBidBidItemInfoCommand implements EBidCommand {

	bidFormSectionFormGroup: FormGroup;
	alternateId: string;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionId: string, bidItemId: string, alternateId: string) {
		super(formId, sectionId, bidItemId);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.formId = formId;
		this.sectionId = sectionId;
		this.alternateId = alternateId;
		this.bidItemId = bidItemId;
	}

	execute(): void {
		const formInfo = this.getInfoForAlternate();

		if (formInfo?.alternate) {

			const isDiabled = formInfo?.alternate.get('IsDisabled')?.value;
			formInfo?.alternate.patchValue({ IsDisabled: !isDiabled });

			formInfo?.alternate.markAsDirty();
			formInfo?.alternate.markAsTouched();
			formInfo?.alternate.updateValueAndValidity();			
		}
	}

	undo(): void {
		const formInfo = this.getInfoForAlternate();

		if (formInfo?.alternate) {

			const isDiabled = formInfo?.alternate.get('IsDisabled')?.value;
			formInfo?.alternate.patchValue({ IsDisabled: !isDiabled });

			formInfo?.alternate.markAsDirty();
			formInfo?.alternate.markAsTouched();
			formInfo?.alternate.updateValueAndValidity();			
		}
	}

	private getInfoForAlternate(): { formIdx: number, form: FormGroup, sectionIdx: number, section: FormGroup, bidItemIdx: number, bidItem: FormGroup, alternate: FormGroup } | null {
		const form = this.getForm(this.bidFormSectionFormGroup);
		const section = this.getSection(form.form);
		const bidItem = this.getBidItem(section.section);
		const alternates = bidItem.bidItem.get('Alternates') as FormArray;
		const alternateIdx = alternates.controls.findIndex((alternate: AbstractControl) => alternate.get('AlternateId')?.value === this.alternateId);
		if (alternateIdx === -1) {
			console.warn(`Alternate not found in bid item - ${this.bidItemId} alternate - ${this.alternateId} in section ${this.sectionId} of form ${this.formId}`);
			return null;			
		}else{
			const alternate = alternates.at(alternateIdx) as FormGroup;
			return { formIdx: form.idx, form: form.form, sectionIdx: section.idx, section: section.section, bidItemIdx: bidItem.idx, bidItem: bidItem.bidItem, alternate: alternate };
		}	
	}


	getInfo<Type>(info: any): Type {
		const alternateInfo = this.getInfoForAlternate();
		const bidFormTitle = alternateInfo?.form.get('Title')?.value;
		const bidSectionTitle = alternateInfo?.section?.get('Title')?.value;
		const bidItemDescription = alternateInfo?.bidItem?.get('Description')?.value;
		const alternateDescription = alternateInfo?.alternate.get('Description')?.value;
		const isDisabled = alternateInfo?.alternate.get('IsDisabled')?.value ? 'Disabled' : 'Enabled';
		const descInfo = `<b>Alternate: ${alternateDescription} </b> for <b>Bid Item: ${bidItemDescription}</b> in <b>Section: ${bidSectionTitle}</b> of <b>Form: ${bidFormTitle}</b>`;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Alternate - ${descInfo} has been ${isDisabled}`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Alternate - ${descInfo} has been ${isDisabled}`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}


export class EBidAddBidItemAlternateCommand extends EBidBidItemInfoCommand implements EBidCommand {

	bidItemAlternateGroup: FormGroup
	bidFormSectionFormGroup: FormGroup;
	idx: number | null;
	alternateId: string;

	constructor(bidFormSectionFormGroup: FormGroup, bidItemAlternateGroup: FormGroup, idx: number | null = null, formId: string, sectionId: string, bidItemId: string, alternateId: string) {
		super(formId, sectionId, bidItemId);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.bidItemAlternateGroup = bidItemAlternateGroup;
		this.idx = idx;
		this.formId = formId;
		this.sectionId = sectionId;
		this.alternateId = alternateId;
		this.bidItemId = bidItemId;
	}

	execute(): void {
		const formInfo = this.getInfoForBidItem();

		if (formInfo.bidItem) {
			var alternates = formInfo.bidItem.get('Alternates') as FormArray;

			if (this.idx) {
				alternates.insert(this.idx, this.bidItemAlternateGroup);
			} else {
				alternates.push(this.bidItemAlternateGroup);
			}

			alternates.markAsDirty();
			alternates.markAsTouched();
			alternates.updateValueAndValidity();
		}

	}

	undo(): void {
		const formInfo = this.getInfoForBidItem();

		if (formInfo.bidItem) {
			var alternates = formInfo.bidItem.get('Alternates') as FormArray;
			if (alternates) {
				var alternateIdx = alternates.controls.findIndex((alternate: AbstractControl) => alternate.get('AlternateId')?.value === this.alternateId);
				if (alternateIdx !== -1) {
					alternates.removeAt(alternateIdx);

					alternates.markAsDirty();
					alternates.markAsTouched();
					alternates.updateValueAndValidity();
				} else {
					console.warn(`Alternate not found in bid item - ${this.bidItemId} alternate - ${this.alternateId} in section ${this.sectionId} of form ${this.formId}`);
				}
			}
		}
	}

	private getInfoForBidItem(): { formIdx: number, form: FormGroup, sectionIdx: number, section: FormGroup, bidItemIdx: number, bidItem: FormGroup } {
		const form = this.getForm(this.bidFormSectionFormGroup);
		const section = this.getSection(form.form);
		const bidItem = this.getBidItem(section.section);
		return { formIdx: form.idx, form: form.form, sectionIdx: section.idx, section: section.section, bidItemIdx: bidItem.idx, bidItem: bidItem.bidItem };
	}


	getInfo<Type>(info: any): Type {
		const biInfo = this.getInfoForBidItem();
		const bidFormTitle = biInfo.form.get('Title')?.value;
		const bidSectionTitle = biInfo.section?.get('Title')?.value;
		const bidItemDescription = biInfo.bidItem?.get('Description')?.value;
		const alternateDescription = this.bidItemAlternateGroup.get('Description')?.value;
		const descInfo = `<b>Bid Item: ${bidItemDescription}</b> in <b>Section: ${bidSectionTitle}</b> of <b>Form: ${bidFormTitle}</b>`;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Added an Alternate - ${descInfo}`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, `Removed an Alternate - ${descInfo}`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}


export class EBidDeleteBidItemAlternateCommand extends EBidBidItemInfoCommand implements EBidCommand {

	bidItemAlternateGroup: FormGroup | null = null;
	bidFormSectionFormGroup: FormGroup;
	idx: number | null = null;
	alternateId: string;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionId: string, bidItemId: string, alternateId: string) {
		super(formId, sectionId, bidItemId);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.alternateId = alternateId;

	}

	execute(): void {
		const biInfo = this.getInfoForBidItem();

		if (biInfo.bidItem) {
			var alternates = biInfo.bidItem.get('Alternates') as FormArray;
			if (alternates) {
				var alternateIdx = alternates.controls.findIndex((alternate: AbstractControl) => alternate.get('AlternateId')?.value === this.alternateId);
				if (alternateIdx !== -1) {
					this.idx = alternateIdx;
					this.bidItemAlternateGroup = alternates.at(alternateIdx) as FormGroup;
					alternates.removeAt(alternateIdx);


					alternates.markAsDirty();
					alternates.markAsTouched();
					alternates.updateValueAndValidity();
				}
			}
		}

	}

	undo(): void {
		const biInfo = this.getInfoForBidItem();

		if (biInfo.bidItem) {
			var alternates = biInfo.bidItem.get('Alternates') as FormArray;

			if (this.idx) {
				alternates.insert(this.idx, this.bidItemAlternateGroup);
			} else {
				alternates.push(this.bidItemAlternateGroup);
			}

			alternates.markAsDirty();
			alternates.markAsTouched();
			alternates.updateValueAndValidity();
		}


	}

	private getInfoForBidItem(): { formIdx: number, form: FormGroup, sectionIdx: number, section: FormGroup, bidItemIdx: number, bidItem: FormGroup } {
		const form = this.getForm(this.bidFormSectionFormGroup);
		const section = this.getSection(form.form);
		const bidItem = this.getBidItem(section.section);
		return { formIdx: form.idx, form: form.form, sectionIdx: section.idx, section: section.section, bidItemIdx: bidItem.idx, bidItem: bidItem.bidItem };
	}

	getInfo<Type>(info: any): Type {
		const biInfo = this.getInfoForBidItem();
		const bidFormTitle = biInfo.form.get('Title')?.value;
		const bidSectionTitle = biInfo.section?.get('Title')?.value;
		const bidItemDescription = biInfo.bidItem?.get('Description')?.value;
		const alternateDescription = this.bidItemAlternateGroup?.get('Description')?.value;
		const descInfo = `<div><b>Alternate:</b> ${alternateDescription}<div><b>Bid Item:</b> ${bidItemDescription}</div><div><b>Section:</b> ${bidSectionTitle}</div> <div><b>Form:</b> ${bidFormTitle}</div>`;
		if (info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, ` <hr /> <b>REMOVED</b> an Alternate <hr /> ${descInfo}`) as Type;
		else if (info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, ` <hr /> <b>ADDED</b> an Alternate  <hr /> ${descInfo}`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.BID_FORM} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}



export class EBidBidFormValueChangeCommand extends ValueChangeBaseCommand implements EBidCommand {

	formId: string;
	bidFormSectionFormGroup: FormGroup;
	constructor(bidFormSectionFormGroup: FormGroup, formId: string, property: string, previousValue: any, newValue: any) {
		super(previousValue, newValue, property);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.formId = formId;
	}
	execute(): void {
		this.patch(this.newValue);
	}

	patch(data: any) {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		var formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			this.patchValue(this.property, forms.at(formIndex), data);
		}
	}

	undo(): void {
		this.patch(this.previousValue);
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}

}

export class EBidBidSectionValueChangeCommand extends ValueChangeBaseCommand implements EBidCommand {

	formId: string;
	sectionId: string;
	bidFormSectionFormGroup: FormGroup;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionId: string, property: string, previousValue: any, newValue: any) {
		super(previousValue, newValue, property);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;

		this.formId = formId;
		this.sectionId = sectionId;
	}
	execute(): void {
		this.patch(this.newValue);
	}

	patch(data: any) {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		var formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const section = (forms.at(formIndex) as FormGroup).get('Sections') as FormArray;
			var sectionIndex = section.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);

			if (sectionIndex !== -1) {
				this.patchValue(this.property, section.at(sectionIndex), data);
			}
		}
	}

	undo(): void {
		this.patch(this.previousValue);
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}

}

export class EBidBidItemValueChangeCommand extends ValueChangeBaseCommand implements EBidCommand {
	formId: string;
	sectionId: string;
	bidItemId: string;
	bidFormSectionFormGroup: FormGroup;

	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionid: string, bidItemId: string, property: string, previousValue: any, newValue: any) {
		super(previousValue, newValue, property);
		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.formId = formId;
		this.sectionId = sectionid;
		this.bidItemId = bidItemId;
		this.property = property;
		this.previousValue = previousValue;
		this.newValue = newValue;
	}

	execute(): void {
		this.patch(this.newValue);
	}

	patch(data: any) {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		var formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);
		if (formIndex !== -1) {
			const section = (forms.at(formIndex) as FormGroup).get('Sections') as FormArray;
			var sectionIndex = section.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);

			if (sectionIndex !== -1) {
				const bidItems = (section.at(sectionIndex) as FormGroup).get('BidItems') as FormArray;
				var bidItemIndex = bidItems.controls.findIndex((bidItem: AbstractControl) => bidItem.get('BidItemId')?.value === this.bidItemId);

				if (bidItemIndex !== -1) {
					const bidItem = bidItems.at(bidItemIndex);
					this.patchValue(this.property, bidItem, data);

				}
			}
		}
	}

	undo(): void {
		this.patch(this.previousValue);
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}
}

export class EBidBidItemAlternateValueChangeCommand extends ValueChangeBaseCommand implements EBidCommand {
	formId: string;
	sectionId: string;
	bidItemId: string;
	alternateId: string;
	bidFormSectionFormGroup: FormGroup;
	constructor(bidFormSectionFormGroup: FormGroup, formId: string, sectionid: string, bidItemId: string, alternateId: string, property: string, previousValue: any, newValue: any) {
		super(previousValue, newValue, property);

		this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.formId = formId;
		this.sectionId = sectionid;
		this.bidItemId = bidItemId;
		this.property = property;
		this.previousValue = previousValue;
		this.newValue = newValue;
		this.alternateId = alternateId;
	}
	execute(): void {
		this.patch(this.newValue);
	}

	patch(data: any) {
		const forms = this.bidFormSectionFormGroup.get('Forms') as FormArray;
		const formIndex = forms.controls.findIndex((form: AbstractControl) => form.get('FormId')?.value === this.formId);

		if (formIndex !== -1) {
			const section = (forms.at(formIndex) as FormGroup).get('Sections') as FormArray;
			const sectionIndex = section.controls.findIndex((section: AbstractControl) => section.get('SectionId')?.value === this.sectionId);

			if (sectionIndex !== -1) {
				const bidItems = (section.at(sectionIndex) as FormGroup).get('BidItems') as FormArray;
				const bidItemIndex = bidItems.controls.findIndex((bidItem: AbstractControl) => bidItem.get('BidItemId')?.value === this.bidItemId);

				if (bidItemIndex !== -1) {
					const alternates = bidItems.at(bidItemIndex).get('Alternates') as FormArray;
					const alternateIndex = alternates.controls.findIndex((alternate: AbstractControl) => alternate.get('AlternateId')?.value === this.alternateId);

					if (alternateIndex !== -1) {
						this.patchValue(this.property, alternates.at(alternateIndex), data);
					}
				}
			}
		}
	}

	undo(): void {
		this.patch(this.previousValue);
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}

}

export class EBidMoveCommand implements EBidCommand {
	data: any[];
	previousIdx: number;
	currentIdx: number;

	constructor(data: any[], previousIdx: number, currentIdx: number) {
		this.data = data;
		this.previousIdx = previousIdx;
		this.currentIdx = currentIdx;
	}
	execute(): void {
		if (this.previousIdx !== this.currentIdx) {
			moveItemInArray(this.data, this.previousIdx, this.currentIdx);
		}
	}
	undo(): void {
		moveItemInArray(this.data, this.currentIdx, this.previousIdx);
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}
}