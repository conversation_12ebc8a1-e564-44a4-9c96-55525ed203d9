import { inject, Injector, input, OnInit } from "@angular/core";
import { EBidService } from "../data-access/ebid.service";
import { BidViews, EBidSection, EBidSectionTypes } from "./ebid";
import { FormArray, FormGroup } from "@angular/forms";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";

export class SectionBase {
	eBidService = inject(EBidService);
	sectionName: string;	
	public readonly BIDFORM_VIEWS: typeof BidViews = BidViews;
	constructor(sectionName: string) {
		this.sectionName = sectionName;
	}

	addBidFormChange(data: any, clearRedo: boolean = false) {
		this.eBidService.addBidFormChange({ ...data, BidSection: this.sectionName }, clearRedo);
	}

	getSectionFormGroup(formArray: FormArray, sectionType: EBidSectionTypes): FormGroup {
		return formArray.controls.find(x => x.get('section')?.value.SectionType === sectionType) as FormGroup;		
	}

	updateFormGroupBaseValues(formGroup: FormGroup, section: EBidSection) {
		formGroup.patchValue({
			SectionName: section.SectionName,
			SectionId: section.SectionId,
			SectionType: section.SectionType
		}, { emitEvent: false, onlySelf: true });
	}

}