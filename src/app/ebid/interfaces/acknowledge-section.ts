import { AbstractControl, FormGroup } from "@angular/forms";
import { EBidCommand, EBidHistoryType, HistoryDefaultMessages, MessageInfo } from "../data-access/ebid-history.service";
import { EBidSection, EBidSectionNames, ValueChangeBaseCommand } from "./ebid";

export interface AcknowledgeSection extends EBidSection{	
	Disclaimer: string;
}


export class DisclaimerValueChangeCommand extends ValueChangeBaseCommand implements EBidCommand {
	acknowledgeFormGroup: FormGroup;
	constructor(acknowledgeFormGroup: FormGroup, property: string, previousValue: any, newValue: any) {
		super(previousValue, newValue, property);
		this.acknowledgeFormGroup = acknowledgeFormGroup;
	}
	execute(): void {
		this.patch(this.newValue);
	}

	patch(data: any) {
		this.patchValue(this.property, this.acknowledgeFormGroup as AbstractControl, data);
		this.acknowledgeFormGroup.markAsDirty();
		this.acknowledgeFormGroup.markAllAsTouched();
		this.acknowledgeFormGroup.updateValueAndValidity();
	}

	undo(): void {
		this.patch(this.previousValue);
		this.acknowledgeFormGroup.markAsDirty();
		this.acknowledgeFormGroup.markAllAsTouched();
		this.acknowledgeFormGroup.updateValueAndValidity();
	}

	getInfo<Type>(info: any): Type {
		if(info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.ACKNOWLEDGE} Section`, `Text has changed`) as Type;
		else if(info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.ACKNOWLEDGE} Section`, `Text has changed`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.ACKNOWLEDGE} Section`, HistoryDefaultMessages.DefaultAction) as Type;
	}

}