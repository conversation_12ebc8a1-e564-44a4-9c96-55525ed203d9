import { BidFormSection } from "./bid-form-section";
import { EBid, EBidSectionTypes, IEBidChangeDetectionService } from "./ebid";

export class EBidChangeDetector implements IEBidChangeDetectionService{
	gatherChanges(oldEBid: EBid, newEbid: EBid) : Array<any> {

		let changes = new Array<any>();

		const newChanges = this.findChanges(oldEBid, newEbid);
		const oldChanges = this.findRemovals(oldEBid, newEbid);

		changes.push(...newChanges);
		changes.push(...oldChanges); 

		return changes;
	}

	private findChanges(oldEBid: EBid, newEbid: EBid){
		let changes = new Array<any>();	
		for(let newEBidSection of newEbid.Sections){
			const oldEBidSection = oldEBid.Sections.find(s => s.SectionType === newEBidSection.SectionType && s.SectionId === newEBidSection.SectionId);
			
			if(oldEBidSection){
				if(oldEBidSection.SectionType === EBidSectionTypes.BID_FORM){	
					this.checkBidFormSectionChanges(oldEBidSection as BidFormSection, newEBidSection as BidFormSection);
				}

			}else{
				console.log("new Bid Section", newEBidSection);
			}
			
		}

		return changes;
	}

	/**
	 * Check for changes in Bid Form Section
	 * @param bidFormSection1 Old Section
	 * @param bidFormSection2 New Section
	 */

	private checkBidFormSectionChanges(bidFormSection1: BidFormSection, bidFormSection2: BidFormSection){
		if(bidFormSection2.Forms){
			for(let newForm of bidFormSection2.Forms){
				if(bidFormSection1.Forms){
					const oldForm =  bidFormSection1.Forms.find(f => f.FormId === newForm.FormId);	
			
					if(oldForm){
						if(newForm.Title !== oldForm.Title){
							console.log("form title changed", oldForm?.Title, newForm.Title);
						}
		
						for(let newSection of newForm.Sections){
							const oldSection = oldForm.Sections.find(s => s.SectionId === newSection.SectionId);
		
							if(oldSection){
								if(oldSection.Title !== newSection.Title){
									console.log("section title changed", oldSection.Title, newSection.Title);
								}
								for(let newBidItem of newSection.BidItems){
									var oldBidItem = oldSection.BidItems.find(bi => bi.BidItemId === newBidItem.BidItemId);
									
									// if(newBidItem.IndexInfo){
									// 	console.log(`bid item as moved from ${newBidItem.IndexInfo.FirstIndex} to ${newBidItem.IndexInfo.CurrentIndex}`);
									// }
		
									if(oldBidItem){											
										this.checkBidItemChanges(oldBidItem, newBidItem);
		
										for(let newAlternate of newBidItem.Alternates){											
											const oldAlternate = oldBidItem.Alternates?.find(a => a.AlternateId === newAlternate.AlternateId);
											if(oldAlternate){
												this.checkBidItemChanges(oldAlternate, newAlternate);
											}else{
												console.log("new Alternate added", newAlternate);
											}
										}
									}else{
										console.log("new Bid Item added", newBidItem);
									}
								}
							}else{
								console.log("new Section added", newSection);
							}
						}
					}else{
						console.log("Form Add", newForm);
					}
				}				
			}
		}
		
	}

	private checkBidItemChanges(oldBidItem: any, newBidItem: any){
		if(oldBidItem.Description !== newBidItem.Description){
			console.log("description changed", oldBidItem, newBidItem);
		}

		if(oldBidItem.Qty !== newBidItem.Qty){
			console.log("qty changed", oldBidItem, newBidItem);
		}

		if(oldBidItem.UnitPrice !== newBidItem.UnitPrice){
			console.log("unit price changed", oldBidItem, newBidItem);
		}

		if (oldBidItem.CustomId1 !== newBidItem.CustomId1){
		// code block
		console.log("custom id 1 changed", oldBidItem, newBidItem);
		}
		 
		if (oldBidItem.CustomId2 !== newBidItem.CustomId2){	
			console.log("custom id 2 changed", oldBidItem, newBidItem);
		// code block
		}		
		if (oldBidItem.Unit !== newBidItem.Unit){
			console.log("unit changed", oldBidItem, newBidItem);
		// code block
		}		
		
		if (oldBidItem.IsQtyEditable !== newBidItem.IsQtyEditable){
			console.log("qty editable changed", oldBidItem, newBidItem);
		// code block
		}
		
		if (oldBidItem.NotRequired !== newBidItem.NotRequired){	
			console.log("not required changed", oldBidItem, newBidItem);
		// code block
		}
		
		if (oldBidItem.UnitPriceSetting !== newBidItem.UnitPriceSetting){	 
			console.log("unit price setting changed", oldBidItem, newBidItem);
		// code block
		}
	}

	private findRemovals(section1: EBid, section2: EBid){
		let changes = new Array<any>();

		for(let oldEBidSection of section1.Sections){
			const newEBidSection = section2.Sections.find(s => s.SectionType === oldEBidSection.SectionType && s.SectionId === oldEBidSection.SectionId);
			
			if(newEBidSection){
				if(newEBidSection.SectionType === EBidSectionTypes.BID_FORM){
					const newBidFormSection = newEBidSection as BidFormSection;
					const oldBidFormSection = oldEBidSection as BidFormSection;
					
					if(oldBidFormSection.Forms){
						for(let oldForm of oldBidFormSection.Forms){
							const newForm =  newBidFormSection.Forms.find(f => f.FormId === oldForm.FormId);
	
							if(newForm){
								for(let oldSection of oldForm.Sections){
									const newSection = newForm.Sections.find(s => s.SectionId === oldSection.SectionId);
	
									if(newSection){
										for(let oldBidItem of oldSection.BidItems){
											const newBidItem = newSection.BidItems.find(bi => bi.BidItemId === oldBidItem.BidItemId);
											
											if(newBidItem){
												
											}else{
												console.log("bid item removed", oldBidItem);	
											}
										}
									}else{
										console.log("old section removed", oldSection);
									}
								}
								
								
							}else{
								console.log("Form Removed", oldForm);
							}
						}
					}				
				}
			}else{
				console.log("bid form section removed", oldEBidSection);
			}
			
		}

		return changes;
	}
}