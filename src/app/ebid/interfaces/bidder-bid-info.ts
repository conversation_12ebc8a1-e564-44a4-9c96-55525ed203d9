import { FormGroup } from "@angular/forms";

export interface GatheredBidderBidInfo extends BidderBidInfo{}


export interface BidderBidInfo{
	Id:string;
	UserId: string;
	ProjectId: string;
	OpenedAt: Date | null;
	CreatedAt: Date;
	SubmittedAt: Date | null;
	LastSubmittedAt: Date | null;
	ReturnedAt: Date | null;
	LastUpdatedAt: Date | null;
	LastUpdateSessionId: string | null;
	LastNotifiedId: string | null;
	LastNotifiedAt: Date | null;
	EBidVersionSync: number | null;
	LastVersionSyncAt: Date | null;
	SectionsData: BidSectionInfo[];

}

export interface BidSectionInfo{
	_t: string[];
	SectionType: string;
	IsComplete: boolean;
}

export interface WorkOrderSectionInfo extends BidSectionInfo{	
	UserValue: number | null;
	Total: number | null;
}

export interface BidFormSectionInfo extends BidSectionInfo{
	BidTotal: number
	Forms: FormInfo[];
}

export interface CompletionTimeSectionInfo extends BidSectionInfo{
	CompletionTimes: Array<CompletionTimeInfo>;	
}

export interface AcknowledgeSectionInfo extends BidSectionInfo{
	AcknowledgedAt: Date | null;
}

export interface RequiredUploadsSectionInfo extends BidSectionInfo{
	RequiredUploads: RequiredUploadInfo[] | null;
}

export interface RequiredDownloadsSectionInfo extends BidSectionInfo{
	RequiredDownloads: RequiredDownloadInfo[] | null;
}

export interface AcknowledgeAddendaSectionInfo extends BidSectionInfo{
	Acknowledgements: AcknowledgementInfo[] | null;
}

export interface AcknowledgementInfo{
	DocumentId: string;
	AcknowledgeAt: Date;
}
export interface RequiredDownloadInfo{
	RequiredDownloadId: string;
	IsDownloaded: boolean;
}

export interface RequiredUploadInfo{
	UploadId: string;
	RequiredUploadId: string;
	DocumentName: string;
	Comment: string;
	S3Bucket: string;
	Key: string;
	CreatedAt: Date;
	Size: number;
	
}

export interface CompletionTimeInfo{
	CompletionTimeId: string;
	TimeFrame: string | null;
	UserValue: number | null;
}

export interface FormInfo{
	FormId: string;	
	FormTotal: number;
	Sections: SectionInfo[];
}

export interface SectionInfo{
	SectionId: string;
	SectionTotal: number;
	BidItems: BidItemInfo[];
}

export interface BidItemInfo{
	BidItemId: string;
	Qty: number;
	UserPrice: number;
	ExtensionPrice: number;
	Alternates: AlternateItemInfo[];
}

export interface AlternateItemInfo{
	AlternateId: string;
	Qty: number;
	UserPrice: number;
	ExtensionPrice: number;
}

export interface IEBidBidInfoGatherer{
	gatherBidInfo(eBidFormGroup: FormGroup): BidderBidInfo;
}