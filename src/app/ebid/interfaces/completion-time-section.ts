import { Form<PERSON>rray, FormGroup } from "@angular/forms";
import { EBidCommand, EBidHistoryType, MessageInfo } from "../data-access/ebid-history.service";
import { EBidSection, EBidSectionNames } from "./ebid";
import { CompletionTimeInfo } from "./bidder-bid-info";


export interface CompletionTimeSection extends EBidSection{
	CompletionTimes: CompletionTime[];
}

export interface CompletionTime{	
	CompletionTimeId: string;
	CompletionType: string;
	CompletionTimeOption: string;
	MaxDays: number | null;
}

export enum CompletionTimeOptions{
	USER_DECIDES = "User Decides",
	WORK_DAYS = "Work Days",
	CALENDAR_DAYS = "Calendar Days"
}

export class CompletionAddTimeSectionCommand implements EBidCommand {
	completionTimeSection: FormGroup;
	completionTimeInfo: FormGroup | null = null;	
	completionTimeId: string;
	idx: number | null = null;

	constructor(completionTimeSection: FormGroup, completionTimeInfo: FormGroup, completionTimeId: string) {
		this.completionTimeSection = completionTimeSection;
		this.completionTimeInfo = completionTimeInfo;
		this.completionTimeId = completionTimeId;
	}
	getInfo<Type>(info: any): Type {
		const cInfo = this.completionTimeInfo?.value as CompletionTime;
		if(info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.COMPLETION_TIME} Section`, `You have added the Completion Time <b>${cInfo?.CompletionType} </b>`) as Type;
		else if(info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.COMPLETION_TIME} Section`, `You have removed the Completion Time <b>${cInfo?.CompletionType} </b>`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.COMPLETION_TIME} Section`, 'Completion Time Section') as Type;		
	}

	execute(): void {
		const completionTimes = this.completionTimeSection.get('CompletionTimes') as FormArray<any>;
		
		if(this.idx !== null){
			completionTimes.insert(this.idx, this.completionTimeInfo);
		}else{
			completionTimes.push(this.completionTimeInfo);	
		}
	
	}
	undo(): void {
		const completionTimes = this.completionTimeSection.get('CompletionTimes') as FormArray<any>;
		const index = completionTimes.controls.findIndex(x => x.get('CompletionTimeId')?.value === this.completionTimeId);
		this.idx = index;
		this.completionTimeInfo = completionTimes.controls[index] as FormGroup;
		completionTimes.removeAt(index);
	}
}

export class CompletionRemoveTimeSectionCommand implements EBidCommand {	
	completionTimeSection: FormGroup;	
	completionTimeInfo: FormGroup | null = null;	
	completionTimeId: string;
	idx: number | null = null;

	constructor(completionTimeSection: FormGroup, completionTimeId: string) {
		this.completionTimeSection = completionTimeSection;
		this.completionTimeId = completionTimeId;
	}
	execute(): void {
		const completionTimes = this.completionTimeSection.get('CompletionTimes') as FormArray<any>;
		const index = completionTimes.controls.findIndex(x => x.get('CompletionTimeId')?.value === this.completionTimeId);
		this.idx = index;
		this.completionTimeInfo = completionTimes.controls[index] as FormGroup;
		completionTimes.removeAt(index);	
	}
	undo(): void {
		const completionTimes = this.completionTimeSection.get('CompletionTimes') as FormArray<any>;
		
		if(this.idx !== null){
			completionTimes.insert(this.idx, this.completionTimeInfo);
		}else{
			completionTimes.push(this.completionTimeInfo);	
		}

	}

	getInfo<Type>(info: any): Type {
		const cInfo = this.completionTimeInfo?.value as CompletionTime;
		if(info === EBidHistoryType.REDO)
			return new MessageInfo(`${EBidSectionNames.COMPLETION_TIME} Section`, `You have removed the Completion Time <b>${cInfo?.CompletionType} </b>`) as Type;
		else if(info === EBidHistoryType.UNDO)
			return new MessageInfo(`${EBidSectionNames.COMPLETION_TIME} Section`, `You have added the Completion Time <b>${cInfo?.CompletionType} </b>`) as Type;
		else
			return new MessageInfo(`${EBidSectionNames.COMPLETION_TIME} Section`, 'Completion Time Section') as Type;	

		
	}
}