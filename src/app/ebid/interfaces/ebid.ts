import { AbstractControl, FormArray, FormGroup } from "@angular/forms";
import { BidderBidInfo } from "./bidder-bid-info";
import { BidFormSection } from "./bid-form-section";
import { EBidCommand, MessageInfo } from "../data-access/ebid-history.service";

export interface EBid {
	Id: string | null;
	IsEnabled: boolean;
	BeginDate: Date | null;
	BeginTimeZone: string | null;
	ProjectId: string;
	Version: number;
	OpenedAt: Date | null;
	Sections: EBidSection[];
	PostedDocuments: PostedDocuments;
}

export interface PostedDocuments {
	BidResultsProjectFileId: string | null;
	BidTabsFileId: string | null;
	BidResultsKey: string | null;
	BidTabsFileKey: string | null;
	S3Bucket: string | null;
	PostedAt: Date | null;
	PostedBy: string | null;
}

export interface EBidSection {
	_t: Array<string>;
	SectionType: string;
	SectionName: string;
	SectionId: string;
}



export interface WorkOrderSection extends EBidSection {

	WorkOrderType: string;
	DecimalValue: number;
	Allowances: number | null;
	Fixed: number | null;
}



export interface RequiredUploadsSection extends EBidSection {
	RequiredUploads: RequiredUpload[];
	OnlyPDF: boolean;
}

export interface RequiredDownloadsSection extends EBidSection {
	RequiredDownloads: RequiredDownload[];
}

export interface AcknowledgeAddendaSection extends EBidSection {
	Acknowledgements: Acknowledgement[];
}

export interface Acknowledgement {
	DocumentId: string;
}

export interface RequiredDownload {
	RequiredDownloadId: string;
	DocumentId: string;
	IsRequired: boolean;
}

export interface RequiredUpload {
	RequiredUploadId: string;
	Name: string;
	Notes: string;
}

export class DocumentInfo {
	constructor(documentId: string, documentName: string, category: string, extension: string, isDownloading: boolean = false, isDownloaded: boolean | null = null) {
		this.DocumentId = documentId;
		this.DocumentName = documentName;
		this.Category = category;
		this.Extension = extension;
		this.IsDownloaded = isDownloaded;
		this.IsDownloading = isDownloading;
	}
	DocumentId: string;
	DocumentName: string;
	Category: string;
	Extension: string;
	IsDownloaded: boolean | null = null;
	IsDownloading: boolean = false;

}





// export class MovedBidItem {
// 	constructor(firstIndex: number, previousIndex: number, currentIndex: number) {
// 	  this.CurrentIndex = currentIndex;
// 	  this.PreviousIndex = previousIndex;
// 	  this.FirstIndex = firstIndex;
// 	}
// 	FirstIndex: number;
// 	PreviousIndex: number;
// 	CurrentIndex: number;
//   }






export enum BidViews {
	VIEW = "view",
	EDIT = "edit",
	BID = "bid"
}

export enum EBidSectionTypes {
	BID_FORM = "BID_FORM",
	WORK_ORDER = "WORK_ORDER",
	COMPLETION_TIME = "COMPLETION_TIME",
	REQUIRED_UPLOADS = "REQUIRED_UPLOADS",
	ACKNOWLEDGE = "ACKNOWLEDGE",
	REQUIRED_DOWNLOADS = "REQUIRED_DOWNLOADS",
	ADDENDA_ACKNOWLEDGE = "ADDENDAS_ACKNOWLEDGEMENT"
}

export enum EBidSectionNames {
	BID_FORM = "Bid Form",
	WORK_ORDER = "Work Order",
	REQUIRED_UPLOADS = "Required Uploads",
	COMPLETION_TIME = "Completion Time",
	ACKNOWLEDGE = "Acknowledge",
	REQUIRED_DOWNLOADS = "Required Downloads",
	ADDENDA_ACKNOWLEDGE = "Addenda Acknowledge"
}

export enum WorkOrderSectionTypes {
	CAWM = "cawm",
	SM = "sm"
}

export interface IEBidChangeDetectionService {
	gatherChanges(oldEBid: EBid, newEbid: EBid): any;
}

export abstract class ValueChangeBaseCommand {
	property: string;
	previousValue: any;
	newValue: any;
	// bidFormSectionFodrmGroup: FormGroup;

	constructor(previousValue: any, newValue: any, property: string) {
		// this.bidFormSectionFormGroup = bidFormSectionFormGroup;
		this.previousValue = previousValue;
		this.newValue = newValue;
		this.property = property;
	}
	patchValue(property: string, control: AbstractControl, data: any) {
		const patchObject: { [key: string]: any } = {};
		patchObject[property] = data;
		control.patchValue(patchObject, { emitEvent: false, onlySelf: true });
		control.updateValueAndValidity();
	}

	abstract patch(data: any): void;
}


export class BidFormChangeData {
	constructor(sectionId: string, sectionType: string, otherData: Array<any> = []) {
		this.sectionId = sectionId;
		this.sectionType = sectionType;
		this.otherData = otherData;
	}
	sectionId: string;
	sectionType: string;
	otherData: Array<any>;
}

export class EBidAddSectionCommand implements EBidCommand {

	eBidFormGroup: FormGroup;
	sectionFormGroup: FormGroup;
	sectionType: string;
	sectionId: string;

	constructor(eBidFormGroup: FormGroup, sectionFormGroup: FormGroup, sectionId: string, sectionType: string) {
		this.eBidFormGroup = eBidFormGroup;
		this.sectionFormGroup = sectionFormGroup;
		this.sectionType = sectionType;
		this.sectionId = sectionId;
	}

	execute(): void {
		let formSelections = this.eBidFormGroup.get('sections') as FormArray;

		if (formSelections) {
			var section = formSelections.value.find((s: any) => s.section.SectionType === this.sectionType);

			if (!section) {
				const sections = this.eBidFormGroup.get('sections') as FormArray;
				sections.push(this.sectionFormGroup);
				sections.markAsDirty();
				sections.markAsTouched();
				sections.updateValueAndValidity();
			}
		} else {
			const sections = this.eBidFormGroup.get('sections') as FormArray;
			if (sections) {
				sections.push(this.sectionFormGroup);
			} else {
				const sections = new FormArray([this.sectionFormGroup]);
				this.eBidFormGroup.addControl('sections', sections);
			}

			sections.markAsDirty();
			sections.markAsTouched();
			sections.updateValueAndValidity();

		}


	}
	undo(): void {
		var sections = this.eBidFormGroup.get('sections') as FormArray;
		var section = sections.controls.find(s => s.value.section.SectionId === this.sectionId);
		var sectionIndex = sections.controls.indexOf(section as FormGroup);
		if (section) {
			sections.removeAt(sectionIndex);

			sections.markAsDirty();
			sections.updateValueAndValidity();
		};
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}

}

export class EBidRemoveSectionCommand implements EBidCommand {
	eBidFormGroup: FormGroup;
	sectionFormGroup: FormGroup | null = null;
	sectionId: string;
	idx: number | null = null;

	constructor(eBidFormGroup: FormGroup, sectionId: string) {
		this.eBidFormGroup = eBidFormGroup;
		this.sectionId = sectionId;
	}
	execute(): void {
		var sections = this.eBidFormGroup.get('sections') as FormArray;
		var section = sections.controls.find(s => s.value.section.SectionId === this.sectionId) as FormGroup;
		var sectionIndex = sections.controls.indexOf(section as FormGroup);
		if (section) {
			this.idx = sectionIndex;
			this.sectionFormGroup = section;
			sections.removeAt(sectionIndex);


			sections.markAsDirty();
			sections.markAsTouched();
			sections.updateValueAndValidity();

			//   this.eBidFormGroup.markAsDirty();
			//   this.eBidFormGroup.markAsTouched();
			//   this.eBidFormGroup.updateValueAndValidity();
		};

	}
	undo(): void {

		const sections = this.eBidFormGroup.get('sections') as FormArray;

		if (this.idx !== null && this.idx !== -1) {
			sections.insert(this.idx, this.sectionFormGroup);
		} else {
			sections.push(this.sectionFormGroup);
		}

		sections.markAsDirty();
		sections.markAsTouched();
		sections.updateValueAndValidity();

		this.idx = null;
		this.sectionFormGroup = null;
	}

	getInfo<Type>(info: any): Type {
		return new MessageInfo('Bid Form Section', 'Bid Form Section') as Type;
	}
}