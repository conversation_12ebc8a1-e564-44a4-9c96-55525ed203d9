import { FormArray, FormGroup } from "@angular/forms";
import { AcknowledgeAddendaSectionInfo, AcknowledgeSectionInfo, AcknowledgementInfo, AlternateItemInfo, BidFormSectionInfo, BidItemInfo, BidderBidInfo, CompletionTimeInfo, CompletionTimeSectionInfo, FormInfo, IEBidBidInfoGatherer, RequiredDownloadInfo, RequiredDownloadsSectionInfo, RequiredUploadsSectionInfo, SectionInfo, WorkOrderSectionInfo } from "./bidder-bid-info";
import { EBidSectionTypes } from "./ebid";
import { HelperTools } from "src/app/shared/utils/helper-tools";

export class EBidInfoGatherer implements IEBidBidInfoGatherer {
	gatherBidInfo(eBidFormGroup: FormGroup): BidderBidInfo {
		let bidInfo: BidderBidInfo = {} as BidderBidInfo;
		bidInfo.SectionsData = [];

		var sections = eBidFormGroup.get('sections') as FormArray;
		
		for (let section of sections.controls) {
			const sectionInfo = section.get(['section']) as FormGroup;
			if(!sectionInfo){
				console.error('Section info not found', section);
				continue;
			}

			const sectionType = sectionInfo.get('SectionType')?.value
			
			if (sectionType  === EBidSectionTypes.BID_FORM) {
				bidInfo.SectionsData.push(this.gatherBidFormInfo(sectionInfo));
			} else if (sectionType === EBidSectionTypes.WORK_ORDER) {
				bidInfo.SectionsData.push(this.gatherWorkOrderInfo(sectionInfo));
			} else if (sectionType === EBidSectionTypes.COMPLETION_TIME) {
				bidInfo.SectionsData.push(this.gatherCompletionTimeInfo(sectionInfo));
			} else if (sectionType === EBidSectionTypes.ACKNOWLEDGE) {
				bidInfo.SectionsData.push(this.gatherAcknowledgeInfo(sectionInfo));
			} else if (sectionType === EBidSectionTypes.REQUIRED_UPLOADS) {
				bidInfo.SectionsData.push(this.gatherRequiredUploadsInfo(sectionInfo));
			} else if (sectionType === EBidSectionTypes.REQUIRED_DOWNLOADS) {
				bidInfo.SectionsData.push(this.gatherRequiredDownloadsInfo(sectionInfo));
			}else if (sectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE) {
				bidInfo.SectionsData.push(this.getAddendaAcknowledgementInfo(sectionInfo));
			}
		}


		return bidInfo;
	}

	private getAddendaAcknowledgementInfo(sectionInfo: FormGroup) {
		const acknowledgeSectionInfo: AcknowledgeAddendaSectionInfo = {} as AcknowledgeAddendaSectionInfo;
		
		acknowledgeSectionInfo._t = ['AddendaAcknowledgeInfoSection'];

		const acknowledgements = sectionInfo.get('Acknowledgements') as FormArray;

		if (acknowledgements) {
			acknowledgeSectionInfo.Acknowledgements = [];

			for (let acknowledgement of acknowledgements.controls) {
				const nAcknowledgement = {
					DocumentId: acknowledgement.get('DocumentId')?.value,
					AcknowledgeAt: acknowledgement.get('AcknowledgeAt')?.value
				} as AcknowledgementInfo;

				acknowledgeSectionInfo.Acknowledgements.push(nAcknowledgement);
			}
		}

		return acknowledgeSectionInfo;
	}
	private gatherRequiredDownloadsInfo(requiredDownloadsSection: FormGroup) {
		const requiredDownloadsSectionInfo: RequiredDownloadsSectionInfo = {} as RequiredDownloadsSectionInfo;
		requiredDownloadsSectionInfo._t = ['RequiredDownloadsInfoSection'];

		var requiredDownloads = requiredDownloadsSection.get('RequiredDownloads') as FormArray<any>;

		var downloads = new Array<RequiredDownloadInfo>();
		for (let download of requiredDownloads.controls) {
			var nDownload = {
				RequiredDownloadId: download.get('RequiredDownloadId')?.value,
				IsDownloaded: download.get('IsDownloaded')?.value
			} as RequiredDownloadInfo;

			downloads.push(nDownload);

		}

		requiredDownloadsSectionInfo.RequiredDownloads = HelperTools.toPascalCase(downloads);

		return requiredDownloadsSectionInfo
	}
	private gatherRequiredUploadsInfo(requiredUploadsSection: FormGroup) {
		const requiredUploadsSectionInfo: RequiredUploadsSectionInfo = {} as RequiredUploadsSectionInfo;
		requiredUploadsSectionInfo._t = ['RequiredUploadsBidderInfoSection'];

		var requiredUploads = requiredUploadsSection.get('RequiredUploads')?.value as Array<any>;

		var uploads = new Array<any>();
		for (let upload of requiredUploads) {

			const innerUploads = upload.Uploads as Array<any>;

			if (innerUploads) {
				for (let innerUpload of innerUploads) {
					uploads.push(innerUpload);
				}
			}

		}

		requiredUploadsSectionInfo.RequiredUploads = HelperTools.toPascalCase(uploads);


		return requiredUploadsSectionInfo;
	}
	private gatherCompletionTimeInfo(completionTimeSection: FormGroup) {
		const completionTimeSectionInfo: CompletionTimeSectionInfo = {} as CompletionTimeSectionInfo;

		completionTimeSectionInfo._t = ['CompletionTimeBidderInfoSection'];

		const completionTimes = completionTimeSection?.get('CompletionTimes') as FormArray;

		if (completionTimes) {
			completionTimeSectionInfo.CompletionTimes = [];

			for (let completionTime of completionTimes.controls) {
				const nCompletionTime = {
					CompletionTimeId: completionTime.get('CompletionTimeId')?.value,
					TimeFrame: completionTime.get('UserCompletionTime')?.value,
					UserValue: completionTime.get('UserTime')?.value
				} as CompletionTimeInfo;

				completionTimeSectionInfo.CompletionTimes.push(nCompletionTime);
			}
		}

		return completionTimeSectionInfo
	}
	private gatherWorkOrderInfo(workOrderSection: FormGroup): WorkOrderSectionInfo {
		const workOrderSectionInfo: WorkOrderSectionInfo = {} as WorkOrderSectionInfo;

		workOrderSectionInfo._t = ['WorkOrderBidderInfoSection'];
		workOrderSectionInfo.SectionType = workOrderSection.get('SectionType')?.value;
		workOrderSectionInfo.IsComplete = workOrderSection.valid;
		workOrderSectionInfo.UserValue = workOrderSection.get('UserValue')?.value;
		workOrderSectionInfo.Total = workOrderSection.get('Total')?.value;

		return workOrderSectionInfo;

	}

	private gatherAcknowledgeInfo(acknowledgeSection: FormGroup) {
		const acknowledgeSectionInfo: AcknowledgeSectionInfo = {} as AcknowledgeSectionInfo;

		acknowledgeSectionInfo._t = ['AcknowledgeBidderInfoSection'];
		acknowledgeSectionInfo.SectionType = acknowledgeSection.get('SectionType')?.value;
		acknowledgeSectionInfo.IsComplete = acknowledgeSection.valid;
		acknowledgeSectionInfo.AcknowledgedAt = acknowledgeSection.get('AcknowledgedAt')?.value;

		return acknowledgeSectionInfo;
	}

	private gatherBidFormInfo(eBidForm: FormGroup): BidFormSectionInfo {
		const bidFormSectionInfo: BidFormSectionInfo = {} as BidFormSectionInfo;

		bidFormSectionInfo.BidTotal = eBidForm.get('BidTotal')?.value;
		bidFormSectionInfo.SectionType = eBidForm.get('SectionType')?.value;
		bidFormSectionInfo.IsComplete = eBidForm.valid;
		bidFormSectionInfo._t = ['BidFormBidderInfoSection'];

		const forms = eBidForm.get('Forms') as FormArray;
		if (forms) {
			bidFormSectionInfo.Forms = [];

			for (let form of forms.controls) {
				let formInfo = {
					FormId: form.get('FormId')?.value,
					FormTotal: form.get('FormTotal')?.value,
					Sections: []
				} as FormInfo;


				const sections = form.get('Sections') as FormArray;

				for (let section of sections.controls) {
					let sectionInfo = {
						BidItems: [],
						SectionId: section.get('SectionId')?.value,
						SectionTotal: section.get('Total')?.value
					} as SectionInfo;

					formInfo.Sections.push(sectionInfo);

					const bidItems = section.get('BidItems') as FormArray;

					for (let bidItem of bidItems.controls) {
						let bidItemInfo = {
							Alternates: [],
							BidItemId: bidItem.get('BidItemId')?.value,
							ExtensionPrice: bidItem.get('ExtensionPrice')?.value,
							Qty: bidItem.get('Qty')?.value,
							UserPrice: bidItem.get('BidderPrice')?.value
						} as BidItemInfo;

						sectionInfo.BidItems.push(bidItemInfo);

						const alternates = bidItem.get('Alternates') as FormArray;

						for (let alternate of alternates.controls) {
							let alternateItemInfo = {
								AlternateId: alternate.get('AlternateId')?.value,
								ExtensionPrice: alternate.get('ExtensionPrice')?.value,
								Qty: alternate.get('Qty')?.value,
								UserPrice: alternate.get('BidderPrice')?.value
							} as AlternateItemInfo;

							bidItemInfo.Alternates.push(alternateItemInfo);
						}
					}
				}


				bidFormSectionInfo.Forms.push(formInfo);
			}
		}


		return bidFormSectionInfo;
	}

}