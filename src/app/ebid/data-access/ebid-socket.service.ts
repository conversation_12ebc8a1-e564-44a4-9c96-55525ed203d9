import { Injectable, signal } from "@angular/core";
import { Subscription } from "rxjs";
import { BaseWebSocketService } from "src/app/shared/utils/base-web-socket";
import { environment } from "src/environments/environment";

@Injectable({
	providedIn: 'root'
  })
export class EBidSocketService extends BaseWebSocketService{

	private wsSubscription: Subscription | null = null;
	private authTokenSubscription: Subscription | null = null;
	eBidIncomingMessage = signal<any>(null);

	override destroy(){	
		super.destroy();	
		this.wsSubscription?.unsubscribe();
		this.authTokenSubscription?.unsubscribe();
		this.eBidIncomingMessage.set(null);		
		this.wsSubscription = null;
		this.authTokenSubscription = null;
		//this.destroy();
	}
	
	connectToWebSocket(projectId: string, ebidArea:string) {
		this.authTokenSubscription = this.authService.getIdentityToken().subscribe(token => {
		  var url = environment.services_web_socket_endpoints.ebid;
		  url= `${url}?token=${token}&projectId=${projectId}&area=${ebidArea}`;
		  this.wsSubscription = this.connect(url)
		  .subscribe((message: MessageEvent) => {
			const data = JSON.parse(message.data);
			console.log('Received message:', data);
			
			this.eBidIncomingMessage.set(data);
			
		  });
		});	
	}
}

export enum EBidActions{
	CHANGE_PAGE = 'page-change',
	MOUSE_CLICK = 'mouse-click',
	BUTTON_CLICK = 'button-click',
	SAVE_BID = 'save-bid',
	DELETE_BID = 'delete-bid',
	ERROR = 'error'
}

export enum EBidActionsContractor{
	ADD_UNIT_PRICE = 'add-unit-price',
	ADD_QTY = 'add-qty',
	SUBMIT_BID = 'submit-bid'
}

export enum EBidActionsEngineer{

	ADD_SECTION = 'add-section'	
}

export enum EBidActionsBidForm{
	EDITING_BID_FORM = 'editing-bid-form',
	EDITING_BID_FORM_SECTION = 'editing-bid-form-section',
	EDITING_BID_FORM_SECTION_FIELD = 'editing-bid-form-section-field',
	ADD_BID_FORM_SECTION = 'add-bid-form-section',
	DELETE_BID_FORM_SECTION = 'delete-bid-form-section',
	ADD_BID_ITEM_ABOVE = 'add-bid-item-above',
	ADD_BID_ITEM_BELOW = 'add-bid-item-below',
	ADD_REPLACEMENT_ALT = 'add-replacement-alt',
	DELETE_BID_ITEM = 'delete-bid-item',
	SET_ALLOW_SET_QTY = 'set-allow-set-qty',
}