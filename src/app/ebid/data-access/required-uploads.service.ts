import { HttpClient } from "@angular/common/http";
import { effect, inject, Injectable, Injector, linkedSignal, signal } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment";
import { BidViews, EBidSection, EBidSectionTypes, RequiredUpload } from "../interfaces/ebid";
import { BidderBidInfo, RequiredUploadInfo, RequiredUploadsSectionInfo } from "../interfaces/bidder-bid-info";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { v4 } from "uuid";
import { FormTools } from "src/app/shared/utils/form-tools";
import { LambdaAWSService } from "src/app/shared/data-access/lambdaaws.service";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { ToastrService } from "ngx-toastr";
import { BaseFormService } from "./base-form-service";
import { EBidFolderService } from "./bid-folder.service";

@Injectable()
export class RequiredUploadService extends BaseFormService {

	eBidFolderService = inject(EBidFolderService);
	httpClient = inject(HttpClient);
	awsService = inject(LambdaAWSService);
	toastrService = inject(ToastrService);
	bidView = this.eBidFolderService.view;
	bidderBidInfoData = this.eBidFolderService.bidderBidInfoData;
	isLoading = this.eBidFolderService.isLoading;
	projectId = this.eBidFolderService.projectId;
	resetAddForm = signal<string | null>(null);
	requiredUploadSection = signal<FormGroup | null>(null);		
	requiredUploadsSettings = signal<Array<RequiredUploadSetting>>([]);


	requiredUploadsFormGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		RequiredUploads: new FormArray<any>([]),
		OnlyPDF: new FormControl(false)
	});

	requiredUploadSetup = signal<boolean>(false);

	constructor() {
		super(inject(Injector));

	}


	startEffects() {
		this.registerEffect(effect(() => {
			this.requiredUploadSetup.set(false);
			if (this.requiredUploadSection()) {
				this.updateFormGroupBaseValues(this.requiredUploadsFormGroup, this.requiredUploadSection()?.value.section);
	
				const arrayInfo = this.getRequiredUploadsArray(this.requiredUploadSection()?.value.section.RequiredUploads || []);
				this.requiredUploadsFormGroup.setControl('RequiredUploads', arrayInfo);
				this.requiredUploadsFormGroup.get('RequiredUploads')?.setValidators(FormTools.minLengthArray(1));
				this.requiredUploadsFormGroup.get('RequiredUploads')?.updateValueAndValidity();
				this.requiredUploadsFormGroup.patchValue({"OnlyPDF" : this.requiredUploadSection()?.value?.section?.OnlyPDF || false});
				this.requiredUploadSection()?.setControl('section', this.requiredUploadsFormGroup, { emitEvent: false });
				this.requiredUploadSetup.set(true);
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.bidderBidInfoData() && this.requiredUploadSetup()) {
				
				var requiredUploadSection = this.bidderBidInfoData()?.SectionsData?.find(s => s.SectionType === EBidSectionTypes.REQUIRED_UPLOADS) as RequiredUploadsSectionInfo;
				if (requiredUploadSection) {
					for (let requiredUpload of requiredUploadSection.RequiredUploads as Array<RequiredUploadInfo>) {
						this.addRequiredUploadInfoToForm(requiredUpload);
					}
				}
				this.requiredUploadsFormGroup?.updateValueAndValidity();
			}
		}, { injector: this.$effectsInjector as Injector }));


		this.registerEffect(effect(() => {
			if (this.bidView() && this.requiredUploadSetup()) {
				var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;
				if (this.bidView() === BidViews.BID) {
					for (let requiredUpload of requiredUploads.controls) {
						requiredUpload.get('Uploads')?.addValidators(FormTools.minLengthArray(1));
						requiredUpload.get('Uploads')?.updateValueAndValidity();
					}
				} else {
					for (let requiredUpload of requiredUploads.controls) {
						requiredUpload.get('Uploads')?.clearValidators();
						requiredUpload.get('Uploads')?.updateValueAndValidity();
					}
				}
	
				this.requiredUploadsFormGroup.updateValueAndValidity();
			}
		}, { injector: this.$effectsInjector as Injector }));
	}

	addRequiredUploadInfoToForm(requiredUploadInfo: RequiredUploadInfo) {
		var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;
		var requiredUpload = requiredUploads.controls.find(r => r.get('RequiredUploadId')?.value === requiredUploadInfo.RequiredUploadId);

		if (requiredUpload) {
			var uploadsArray = requiredUpload.get('Uploads') as FormArray;

			var upload = uploadsArray.controls.find(u => u.get('UploadId')?.value === requiredUploadInfo.UploadId);
			if (!upload) {
				var fg = this.getRequiredUploadsInfoFormGroup(requiredUploadInfo.UploadId,
					requiredUploadInfo.RequiredUploadId, requiredUploadInfo.S3Bucket,
					requiredUploadInfo.DocumentName, requiredUploadInfo.Key,
					requiredUploadInfo.Comment, requiredUploadInfo.Size, requiredUploadInfo.CreatedAt);
				uploadsArray.push(fg);
			} else {
				upload.get('comment')?.setValue(requiredUploadInfo.Comment);
				upload.patchValue({
					comment: requiredUploadInfo.Comment,
					documentName: requiredUploadInfo.DocumentName,
					requiredUploadId: requiredUploadInfo.RequiredUploadId,
					s3Bucket: requiredUploadInfo.S3Bucket,
					key: requiredUploadInfo.Key,
					size: requiredUploadInfo.Size,
					createdAt: requiredUploadInfo.CreatedAt,
					isDownloading: false,
					uploadId: requiredUploadInfo.UploadId,

				})
			}

		}

		requiredUpload?.get('Uploads')?.addValidators(FormTools.minLengthArray(1));
		requiredUpload?.get('Uploads')?.updateValueAndValidity();
		requiredUpload?.updateValueAndValidity();
	}

	downloadFile(key: string, fileName: string, fileGroup: FormGroup) {
		fileGroup.get('IsDownloading')?.setValue(true);
		this.awsService.getDownloadPresignedUrl(key, fileName).subscribe({
			next: (result: any) => {
				HelperTools.downloadFile(result.PresignedUrl, result.FileName);
				fileGroup.get('IsDownloading')?.setValue(false);
			}, error: (err: any) => {
				console.log(err);
				fileGroup.get('IsDownloading')?.setValue(false);
			}
		});
	}

	removeUpload(requiredUploadId: string, uploadId: string) {
		var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;
		var requiredUpload = requiredUploads.controls.find(r => r.get('RequiredUploadId')?.value === requiredUploadId);
		if (requiredUpload) {
			var uploadsArray = requiredUpload.get('Uploads') as FormArray;
			var upload = uploadsArray.controls.find(u => u.get('UploadId')?.value === uploadId);
			if (upload) {
				uploadsArray.removeAt(uploadsArray.controls.indexOf(upload));
			}

			this.requiredUploadsFormGroup.markAsDirty();
			this.requiredUploadsFormGroup.markAllAsTouched();
			this.requiredUploadsFormGroup.updateValueAndValidity();
		}
	}

	addRequiredUploadInfo(requiredUploadId: string, uploadId: string, documentName: string, key: string, s3Bucket: string, createdAt: Date, size: number, comment: string) {
		let nRequiredUploadInfo = {
			RequiredUploadId: requiredUploadId,
			UploadId: uploadId,
			S3Bucket: s3Bucket,
			Key: key,
			DocumentName: documentName,
			CreatedAt: createdAt,
			Size: size,
			Comment: comment
		} as RequiredUploadInfo;

		this.addRequiredUploadInfoToForm(nRequiredUploadInfo);

	}

	getRequiredUploadsArray(requiredUploads: Array<RequiredUpload>): FormArray<any> {
		var formArray = new FormArray<any>([]);

		if (requiredUploads) {
			for (let requiredUpload of requiredUploads) {
				var rUpload = this.getRequiredUploadFormGroup(requiredUpload.RequiredUploadId, requiredUpload.Name, requiredUpload.Notes);
				formArray.push(rUpload);
			}
		}

		return formArray;
	}

	getRequiredUploadFormGroup(requiredUploadId: string, name: string, notes: string): FormGroup {

		const fg = new FormGroup({
			RequiredUploadId: new FormControl(requiredUploadId, Validators.required),
			Name: new FormControl(name, Validators.required),
			Notes: new FormControl(notes),
			IsEditting: new FormControl(false),
			FileUploadInfo: new FormGroup({
				IsProgressing: new FormControl(false),
				UploadProgress: new FormControl(0),
				IsComplete: new FormControl(false),
				Size: new FormControl(0),
				Name: new FormControl('')
			}),
			Uploads: new FormArray<any>([])
		});

		return fg;
	}

	onFileChange(event: any, requiredUploadId: string) {
		console.log(event);
		console.log(requiredUploadId);

		const file = event.target.files[0];
		console.log(file);

		const ext = file.name.split('.').pop();

		if (this.requiredUploadsFormGroup.get('OnlyPDF')?.value && ext !== 'pdf') {
			alert('Only PDF files are allowed');
			return;
		}

		this.uploadFile(file, requiredUploadId).subscribe({
			next: (result: any) => {
				event.target.value = '';
				this.addRequiredUploadInfo(requiredUploadId, result.UploadId, file.name, result.Key, result.S3Bucket, result.CreatedAt, file.size, '');
			}, error: (err: any) => {
				console.log(err);
				event.target.value = '';
			}
		});
	}

	uploadFile(file: any, requiredUploadId: string): Observable<any> {

		return new Observable<any>((observer) => {
			const fileName = `${v4()}-${file.name}`;

			this.getUploadPresignedUrl(requiredUploadId, this.projectId() as string, fileName, file.type).subscribe({
				next: (result: any) => {
					console.log(result);
					var uploadsFormArray = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;
					let requiredUpload = uploadsFormArray.controls.find(r => r.get('RequiredUploadId')?.value === requiredUploadId);
					if (requiredUpload) {
						requiredUpload.get('FileUploadInfo')?.patchValue({
							Size: file.size,
							Name: file.name,
							IsProgressing: true
						});
						this.awsService.uploadFileWithSignedURLWithProgress(result.PresignedUrl, file).subscribe({
							next: (event) => {
								if (event && typeof event === 'number') {
									requiredUpload.get('FileUploadInfo')?.patchValue({
										UploadProgress: +event
									});
								} else if (file.uploadProgress === 100 && !event) {
									requiredUpload.get('FileUploadInfo')?.patchValue({
										UploadProgress: event,
										IsComplete: true
									});
								}

								this.requiredUploadsFormGroup.markAsDirty();
								this.requiredUploadsFormGroup.markAllAsTouched();
								this.requiredUploadsFormGroup.updateValueAndValidity();
							}, error: (err) => {
								observer.error(err);
							}, complete: () => {
								console.log('Upload Complete');
								setTimeout(() => {

									requiredUpload.get('FileUploadInfo')?.patchValue({
										IsProgressing: false,
										UploadProgress: 0,
										IsComplete: false,
										Size: 0,
										Name: ''
									});
									observer.next(result);
									observer.complete();
								}, 1500);

							}
						});
					}

				}, error: (err: any) => {
					console.log(err);
					observer.error(err);
				}
			});
		});


	}

	getRequiredUploadsInfoArray(requiredUploadsInfo: Array<RequiredUploadInfo>): FormArray {
		var formArray = new FormArray<any>([]);

		if (requiredUploadsInfo) {
			for (let requiredUploadInfo of requiredUploadsInfo) {
				var rUpload = this.getRequiredUploadsInfoFormGroup(requiredUploadInfo.UploadId,
					requiredUploadInfo.RequiredUploadId, requiredUploadInfo.S3Bucket,
					requiredUploadInfo.DocumentName, requiredUploadInfo.Key, requiredUploadInfo.Comment, requiredUploadInfo.Size, requiredUploadInfo.CreatedAt);
				formArray.push(rUpload);
			}
		}

		return formArray;
	}

	getRequiredUploadsInfoFormGroup(uploadId: string, requiredUploadId: string, s3Bucket: string, documentName: string, key: string, comment: string, size: number, createdAt: Date): FormGroup {
		return new FormGroup({
			UploadId: new FormControl(uploadId, Validators.required),
			RequiredUploadId: new FormControl(requiredUploadId, Validators.required),
			DocumentName: new FormControl(documentName, Validators.required),
			S3Bucket: new FormControl(s3Bucket, Validators.required),
			Key: new FormControl(key, Validators.required),
			Comment: new FormControl(comment),
			Size: new FormControl(size),
			CreatedAt: new FormControl(createdAt, Validators.required),
			IsDownloading: new FormControl(false)
		});

	}

	addRequiredUpload(requiredUploadId: string, name: string, notes: string) {
		var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;

		const fg = this.getRequiredUploadFormGroup(requiredUploadId as string, name as string, notes as string);

		requiredUploads.push(fg);	

		this.resetAddForm.set(new Date().toISOString());
	}

	addRequiredUploadFromExisting(uploadSetting: RequiredUploadSetting) {
		var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;

		const hasSetting = requiredUploads.controls.find(r => r.get('Name')?.value === uploadSetting.name);
		if (!hasSetting) {
			const fg = this.getRequiredUploadFormGroup(v4(), uploadSetting.name, uploadSetting.notes);

			requiredUploads.push(fg);

			fg.updateValueAndValidity();
			fg.markAsTouched();
			requiredUploads.markAsDirty();
			requiredUploads.markAsTouched();
			requiredUploads.updateValueAndValidity();

		} else {
			this.toastrService.info(`Document ${hasSetting.value.Name} already exists`);
		}


	}

	removeRequiredUpload(requiredUploadId: string) {
		var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;
		var requiredUpload = requiredUploads.controls.find(r => r.get('RequiredUploadId')?.value === requiredUploadId);
		var requiredUploadIndex = requiredUploads.controls.indexOf(requiredUpload as FormGroup);
		if (requiredUpload) {
			requiredUploads.removeAt(requiredUploadIndex);

			requiredUploads.markAsDirty();
			requiredUploads.markAsTouched();
			requiredUploads.updateValueAndValidity();
		}
	}

	setIsEditting(requiredUploadId: string, isEditting: boolean) {
		var requiredUploads = this.requiredUploadsFormGroup.get('RequiredUploads') as FormArray;
		var requiredUpload = requiredUploads.controls.find(r => r.get('RequiredUploadId')?.value === requiredUploadId);
		if (requiredUpload) {
			requiredUpload.get('IsEditting')?.setValue(isEditting);
		}
	}


	getUploadPresignedUrl(requiredUploadId: string, projectId: string, fileName: string, contentType: string): Observable<string> {
		const url = encodeURI(`${environment.services_root_endpoints.bid_ops_ebids}/${projectId}/upload-presigned-url?requiredUploadId=${requiredUploadId}&fileName=${fileName}&contentType=${contentType}`);

		return this.httpClient.get<string>(url);

	}
}

export class RequiredUploadSetting {
	constructor(
		name: string,
		notes: string
	) {
		this.name = name;
		this.notes = notes;
	}

	name: string;
	notes: string;

}