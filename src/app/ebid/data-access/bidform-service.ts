import { ChangeDetectorRef, computed, effect, inject, Injectable, Injector, linkedSignal, Signal, signal, WritableSignal } from "@angular/core";
import { v4 } from "uuid";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { debounceTime, Observable, pairwise, startWith, Subject, Subscription, switchMap, tap, throwError } from "rxjs";
import { AlternateBidItem, BidForm, BidFormSection, BidItem, BidSection, EBidAddBidItemAlternateCommand, EBidAddFormCommand, EBidAddFormSectionBidItemCommand, EBidAddFormSectionCommand, EBidBidFormValueChangeCommand, EBidBidItemAlternateValueChangeCommand, EBidBidItemValueChangeCommand, EBidBidSectionValueChangeCommand, EBidDeleteBidItemAlternateCommand, EBidDeleteFormCommand, EBidDeleteFormSectionBidItemCommand, EBidDeleteFormSectionCommand, EBidDisableBidItemAlternateCommand, EBidDisableFormSectionBidItemCommand, EBidMoveCommand, UnitPriceSettings } from "../interfaces/bid-form-section";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { BidViews, EBidSectionTypes } from "../interfaces/ebid";
import { BaseFormService } from "./base-form-service";
import { EBidHistoryService } from "./ebid-history.service";
import { add, BigNumber, bignumber, evaluate, multiply, number } from "mathjs";
import { BidderBidInfo, BidFormSectionInfo } from "../interfaces/bidder-bid-info";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { LambdaAWSService } from "src/app/shared/data-access/lambdaaws.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ToastrService } from "ngx-toastr";
import { FormTools } from "src/app/shared/utils/form-tools";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { AuthService } from "src/app/shared/data-access/auth.service";
import { EBidFolderService } from "./bid-folder.service";

@Injectable()
export class BidFormService extends BaseFormService {

	httpClient = inject(HttpClient);	
	modalService = inject(NgbModal);
	eBidHistoryService = inject(EBidHistoryService);
	eBidFolderService = inject(EBidFolderService);
	awsService = inject(LambdaAWSService);
	toastrService = inject(ToastrService);
	authService = inject(AuthService);
	recalculateSubscription: Subscription | null = null;
	isCalculating = signal<boolean>(false);	
	bidFormSetupComplete = linkedSignal({
		source: this.eBidFolderService.projectId,
		computation: () => false
	});

	uploadProgress = linkedSignal({
		source: this.eBidFolderService.projectId,
		computation: () => 0
	});

	bidFormSection = signal<FormGroup | null>(null);

	bidderInfoData = this.eBidFolderService.bidderBidInfoData;	
	view = this.eBidFolderService.view;
	isLoading = this.eBidFolderService.isLoading;
	isExporting = signal<boolean>(false);
	isImportBidExcel = signal<boolean>(false);
	isUploadingToS3 = signal<boolean>(false);
	showImportBidFormModal = signal<BidderInfoImportResponse | null>(null);
	bidFormImportIssues = signal<Array<string> | null>(null);
	isUploading = computed(() => this.isUploadingToS3());
	isDragDisabled = signal<boolean>(false);
	isBidderInfoComplete = signal<boolean>(false);	
	showList = signal<boolean>(false);
	settings = {
		maxCurrencyLength: 11,
		maxQtyLength: 10,
		calculateValueChangeDebounce: 400,
	};

	bidFormSectionGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		Forms: new FormArray<any>([])
	});

	private recalculateSubject = new Subject<void>(); // Trigger recalculation

	destroy() {
		this.recalculateSubscription?.unsubscribe();		
		this.stopEffects();
	}	

	constructor() {
		super(inject(Injector));

		this.recalculateSubscription = this.recalculateSubject
		.pipe(
		  switchMap(() => {
			this.isCalculating.set(true); // Set to true when starting
			return this.recalculateTotals(); // Switch to the recalculation observable
		  })
		)
		.subscribe({
		  next: () => {
			this.isCalculating.set(false); // Set to false when done
		  },
		  complete: () => {
			this.isCalculating.set(false); // Also on completion
		  },
		  error: (err) => {
			console.error('Recalculation error:', err);
			this.isCalculating.set(false); // Handle errors
		  },
		});
	}


	startEffects(){
		(this.bidFormSectionGroup.get('Forms') as FormArray)?.clear();

		this.registerEffect(effect(() => {			
			this.isDragDisabled.set(this.view() !== BidViews.EDIT);
	
			if (this.view() === BidViews.BID) {
				//this.recalculate();
			}		

			this.showList.set(true);
			
		},{injector: this.$effectsInjector as Injector}));

		this.registerEffect(effect(() => {			
			this.bidFormSetupComplete.set(false);

			this.updateFormGroupBaseValues(this.bidFormSectionGroup, this.bidFormSection()?.value.section);
			if (this.bidFormSection()?.getRawValue().section.Forms) {
				this.setupForms(this.bidFormSection()?.getRawValue().section.Forms);
			}

			this.bidFormSection()?.setControl('section', this.bidFormSectionGroup, { emitEvent: false });

			this.bidFormSetupComplete.set(true);
			
		},{injector: this.$effectsInjector as Injector}));

		this.registerEffect(effect(() => {
			this.isBidderInfoComplete.set(false);
			if(this.bidderInfoData() && this.bidFormSection() && this.bidFormSetupComplete()){
				const bidFormData = this.bidderInfoData()?.SectionsData?.find(x => x.SectionType === EBidSectionTypes.BID_FORM) as BidFormSectionInfo;
	
				if(bidFormData){	
					this.setupBidderInfoForms(bidFormData, this.bidFormSection()?.get('section') as FormGroup);			
					this.isBidderInfoComplete.set(true);					
				}			
			}else{
				this.setupBidderInfoForms(null, this.bidFormSection()?.get('section') as FormGroup);
			}


		},{injector: this.$effectsInjector as Injector}));

	}
	recalculate() {	
		this.recalculateSubject.next(); // Trigger recalculation			
	}

	recalculateTotals(): Observable<any> {
		return new Observable(observer => {

			var forms = this.bidFormSectionGroup.get('Forms') as FormArray;
			for (let form of forms.controls) {
				const formId = form.get('FormId')?.value;
				var formTotal = bignumber(0);
				var sections = form.get('Sections') as FormArray;
				for (let section of sections.controls) {
					var sectionTotal = bignumber(0);
					var bidItems = section.get('BidItems') as FormArray;

					for (let bidItem of bidItems.controls) {
						var extensionPrice = bignumber(0);

						var alternates = bidItem.get('Alternates') as FormArray;

						var altExtensionPrice: BigNumber | null = null;

						if (alternates && alternates.controls.length > 0) {

							for (let alternate of alternates.controls) {
								const altBidderPriceControl = alternate.get('BidderPrice');
								const altQtyControl = alternate.get('Qty');

								if (altQtyControl?.value === null || altBidderPriceControl?.value === null) {
									continue;
								}

								altExtensionPrice = bignumber(multiply(bignumber(altQtyControl?.value), bignumber(altBidderPriceControl?.value)).toString());

								alternate.patchValue({ ExtensionPrice: number(altExtensionPrice) });

							}
						}

						const bidderPriceControl = bidItem.get('BidderPrice');
						const qtyControl = bidItem.get('Qty');
						const unitPriceControl = bidItem.get('UnitPrice');
						const unitPriceSettingControl = bidItem.get('UnitPriceSetting');

						if (altExtensionPrice) {
							sectionTotal = add(sectionTotal, bignumber(altExtensionPrice.toString()));
						} else {
							if (unitPriceSettingControl?.value === UnitPriceSettings.MAX) {
								if (bidderPriceControl?.value > unitPriceControl?.value) {
									bidItem.patchValue({ ExtensionPrice: null });	
									continue;
								} else {
									if (qtyControl?.value === null || bidderPriceControl?.value === null) {
										continue;
									}

									let qtyValue = bignumber(qtyControl?.value || 0);
									let bidderPriceValue = bignumber(bidderPriceControl?.value || 0);

									extensionPrice = bignumber(multiply(qtyValue, bidderPriceValue).toString());
								}
							} else if (unitPriceSettingControl?.value === UnitPriceSettings.MIN) {
								if (bidderPriceControl?.value < unitPriceControl?.value) {
									bidItem.patchValue({ ExtensionPrice: null });									
									// bidItem.setErrors({ 'minPrice': true });
									continue;
								} else {
									if (qtyControl?.value === null || bidderPriceControl?.value === null) {
										continue;
									}

									let qtyValue = bignumber(qtyControl?.value || 0);
									let bidderPriceValue = bignumber(bidderPriceControl?.value || 0);

									extensionPrice = bignumber(multiply(qtyValue, bidderPriceValue).toString());
								}
							}
							else {
								if (qtyControl?.value === null || bidderPriceControl?.value === null) {
									continue;
								}

								let qtyValue = bignumber(qtyControl?.value || 0);
								let bidderPriceValue = bignumber(bidderPriceControl?.value || 0);

								extensionPrice = bignumber(multiply(qtyValue, bidderPriceValue).toString());
							}

							sectionTotal = add(sectionTotal, extensionPrice);

							bidItem.patchValue({ ExtensionPrice: number(extensionPrice) });
						}


					}

					section.patchValue({ Total: number(sectionTotal) });

					formTotal = add(bignumber(formTotal), bignumber(sectionTotal));
				}

				const fTotal = number(formTotal);

				form.patchValue({ FormTotal: fTotal});
				
				for (let section of sections.controls) {
					var bidItems = section.get('BidItems') as FormArray;
					let bidItemItemWithPercentage =  bidItems.controls.filter(x => x.value.UnitPriceSetting === UnitPriceSettings.PERCENTAGE);

					for(let bi of bidItemItemWithPercentage){						
						var bidderPrice = bi.get('BidderPrice');	
						var notRequired = bi.get('NotRequired')?.value ?? false;
						
						bidderPrice?.clearValidators();
						bidderPrice?.setValidators(FormTools.maxPercentValidator(bi.get('UnitPrice')?.value, fTotal));
						
						if(!notRequired){
							bidderPrice?.addValidators(Validators.required);
						}else{
							bidderPrice?.removeValidators(Validators.required);
						}
						
						bidderPrice?.updateValueAndValidity({ emitEvent: false });

					
					}

					for(let bi of bidItems.controls){
						const alternates = bi.get('Alternates') as FormArray;

						let alternatesWithPercentage = alternates?.controls.filter(x => x.value.UnitPriceSetting === UnitPriceSettings.PERCENTAGE);

						for(var alt of alternatesWithPercentage){
							var altPrice = alt.get('BidderPrice');	
							var altRequired = alt.get('NotRequired')?.value ?? false;

							altPrice?.clearValidators();
							altPrice?.setValidators(FormTools.maxPercentValidator(alt.get('UnitPrice')?.value, fTotal));
							
							if(!altRequired){
								altPrice?.addValidators(Validators.required);
							}else{
								altPrice?.removeValidators(Validators.required);
							}
							
							altPrice?.updateValueAndValidity({ emitEvent: false });
						}
					}
				}		
			}

			observer.next();
			observer.complete();

		});

	}

	setupBidderInfoForms(bidFormData: BidFormSectionInfo | null, bidFormSection: FormGroup) {

		if (bidFormSection && bidFormData) {
			var forms = bidFormSection.get('Forms') as FormArray;
			//var forms = section.get('Forms') as FormArray;

			if (forms) {
				const bidFormTotal = bidFormData.BidTotal;

				if (bidFormTotal) {
					bidFormSection.patchValue({ 'sectionTotal': bidFormTotal }, { emitEvent: false });
				}

				for (let form of forms.controls) {
					const formId = form.get('FormId')?.value;
					const sections = form.get('Sections') as FormArray;
					const bidderBidForm = bidFormData.Forms?.find(f => f.FormId === formId);

					if (bidderBidForm) {
						const formTotal = bidderBidForm.FormTotal;

						if (formTotal) {
							form.patchValue({ "FormTotal": formTotal }, { emitEvent: false });
						}

						for (let section of sections.controls) {
							const sectionId = section.get("SectionId");

							const bidderBidFormSection = bidderBidForm.Sections.find(s => s.SectionId === sectionId?.value);

							if (bidderBidFormSection) {
								const sectionTotal = bidderBidFormSection.SectionTotal;

								if (sectionTotal) {
									section.patchValue({ "Total": sectionTotal }, { emitEvent: false });
								}

								const bidItems = section.get('BidItems') as FormArray;
								for (let bidItem of bidItems.controls) {
									const bidItemId = bidItem.get('BidItemId')?.value;
									const bidItemData = bidderBidFormSection.BidItems.find(b => b.BidItemId === bidItemId);
									const isQtyEditable = bidItem.get('IsQtyEditable')?.value ?? false;
									const isRequired = bidItem.get('NotRequired')?.value ?? false;
									const isDisabled = bidItem.get('IsDisabled')?.value ?? false;

									if (bidItemData) {
										if(!isDisabled){
											bidItem.patchValue({ 
												"ExtensionPrice": bidItemData.ExtensionPrice,
												"BidderPrice": bidItemData.UserPrice,
												"Qty": (isQtyEditable && bidItemData) ? bidItemData.Qty : bidItem.value?.Qty
											}, { emitEvent: false });

											if(bidItem.value.UnitPriceSetting === UnitPriceSettings.PERCENTAGE){
												bidItem?.get("BidderPrice")?.setValidators(FormTools.maxPercentValidator(bidItem?.get("UnitPrice")?.value ?? 0, formTotal));
												bidItem?.get("BidderPrice")?.updateValueAndValidity({ emitEvent: false });
											}
										}									
									

										 if(isDisabled){
											bidItem.get('BidderPrice')?.clearValidators();
											bidItem.get('Qty')?.clearValidators();
											bidItem.get('BidderPrice')?.disable();
											bidItem.get('Qty')?.disable();
										 }

										var alternates = bidItem.get('Alternates') as FormArray;

										for (let alternate of alternates.controls) {
											const alternateId = alternate.get('AlternateId')?.value;
											const altQtyEditable = alternate.get('IsQtyEditable')?.value ?? false;
											const altIsDisabled = alternate.get('IsDisabled')?.value ?? false;

											

											if (!altIsDisabled) {
												if (bidItemData.UserPrice) {
													alternate.get('BidderPrice')?.disable();
													if (alternate.get('IsQtyEditable')?.value) {
														alternate.get('Qty')?.disable();
													}
												} else {
													alternate.get('BidderPrice')?.enable();
													alternate.get('Qty')?.enable();
												}

												if(alternate.value.UnitPriceSetting === UnitPriceSettings.PERCENTAGE){
													alternate?.get("BidderPrice")?.setValidators(FormTools.maxPercentValidator(alternate?.get("UnitPrice")?.value ?? 0, formTotal));
													alternate?.get("BidderPrice")?.updateValueAndValidity({ emitEvent: false });
												}
											}

											if (bidItemData.Alternates) {
												const alternateInfo = bidItemData.Alternates.find(a => a.AlternateId === alternateId);
												const isAltDisabled = alternate.get('IsDisabled')?.value ?? false;
												if (alternateInfo) {
													if (isAltDisabled) {
														if (alternateInfo.UserPrice) {
															alternate.patchValue({
																"Qty": (altQtyEditable && alternateInfo) ? alternateInfo.Qty : alternate.value?.Qty,
																"BidderPrice": alternateInfo.UserPrice,
																"ExtensionPrice": alternateInfo.ExtensionPrice
															}, { emitEvent: false });



															bidItem.get('BidderPrice')?.disable();
															alternate.get('BidderPrice')?.enable();
														} else {
															//alternate.get('BidderPrice')?.disable();
															bidItem.get('BidderPrice')?.enable();
															bidItem.get('Qty')?.enable();
														}
													}
												}
											}

											if(altIsDisabled){
												alternate.get('BidderPrice')?.clearValidators();
												alternate.get('Qty')?.clearValidators();
												alternate.get('BidderPrice')?.disable();
												alternate.get('Qty')?.disable();
											}

											alternate.updateValueAndValidity();
										}								

										bidItem.updateValueAndValidity();
									}
								}
							}
						}
					}
				}				
			}
		}
	}
	addNewBidItem(formId: string, sectionId: string, idx: number | null = null) {
		var nBidItem = {
			UnitPriceSetting: null,
			Unit: '',
			UnitPrice: null,
			BidItemId: v4(),
			Alternates: [],
			BidderPrice: null,
			CustomId1: '',
			CustomId2: null,
			Description: '',
			ExtensionPrice: null,
			IsQtyEditable: false,
			NotRequired: false,
			Qty: 0,
			IsDisabled: false
		} as BidItem

		this.addBidItem(formId, sectionId, nBidItem, true, idx);
	}


	openImportBidFormModal(importResponse: BidderInfoImportResponse) {
		this.showImportBidFormModal.set(importResponse);
	}


	drop(data: any[], event: CdkDragDrop<string[]>) {
		const moveBidItemCommand = new EBidMoveCommand(data, event.previousIndex, event.currentIndex);
		this.eBidHistoryService.executeCommand(moveBidItemCommand);
	}

	addForm(bidFormSectionFormGroup: FormGroup, form: BidForm, idx: number | null = null, skipChangeRequest: boolean = false, isNew = true) {
		const addFormCommand = new EBidAddFormCommand(bidFormSectionFormGroup, this.getForm(form.FormId, form.Title, form.FormTotal,form.CountTowardsTotal, this.getSectionsArray(form.FormId, form.Sections, isNew), isNew), idx, form.FormId);
		this.eBidHistoryService.executeCommand(addFormCommand);
	}

	deleteForm(formId: string, skipChangeRequest: boolean = false) {
		const deleteFormCommand = new EBidDeleteFormCommand(this.bidFormSectionGroup, formId);
		this.eBidHistoryService.executeCommand(deleteFormCommand);
	}

	addSection(formId: string, section: BidSection, idx: number | null = null, isNew: boolean = true, skipChangeRequest: boolean = false) {
		const sectionGroup = this.getBidSection(section.Title, formId, section.SectionId, section.Total, this.getBidItemArray(formId, section.SectionId, section.BidItems, isNew), isNew);
		const addSectionCommand = new EBidAddFormSectionCommand(this.bidFormSectionGroup, sectionGroup, idx, formId, section.SectionId);
		this.eBidHistoryService.executeCommand(addSectionCommand);
	}

	deleteSection(formId: string, sectionId: string, skipChangeRequest: boolean = false) {
		const deleteSectionCommand = new EBidDeleteFormSectionCommand(this.bidFormSectionGroup, formId, sectionId);
		this.eBidHistoryService.executeCommand(deleteSectionCommand);
	}

	deleteAlternate(formId: string, sectionId: string, bidItemId: string, alternateId: string, skipChangeRequest: boolean = false) {
		const deleteAlternateCommand = new EBidDeleteBidItemAlternateCommand(this.bidFormSectionGroup, formId, sectionId, bidItemId, alternateId);
		this.eBidHistoryService.executeCommand(deleteAlternateCommand);
	}

	disableAlternate(formId: string, sectionId: string, bidItemId: string, alternateId: string, skipChangeRequest: boolean = false) {		
		const deleteAlternateCommand = new EBidDisableBidItemAlternateCommand(this.bidFormSectionGroup, formId, sectionId, bidItemId, alternateId);
		this.eBidHistoryService.executeCommand(deleteAlternateCommand);
	}

	deleteBidItem(formId: string, sectionId: string, bidItemId: string, skipChangeRequest: boolean = false) {
		const deleteBidItemCommand = new EBidDeleteFormSectionBidItemCommand(this.bidFormSectionGroup, formId, sectionId, bidItemId);
		this.eBidHistoryService.executeCommand(deleteBidItemCommand);
	}

	disableBidItem(formId: string, sectionId: string, bidItemId: string, isDisabled: boolean, skipChangeRequest: boolean = false) {
		const deleteBidItemCommand = new EBidDisableFormSectionBidItemCommand(this.bidFormSectionGroup, formId, sectionId, bidItemId);
		this.eBidHistoryService.executeCommand(deleteBidItemCommand);	
	}


	addBidItem(formId: string, sectionId: string, bidItem: BidItem, isNew:boolean, idx: number | null = null, skipChangeRequest: boolean = false) {
		const bidItemForm = this.getBidItemForm(
			formId,
			sectionId,
			bidItem.BidItemId,
			bidItem.Description,
			bidItem.CustomId1,
			bidItem.CustomId2,
			bidItem.Qty,
			bidItem.Unit,
			bidItem.UnitPrice,
			bidItem.ExtensionPrice,
			bidItem.IsQtyEditable,
			bidItem.NotRequired,
			bidItem.UnitPriceSetting,
			bidItem.Alternates,
			isNew,			
			bidItem.IsDisabled,
			bidItem.BidderPrice);

		const addBidItemCommand = new EBidAddFormSectionBidItemCommand(this.bidFormSectionGroup, bidItemForm, idx, formId, sectionId, bidItem.BidItemId);
		this.eBidHistoryService.executeCommand(addBidItemCommand);

	}

	addNewReplacementAlternate(formId: string, sectionId: string, bidItemId: string) {
		var nReplacementAlt = {
			AlternateId: v4(),
			UnitPriceSetting: null,
			Unit: '',
			UnitPrice: null,
			BidItemId: bidItemId,
			Alternates: [],
			BidderPrice: null,
			CustomId1: '',
			CustomId2: null,
			Description: '',
			ExtensionPrice: null,
			IsQtyEditable: false,
			NotRequired: false,
			Qty: 0,
			IsDisabled: false,
			IsNew: true
		} as AlternateBidItem

		this.addReplacementAlternate(formId, sectionId, bidItemId, nReplacementAlt, null, false, true);
	}

	addReplacementAlternate(formId: string, sectionId: string, bidItemId: string, alternateBidItem: AlternateBidItem, idx: number | null = null, skipChangeRequest: boolean = false, isNew = false) {
		const alternateItemForm = this.getAlternateBidItem(
			formId,
			sectionId,
			bidItemId,
			alternateBidItem.AlternateId,
			alternateBidItem.Description,
			alternateBidItem.CustomId1,
			alternateBidItem.CustomId2,
			alternateBidItem.Qty,
			alternateBidItem.Unit,
			alternateBidItem.UnitPrice,
			alternateBidItem.ExtensionPrice,
			alternateBidItem.IsQtyEditable,
			alternateBidItem.NotRequired,
			alternateBidItem.UnitPriceSetting,
			alternateBidItem.IsDisabled,
			isNew,		
			alternateBidItem.BidderPrice);

		const addAlternateItemCommand = new EBidAddBidItemAlternateCommand(this.bidFormSectionGroup, alternateItemForm, idx, formId, sectionId, bidItemId, alternateBidItem.AlternateId);
		this.eBidHistoryService.executeCommand(addAlternateItemCommand);

	}


	setupForms(forms: BidForm[], isNew: boolean = false) {
		var formsArray = this.bidFormSectionGroup.get('Forms') as FormArray<any>;
		formsArray.clear();

		for (let form of forms) {
			let formFG = this.getForm(form.FormId, form.Title, form.FormTotal, form.CountTowardsTotal, this.getSectionsArray(form.FormId, form.Sections, isNew), isNew);
			formsArray.push(formFG);
		}

		this.bidFormSectionGroup.setControl('Forms', formsArray);
		this.bidFormSectionGroup.get('Forms')?.setValidators(FormTools.minLengthArray(1));
		this.bidFormSectionGroup.get('Forms')?.updateValueAndValidity();

		this.setupForm();
	}



	setupForm() {
		var forms = this.bidFormSectionGroup.get('Forms') as FormArray;

		for (let form of forms.controls) {
			var sections = form.get('Sections') as FormArray;

			for (let section of sections.controls) {
				var bidItems = section.get('BidItems') as FormArray;

				for (let bidItem of bidItems.controls) {
					const bidItemQty = bidItem.get('Qty');
					const bidderPrice = bidItem.get('BidderPrice');
					const unitPriceSetting = bidItem.get('UnitPriceSetting');
					const unitPrice = bidItem.get('UnitPrice');
					const isQtyEditable = bidItem.get('IsQtyEditable');
					const extensionPrice = bidItem.get('ExtensionPrice');
					const alternates = bidItem.get('Alternates') as FormArray;
					const notRequired = bidItem.get('NotRequired')?.value;

					if (this.view() === BidViews.BID) {

						if (!bidderPrice?.hasValidator(Validators.required))
							bidderPrice?.addValidators(Validators.required);

						if (unitPriceSetting?.value === UnitPriceSettings.FIXED) {
							if (unitPrice?.value)
								bidderPrice?.patchValue(unitPrice.value, { emitEvent: false });
						}else if(unitPriceSetting?.value === UnitPriceSettings.MIN){
							if(unitPrice?.value){
								if(!bidderPrice?.hasValidator(Validators.min(unitPrice?.value)))
									bidderPrice?.addValidators(Validators.min(unitPrice?.value));								
							}
							
						}else if(unitPriceSetting?.value === UnitPriceSettings.MAX){
							if(unitPrice?.value){
								if(!bidderPrice?.hasValidator(Validators.max(unitPrice?.value)))
									bidderPrice?.addValidators(Validators.max(unitPrice?.value));								
							}
						}else if(unitPriceSetting?.value === UnitPriceSettings.PERCENTAGE){
							// if(unitPrice?.value){
							// 	if(!bidderPrice?.hasValidator(FormTools.maxPercentValidator(unitPrice?.value, 0)))
							// 		bidderPrice?.addValidators(FormTools.maxPercentValidator(unitPrice?.value, 0));								
							// }
						}
			

						bidderPrice?.updateValueAndValidity();

						if (isQtyEditable?.value) {
							if (!bidItemQty?.hasValidator(Validators.required))
								bidItemQty?.addValidators(Validators.required);

						} else {
							bidItemQty?.clearValidators();
						}

						if (notRequired) {
							bidderPrice?.removeValidators(Validators.required);
						} else {
							if (!bidderPrice?.hasValidator(Validators.required))
								bidderPrice?.addValidators([Validators.required]);
						}


						bidderPrice?.valueChanges.pipe(debounceTime(this.settings.calculateValueChangeDebounce)).subscribe((value) => {
														
							if(bidderPrice.dirty){						

								if(value){
									for (let alternate of alternates.controls) {
										alternate.disable({ emitEvent: false });
										
									}
								
								}else{
									for (let alternate of alternates.controls) {
										alternate.enable({ emitEvent: false });
										
									}
								}

								this.recalculate();
							}
						});

						bidItemQty?.valueChanges.pipe(debounceTime(this.settings.calculateValueChangeDebounce)).subscribe((value) => {
							if(bidItemQty.dirty){
								if (!value) {
									extensionPrice?.patchValue(null, { emitEvent: false });
								}

								this.recalculate();
							}
						});

						for (let alternate of alternates.controls) {
							const alternateBidderPrice = alternate.get('BidderPrice');
							const alternateQty = alternate.get('Qty');
							const alternateExtensionPrice = alternate.get('ExtensionPrice');

							alternateBidderPrice?.addValidators(Validators.required);
							alternateQty?.valueChanges.pipe(debounceTime(this.settings.calculateValueChangeDebounce)).subscribe((value) => {
								if(alternateQty.dirty){
									this.recalculate();

									if(alternateQty?.value === null){
										alternateExtensionPrice?.patchValue(null, { emitEvent: false });
									}
								}
							});

							alternateBidderPrice?.valueChanges.pipe(debounceTime(this.settings.calculateValueChangeDebounce)).subscribe((value) => {
							

								if(alternateBidderPrice.dirty){								

									if (value) {
										bidderPrice?.patchValue(null, { emitEvent: false });
										extensionPrice?.patchValue(null, { emitEvent: false });
										bidderPrice?.disable();
										
										if(isQtyEditable){
											bidItemQty?.disable();
										}
										
									} else {
										alternateExtensionPrice?.patchValue(null, { emitEvent: false });
										bidderPrice?.enable();
										bidItemQty?.enable();
									}

									this.recalculate();
								}
								
							});
						}
					} else if (this.view() === BidViews.EDIT) {
						bidderPrice?.clearValidators();

					
						unitPriceSetting?.valueChanges.subscribe((value) => {	
							if(value === UnitPriceSettings.PERCENTAGE){						
								bidItemQty?.setValue(1, { emitEvent: false });								
							}
						});

						for (let alternate of alternates.controls) {
							const altUnitPriceSetting = alternate.get('UnitPriceSetting');
							altUnitPriceSetting?.valueChanges.subscribe((value) => {
								if(value === UnitPriceSettings.PERCENTAGE){
									alternate.get('Qty')?.setValue(1, { emitEvent: false });
								}
							});
						
						}
						
					}

					bidItemQty?.updateValueAndValidity();
					bidderPrice?.updateValueAndValidity();
				}
			}

			form.updateValueAndValidity();
		}
	}

	getForm(formId: string, title: string, total: number, countTowardsTotal: boolean, sections: FormArray<any>, isNew: boolean = false) {
		const fg = new FormGroup({
			FormId: new FormControl(formId, Validators.required),
			Title: new FormControl(title, Validators.required),
			FormTotal: new FormControl(total),
			IsNew: new FormControl(isNew),
			CountTowardsTotal: new FormControl(countTowardsTotal),
			Sections: sections			
		});

		fg.get('Sections')?.setValidators(FormTools.minLengthArray(1));
		fg.get('Sections')?.updateValueAndValidity();


		this.setupFormGroupListeners(fg, ['Title'], { FormId: formId, Action: 'Edit', ActionType: "form" });

		return fg;
	}


	getSectionsArray(formId: string, sections: Array<BidSection>, isNew: boolean): FormArray<any> {
		let sectionsArray = new FormArray<any>([]);

		if (sections) {
			for (let section of sections) {

				sectionsArray.push(this.getBidSection(section.Title, formId, section.SectionId, section.Total, this.getBidItemArray(formId, section.SectionId, section.BidItems, isNew), isNew));
			}
		}



		return sectionsArray;
	}


	getBidSection(sectionTitle: string, formId: string, sectionId: string, sectionTotal: number, bidItems: FormArray<any>, isNew: boolean = false) {
		const fg = new FormGroup({
			SectionId: new FormControl(sectionId, Validators.required),
			Title: new FormControl(sectionTitle, Validators.required),
			Total: new FormControl(sectionTotal),
			IsNew: new FormControl(isNew),
			BidItems: bidItems
		});

		fg.get('BidItems')?.setValidators(FormTools.minLengthArray(1));
		fg.get('BidItems')?.updateValueAndValidity();

		this.setupFormGroupListeners(fg, ['Title'], { FormId: formId, SectionId: sectionId, Action: 'Edit', ActionType: "section" });

		return fg;
	}

	getBidItemArray(formId: string, sectionId: string, bidItems: Array<BidItem>, isNew: boolean): FormArray<any> {

		if (!bidItems) {
			throw new Error("BidItems cannot be null");
		}

		let bidItemsArray = new FormArray<any>([]);

		for (let bidItem of bidItems) {
			bidItemsArray.push(this.getBidItemForm(
				formId,
				sectionId,
				bidItem.BidItemId,
				bidItem.Description,
				bidItem.CustomId1,
				bidItem.CustomId2,
				bidItem.Qty,
				bidItem.Unit,
				bidItem.UnitPrice,
				bidItem.ExtensionPrice,
				bidItem.IsQtyEditable,
				bidItem.NotRequired,
				bidItem.UnitPriceSetting,
				bidItem.Alternates,
				isNew,
				bidItem.IsDisabled,
				bidItem.BidderPrice));
		}

		return bidItemsArray;
	}

	getBidItemForm(formId: string, sectionId: string, bidItemId: string,
		description: string,
		customId1: string,
		customId2: string | null,
		qty: number,
		unit: string | null,
		unitPrice: number | null,
		extensionPrice: number | null,
		isQtyEditable: boolean = false,
		notRequired: boolean = false,
		unitPriceSetting: string | null = null,
		alternates: Array<any> | null = null,
		isNew: boolean = false,
		isDisabled: boolean = false,
		bidderPrice: number | null = null) {

		var alternatesArray = alternates ? new FormArray(alternates.map(alternate =>
			this.getAlternateBidItem(
				formId,
				sectionId,
				alternate.BidItemId,
				alternate.AlternateId,
				alternate.Description,
				alternate.CustomId1,
				alternate.CustomId2,
				alternate.Qty,
				alternate.Unit,
				alternate.UnitPrice,
				alternate.ExtensionPrice,
				alternate.IsQtyEditable,
				alternate.NotRequired,
				alternate.UnitPriceSetting,
				isNew,
				alternate.BidderPrice))) : new FormArray([]);

		var fg = new FormGroup({
			BidItemId: new FormControl(bidItemId, Validators.required),
			Description: new FormControl(description, Validators.required),
			CustomId1: new FormControl(customId1, Validators.required),
			CustomId2: new FormControl(customId2),
			Qty: new FormControl(qty),
			Unit: new FormControl(unit, Validators.required),
			UnitPrice: new FormControl(unitPrice),
			ExtensionPrice: new FormControl(extensionPrice),
			IsDisabled: new FormControl(isDisabled),
			UnitPriceSetting: new FormControl(unitPriceSetting),
			IsQtyEditable: new FormControl(isQtyEditable),
			NotRequired: new FormControl(notRequired),
			BidderPrice: new FormControl(bidderPrice),
			Alternates: alternatesArray,
			IsNew: new FormControl(isNew)
		});

		if (unitPrice) {
			fg.get('UnitPriceSetting')?.addValidators(Validators.required);
			fg.get('UnitPriceSetting')?.updateValueAndValidity();
		}

		if (!isQtyEditable) {
			fg.get('Qty')?.addValidators(Validators.required);
			fg.get('Qty')?.updateValueAndValidity();
		}


		const bidItemQty = fg.get('Qty');

		fg.get('IsQtyEditable')?.valueChanges.subscribe((value) => {
			if (value) {
				if (bidItemQty?.hasValidator(Validators.required)) {
					bidItemQty?.removeValidators(Validators.required);
					bidItemQty?.updateValueAndValidity();
				}
			} else {
				if (!bidItemQty?.hasValidator(Validators.required)) {
					bidItemQty?.addValidators(Validators.required);
					bidItemQty?.updateValueAndValidity();
				}
			}
		});


		const bidUnitPrice = fg.get('UnitPrice');
		const bidUnitPriceSetting = fg.get('UnitPriceSetting');

		bidUnitPrice?.valueChanges.subscribe((value) => {
			if (value) {
				if (!bidUnitPriceSetting?.hasValidator(Validators.required)) {
					bidUnitPriceSetting?.addValidators(Validators.required);
					bidUnitPriceSetting?.updateValueAndValidity();
				}
			} else {
				if (bidUnitPriceSetting?.hasValidator(Validators.required)) {
					bidUnitPriceSetting?.removeValidators(Validators.required);
					bidUnitPriceSetting?.updateValueAndValidity();
				}
			}
		});

		bidUnitPriceSetting?.valueChanges.subscribe((value) => {
			if (value) {
				if (!bidUnitPrice?.hasValidator(Validators.required)) {
					bidUnitPrice?.addValidators(Validators.required);
					bidUnitPrice?.updateValueAndValidity();
				}

				if(value === UnitPriceSettings.PERCENTAGE){						
					bidItemQty?.setValue(1, { emitEvent: false });								
				}
		
			} else {
				if (bidUnitPrice?.hasValidator(Validators.required)) {
					bidUnitPrice?.removeValidators(Validators.required);
					bidUnitPrice?.updateValueAndValidity();
				}
			}
		});

		var bgData = { FormId: formId, SectionId: sectionId, BidItemId: bidItemId, Action: 'Edit', ActionType: "biditem" };

		this.setupFormGroupListeners(fg, ['Description', 'CustomId1', 'CustomId2', 'Qty', 'Unit', 'UnitPrice', 'UnitPriceSetting', 'IsQtyEditable', 'NotRequired'], bgData);

		var bgDataSort = { FormId: formId, SectionId: sectionId, BidItemId: bidItemId, Action: 'Edit', ActionType: "biditem-sort" };

		return fg;
	}


	getAlternateBidItem(
		formId: string,
		sectionId: string,
		bidItemId: string,
		alternateId: string,
		description: string,
		customId1: string,
		customId2: string | null,
		qty: number | null,
		unit: string,
		unitPrice: number | null,
		extensionPrice: number | null,
		isQtyEditable: boolean = false,
		notRequired: boolean = false,
		unitPriceSetting: string | null = null,
		isDisabled: boolean = false,
		isNew: boolean = false,
		bidderPrice: number | null = null) {
		const fg = new FormGroup({
			AlternateId: new FormControl(alternateId, Validators.required),
			BidItemId: new FormControl(bidItemId, Validators.required),
			Description: new FormControl(description, Validators.required),
			CustomId1: new FormControl(customId1, Validators.required),
			CustomId2: new FormControl(customId2),
			Qty: new FormControl(qty),
			Unit: new FormControl(unit, Validators.required),
			UnitPrice: new FormControl(unitPrice),
			ExtensionPrice: new FormControl(extensionPrice),
			UnitPriceSetting: new FormControl(unitPriceSetting),
			IsQtyEditable: new FormControl(isQtyEditable),
			NotRequired: new FormControl(notRequired),
			BidderPrice: new FormControl(bidderPrice),
			IsDisabled: new FormControl(isDisabled),
			IsNew: new FormControl(isNew)
		});

		if (unitPrice) {
			fg.get('UnitPriceSetting')?.addValidators(Validators.required);
			fg.get('UnitPriceSetting')?.updateValueAndValidity();
		}

		if (!isQtyEditable) {
			fg.get('Qty')?.addValidators(Validators.required);
			fg.get('Qty')?.updateValueAndValidity();
		}

		const bidItemQty = fg.get('Qty');

		fg.get('IsQtyEditable')?.valueChanges.subscribe((value) => {
			if (value) {
				if (bidItemQty?.hasValidator(Validators.required)) {
					bidItemQty?.removeValidators(Validators.required);
					bidItemQty?.updateValueAndValidity();
				}
			} else {
				if (!bidItemQty?.hasValidator(Validators.required)) {
					bidItemQty?.addValidators(Validators.required);
					bidItemQty?.updateValueAndValidity();
				}
			}
		});


		const bidUnitPrice = fg.get('UnitPrice');
		const bidUnitPriceSetting = fg.get('UnitPriceSetting');

		bidUnitPrice?.valueChanges.subscribe((value) => {
			if (value) {
				if (!bidUnitPriceSetting?.hasValidator(Validators.required)) {
					bidUnitPriceSetting?.addValidators(Validators.required);
					bidUnitPriceSetting?.updateValueAndValidity();
				}
			} else {
				if (bidUnitPriceSetting?.hasValidator(Validators.required)) {
					bidUnitPriceSetting?.removeValidators(Validators.required);
					bidUnitPriceSetting?.updateValueAndValidity();
				}
			}
		});

		bidUnitPriceSetting?.valueChanges.subscribe((value) => {
			if (value) {
				if (!bidUnitPrice?.hasValidator(Validators.required)) {
					bidUnitPrice?.addValidators(Validators.required);
					bidUnitPrice?.updateValueAndValidity();
				}
			} else {
				if (bidUnitPrice?.hasValidator(Validators.required)) {
					bidUnitPrice?.removeValidators(Validators.required);
					bidUnitPrice?.updateValueAndValidity();
				}
			}
		});

		var bgData = { FormId: formId, SectionId: sectionId, BidItemId: bidItemId, AlternateId: fg.get('AlternateId')?.value, Action: 'Edit', ActionType: "biditem-alt" }

		this.setupFormGroupListeners(fg, ['Description', 'CustomId1', 'CustomId2', 'Qty', 'Unit', 'UnitPrice', 'UnitPriceSetting', 'IsQtyEditable', 'NotRequired'], bgData);

		return fg;
	}

	setupFormGroupListeners(fg: FormGroup, names: Array<string>, bidChangeData: any) {
		for (let name of names) {
			fg.get(name)?.valueChanges.pipe(
				debounceTime(400),
				startWith(fg.get(name)?.value),
				pairwise()
			).subscribe(([prevValue, currValue]) => {
				if (prevValue === currValue)
					return;

				if (bidChangeData.ActionType === "biditem") {
					const valueChangeCommand = new EBidBidItemValueChangeCommand(this.bidFormSectionGroup, bidChangeData.FormId, bidChangeData.SectionId, bidChangeData.BidItemId, name, prevValue, currValue);
					this.eBidHistoryService.addCommand(valueChangeCommand);
				} else if (bidChangeData.ActionType === "biditem-alt") {
					const valueChangeCommand = new EBidBidItemAlternateValueChangeCommand(this.bidFormSectionGroup, bidChangeData.FormId, bidChangeData.SectionId, bidChangeData.BidItemId, bidChangeData.AlternateId, name, prevValue, currValue);
					this.eBidHistoryService.addCommand(valueChangeCommand);
				} else if (bidChangeData.ActionType === "section") {
					const valueChangeCommand = new EBidBidSectionValueChangeCommand(this.bidFormSectionGroup, bidChangeData.FormId, bidChangeData.SectionId, name, prevValue, currValue);
					this.eBidHistoryService.addCommand(valueChangeCommand);
				} else if (bidChangeData.ActionType === "form") {
					const formChangeCommand = new EBidBidFormValueChangeCommand(this.bidFormSectionGroup, bidChangeData.FormId, name, prevValue, currValue);
					this.eBidHistoryService.addCommand(formChangeCommand);
				}
			});
		}
	}

	uploadFileToS3(file: File, s3FolderName: string) {
		return new Observable<{ bucket: string, key: string }>(observer => {
			this.isUploadingToS3.set(true);
			this.uploadProgress.set(0);

			this.authService.getSubId().then((subId) => {

				if(subId){
					const key = `online-bids/${this.eBidFolderService.projectId()}/${s3FolderName}/${subId}/${v4()}.xlsx`;
					this.awsService.getUploadPresignedUrl(key, file.type).subscribe({
						next: (value) => {
							this.awsService.uploadFileWithSignedURLWithProgress(value.PresignedUrl, file).subscribe({
								next: (event) => {
									if (event && typeof event === 'number') {
										this.uploadProgress.set(+event);
									} else if (this.uploadProgress() === 100 && !event) {	
										// this.fileUploadForm.reset();
										this.isUploadingToS3.set(false);
	
										observer.next({ bucket: value.Bucket, key: key });
										observer.complete();
									}
								}
							});
						},
						error: (err) => {
							this.isUploadingToS3.set(false);
							observer.error(err);
						}
					});
				}else{
					this.isUploadingToS3.set(false);
					observer.error("SubId is required");
				}
				
			});


		});
	}

	importBidderInfo(file: File) {

		if (!this.eBidFolderService.projectId()) {
			throw new Error("Project Id is required");
		}

		if (!file) {
			throw new Error("File is required");
		}


		if (file) {
			this.isImportBidExcel.set(true);
			this.uploadFileToS3(file, 'bid-form-import').subscribe({
				next: (fileInfo: { bucket: string, key: string }) => {

					this.getBidderBidSectionInfo(new BidFormExcelImportRequest(this.eBidFolderService.projectId() as string, fileInfo.bucket, fileInfo.key)).subscribe({
						next: (response: BidderInfoImportResponse) => {
							if (response) {												

								
								if(response.Infos?.length > 0 || response.Warnings.length > 0 || response.Errors.length > 0 || response.Critical.length > 0){
									this.openImportBidFormModal(response);
								}else{
									this.setupBidderInfoForms(response.BidFormSectionInfo, this.bidFormSectionGroup);
								}

								this.isImportBidExcel.set(false);

								
							}
						},
						error: (err) => {
							console.log(err);
							this.isImportBidExcel.set(false);
							this.toastrService.error('Error uploading file');
						}
					});

				},
				error: (err) => {
					console.log(err);
					this.isImportBidExcel.set(false);
					this.toastrService.error('Error uploading file');
				}
			});
		}
	}


	importBidForm(file: File) {
		if (file) {
			this.isImportBidExcel.set(true);
			this.uploadFileToS3(file, 'bid-form-import').subscribe({
				next: (fileInfo: { bucket: string, key: string }) => {

					const request = new BidFormExcelImportRequest(this.eBidFolderService.projectId() as string, fileInfo.bucket, fileInfo.key);
					this.getBidFormFromExcel(request).subscribe({
						next: (response: BidFormExcelImportResponse) => {
							if (response) {
								this.isImportBidExcel.set(false);
								this.setupForms(response.BidFormSection.Forms as BidForm[]);
								if(response.Issues.length > 0){
									this.bidFormImportIssues.set(response.Issues);
								}
							}

							
							this.bidFormSectionGroup.markAsDirty();
							this.bidFormSectionGroup.markAllAsTouched();
							this.bidFormSectionGroup.updateValueAndValidity();
						},
						error: (err) => {
							this.isImportBidExcel.set(false);
							console.log(err);
							this.toastrService.error('Error uploading file');
						}
					});
				},
				error: (err) => {
					this.isImportBidExcel.set(false);
					console.log(err);
					this.toastrService.error('Error uploading file');
				}
			});
		}
	}

	exportBidFormToFile() {
		this.isExporting.set(true);
		const request = new ExportBidFormExcelRequest(this.eBidFolderService.projectId() as string);

		this.exportBidForm(request).subscribe({
			next: (response) => {
				if (response) {
					HelperTools.downloadFile(response.PresignedUrl);					
				}

				this.isExporting.set(false);
			}, error: (err) => {
				console.error(err);
				this.isExporting.set(false);
				this.toastrService.error('Error exporting file');
			}
		});
	}

	getBidderBidSectionInfo(request: BidFormExcelImportRequest) {
		if (!request.projectId)
			return throwError(() => "ProjectId is required");

		if (!request.s3Bucket)
			return throwError(() => "S3Bucket is required");

		if (!request.s3Key)
			return throwError(() => "S3Key is required");

		return this.httpClient.get<BidderInfoImportResponse>(`${environment.services_root_endpoints.bid_ops_ebids}/${request.projectId}/import-bidform?bucket=${request.s3Bucket}&key=${request.s3Key}`);
	}

	getBidFormFromExcel(request: BidFormExcelImportRequest) {
		if (!request.projectId)
			return throwError(() => "ProjectId is required");

		if (!request.s3Bucket)
			return throwError(() => "S3Bucket is required");

		if (!request.s3Key)
			return throwError(() => "S3Key is required");

		return this.httpClient.get<BidFormExcelImportResponse>(`${environment.services_root_endpoints.adverts_ebid}/${request.projectId}/import-bidform?s3Bucket=${request.s3Bucket}&s3Key=${request.s3Key}`);
	}

	exportBidForm(request: ExportBidFormExcelRequest) {
		if (!request.projectId)
			return throwError(() => "ProjectId is required");

		// if (!request.projectTitle)
		// 	return throwError(() => "Project Title is required");

		return this.httpClient.get<ExportBidFormExcelResponse>(`${environment.services_root_endpoints.adverts_ebid}/${request.projectId}/export-bidform`);
	}

}

export class ExportBidFormExcelRequest {
	constructor(projectId: string) {
		this.projectId = projectId;
	}
	requestId: string = v4();
	projectId: string;

}

export interface ExportBidFormExcelResponse {
	PresignedUrl: string;
}

export class BidFormExcelImportRequest {
	constructor(projectId: string, s3Bucket: string, s3Key: string) {
		this.projectId = projectId;
		this.s3Bucket = s3Bucket;
		this.s3Key = s3Key;
	}

	s3Bucket: string;
	s3Key: string;
	projectId: string;

}

export interface BidFormExcelImportResponse {
	BidFormSection: BidFormSection;
	Issues: Array<string>;
}

export interface BidderInfoImportResponse {
	BidFormSectionInfo: BidFormSectionInfo;
	Errors: Array<string>;
	Warnings: Array<string>;
	Infos: Array<string>;
	Success: Array<string>;
	Critical: Array<string>;
}