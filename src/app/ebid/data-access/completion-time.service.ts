import { HttpClient, HttpHeaders } from "@angular/common/http";
import { effect, inject, Injectable, Injector, OnD<PERSON>roy, signal } from "@angular/core";
import { FormGroup, FormControl, Validators, FormArray } from "@angular/forms";
import { Observable } from "rxjs";
import { FormTools } from "src/app/shared/utils/form-tools";
import { BidderBidInfo, CompletionTimeSectionInfo } from "../interfaces/bidder-bid-info";
import { CompletionRemoveTimeSectionCommand, CompletionAddTimeSectionCommand, CompletionTime, CompletionTimeOptions } from "../interfaces/completion-time-section";
import { BidViews, EBidSectionTypes } from "../interfaces/ebid";
import { BaseFormService } from "./base-form-service";
import { EBidHistoryService } from "./ebid-history.service";
import { EBidFolderService } from "./bid-folder.service";

@Injectable()
export class CompletionTimeService extends BaseFormService implements OnDestroy {
	httpClient = inject(HttpClient);
	eBidHistoryService = inject(EBidHistoryService);
	eBidFolderService = inject(EBidFolderService);
	bidView = this.eBidFolderService.view;
	bidderInfoData = this.eBidFolderService.bidderBidInfoData;
	isLoading = this.eBidFolderService.isLoading;
	completionTypes = signal<Array<string> | null>(null);	
	completionTimeSection = signal<FormGroup | null>(null);
	completionTimeSetup = signal<boolean>(false);
	isBidderInfoComplete = signal<boolean>(false);	
	defaultCompletionTimes: Array<string> = ["Complete", "Substantially Complete", "User Decides"];
	public readonly COMPLETION_TIME_OPTIONS: typeof CompletionTimeOptions = CompletionTimeOptions;
	completionTimeOptions = [this.COMPLETION_TIME_OPTIONS.CALENDAR_DAYS, this.COMPLETION_TIME_OPTIONS.WORK_DAYS, this.COMPLETION_TIME_OPTIONS.USER_DECIDES];
	bidderCompletionTimeOptions = [this.COMPLETION_TIME_OPTIONS.CALENDAR_DAYS, this.COMPLETION_TIME_OPTIONS.WORK_DAYS];
	completionTimeFormGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		CompletionTimes: new FormArray<any>([], FormTools.minLengthArray(1)),
	});


	constructor() {
		super(inject(Injector));
	}
	ngOnDestroy(): void {
		console.log('Destroying CompletionTimeService');
	}

	startEffects() {
		(this.completionTimeFormGroup.get('CompletionTimes') as FormArray).clear();
		this.registerEffect(effect(() => {
			this.completionTimeSetup.set(false);
			if (this.completionTimeSection()) {			
				this.updateFormGroupBaseValues(this.completionTimeFormGroup, this.completionTimeSection()?.value.section);
	
				const arrayInfo = this.getCompletionTimesFormArray(this.completionTimeSection()?.value.section.CompletionTimes || []);
				this.completionTimeFormGroup.setControl('CompletionTimes', arrayInfo);
				this.completionTimeSection()?.setControl('section', this.completionTimeFormGroup, { emitEvent: false });
				this.completionTimeFormGroup.get('CompletionTimes')?.setValidators(FormTools.minLengthArray(1));
				this.completionTimeFormGroup.get('CompletionTimes')?.updateValueAndValidity();
				this.completionTimeSetup.set(true);
			}
	
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.bidView()) {
				var completionTimes = this.completionTimeFormGroup.get('CompletionTimes') as FormArray;
				for (let item of completionTimes.controls) {
					const userTimeFG = item.get('UserTime');
	
					if (this.bidView() === BidViews.BID) {
						const maxValue = item.get('MaxDays')?.value;
						userTimeFG?.addValidators(Validators.required);
	
						if (maxValue) {
							userTimeFG?.addValidators(Validators.max(maxValue));
						}
	
						if(item.get('CompletionTimeOption')?.value === this.COMPLETION_TIME_OPTIONS.USER_DECIDES){
							item.get('UserCompletionTime')?.setValidators(Validators.required);
						}else{
							item.get('UserCompletionTime')?.clearValidators();
						}
						//
					} else {
						userTimeFG?.clearValidators();
						item.get('UserCompletionTime')?.clearValidators();
					}
	
					userTimeFG?.updateValueAndValidity();
					item.get('UserCompletionTime')?.updateValueAndValidity();
				}
				
				this.completionTimeFormGroup.updateValueAndValidity();
			}
		}, { injector: this.$effectsInjector as Injector }));
	
		this.registerEffect(effect(() => {
			this.isBidderInfoComplete.set(false);
			if (this.bidderInfoData() && this.completionTimeSection() && this.completionTimeSetup()) {
				const completionTimeInfoData = this.bidderInfoData()?.SectionsData?.find(x => x.SectionType === EBidSectionTypes.COMPLETION_TIME) as CompletionTimeSectionInfo;
				if (completionTimeInfoData) {
	
					if (completionTimeInfoData.CompletionTimes) {
						const completionTimesArray = this.completionTimeFormGroup.get('CompletionTimes') as FormArray;
	
						if (completionTimesArray) {
							for (let ct of completionTimeInfoData.CompletionTimes) {
								const ctInfo = completionTimesArray.controls.find(x => x.get('CompletionTimeId')?.value === ct.CompletionTimeId);
								if (ctInfo) {
									ctInfo.get('UserTime')?.setValue(ct.UserValue);
									ctInfo.get('UserCompletionTime')?.setValue(ct.TimeFrame);
								}
							}
						}

						this.isBidderInfoComplete.set(true);
					}
				}
			}
		}, { injector: this.$effectsInjector as Injector }));
	}

	destroy() {
		this.stopEffects();
	}




	remove(completionTimeId: string) {
		const removeSection = new CompletionRemoveTimeSectionCommand(this.completionTimeFormGroup, completionTimeId);
		this.eBidHistoryService.executeCommand(removeSection);
		this.completionTimeFormGroup.markAsDirty();
		this.completionTimeFormGroup.markAllAsTouched();
	}

	addTime(completionTime: CompletionTime): void {
		const addSection = new CompletionAddTimeSectionCommand(this.completionTimeFormGroup, this.getCompletionTimeFormGroup(completionTime), completionTime.CompletionTimeId);
		this.eBidHistoryService.executeCommand(addSection);
		this.completionTimeFormGroup.markAsDirty();
		this.completionTimeFormGroup.markAllAsTouched();
	}

	getCompletionTimesFormArray(completionTimes: Array<CompletionTime>): FormArray<any> {
		var formArray = new FormArray<any>([]);

		completionTimes.forEach(completionTime => {
			formArray.push(this.getCompletionTimeFormGroup(completionTime));
		});

		return formArray;
	}

	getCompletionTimeFormGroup(completionTime: CompletionTime): FormGroup {
		return new FormGroup({
			CompletionTimeId: new FormControl(completionTime.CompletionTimeId, Validators.required),
			CompletionTimeOption: new FormControl(completionTime.CompletionTimeOption, Validators.required),
			CompletionType: new FormControl(completionTime.CompletionType, Validators.required),
			MaxDays: new FormControl(completionTime.MaxDays),
			UserTime: new FormControl(null),
			UserCompletionTime: new FormControl(completionTime.CompletionTimeOption === this.COMPLETION_TIME_OPTIONS.USER_DECIDES ? null : completionTime.CompletionType)
		});
	}


	setupBidderInfo(bidderInfo: BidderBidInfo): Observable<any> {
		return new Observable<any>(observer => {
			if (bidderInfo) {

			}

			observer.next();
			observer.complete();
		});
	}

	getCompletionTypes() {
		if (!this.completionTypes()) {
			const headers = new HttpHeaders({
				'ContentType': 'application/json'
			});

			this.httpClient.get<Array<string>>('./assets/data/completion-types.json', { headers }).subscribe({
				next: (completionTypes) => {
					this.completionTypes.set(completionTypes);
				},
				error: (err) => {
					console.log(err);
				}
			});
		}

	}
}