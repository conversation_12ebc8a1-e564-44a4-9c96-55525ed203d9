import { computed, effect, inject, Injectable, Injector, signal } from "@angular/core";
import { toSignal, toObservable } from "@angular/core/rxjs-interop";
import { tap } from "rxjs";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";
import { HelperTools } from "src/app/shared/utils/helper-tools";

@Injectable()
export class BidClockService extends BaseEffectsService {

	endDate = signal<Date | undefined>(undefined);
	timeLeft = signal<number>(-1); //in seconds
	timeInterval: any = null;
	startDate = signal<Date>(new Date());

	daysLeft = signal<number>(0);
	monthsLeft = signal<number>(0);
	yearsLeft = signal<number>(0);
	hoursLeft = signal<number>(0);
	minutesLeft = signal<number>(0);
	secondsLeft = signal<number>(0);
	isBidExpiringSoon = computed(() => this.timeLeft() <= 60 * 5); // 5 minutes
	clockStart = signal<ClockInfo | null>(null);
	clockEnd = signal<ClockInfo | null>(null);	

	clockInjector = inject(Injector);

	constructor(){
		super();
	}

	registerClock(clockId: string){
		
	}

	startEffects(){
		this.registerEffect(effect(() => {
			this.setTimeLeft(this.timeLeft());
		}, {injector: this.clockInjector}));

		this.registerEffect(effect(() => {
			if (this.clockEnd()) {
				clearInterval(this.timeInterval);
				this.setTimeLeft(0);				
			}
		}, {injector: this.clockInjector}));

		this.registerEffect(effect(() => {
			this.calculateTimeLeft(this.endDate() as Date);
		}, {injector: this.clockInjector}));
	}


	calculateTimeLeft(endDate: Date): void {
		if(endDate){
			const now = HelperTools.getUTCDate().getTime();
			const end = new Date(endDate).getTime();
			const difference = end - now;
	
			if (difference > 0) {
				this.timeLeft.set(Math.floor(difference / 1000));
				this.startDate.set(new Date(now));
				
				this.timeInterval = setInterval(() => {
					if (this.timeLeft() > 0) {
						this.timeLeft.update((currentValue) => { return currentValue = currentValue - 1 });
					}else{
						this.clockEnd.set(new ClockInfo(this.startDate(), this.endDate() as Date, this.timeLeft(), this.clockStart()?.projectId as string));
					}
				}, 1000);
			} else {
				this.timeLeft.set(0);
				this.clockEnd.set(new ClockInfo(this.startDate(), this.endDate() as Date, this.timeLeft(), this.clockStart()?.projectId as string));
			}
		}

	}

	secondsToMonths(seconds: number) {
		const secondsInMonth = 60 * 60 * 24 * 30;
		return seconds / secondsInMonth;
	}



	secondsToDays(seconds: number) {
		const secondsInDay = 60 * 60 * 24;
		return seconds / secondsInDay;
	}

	secondsToHours(seconds: number) {
		const hours = Math.floor(seconds / 3600);
		return hours;
	}

	secondsToMinutes(seconds: number) {
		return seconds / 60;
	}

	setTimeLeft(totalSeconds: number) {
		const secondsInMinute = 60;
		const secondsInHour = 60 * 60;
		const secondsInDay = 60 * 60 * 24;
		const secondsInMonth = 60 * 60 * 24 * 30.44; // Average month length in seconds
		const secondsInYear = 60 * 60 * 24 * 365.25; // Average year length in seconds

		const years = Math.floor(totalSeconds / secondsInYear);
		totalSeconds %= secondsInYear;

		const months = Math.floor(totalSeconds / secondsInMonth);
		totalSeconds %= secondsInMonth;

		const days = Math.floor(totalSeconds / secondsInDay);
		totalSeconds %= secondsInDay;

		const hours = Math.floor(totalSeconds / secondsInHour);
		totalSeconds %= secondsInHour;

		const minutes = Math.floor(totalSeconds / secondsInMinute);
		const seconds = totalSeconds % secondsInMinute;

		this.secondsLeft.set(seconds);
		this.minutesLeft.set(minutes);
		this.hoursLeft.set(hours);
		this.daysLeft.set(days);
		this.monthsLeft.set(months);
		this.yearsLeft.set(years);

		//return `${years} years, ${months} months, ${days} days, ${hours} hours, ${minutes} minutes, ${seconds} seconds`;
	}
}

export class ClockInfo {
	constructor(startTime: Date, endTime: Date, remainingTime: number, projectId: string) {
		this.startTime = startTime;
		this.endTime = endTime;
		this.remainingTime = remainingTime;
		this.projectId = projectId
	}
	startTime: Date;
	endTime: Date;
	remainingTime: number;
	projectId: string;
}