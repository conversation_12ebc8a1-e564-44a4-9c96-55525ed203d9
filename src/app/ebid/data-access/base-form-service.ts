import { FormArray, FormGroup } from "@angular/forms";
import { EBidSectionTypes, EBidSection } from "../interfaces/ebid";
import { EffectRegistrar } from "src/app/shared/interfaces/effect-registrar";
import { Injector } from "@angular/core";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";

export abstract class BaseFormService extends BaseEffectsService {
	$effectsInjector: Injector | null = null;
	constructor(effectsInjector: Injector | null = null) {
		super();
		this.$effectsInjector = effectsInjector;
	}
	
	getSectionFormGroup(formArray: FormArray, sectionType: EBidSectionTypes): FormGroup {
		return formArray.controls.find(x => x.get('section')?.value.SectionType === sectionType) as FormGroup;		
	}


	updateFormGroupBaseValues(formGroup: FormGroup, section: EBidSection) {
		formGroup.patchValue({
			SectionName: section.SectionName,
			SectionId: section.SectionId,
			SectionType: section.SectionType
		}, { emitEvent: false, onlySelf: true });
	}
}