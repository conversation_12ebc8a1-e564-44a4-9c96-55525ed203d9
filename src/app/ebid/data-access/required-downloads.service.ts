import { effect, Inject, inject, Injectable, Injector, linkedSignal, signal } from "@angular/core";
import { BidViews, DocumentInfo, EBidSectionTypes, RequiredDownload } from "../interfaces/ebid";
import { FormGroup, FormControl, Validators, FormArray } from "@angular/forms";
import { FormTools } from "src/app/shared/utils/form-tools";
import { v4 } from "uuid";
import { BidderBidInfo, RequiredDownloadsSectionInfo, RequiredDownloadInfo } from "../interfaces/bidder-bid-info";
import { BaseFormService } from "./base-form-service";
import { EBidService } from "./ebid.service";
import { EBidFolderService } from "./bid-folder.service";

@Injectable()
export class RequiredDownloadsService extends BaseFormService {	
	eBidFolderService = inject(EBidFolderService);
	projectInfoDocuments = signal<Array<DocumentInfo> | null>(null);	
	requiredDownloadsSection = signal<FormGroup | null>(null);
	// view = signal<BidViews>(BidViews.BID);
	setupComplete = signal<boolean>(false);
	projectDocumentDownloadHistory = signal<Array<string> | null>(null);	
	view = this.eBidFolderService.view;
	bidderBidInfoData = this.eBidFolderService.bidderBidInfoData;		
	isLoading = this.eBidFolderService.isLoading;

	bidderInfoData = signal<BidderBidInfo | null>(null);  
	downloadDocumentRequest = signal<DocumentInfo | null>(null);	
	isDownloadHistoryLoading = signal<boolean>(false);

	requiredDownloadsFormGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		RequiredDownloads: new FormArray<any>([], [FormTools.minLengthArray(1), FormTools.atLeastOneRequiredValidator('IsRequired')]),
	});

	constructor() {
		super(inject(Injector));	
	}

	startEffects() {
		(this.requiredDownloadsFormGroup.get('RequiredDownloads') as FormArray).clear();

		this.registerEffect(effect(() => {
			this.setupComplete.set(false);
			if (this.projectInfoDocuments() && this.requiredDownloadsSection()) {		
				this.updateFormGroupBaseValues(this.requiredDownloadsFormGroup, this.requiredDownloadsSection()?.value.section);
	
				const downloads = this.getRequiredDownloadsArray(this.projectInfoDocuments() as DocumentInfo[], this.requiredDownloadsSection()?.value.section.RequiredDownloads as Array<RequiredDownload>);
				this.requiredDownloadsFormGroup.setControl('RequiredDownloads', downloads);
				this.requiredDownloadsFormGroup.get('RequiredDownloads')?.setValidators([FormTools.minLengthArray(1), FormTools.atLeastOneRequiredValidator('IsRequired')]);
				this.requiredDownloadsFormGroup.get('RequiredDownloads')?.updateValueAndValidity();
				this.requiredDownloadsSection()?.setControl('section', this.requiredDownloadsFormGroup, { emitEvent: false });
				this.setupComplete.set(true);			
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.bidderInfoData() && this.requiredDownloadsSection() && this.projectInfoDocuments() && this.setupComplete()) {
				var requiredDownloadSection = this.bidderInfoData()?.SectionsData.find(s => s.SectionType === EBidSectionTypes.REQUIRED_DOWNLOADS) as RequiredDownloadsSectionInfo;
				if (requiredDownloadSection) {
					if(requiredDownloadSection.RequiredDownloads){
						for (let requiredDownload of requiredDownloadSection.RequiredDownloads as Array<RequiredDownloadInfo>) {
							var rdArray = this.requiredDownloadsFormGroup.get('RequiredDownloads') as FormArray;
							var rd = rdArray.controls.find(x => x.get('RequiredDownloadId')?.value === requiredDownload.RequiredDownloadId) as FormGroup;
							if (rd) {
								rd.get("IsDownloaded")?.setValue(requiredDownload.IsDownloaded, { emitEvent: false });
							}
						}
					}					
				} 
			}
		}, { injector: this.$effectsInjector as Injector }));
	
		this.registerEffect(effect(() => {
			if (this.view() && this.setupComplete()) {
				var requiredDownloads = this.requiredDownloadsFormGroup.get('RequiredDownloads') as FormArray;
	
				if (this.view() === BidViews.BID) {
					for (let fg of requiredDownloads.controls) {
						if (fg.value.IsRequired) {
							fg.get('IsDownloaded')?.addValidators(Validators.required);
							fg.get('IsDownloaded')?.updateValueAndValidity();
						} else {
							fg.get('IsDownloaded')?.clearValidators();
							fg.get('IsDownloaded')?.updateValueAndValidity();
						}
					}
				} else {
					for (let fg of requiredDownloads.controls) {
						 fg.get('IsDownloaded')?.clearValidators();
						 fg.get('IsDownloaded')?.updateValueAndValidity();
					}
				}
	
				this.requiredDownloadsFormGroup.updateValueAndValidity();
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if(this.setupComplete() && this.projectDocumentDownloadHistory()){
				for(let documentId of this.projectDocumentDownloadHistory() as Array<string>){
					this.markDownloadComplete(documentId);
				}	
			}		
			this.isDownloadHistoryLoading.set(false);
		}, { injector: this.$effectsInjector as Injector }));
	}


	getRequiredDownloadsArray(docs: Array<DocumentInfo>, requiredDownloads: Array<RequiredDownload>): FormArray<any> {
		let formArray = new FormArray<any>([]);

		if (this.view() === BidViews.EDIT) {
			for (let document of docs) {
				const rd = requiredDownloads?.find(d => d.DocumentId === document.DocumentId) as RequiredDownload;
				let fg = new FormGroup({});
				if (!rd) {
					const rd = {
						RequiredDownloadId: v4(),
						DocumentId: document.DocumentId,
						IsRequired: false
					} as RequiredDownload;

					fg = this.getRequiredDownloadFormGroup(rd, document.DocumentName, document.Category, document.IsDownloading, document.IsDownloaded)

				} else {
					fg = this.getRequiredDownloadFormGroup(rd, document.DocumentName, document.Category, document.IsDownloading, document.IsDownloaded);
				}

				formArray.push(fg);
			}
		} else {
			for (let document of requiredDownloads.filter(x => x.IsRequired)) {
				const doc = docs.find(x => x.DocumentId === document.DocumentId) as DocumentInfo;
				if (doc) {
					let fg = this.getRequiredDownloadFormGroup(document, doc.DocumentName, doc.Category, doc.IsDownloading, doc.IsDownloaded);
					formArray.push(fg);
				} else {
					let fg = this.getRequiredDownloadFormGroup(document, '', '', false, null);
					formArray.push(fg);
				}

			}
		}

		return formArray;
	}

	getRequiredDownloadFormGroup(requiredDownload: RequiredDownload, documentName: string, category: string, isDownloading: boolean = false, isDownloaded: boolean | null): FormGroup {
		var fg = new FormGroup({
			DocumentName: new FormControl(documentName),
			Category: new FormControl(category),
			IsDownloaded: new FormControl(isDownloaded),
			RequiredDownloadId: new FormControl(requiredDownload.RequiredDownloadId, Validators.required),
			DocumentId: new FormControl(requiredDownload.DocumentId, Validators.required),
			IsDownloading: new FormControl(isDownloading),
			IsRequired: new FormControl(requiredDownload.IsRequired)
		});

		return fg;
	}

	downloadFile(documentId: string) {
		const download = this.projectInfoDocuments()?.find(x => x.DocumentId === documentId) as DocumentInfo | null;
		if (download) {						
			this.downloadDocumentRequest.set(download);
		}else{
			this.downloadDocumentRequest.set(null);
		}		
	}

	
	markDownloadComplete(documentId: string) {
		var requiredDownloads = this.requiredDownloadsFormGroup.get('RequiredDownloads') as FormArray;
		const reqDownload = requiredDownloads?.controls.find(x => x.get('DocumentId')?.value === documentId);

		if (reqDownload) {
			reqDownload.get('IsDownloading')?.setValue(false, { emitEvent: false });
			const isDownloaded = reqDownload.get('IsDownloaded');
			if(isDownloaded)
				isDownloaded.setValue(true, { emitEvent: false });
		}
	}
}



export class GatherHistoryRequest {
	constructor(projectId: string, documentIds: Array<string>) {
		this.ProjectId = projectId;
		this.DocumentIds = documentIds;
	}

	ProjectId: string;
	DocumentIds: Array<string>;

}

export class DownloadHistoryInfo {
	constructor(documents: Array<ReqDocumentInfo>, projectId: string) {
		this.Documents = documents;
		this.ProjectId = projectId;
	}
	Documents: Array<ReqDocumentInfo>;
	ProjectId: string;
}

export class ReqDocumentInfo {
	constructor(documentId: string, isDownloaded: boolean) {
		this.DocumentId = documentId;
		this.IsDownloaded = isDownloaded;
	}
	DocumentId: string;
	IsDownloaded: boolean;
}

export class DownloadComplete {
	constructor(public documentId: string) {
		this.DocumentId = documentId;
	}
	DocumentId: string;
}