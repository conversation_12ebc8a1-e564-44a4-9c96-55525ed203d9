import { effect, inject, Injectable, Injector, linkedSignal, OnInit, signal } from "@angular/core";
import { BaseFormService } from "./base-form-service";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { BidViews, EBidSectionTypes } from "../interfaces/ebid";
import { AcknowledgeSectionInfo, BidderBidInfo } from "../interfaces/bidder-bid-info";
import { debounceTime, startWith, pairwise, tap, of, Observable } from "rxjs";
import { DisclaimerValueChangeCommand } from "../interfaces/acknowledge-section";
import { EBidHistoryService } from "./ebid-history.service";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";
import { EBidFolderService } from "./bid-folder.service";

Injectable()
export class AcknowledgeService extends BaseFormService{
	eBidHistoryService = inject(EBidHistoryService);
	eBidFolderService = inject(EBidFolderService);
	acknowledgeSection = signal<FormGroup | null>(null);
	projectId = this.eBidFolderService.projectId;
	bidView = this.eBidFolderService.view;
	bidderInfoData = this.eBidFolderService.bidderBidInfoData;
	s3Prefix = signal<string | null>(null);
	bidSettingDisclaimer = signal<string | null>(null);	
	acknowledgeFormGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		Disclaimer: new FormControl('', Validators.required),
		AcknowledgedAt: new FormControl<Date | null>(null)
	});
	acknowledgeSetup = signal<boolean>(false);	
	
	constructor(){
		super(inject(Injector));

	}

	startEffects(){
		
		this.registerEffect(effect(() => {
			this.acknowledgeSetup.set(false);
			if (this.acknowledgeSection()) {
				this.updateFormGroupBaseValues(this.acknowledgeFormGroup, this.acknowledgeSection()?.value?.section);
	
				this.acknowledgeFormGroup.patchValue({
					Disclaimer: this.acknowledgeSection()?.value?.section.Disclaimer
				}, { emitEvent: false, onlySelf: true });
	
				this.acknowledgeFormGroup.get('Disclaimer')?.valueChanges.pipe(
					debounceTime(400),
					startWith(this.acknowledgeFormGroup.get('Disclaimer')?.value),
					pairwise()
				).subscribe(([prevValue, currValue]) => {
					if (prevValue === currValue)
						return;
	
					const valueChangeCommand = new DisclaimerValueChangeCommand(this.acknowledgeFormGroup, 'Disclaimer', prevValue, currValue);
					this.eBidHistoryService.addCommand(valueChangeCommand);
	
				});
	
				this.acknowledgeSection()?.setControl('section', this.acknowledgeFormGroup, { emitEvent: false });
				this.s3Prefix.set(`public/project/${this.projectId()}/online-bid/disclaimer`);
	
				this.acknowledgeSetup.set(true);
			}
		}, {injector: this.$effectsInjector as Injector}));

		this.registerEffect(effect(() => {
			if (this.bidView() && this.acknowledgeSetup()) {
				if (this.bidView() === BidViews.BID) {
					this.acknowledgeFormGroup.get('AcknowledgedAt')?.addValidators(Validators.required);
				} else {
					this.acknowledgeFormGroup.get('AcknowledgedAt')?.removeValidators(Validators.required);
				}
	
				this.acknowledgeFormGroup.get('AcknowledgedAt')?.updateValueAndValidity();
			}
		}, {injector: this.$effectsInjector as Injector}));

		this.registerEffect(effect(() => {
			if (this.bidderInfoData() && this.acknowledgeSection() && this.acknowledgeSetup()) {
				const acknowledgeInfoData = this.bidderInfoData()?.SectionsData?.find(x => x.SectionType === EBidSectionTypes.ACKNOWLEDGE) as AcknowledgeSectionInfo;
	
				if (acknowledgeInfoData) {
					this.acknowledgeFormGroup.patchValue({
						AcknowledgedAt: acknowledgeInfoData.AcknowledgedAt
					});
				}
			}
		}, {injector: this.$effectsInjector as Injector}));
	}	

	accept() {
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.setValue(HelperTools.getUTCDate());
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.markAsDirty();
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.markAsTouched();
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.updateValueAndValidity()
	  }
	
	  undo() {
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.setValue(null);
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.markAsDirty();
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.markAsTouched();
		this.acknowledgeFormGroup.get('AcknowledgedAt')?.updateValueAndValidity()
	  }
	  clear() {
		this.acknowledgeFormGroup.get('Disclaimer')?.setValue(null);
	  }
	
	  importDisclaimer() {
		this.acknowledgeFormGroup.get('Disclaimer')?.setValue(this.bidSettingDisclaimer());
		this.acknowledgeFormGroup.markAsTouched();
		this.acknowledgeFormGroup.markAsDirty();
	  }
}