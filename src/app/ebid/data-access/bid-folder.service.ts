import { effect, inject, Injectable, Injector, signal } from "@angular/core";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { EBid, EBidAddSectionCommand, EBidRemoveSectionCommand, EBidSection } from "../interfaces/ebid";
import { EBidHistoryService } from "./ebid-history.service";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";
import { BidderBidInfo } from "../interfaces/bidder-bid-info";
import { CivCastAccountService } from "src/app/shared/data-access/civcast-account-service";
import { HelperTools } from "src/app/shared/utils/helper-tools";

Injectable();
export class EBidFolderService extends BaseEffectsService {

	eBidHistoryService = inject(EBidHistoryService);	
	 accountService = inject(CivCastAccountService);
	eBid = signal<EBid | null>(null);
	view = signal<string | null>(null);
	projectId = signal<string | null>(null);
	ebidFormGroup = new FormGroup({
		IsEnabled: new FormControl(true, Validators.required),
		BeginDate: new FormControl(new Date(), Validators.required),
		BeginTimeZone: new FormControl<string | null>(null, Validators.required),
		ProjectId: new FormControl<string | null>(null, Validators.required),
		sections: new FormArray([])
	});
	
	addSectionSignal = signal<{ name: string, type: string } | null>(null);
	removeSectionSignal = signal<{ name: string, type: string } | null>(null);
	sectionSetupComplete = signal<boolean>(false);
	bidderBidInfoData = signal<BidderBidInfo | null>(null);
	isLoading = signal(false);
	folderEffectInjector = inject(Injector);
	constructor() {
		super();

		this.ebidFormGroup.get('BeginDate')?.valueChanges.subscribe((value) => {
			console.log(value);
		});
	}

	startEffects() {
		this.registerEffect(effect(() => {		
			if(this.eBid()){
				if(this.eBid()?.Sections) {
					let sections = this.ebidFormGroup.get('sections') as FormArray;
	
					if (!sections) {
						this.ebidFormGroup.addControl('sections', new FormArray([]));
						sections = this.ebidFormGroup.get('sections') as FormArray;
					}
		
					sections.clear({ emitEvent: false });
		
					for (let section of this.eBid()?.Sections as Array<EBidSection>) {
						const sectionExists = sections?.controls.find((s: any) => s.value.section.SectionId === section.SectionId);
						if (!sectionExists) {
							sections.push(this.getEBidSectionFormGroup(section));
						}
					}
				}
	
				this.ebidFormGroup.patchValue(
					{
						ProjectId: this.eBid()?.ProjectId,
						IsEnabled: this.eBid()?.IsEnabled,
						BeginDate: this.eBid()?.BeginDate,
						BeginTimeZone: this.eBid()?.BeginTimeZone
					}, {emitEvent: false});

					this.ebidFormGroup.markAsPristine();
					
					


				
			}else{
				this.ebidFormGroup.patchValue(
					{
						ProjectId: this.projectId(),
						IsEnabled: true,
						BeginDate: this.getCurrentHourDate(),
						BeginTimeZone: HelperTools.getBrowserTimezone(),
					}, {emitEvent: false});


					let sections = this.ebidFormGroup.get('sections') as FormArray;
					sections.clear({ emitEvent: false });

					this.ebidFormGroup.markAsPristine();
			}
	
			this.sectionSetupComplete.set(true);
		}, { injector: this.folderEffectInjector as Injector }));
	}

	getEBidSectionFormGroup(section: EBidSection): FormGroup {
		var fg = new FormGroup({
			section: new FormControl<EBidSection>(section, Validators.required)
		});

		return fg;
	}

	addNewSection(nSection: EBidSection) {
		if (nSection) {
			const addSectionCommand = new EBidAddSectionCommand(this.ebidFormGroup, this.getEBidSectionFormGroup(nSection), nSection.SectionId, nSection.SectionType);
			this.eBidHistoryService.executeCommand(addSectionCommand);
			this.addSectionSignal.set({ name: nSection.SectionName, type: nSection.SectionType });
		}
	}

	removeSection(sectionId: string) {
		const sections = this.ebidFormGroup.get('sections') as FormArray;
		if (sections) {
			const section = sections.controls.find((s: any) => s.value.section.SectionId === sectionId);

			if (section) {
				const removeSectionCommand = new EBidRemoveSectionCommand(this.ebidFormGroup, sectionId);
				this.eBidHistoryService.executeCommand(removeSectionCommand);
				this.removeSectionSignal.set({ name: section.value.section.SectionName, type: section.value.section.SectionType });
			}
		}
	}

	getCurrentHourDate(): Date {
		const now = new Date();
		now.setMinutes(0, 0, 0); // Set minutes, seconds, and milliseconds to 0
		return now;
	}
}