import { effect, inject, Injectable, Injector, signal } from "@angular/core";
import { BaseFormService } from "./base-form-service";
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { BidViews, EBidSectionTypes, WorkOrderSectionTypes } from "../interfaces/ebid";
import { add, multiply } from "mathjs";
import { BidderBidInfo, WorkOrderSectionInfo } from "../interfaces/bidder-bid-info";
import { EBidFolderService } from "./bid-folder.service";

@Injectable()
export class WorkOrderService extends BaseFormService {
	eBidFolderService = inject(EBidFolderService);
	workOrderSection = signal<FormGroup | null>(null);
	bidderInfoData = this.eBidFolderService.bidderBidInfoData;
	view = this.eBidFolderService.view;	
	isComplete = signal<boolean>(false);
	precision = signal<number>(2);
	isLoading = this.eBidFolderService.isLoading;
	public readonly WORK_ORDER_SECTION_TYPES: typeof WorkOrderSectionTypes = WorkOrderSectionTypes;

	workOrderSectionFormGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		WorkOrderType: new FormControl<string | null>(this.WORK_ORDER_SECTION_TYPES.SM, Validators.required),
		UserValue: new FormControl<number | null>(null),
		DecimalValue: new FormControl(2, Validators.required),
		Allowances: new FormControl<number | null>(null),
		Fixed: new FormControl<number | null>(null),
		Total: new FormControl<number | null>(null)
	});

	constructor() {
		super(inject(Injector));		
	}

	startEffects(){
		this.registerEffect(effect(() => {
			if (this.bidderInfoData() && this.workOrderSection() && this.isComplete()) {
				const workOrderInfoData = this.bidderInfoData()?.SectionsData?.find(x => x.SectionType === EBidSectionTypes.WORK_ORDER) as WorkOrderSectionInfo;
				if (workOrderInfoData) {
					this.workOrderSectionFormGroup.patchValue({
						UserValue: workOrderInfoData.UserValue,
						Total: workOrderInfoData.Total
					}, { emitEvent: false, onlySelf: true });
				}
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if(this.view() && this.isComplete()) {
				if (this.view() === BidViews.VIEW) {
					//this.workOrderSectionFormGroup.disable();
					this.workOrderSectionFormGroup.get('UserValue')?.clearValidators();
					this.workOrderSectionFormGroup.get('Allowances')?.clearValidators();
					this.workOrderSectionFormGroup.get('Fixed')?.clearValidators();
				} else if (this.view() === BidViews.EDIT) {
					//this.workOrderSectionFormGroup.enable();
					this.workOrderSectionFormGroup.get('UserValue')?.clearValidators();
					if (this.workOrderSectionFormGroup.get('WorkOrderType')?.value === WorkOrderSectionTypes.CAWM) {
						this.workOrderSectionFormGroup.get('Allowances')?.addValidators(Validators.required);
						this.workOrderSectionFormGroup.get('Fixed')?.addValidators(Validators.required);
					} else {
						this.workOrderSectionFormGroup.get('Allowances')?.clearValidators();
						this.workOrderSectionFormGroup.get('Fixed')?.clearValidators();
					}
				} else if (this.view() === BidViews.BID) {
					//this.workOrderSectionFormGroup.enable();
					this.workOrderSectionFormGroup.get('UserValue')?.addValidators(Validators.required);
					this.workOrderSectionFormGroup.get('Allowances')?.clearValidators();
					this.workOrderSectionFormGroup.get('Fixed')?.clearValidators();
				}
	
				this.workOrderSectionFormGroup.get('Allowances')?.updateValueAndValidity();
				this.workOrderSectionFormGroup.get('Fixed')?.updateValueAndValidity();
				this.workOrderSectionFormGroup.get('UserValue')?.updateValueAndValidity();
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			this.isComplete.set(false);
			if(this.workOrderSection()){
				this.updateFormGroupBaseValues(this.workOrderSectionFormGroup, this.workOrderSection()?.value.section);
	
					this.workOrderSectionFormGroup.patchValue({
						SectionName: this.workOrderSection()?.value?.section?.SectionName,
						SectionId: this.workOrderSection()?.value?.section?.SectionId,
						SectionType: this.workOrderSection()?.value?.section?.SectionType,
						DecimalValue: this.workOrderSection()?.value?.section?.DecimalValue,
						Allowances: this.workOrderSection()?.value?.section?.Allowances,
						Fixed: this.workOrderSection()?.value?.section?.Fixed,
						WorkOrderType: this.workOrderSection()?.value?.section?.WorkOrderType
					}, { emitEvent: false, onlySelf: true });
	
					if (this.workOrderSection()?.value?.section?.DecimalValue) {
						this.precision.set(this.workOrderSection()?.value?.section?.DecimalValue || 2);
						//this.currencyMaskUserInpuOptions.precision = workOrderFormGroupSection.value?.section?.DecimalValue || 2;
					}
	
					this.workOrderSection()?.setControl('section', this.workOrderSectionFormGroup, { emitEvent: false });
	
	
					this.isComplete.set(true);				
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if(this.isComplete()){
				this.workOrderSectionFormGroup.get('DecimalValue')?.valueChanges.subscribe((value) => {
					if (value) {
						this.precision.set(value);
						//this.currencyMaskUserInpuOptions.precision = value;
					}
				});
		
				this.workOrderSectionFormGroup.get('UserValue')?.valueChanges.subscribe((value) => {
		
					const fixedValue = this.workOrderSectionFormGroup.get('Fixed')?.value;
					const allowancesValue = this.workOrderSectionFormGroup.get('Allowances')?.value;
					let total = 0;
		
					if (fixedValue !== null && fixedValue !== undefined && value !== null && allowancesValue !== null && allowancesValue !== undefined) {
						//const firstPart =;
						total = add(allowancesValue, multiply(fixedValue, value));
					}
		
		
					this.workOrderSectionFormGroup.patchValue({
						Total: total
					}, { emitEvent: false, onlySelf: true });
		
				});
		
				this.workOrderSectionFormGroup.get('WorkOrderType')?.valueChanges.subscribe((value) => {
					if (value) {
						if (value === WorkOrderSectionTypes.CAWM) {
							this.workOrderSectionFormGroup.get('Allowances')?.setValidators([Validators.required]);
							this.workOrderSectionFormGroup.get('Fixed')?.setValidators([Validators.required]);
		
						} else if (value === WorkOrderSectionTypes.SM) {
							this.workOrderSectionFormGroup.get('Allowances')?.clearValidators();
							this.workOrderSectionFormGroup.get('Fixed')?.clearValidators();
						}
		
						this.workOrderSectionFormGroup.get('Allowances')?.updateValueAndValidity();
						this.workOrderSectionFormGroup.get('Fixed')?.updateValueAndValidity();
					}
				});
			}
		}, { injector: this.$effectsInjector as Injector }));
	}
	
}