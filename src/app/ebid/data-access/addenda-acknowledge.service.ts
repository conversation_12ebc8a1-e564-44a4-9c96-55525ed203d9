import { effect, inject, Injectable, Injector, signal } from "@angular/core";
import { FormGroup, FormControl, Validators, FormArray } from "@angular/forms";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { AcknowledgeAddendaSectionInfo, AcknowledgementInfo } from "../interfaces/bidder-bid-info";
import { EBidSectionTypes, BidViews, Acknowledgement, DocumentInfo } from "../interfaces/ebid";
import { BaseFormService } from "./base-form-service";
import { EBidFolderService } from "./bid-folder.service";

Injectable()
export class AddendaAcknowledgeService extends BaseFormService {	
	eBidFolderService = inject(EBidFolderService);
	bidView = this.eBidFolderService.view;
	bidderInfoData = this.eBidFolderService.bidderBidInfoData;
	addendaAckSection = signal<FormGroup | null>(null);
	projectInfoDocuments = signal<Array<DocumentInfo> | null>(null);
	isLoading = this.eBidFolderService.isLoading;
	
	ackSectionComplete = signal<boolean>(false);
	acknowledgeAddendaFormGroup = new FormGroup({
		SectionName: new FormControl('', Validators.required),
		SectionId: new FormControl('', Validators.required),
		SectionType: new FormControl('', Validators.required),
		Acknowledgements: new FormArray<any>([])
	});

	constructor() {
		super(inject(Injector));

		
	}

	startEffects() {
		this.registerEffect(effect(() => {
			this.ackSectionComplete.set(false);
			const addendaAckSection = this.addendaAckSection();
			const projectDocuments = this.projectInfoDocuments();
		  
			if (projectDocuments && addendaAckSection) {
			  this.updateFormGroupBaseValues(this.acknowledgeAddendaFormGroup, addendaAckSection.value.section);
			  addendaAckSection.setControl('section', this.acknowledgeAddendaFormGroup, { emitEvent: false });
		  
			  const arrayInfo = this.getAcknowledgements(
				addendaAckSection.value.section.Acknowledgements || [],
				projectDocuments
			  );
			  this.acknowledgeAddendaFormGroup.setControl('Acknowledgements', arrayInfo);
		  
			  this.ackSectionComplete.set(true);
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.projectInfoDocuments() && this.addendaAckSection() && this.bidderInfoData() && this.ackSectionComplete()) {
				const acknowledgeAddendaSection = this.bidderInfoData()?.SectionsData.find(
				  s => s.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE
				) as AcknowledgeAddendaSectionInfo;
			
				if (acknowledgeAddendaSection) {
				  const rdArray = this.acknowledgeAddendaFormGroup.get('Acknowledgements') as FormArray;
				  const rdMap = new Map(rdArray.controls.map(control => [control.value.DocumentId, control]));
			
				  for (const ack of acknowledgeAddendaSection.Acknowledgements as Array<AcknowledgementInfo>) {
					const rd = rdMap.get(ack.DocumentId);
					if (rd) {
					  rd.patchValue({ AcknowledgeAt: ack.AcknowledgeAt }, { emitEvent: false });
					}
				  }
				}
			  }
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.bidView() && this.ackSectionComplete()) {
			  this.updateAcknowledgementValidators(this.bidView() === BidViews.BID);
			}
		  }, { injector: this.$effectsInjector as Injector }));
	}

	private updateAcknowledgementValidators(isBidView: boolean) {
		const acknowledgements = this.acknowledgeAddendaFormGroup.get('Acknowledgements') as FormArray;
	  
		for (let fg of acknowledgements.controls) {
		  const control = fg.get('AcknowledgeAt');
		  if (isBidView) {
			control?.addValidators(Validators.required);
		  } else {
			control?.clearValidators();
		  }
		  control?.updateValueAndValidity();
		}
	  
		this.acknowledgeAddendaFormGroup.updateValueAndValidity();
	  }


	getAcknowledgements(acknowledgements: Array<Acknowledgement>, documents: Array<DocumentInfo>): FormArray<any> {
		if (!acknowledgements || !documents) {
			console.error('Acknowledgements or documents are missing.');
			return new FormArray<any>([]);
		  }
		
		  const fa = new FormArray<any>([]);
		
		  for (let ack of acknowledgements) {
			const downloadInfo = documents.find(d => d.DocumentId === ack.DocumentId);
		
			if (downloadInfo) {
			  fa.push(this.getAcknowledgeFormGroup(ack, downloadInfo.DocumentName, downloadInfo.Category, null));
			} else {
			  console.warn(`Document ${ack.DocumentId} not found in project documents.`);
			}
		  }
		
		  for (let doc of documents.filter(d => d.Category === 'Addenda')) {
			if (!acknowledgements.find(a => a.DocumentId === doc.DocumentId)) {
			  fa.push(this.getAcknowledgeFormGroup({ DocumentId: doc.DocumentId }, doc.DocumentName, doc.Category, null));
			}
		  }
		
		  return fa;
	}

	getAcknowledgeFormGroup(acknowledge: Acknowledgement, documentName: string, category: string, acknowledgeAt: Date | null = null): FormGroup {
		var fg = new FormGroup({
			DocumentId: new FormControl(acknowledge.DocumentId, Validators.required),
			DocumentName: new FormControl(documentName),
			Category: new FormControl(category),
			AcknowledgeAt: new FormControl(acknowledgeAt),
			IsRequired: new FormControl(true, Validators.required)
		});

		fg.get('IsRequired')?.disable();

		return fg;
	}

	acknowledgeAddenda(documentId: string) {
		var acknowledgements = this.acknowledgeAddendaFormGroup.get('Acknowledgements') as FormArray;
		if (!acknowledgements) {
			console.error('Acknowledgements array is missing.');
			return;
		}
		
		var acknowledgement = acknowledgements.controls.find(x => x.get('DocumentId')?.value === documentId);
		
		if (!acknowledgement) {
			console.error(`Acknowledgement for document ID ${documentId} not found.`);
			return;
		}
			
		acknowledgement.get('AcknowledgeAt')?.setValue(HelperTools.getUTCDate());
	}
}