import { inject, Injectable, signal } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Injectable()
export class EBidHistoryService {
  toastrService = inject(ToastrService);
  undoStack = signal<EBidCommand[]>([]);
  redoStack = signal<EBidCommand[]>([]);
  // sendMessage = signal<HistorySendMessage | null>(null);

  destroy(): void {
    this.undoStack.set([]);
    this.redoStack.set([]);
    // this.sendMessage.set(null);
  }

  executeCommand(command: EBidCommand, skipHistory: boolean = false): void {
    command.execute();

    if (!skipHistory) {
      this.addCommand(command);
    }
  }

  addCommand(command: EBidCommand): void {
    this.undoStack.update((stack) => [...stack, command]);
    this.redoStack.set([]); // Clear redo stack on new action
  }

  undo(): void {
    const undoStackValue = this.undoStack();
    if (undoStackValue.length === 0) {
      console.log('No actions to undo.');
      return;
    }
    const command = undoStackValue[undoStackValue.length - 1];
    command.undo();
    this.undoStack.update((stack) => stack.slice(0, -1));
    this.redoStack.update((stack) => [...stack, command]);
    
    const message = command.getInfo<MessageInfo>(EBidHistoryType.UNDO);
    this.showMessage(message, EBidHistoryType.UNDO);
  }

  redo(): void {
    const redoStackValue = this.redoStack();
    if (redoStackValue.length === 0) {
      console.log('No actions to redo.');
      return;
    }
    const command = redoStackValue[redoStackValue.length - 1];
    command.execute();
    this.redoStack.update((stack) => stack.slice(0, -1));
    this.undoStack.update((stack) => [...stack, command]);

    const message = command.getInfo<MessageInfo>(EBidHistoryType.REDO);
    this.showMessage(message, EBidHistoryType.REDO);
  }

  showMessage(messageInfo: MessageInfo, historyType: EBidHistoryType){
    this.toastrService.toastrConfig.preventDuplicates = false;
    this.toastrService.info(`${messageInfo.Message}`, `${messageInfo.Title} - ${historyType.toUpperCase()}`,
      { positionClass: 'toast-top-right', timeOut: 5000, closeButton: true, enableHtml: true, extendedTimeOut: 2000, toastClass: 'ngx-toastr custom-toast-width' });
    this.toastrService.toastrConfig.preventDuplicates = true;
    
  }
}

export class HistorySendMessage{
  Message: MessageInfo;
  HistoryType: EBidHistoryType;

  constructor(message: MessageInfo, historyType: EBidHistoryType){
    this.Message = message;
    this.HistoryType = historyType;
  }
}

export class MessageInfo{
  Title: string;
  Message: string;
  
  constructor(title: string, message: string){
    this.Title = title;
    this.Message = message;
  }

}
export enum EBidHistoryType{
  REDO = "redo",
  UNDO = "undo"
}

export interface EBidCommand {
  execute(): void;
  undo(): void;
  getInfo<Type>(info: any): Type;
}

export enum HistoryDefaultMessages{
  DefaultAction = "Action occured in this section"
}