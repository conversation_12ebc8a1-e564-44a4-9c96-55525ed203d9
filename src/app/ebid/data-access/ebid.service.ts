import { Injectable, signal } from "@angular/core";
import { DocumentInfo, EBid, EBidSection, IEBidChangeDetectionService } from "../interfaces/ebid";
import { Observable } from "rxjs";
import { BidderBidInfo, IEBidBidInfoGatherer } from "../interfaces/bidder-bid-info";
import { AbstractControl, FormArray, FormGroup, ValidationErrors } from "@angular/forms";

@Injectable()
export class EBidService {
	
	bidFormUndo = signal<any>({});
	bidFormRedo = signal<any>({});
	bidFormUndoHistory = new Array<any>();
	bidFormRedoHistory = new Array<any>();
	bidFormUndoHistoryCount = signal<number>(0);
	bidFormRedoHistoryCount = signal<number>(0);	
	changeData = signal<any>({});	
	gatherDocumentsByProjectId = signal<string | null>(null);	


	destroy(){
		this.bidFormUndo.set({});
		this.bidFormRedo.set({});
		this.bidFormUndoHistory = [];
		this.bidFormRedoHistory = [];
		this.bidFormUndoHistoryCount.set(0);
		this.bidFormRedoHistoryCount.set(0);
		this.changeData.set({});
	}

	undo() {
		var lastItem = this.getLastBidFormChange();
		if(lastItem){
			this.bidFormUndo.set(lastItem);		
		}				
	}

	redo(){
		var lastItem = this.getLastBidFormRedo();
		if(lastItem){
			this.bidFormRedo.set(lastItem);		
		}
	}



	addBidFormChange(data: any, clearRedo: boolean = false){
		this.cleanItemFromUndoHistory(data);

		this.bidFormUndoHistory.push({...data, timeStamp: new Date().getTime()});

		if(clearRedo){
			this.bidFormRedoHistory = [];
			this.bidFormRedoHistoryCount.set(this.bidFormRedoHistory.length);
		}

		this.bidFormUndoHistoryCount.set(this.bidFormUndoHistory.length);
	}

	getLastBidFormChange(remove: boolean = true): any{
		var lastItem = this.bidFormUndoHistory.sort((a, b) => a.timeStamp - b.timeStamp).pop();
		this.bidFormUndoHistoryCount.set(this.bidFormUndoHistory.length);
		if(lastItem){
			this.addUndoToBidFormRedo(lastItem);
		}

		return lastItem;
	}

	addUndoToBidFormRedo(lastItem:any){
		this.bidFormRedoHistory.push({...lastItem, timeStamp: new Date().getTime()});
		this.bidFormRedoHistoryCount.set(this.bidFormRedoHistory.length);

	}

	cleanItemFromUndoHistory(item: any){
		var items = this.bidFormUndoHistory.filter(historyItem => {
			return this.areObjectsEqual(historyItem.Data, item.Data);
		});
	
		for(let item of items){
			var index = this.bidFormUndoHistory.indexOf(item);
			this.bidFormUndoHistory.splice(index, 1);
		}
	}


	areObjectsEqual(obj1: any, obj2: any): boolean {
		return JSON.stringify(obj1) === JSON.stringify(obj2);
	}
	getLastBidFormRedo(remove: boolean = true): any{
		var lastItem = null;

		var lastItem = this.bidFormRedoHistory.sort((a, b) => a.timeStamp - b.timeStamp).pop();

		this.bidFormRedoHistoryCount.set(this.bidFormRedoHistory.length);

		if(lastItem){
			this.addBidFormChange(lastItem);
		}

		return lastItem;
	}

	gatherBidChanges<T extends IEBidChangeDetectionService>(oldEBid: EBid, newEBid: EBid, changeDetectionService : T) : Observable<any>{
		return new Observable(observer => {
			if(oldEBid && newEBid){
				changeDetectionService.gatherChanges(oldEBid, newEBid);
			}		
		});
	}

	gatherBidInfo<T extends IEBidBidInfoGatherer>(eBidFormGroup: FormGroup, infoGatherService: T): Observable<BidderBidInfo>{
		return new Observable(observer => {

			if(eBidFormGroup){				
				var bidInfo = infoGatherService.gatherBidInfo(eBidFormGroup);
				observer.next(bidInfo);				
			}else{
				observer.error('eBid form group is required');				
			}

			observer.complete();
		});
	
	}
}
