import { Component, effect, inject, input, OnDestroy, OnInit } from '@angular/core';
import { SectionBase } from '../../interfaces/section-base';
import { BidViews, EBidSectionTypes } from '../../interfaces/ebid';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RequiredDownloadsService } from '../../data-access/required-downloads.service';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';

@Component({
  selector: 'app-bid-required-downloads',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './bid-required-downloads.component.html',
  styleUrl: './bid-required-downloads.component.css'
})
export class BidRequiredDownloadsComponent extends SectionBase implements OnInit, OnDestroy {

  requiredDownloadService = inject(RequiredDownloadsService);  
  bidOpsProjectService = inject(BidOpsProjectService);
  bidView = this.requiredDownloadService.view;
  bidderInfoData = this.requiredDownloadService.bidderBidInfoData;
  requiredDownloadsSection = input.required<FormGroup>();  
  isLoading = this.requiredDownloadService.isLoading;
  projectDocuments = this.requiredDownloadService.projectInfoDocuments;  
  requiredDownloadsFormGroup = this.requiredDownloadService.requiredDownloadsFormGroup;
  isDownloadHistoryLoading = this.requiredDownloadService.isDownloadHistoryLoading;
  constructor() {
    super(EBidSectionTypes.REQUIRED_DOWNLOADS);

    effect(() => {
      this.requiredDownloadService.requiredDownloadsSection.set(this.requiredDownloadsSection());
    });
  }
  ngOnInit(): void {
    this.requiredDownloadService.startEffects();
  }
  ngOnDestroy(): void {    
    this.requiredDownloadService.downloadDocumentRequest.set(null);    
    this.requiredDownloadService.stopEffects();
  }

  downloadFile(documentId: string) {
    this.requiredDownloadService.downloadFile(documentId);
  }
}
