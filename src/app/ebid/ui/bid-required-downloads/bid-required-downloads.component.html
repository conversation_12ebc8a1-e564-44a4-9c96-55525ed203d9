
<header class="d-flex justify-content-between align-items-center mb-3">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div class="d-flex justify-content-end">
			<ng-content select="[grab]"></ng-content>
		</div>
	}

	<h5 class="page-title">Required Downloads</h5>

	@if(bidView() === BIDFORM_VIEWS.EDIT){
	<!-- engineer: header -->
	<div class="d-flex justify-content-end">
		<ng-content select="[remove]"></ng-content>
	</div>
	}
	@else if(bidView() === BIDFORM_VIEWS.BID && !isLoading()){
	<!-- bidder: header -->
	<div>
		@if(requiredDownloadsFormGroup?.valid){
		<span class="badge text-bg-success">Completed</span>
		}@else {
		<span class="badge text-bg-danger">Not Completed</span>
		}
	</div>
	}

</header>

@if(isLoading()){
	<section class="mb-4 bid-form placeholder-glow">
		<div class="placeholder col-12" style="height: 30px;"></div>
	</section>
}@else {

<form [formGroup]="requiredDownloadsFormGroup">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<p>Select the documents bidders must download before they are allowed to submit a bid.</p>
		<!-- Error message for at least one required download -->
		@if(requiredDownloadsFormGroup.get('RequiredDownloads')?.hasError('atLeastOneRequired')){
			<div class="text-danger">
				<i class="fas fa-exclamation-triangle"></i>
				At least one required download must be selected or remove this section from the bid folder.
			</div>
		}

		@if(requiredDownloadsFormGroup.get('RequiredDownloads')?.hasError('minLengthArray')){
			<div class="text-danger">
				<i class="fas fa-exclamation-triangle"></i>
				Must have at least 	{{requiredDownloadsFormGroup.get('RequiredDownloads')?.errors?.minLengthArray?.minLength }}  required download added to this section
			</div>
		}


		<ul class="list-group">
			@for (item of requiredDownloadsFormGroup.get('RequiredDownloads')?.controls; track $index) {	
				<form [formGroup]="item">
					<li class="list-group-item">
						<div class="row d-flex align-items-center">
							<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
								{{ item.value.DocumentName }}
							</div>
							<div class="col-12 col-md-6 d-flex justify-content-md-end">
								<div class="form-check">
									<input type="checkbox" class="form-check-input" [id]="$index + 'isRequired'" [name]="$index + 'isRequired'" formControlName="IsRequired">
									<label class="form-check-label" [for]="$index + 'isRequired'">Required</label>
								</div>
							</div>
						</div>
					</li>
				</form>		
		
			}
		</ul>
		}@else if(bidView() === BIDFORM_VIEWS.BID){
		
			<ul class="list-group">
				@for (item of requiredDownloadsFormGroup.get('RequiredDownloads')?.controls; track $index) {	
					<form [formGroup]="item">
						@if(item.value.IsRequired){
							<li class="list-group-item">						
								<div class="row d-flex align-items-center">								
									<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
										{{ item.value.DocumentName }}
									</div>
									<div class="col-12 col-md-6 d-flex justify-content-md-end">
										@if(isDownloadHistoryLoading()){
											<i class="fas fa-circle-notch fa-spin fa-1x"></i>
										}@else {
											@if(item.value.IsDownloaded){						
												<i class="fas fa-check fa-lg text-success" aria-hidden="true"></i>
											}@else {
												<button class="btn btn-outline-success" (click)="downloadFile(item.value.DocumentId)" [disabled]="item.value.IsDownloading">
													@if(item.value.IsDownloading || isDownloadHistoryLoading()){												
														<i class="fas fa-circle-notch fa-spin fa-1x"></i>									
													}
													Download
												</button>
											}
										}
									</div>
								</div>
							</li>
						}
					</form>					
				}
			</ul>		
		}@else if(bidView() === BIDFORM_VIEWS.VIEW){
		<ul class="list-group">
				@for (item of requiredDownloadsFormGroup.get('RequiredDownloads')?.value; track $index) {		
					@if(item.IsRequired){
						<li class="list-group-item">			
							<div class="row d-flex align-items-center">
								<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
									{{ item.DocumentName }}
								</div>
								<div class="col-12 col-md-6 d-flex justify-content-md-end">					
									@if(item.IsDownloaded){						
										<i class="fas fa-check fa-lg text-success" aria-hidden="true"></i>
									}@else {
										<i class="fas fa-times fa-lg text-danger" aria-hidden="true"></i>
									}						
								</div>
							</div>
						</li>	
					}
				}
			</ul>
		}
</form>
}