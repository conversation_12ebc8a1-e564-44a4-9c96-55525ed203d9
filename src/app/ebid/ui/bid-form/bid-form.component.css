/* .bid-item{	
}

.form-list{

} */

.grip {
	text-align: center; 
	vertical-align: middle;
	cursor: move;
}
.replacement-alt {
	text-align: center; 
	vertical-align: middle;	
}

.alternate-bid-item td{
	background-color: rgb(240, 245, 240);
}

.dropdown-menu a{
	cursor: pointer !important;
}

.cdk-drag-preview {
	border: lightblue 1px solid;	
	border-radius: 4px;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
				0 8px 10px 1px rgba(0, 0, 0, 0.14),
				0 3px 14px 2px rgba(0, 0, 0, 0.12);
	display: table;
	padding: 5px;
	background-color: rgb(255, 255, 255); /* Match the background color of your table rows */
	color: white; /* Match the text color of your table rows */
    width: 100%; /* Ensure it takes the full width */
    height: auto; /* Ensure it takes the full height */
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
  }
  
  .cdk-drag-placeholder {	
	opacity: 0.2;
	
  }
  
  .cdk-drag-animating {
	transition: transform 150ms cubic-bezier(0, 0, 0.2, 1);
  }
  
  .bid-item:last-child {
	border: solid 1px #ccc;
  }
  
  .form-list.cdk-drop-list-dragging .bid-item:not(.cdk-drag-placeholder) {
	transition: transform 150ms cubic-bezier(0, 0, 0.2, 1);
  }

  input.ng-invalid.ng-dirty {
	border: 2px solid red;
	background-color: #ffe6e6;
  }
  
  input.ng-invalid.ng-dirty:focus {
	outline: none;
	box-shadow: 0 0 5px red;
  }

  .invalid{
	background-color: red;
  }

  .disabled td{
	background-color: #d80202 !important;
	color: white
  }