import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { Component, inject, input, viewChild, HostListener, Renderer2, signal, OnDestroy, computed, effect, OnInit, ChangeDetectorRef, ChangeDetectionStrategy } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgxCurrencyDirective, NgxCurrencyInputMode } from 'ngx-currency';
import { ToastContainerDirective, ToastrService } from 'ngx-toastr';
import { BidViews } from 'src/app/ebid/interfaces/ebid';
import { v4 } from 'uuid';
import { Subscription } from 'rxjs';
import { SectionBase } from '../../interfaces/section-base';
import { BidFormSectionInfo } from '../../interfaces/bidder-bid-info';
import { BidForm, BidFormSection, BidSection, UnitPriceSettings } from '../../interfaces/bid-form-section';
import { environment } from 'src/environments/environment';
import { BidFormService, BidderInfoImportResponse } from '../../data-access/bidform-service';
import { NgbModalModule, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { BidderImportInfoComponent } from '../bidder-import-info/bidder-import-info.component';
import { HelperTools } from 'src/app/shared/utils/helper-tools';
import { BidFormSkeletonComponent } from './bid-form-skeleton/bid-form-skeleton.component';
import { EBidFolderService } from '../../data-access/bid-folder.service';

@Component({
  selector: 'app-bid-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, BidFormSkeletonComponent,
    CdkDrag, CdkDropList, CdkDragHandle, NgxCurrencyDirective, NgbPopover, NgbModalModule, BidderImportInfoComponent],
  templateUrl: './bid-form.component.html',
  styleUrl: './bid-form.component.css'
})
export class BidFormComponent extends SectionBase implements OnInit, OnDestroy {
  @HostListener('document:keydown.enter', ['$event'])
  handleEnterKey(event: KeyboardEvent) {
    if (this.bidView() === BidViews.BID) {
      event.preventDefault();
      this.focusNextInput(event.target as HTMLElement);
    }
  }

  ////////////////INJECT SERVICES
  toastrService = inject(ToastrService);
  bidFormService = inject(BidFormService);
  changeDetection = inject(ChangeDetectorRef);
  eBidFolderService = inject(EBidFolderService);

  ////////////////INPUTS  
  bidFormSection = input.required<FormGroup>();  
  showBidFormImport = input<boolean>(true);  
  useDisabledOnBidFormItem = input<boolean>(false);
  showHeader = input<boolean>(true);
  allowFullBidFormDelete = input<boolean>(false);
  bidView = this.bidFormService.view;
  bidderInfoData = this.bidFormService.bidderInfoData;  
  isLoading = this.bidFormService.isLoading;

  ////////////////VIEW CHILDS
  fileInput = viewChild<any>('fileInput');
  toastContainer = viewChild<ToastContainerDirective>("toastContainer");
  bidFormPreviewModal = viewChild<any>('bidFormPreviewModal');
  importResponseInfo = viewChild<BidderImportInfoComponent>('importResponseInfo');

  ////////////////SIGNALS
  isExporting = this.bidFormService.isExporting;
  isImportBidExcel = this.bidFormService.isImportBidExcel;
  isUploadingToS3 = this.bidFormService.isUploadingToS3;
  showImportBidFormModal = this.bidFormService.showImportBidFormModal;
  bidFormImportIssues = this.bidFormService.bidFormImportIssues;

  showList = this.bidFormService.showList;
  ////////////////COMPUTED
  uploadProgress = this.bidFormService.uploadProgress;
  isUploading = computed(() => this.uploadProgress() > 0 && this.uploadProgress() < 100);

  ////////////////VARIABLES
  templateUrl = environment.online_bid_import_form_template_url;
  isDragDisabled = this.bidFormService.isDragDisabled;
  currencyMaskOptions = { prefix: '', thousands: ',', decimal: '.', nullable: true };
  currencyMaskNaturalOptions = { prefix: '', thousands: ',', decimal: '.', nullable: true, inputMode: NgxCurrencyInputMode.Natural };
  currencyMaskQtyOptions = { prefix: '', thousands: ',', decimal: '.', nullable: true, precision: 3, inputMode: NgxCurrencyInputMode.Natural };
  public readonly UNIT_PRICE_SETTINGS: typeof UnitPriceSettings = UnitPriceSettings;

  uploadToS3Subscription: Subscription | null = null;
  latestErrorItem: string | null = null;
  settings = this.bidFormService.settings;
  bidFormSectionFormGroup = this.bidFormService.bidFormSectionGroup;
  importModal: any;
  fileUploadForm = new FormGroup({
    file: new FormControl(null, Validators.required)
  });


  constructor() {
    super('BidForm');
   
    effect(() => {        
      this.bidFormService.bidFormSection.set(this.bidFormSection());      
    });

    effect(() => {
      if (this.uploadProgress() > 0 && this.uploadProgress() >= 100) {
        this.fileUploadForm.reset();
      }
    });

    effect(() => {
      if (this.showImportBidFormModal()) {
        const bfSection = HelperTools.toPascalCase(this.bidFormSection().get("section")?.getRawValue()) as BidFormSection;
        this.importResponseInfo()?.bidFormSection.set(bfSection);
        this.importResponseInfo()?.response.set(this.bidFormService.showImportBidFormModal() as BidderInfoImportResponse);

        this.showModal();
      }
    });

    effect(() => {
      if (this.bidFormService.isBidderInfoComplete()) {
        this.changeDetection.detectChanges();
      }
    });
  }

  ngOnInit() {    
    this.bidFormService.startEffects();
  }

  

  ngOnDestroy(): void {    
    this.bidFormService.destroy();
    this.bidFormService.stopEffects();
    this.showImportBidFormModal.set(null);
  }

  removeIssue(issue: string) {
    this.bidFormImportIssues.update(issues => issues && issues.filter(i => i !== issue));
  }

  showModal() {
    this.getModal()?.show();
  }

  hideModal() {
    this.getModal()?.hide();
  }



  getModal() {
    if (this.importModal) {
      return this.importModal;
    }

    const modalElement = this.bidFormPreviewModal()?.nativeElement;
    this.importModal = new (window as any).bootstrap.Modal(modalElement);
    return this.importModal;

  }

  focusNextInput(currentElement: HTMLElement) {
    const formElements = Array.from(document.querySelectorAll('input, select, textarea'));
    const currentIndex = formElements.indexOf(currentElement);
    if (currentIndex > -1 && currentIndex < formElements.length - 1) {
      const nextElement = formElements[currentIndex + 1] as HTMLElement;
      nextElement.focus();
    }
  }



  finalImport() {
    this.replaceBidderInfoFromImport(this.importResponseInfo()?.response().BidFormSectionInfo as BidFormSectionInfo);
    this.hideModal();
  }

  exportBidForm() {
    this.bidFormService.exportBidFormToFile();
  }

  replaceBidderInfoFromImport(sectionInfo: BidFormSectionInfo) {
    this.bidFormService.setupBidderInfoForms(sectionInfo, this.bidFormSection()?.get('section') as FormGroup);
    this.bidFormService.recalculate();
  }

  importBidderInfo() {
    const file = this.getFileFromInput();

    if (file) {
      //this.openImportBidFormModal({} as BidderInfoImportResponse);
      this.bidFormService.importBidderInfo(file);
    }
  }

  importBidForm() {
    const file = this.getFileFromInput();

    if (file) {
      this.bidFormService.importBidForm(file);
    }
  }



  private getFileFromInput(): File | null {
    const fileInputElement = this.fileInput()?.nativeElement as HTMLInputElement;
    const files = fileInputElement?.files;

    if (files && files.length > 0) {
      const file = files[0];
      return file;
    }

    return null;
  }




  addNewForm() {
    const nForm = {
      FormId: v4(),
      FormTotal: 0,
      CountTowardsTotal: true,
      Sections: [{
        BidItems: [],
        SectionId: v4(),
        Title: '',
        Total: 0
      }
      ],
      Title: ''
    } as BidForm

    this.bidFormService.addForm(this.bidFormSectionFormGroup, nForm, null, false, true);


    const bidForms = document.querySelectorAll('.bid-form');
    const lastBidForm = bidForms[bidForms.length - 1];
    if (lastBidForm) {
      lastBidForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  addNewSection(formId: string) {
    if (!formId) {
      console.error('FormId is required to add a new section', formId);
      return;
    }

    const newSection = {
      BidItems: [],
      SectionId: v4(),
      Title: '',
      Total: 0      
    } as BidSection

    this.bidFormService.addSection(formId, newSection);

  }



  getBidItemErrors(item: FormGroup) {
    let start = "<ul>"
    let end = "</ul>"
    let innerItems = new Array<string>();

    if (!item.get('CustomId1')?.valid) {
      innerItems.push("<li>Custom Id 1 is required</li>");
    }

    if (!item.get('Description')?.valid) {
      innerItems.push("<li>Description is required</li>");
    }

    if (!item.get('Qty')?.valid) {
      innerItems.push("<li>Quantity is required</li>");
    }

    if (!item.get('Unit')?.valid) {
      innerItems.push("<li>Unit is required</li>");
    }

    if (!item.get('UnitPrice')?.valid) {
      innerItems.push("<li>Unit Price is required</li>");
    }

    if (!item.get('UnitPriceSetting')?.valid) {
      innerItems.push("<li>Unit Price Setting cannot be none and is required</li>");
    }



    let finalstr = start;
    for (let item of innerItems) {
      finalstr += item;
    }

    finalstr += end;

    this.latestErrorItem = finalstr;
  }


  drop(data: any[], event: CdkDragDrop<string[]>) {
    this.bidFormService.drop(data, event);
  }

  addRow(formId: string, sectionId: string) {
    this.bidFormService.addNewBidItem(formId, sectionId);
  }

  addRowAbove(formId: string, sectionId: string, currentIndex: number) {
    var idx = (currentIndex) ? currentIndex : 0;
    this.bidFormService.addNewBidItem(formId, sectionId, idx);
  }

  addRowBelow(formId: string, sectionId: string, currentIndex: number) {
    const idx = currentIndex + 1;
    this.bidFormService.addNewBidItem(formId, sectionId, idx);
  }

  deleteSection(formId: string, sectionId: string, skipChangeRequest: boolean = false) {
    this.bidFormService.deleteSection(formId, sectionId, skipChangeRequest);
  }


  deleteForm(formId: string, skipChangeRequest: boolean = false) {
    this.bidFormService.deleteForm(formId, skipChangeRequest);
  }

  addNewReplacementAlternate(formId: string, sectionId: string, bidItemId: string) {
    this.bidFormService.addNewReplacementAlternate(formId, sectionId, bidItemId);
  }

  deleteAlternate(formId: string, sectionId: string, bidItemId: string, alternateId: string, skipChangeRequest: boolean = false) {
    this.bidFormService.deleteAlternate(formId, sectionId, bidItemId, alternateId, skipChangeRequest);
  }

  disableAlternate(formId: string, sectionId: string, bidItemId: string, alternateId: string, skipChangeRequest: boolean = false) {
    this.bidFormService.disableAlternate(formId, sectionId, bidItemId, alternateId, skipChangeRequest);
  }

  deleteBidItem(formId: string, sectionId: string, bidItemId: string, skipChangeRequest: boolean = false) {
    this.bidFormService.deleteBidItem(formId, sectionId, bidItemId, skipChangeRequest);
  }

  disableBidItem(formId: string, sectionId: string, bidItemId: string, skipChangeRequest: boolean = false) {
    this.bidFormService.disableBidItem(formId, sectionId, bidItemId, skipChangeRequest);
  }

  getColSize(): string {
    return this.bidView() === BidViews.EDIT ? '8' : '7';
  }
}