@let colSize = getColSize();
@let isEdit = bidView() === BIDFORM_VIEWS.EDIT;
@let isBid = bidView() === BIDFORM_VIEWS.BID;
@let isView = bidView() === BIDFORM_VIEWS.VIEW;

@if(showHeader()){
<!-- header -->
<div class="d-flex justify-content-between align-items-center mb-3">
	<div class="d-flex justify-content-start align-items-center">
		@if(isEdit){
		<div class="me-2 grab"><ng-content select="[grab]"></ng-content></div>
		}
		<h3 class="page-title fs-6 m-0">Bid Form</h3>
	</div>
	@if(isEdit){
	<div class="d-flex justify-content-end">
		<ng-content select="[remove]"></ng-content>
		<!-- <button type="button" class="btn btn-outline-danger mx-2">Delete</button> -->
		<button type="button" class="btn btn-outline-dark ms-2" (click)="addNewForm()">+ Add Bid Form</button>
	</div>
	}@else if(isBid && !isLoading()){
	<div>
		@if(bidFormSectionFormGroup?.valid){
		<span class="badge text-bg-success">Complete</span>
		}@else {
		<span class="badge text-bg-danger">Incomplete</span>
		}
	</div>
	}
</div>
}

@if (((isEdit || isBid) && !isLoading() && showBidFormImport()) || this.bidFormSectionFormGroup.get('Forms')?.length <= 0) {
<form [formGroup]="fileUploadForm">
	<!-- import bid items (hide/show) -->
	<div class="border p-3 mb-3">
		<div class="row gx-2">
			<!-- choose file -->
			<div class="col-8 col-sm-auto d-flex align-items-end">
				<div>
					@if(isEdit){
					<label for="formFile" class="form-label">Import your bid form(s) using our <a
							[attr.href]="templateUrl" target="_blank">template</a>.</label>
					}
					<input class="form-control" type="file" id="formFile" name="importFile" formControlName="file"
						accept=".xlsx" #fileInput>
				</div>
			</div>
			<!-- button -->
			<div class="col-4 col-sm-auto d-flex align-items-end">
				@if(isBid){
				<button type="button" class="btn btn-outline-dark" (click)="importBidderInfo()"
					[disabled]="!fileUploadForm.valid || isUploadingToS3() || isImportBidExcel()">
					@if(isUploadingToS3()){
					<i class="fas fa-spinner-third fa-fw fa-spin fa-1x"></i>
					}
					Import
				</button>
				<button type="button" class="btn btn-outline-danger ms-2" (click)="exportBidForm()"
					[disabled]="isExporting()">
					@if(isExporting()){
					<i class="fas fa-spinner-third fa-fw fa-spin fa-1x"></i>
					}
					Export
				</button>
				}@else if (isEdit) {
				<button type="button" class="btn btn-outline-dark" (click)="importBidForm()"
					[disabled]="!fileUploadForm.valid || isUploadingToS3() || isImportBidExcel()">
					@if(isUploadingToS3() || isImportBidExcel()){
					<i class="fas fa-spinner-third fa-fw fa-spin fa-1x"></i>
					}
					Import
				</button>
				}
			</div>
		</div>
		@if(bidFormImportIssues()?.length > 0){
		<h6 class="mt-4">Import Issues:</h6>
		@for (item of bidFormImportIssues(); track $index) {
		<div class="d-flex justify-content-between alert alert-danger mt-2">
			<div class="justify-content-start">
				{{item}}
			</div>
			<div class="justify-content-end">
				<i class="fas fa-times float-right" style="cursor: pointer;" (click)="removeIssue(item)"></i>
			</div>
		</div>
		}
		}
	</div>
</form>
}


@if(isLoading() || isUploading()){
<app-bid-form-skeleton></app-bid-form-skeleton>
}@else {

<form [formGroup]="bidFormSectionFormGroup">
	@let forms = bidFormSectionFormGroup.get('Forms');
	@if(forms?.hasError('minLengthArray'))
	{
	<div class="text-danger">
		<i class="fas fa-exclamation-triangle"></i>
		Must have at least {{bidFormSectionFormGroup.get('Forms')?.errors?.minLengthArray?.minLength }} form added or
		remove this section from the bid folder.
	</div>
	}

	@if(showList()){
	@defer (when !isLoading()) {
	@for (form of forms?.controls; track $index; let formIdx = $index) {
	@let sections = form.get('Sections');
	@let formId = form.value?.FormId;
	@let title = form.value?.Title;
	@let formTotal = form.value?.FormTotal;
	@let formIsNew = form.value?.IsNew ?? false;

	<!-- <button (click)="formValidationInfo(form)">Form Validation Info</button> -->
	@if(!form.valid && isEdit){
	<div class="text-danger">
		<i class="fas fa-exclamation-triangle"></i>
		The bid form in incomplete.
	</div>
	}

	@if(sections?.hasError('minLengthArray'))
	{
	<div class="text-danger">
		<i class="fas fa-exclamation-triangle"></i>
		Must have at least {{sections?.errors?.minLengthArray?.minLength }} section
	</div>
	}

	<section class="mb-4 bid-form" [formGroup]="form">
		<table class="table table-bordered">
			<!-- col group for column styles -->
			<colgroup>
				@if(isEdit){
				<col style="width: 50px;" />
				}
				<col style="width: 100px;" />
				<col style="width: 100px;" />
				<col style="width: 300px;" />
				<col style="width: 60px;" />
				<col style="width: 100px;" />
				<col style="width: 150px;" />
				@if(isEdit){
				<col style="width: 50px;" />
				}@else {
				<col style="width: 150px;" />
				}
			</colgroup>
			<!-- base bid -->
			<thead>
				<tr>
					<th [attr.colspan]="colSize" class="bg-dark">
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<div class="row">
							<div class="col-10 col-md-6 col-lg-4">
								<input type="text" class="form-control" [id]="'formTitle' + formIdx"
									[name]="'formTitle' + formIdx" formControlName="Title"
									placeholder="Bid Form Name (Base Bid, Alt 1, etc.)" />
							</div>
							<div class="col-2 col-md-6 col-lg-8 d-flex justify-content-end">
								<button type="button" class="btn btn-outline-light btn-sm me-1"
									(click)="addNewSection(formId)">+ Add Section</button>
								@if(!useDisabledOnBidFormItem() || formIsNew){
								<button type="button" class="btn btn-outline-light btn-sm"
									(click)="deleteForm(formId)">Delete Form</button>
								}
							</div>
						</div>
						}@default {
						<h1 class="page-title fs-5 text-white m-0 p-0 d-flex justify-content-between">
							<span class="text-white">{{ title}}</span>
							<span class="text-white">{{ formTotal | currency}}</span>
						</h1>
						}
						}
					</th>
				</tr>
			</thead>
			<thead>
				<tr>
					@if(isEdit){
					<th scope="col"></th>
					}
					<th scope="col">ID 1</th>
					<th scope="col">ID 2</th>
					<th scope="col">Desc.</th>
					<th scope="col">Unit</th>
					<th scope="col">Qty</th>
					<th scope="col">Unit Price</th>
					<th scope="col">
						@if(!isEdit){
						Ext. Price
						}
					</th>

				</tr>
			</thead>
			@for (section of sections.controls; track $index; let sectionIdx = $index) {
			@let bidItems = section.get('BidItems');
			@let sectionId = section.value?.SectionId;
			@let isSectionNew = section.value?.IsNew ?? false;

			<tbody class="form-list" cdkDropList (cdkDropListDropped)="drop(bidItems?.controls, $event)">
				@if(bidItems?.hasError('minLengthArray'))
				{
				<tr>
					<td [attr.colspan]="getColSize()">
						<div class="text-danger">
							<i class="fas fa-exclamation-triangle"></i>
							Must have at least {{bidItems?.errors?.minLengthArray?.minLength }} bid item added to this
							section or remove this section from the bid folder.
						</div>
					</td>
				</tr>
				}
				<tr [formGroup]="section">
					<td [attr.colspan]="colSize" class="bg-light fw-bold">
						<div class="row">
							<div class="col-10 col-md-6 col-lg-4">
								@switch(bidView()){
								@case(BIDFORM_VIEWS.EDIT){
								<input type="text" class="form-control" [name]="'sectionTitle' + sectionIdx"
									[id]="'sectionTitle' + sectionIdx" formControlName="Title"
									placeholder="Section Name" />
								}@default {
								@if(section.value.Title){
								{{section.value.Title }}
								}@else {
								<i style="font-weight: 100;">
									No Section Title
								</i>
								}
								}
								}
							</div>
							<div class="col-2 col-md-6 col-lg-8 d-flex justify-content-end">
								@if(isEdit){
								<button type="button" class="btn btn-outline-dark btn-sm mx-2"
									(click)="addRow(formId, sectionId)">+ Add Row</button>
								@if(!useDisabledOnBidFormItem() || isSectionNew){
								<button type="button" class="btn btn-outline-dark btn-sm"
									(click)="deleteSection(formId, sectionId)">Delete Section</button>
								}

								}
							</div>
						</div>
					</td>
				</tr>
				@for (bidItem of bidItems?.controls; track $index; let bidItemIdx = $index) {
				@let bidItemId = bidItem.value.BidItemId;
				@let customId1 = bidItem.value.CustomId1;
				@let customId2 = bidItem.value.CustomId2;
				@let description = bidItem.value.Description;
				@let unit = bidItem.value.Unit;
				@let isQtyEditable = bidItem.value.IsQtyEditable;
				@let qty = bidItem.value.Qty | number: '1.0-3';
				@let isQtyEnabled = bidItem.get('Qty')?.enabled;
				@let isQtyValid = bidItem.get('Qty')?.valid;
				@let unitPriceSetting = bidItem.value.UnitPriceSetting;
				@let notRequired = bidItem.value.NotRequired;
				@let bidderPrice = bidItem.value.BidderPrice | currency;
				@let isBidderPriceValid = bidItem.get('BidderPrice')?.valid;
				@let isBidderPriceDisabled = bidItem.get('BidderPrice')?.disabled;
				@let unitPrice = bidItem.value.UnitPrice;
				@let extensionPrice = bidItem.value.ExtensionPrice | currency;
				@let percentMaxAllowedError = bidItem.get('BidderPrice')?.errors?.exceedsTenPercent?.maxAllowed;
				@let isDisabled = bidItem.value.IsDisabled;
				@let isNew = bidItem.value.IsNew;


				<tr class="bid-item" [formGroup]="bidItem" cdkDrag cdkDragLockAxis="y"
					[cdkDragDisabled]="isDragDisabled()" [cdkDragPreviewContainer]="'parent'"
					[ngClass]="{'disabled': isDisabled}">
					@if(isEdit){
					<td class="grip" cdkDragHandle [ngClass]="{'disabledcl': isDisabled}">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
						@if(!bidItem.valid)
						{
						<span class="position-relative" [ngbPopover]="errorItem" [popoverTitle]="'Bid Item Not Valid'"
							(mouseover)="getBidItemErrors(bidItem)" triggers="mouseenter:mouseleave"
							popoverTitle="Pop title">
							<i class="fas fa-exclamation-triangle text-danger fa-1x"
								style="position: absolute; left: -47px; top: 50%; transform: translateY(-50%);"></i>
						</span>
						}
					</td>
					}
					<td>
						@if(isDisabled){
						<span style="text-decoration: line-through;">{{customId1}}</span>
						}@else {
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'bidItemCustomId1' + bidItemIdx"
							[name]="'bidItemCustomId1' + bidItemIdx" formControlName="CustomId1" />
						}@default {
						{{customId1}}
						}
						}

						}
					</td>
					<td>
						@if(isDisabled){
						<span style="text-decoration: line-through;">{{customId2}}</span>
						}@else {
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'bidItemCustomId2' + bidItemIdx"
							[name]="'bidItemCustomId2' + bidItemIdx" formControlName="CustomId2" />
						}@default {
						{{customId2}}
						}
						}
						}

					</td>
					<td>
						@if(isDisabled){
						<span style="text-decoration: line-through;">{{description}}</span>
						}@else {
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'bidItemDescription' + bidItemIdx"
							[name]="'bidItemDescription' + bidItemIdx" formControlName="Description" />
						}@default {
						{{description}}
						}
						}
						}

					</td>
					<td>
						@if(isDisabled){
						<span style="text-decoration: line-through;">{{unit}}</span>
						}@else {
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'bidItemUnit' + bidItemIdx"
							[name]="'bidItemUnit' + bidItemIdx" formControlName="Unit" />
						}@default {
						{{unit}}
						}
						}
						}
					</td>
					<td class="text-end">
						@if(isDisabled){
						<span style="text-decoration: line-through;">{{qty}}</span>
						}@else {
						@if((isEdit || (isBid && isQtyEditable)) && unitPriceSetting !==
						UNIT_PRICE_SETTINGS.PERCENTAGE){
						@if(isQtyEnabled){
						<input type="text" class="form-control" [id]="'qtyInputBidItem' + bidItemIdx"
							[name]="'qtyInputBidItem' + bidItemIdx" [ngClass]="{'bg-warning-subtle': !isQtyValid}"
							formControlName="Qty" [currencyMask]="currencyMaskQtyOptions"
							[maxlength]="settings.maxQtyLength" />

						<div>
							@if(isBid && isQtyEditable){
							<span class="badge text-bg-info">Input Qty</span>
							}@else {
							@if(isEdit && isQtyEditable){
							<span class="badge text-bg-info">Bidder to Input Qty</span>
							}
							}
						</div>
						}@else{
						{{ qty}}
						}

						}
						@else {
						{{ qty }}
						}
						}

					</td>
					<td class="text-end">
						@if(isDisabled){
						<span style="text-decoration: line-through;">{{unitPrice}}</span>
						}@else {
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						@if(unitPriceSetting === UNIT_PRICE_SETTINGS.PERCENTAGE){
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">%</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder=""
								formControlName="UnitPrice" [id]="'unitPriceBidItem' + bidItemIdx"
								[name]="'unitPriceBidItem' + bidItemIdx" [currencyMask]="currencyMaskNaturalOptions"
								[maxlength]="settings.maxCurrencyLength">
						</div>
						}@else {
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder=""
								[id]="'unitPriceBidItem' + bidItemIdx" [name]="'unitPriceBidItem' + bidItemIdx"
								formControlName="UnitPrice" [currencyMask]="currencyMaskOptions"
								[maxlength]="settings.maxCurrencyLength">
						</div>
						}

						@if(notRequired){
						<div>
							<span class="badge text-bg-info">Bidder Not Required</span>
						</div>
						}

						}@case (BIDFORM_VIEWS.BID) {
						@if(isBidderPriceDisabled){
						<span class="badge text-bg-info">Replacement Alt Used</span>
						}@else {
						@if(unitPriceSetting === UNIT_PRICE_SETTINGS.FIXED){
						<span>{{ unitPrice | currency }}</span>
						}@else {
						<div class="input-group">
							<span class="input-group-text">$</span>
							<input type="text" class="form-control"
								[ngClass]="{'bg-warning-subtle': !isBidderPriceValid}"
								[id]="'bidderPriceBidItem' + bidItemIdx" [name]="'bidderPriceBidItem' + bidItemIdx"
								formControlName="BidderPrice" placeholder="Enter Bid Amount"
								[currencyMask]="currencyMaskOptions" />
						</div>

						@if(notRequired){
						<div>
							<span class="badge text-bg-info">Not Required</span>
						</div>
						}
						}
						}
						}
						@default {
						<span>
							{{ bidderPrice }}
						</span>
						}
						}
						@if((isBid || isEdit) && !isBidderPriceDisabled){
						<div>
							@switch(unitPriceSetting){
							@case(UNIT_PRICE_SETTINGS.MIN){
							<span class="badge text-bg-info">
								Min: {{ unitPrice | currency }}
							</span>
							}
							@case(UNIT_PRICE_SETTINGS.MAX){
							<span class="badge text-bg-info">
								Max: {{ unitPrice | currency }}
							</span>
							}@case(UNIT_PRICE_SETTINGS.FIXED){
							<span class="badge text-bg-info">
								Fixed Unit Price
							</span>
							}@case(UNIT_PRICE_SETTINGS.PERCENTAGE){
							<span class="badge text-bg-info">
								Max: {{ unitPrice }} % of Total Bid
							</span>

							@if(percentMaxAllowedError){
							<div style="color: red">
								You can only bid up to {{ percentMaxAllowedError | currency}}
							</div>
							}
							}
							}
						</div>
						}
						}
					</td>
					<td class="text-end">
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								@if(isDisabled){
								<a class="dropdown-item text-danger"
									(click)="disableBidItem(formId, sectionId, bidItemId)">
									Enable Bid Item
								</a>
								}@else {
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<div class="px-3">
									<div class="mb-3" (click)="$event.stopPropagation()">
										<select class="form-select mb-1" aria-label="Default select example"
											[id]="'unitPriceSettingBidItem' + bidItemIdx"
											[name]="'unitPriceSettingBidItem' + bidItemIdx"
											formControlName="UnitPriceSetting">
											<option [ngValue]="null">None</option>
											<option [value]="UNIT_PRICE_SETTINGS.FIXED">Fixed</option>
											<option [value]="UNIT_PRICE_SETTINGS.MAX">Max</option>
											<option [value]="UNIT_PRICE_SETTINGS.MIN">Min</option>
											<option [value]="UNIT_PRICE_SETTINGS.PERCENTAGE">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check" (click)="$event.stopPropagation()">
										<input type="checkbox" class="form-check-input"
											[id]="'bidItemDropdownCheck' + bidItemIdx" formControlName="NotRequired">
										<label class="form-check-label" [for]="'bidItemDropdownCheck' + bidItemIdx">
											Not required.
										</label>
									</div>
								</div>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<div class="px-3">
									<div class="form-check" (click)="$event.stopPropagation()">
										<input type="checkbox" class="form-check-input"
											[id]="'bidItemQtyDropdownCheck' + bidItemIdx"
											formControlName="IsQtyEditable">
										<label class="form-check-label" [for]="'bidItemQtyDropdownCheck' + bidItemIdx">
											Allow bidder to set qty.
										</label>
									</div>
								</div>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">More</h5>
								</li>
								<a class="dropdown-item" (click)="addRowAbove(formId, sectionId, bidItemIdx)">Add Bid
									Item Above</a>
								<a class="dropdown-item" (click)="addRowBelow(formId, sectionId, bidItemIdx)">Add Bid
									Item Below</a>
								<a class="dropdown-item"
									(click)="addNewReplacementAlternate(formId, sectionId, bidItemId)">Add Replacement
									Alternate</a>
								@if(useDisabledOnBidFormItem() && !isNew){
								<a class="dropdown-item text-danger"
									(click)="disableBidItem(formId, sectionId, bidItemId)">
									Disable Bid Item
								</a>
								}@else {
								<a class="dropdown-item text-danger"
									(click)="deleteBidItem(formId, sectionId, bidItemId)">Delete Bid Item</a>
								}

								}

							</ul>
						</div>
						}
						@case (BIDFORM_VIEWS.BID) {
						{{ extensionPrice }}
						}
						@default {
						{{ extensionPrice }}
						}
						}
					</td>
				</tr>
				@for (bidItemAlternate of bidItem.get('Alternates')?.controls; track $index; let bidAltItemIdx = $index)
				{
				@let altPercentMaxAllowedError =
				bidItemAlternate.get('BidderPrice')?.errors?.exceedsTenPercent?.maxAllowed;
				@let altIsNew = bidItemAlternate.value.IsNew ?? false;
				@let alternateId = bidItemAlternate.value.AlternateId;
				@let isAltDisabled = bidItemAlternate.value.IsDisabled ?? false;

				<tr class="alternate-bid-item" [formGroup]="bidItemAlternate" [ngClass]="{'disabled': isAltDisabled}">


					@if (bidView() === BIDFORM_VIEWS.EDIT){
					<td>
						<span class="badge rounded-pill bg-success">ALT</span>
						@if(!bidItemAlternate.valid)
						{
						<span class="position-relative" [ngbPopover]="errorItem" [popoverTitle]="'Alternate Not Valid'"
							(mouseover)="getBidItemErrors(bidItemAlternate)" triggers="mouseenter:mouseleave"
							popoverTitle="Pop title">
							<i class="fas fa-exclamation-triangle text-danger fa-1x"
								style="position: absolute; left: -47px; top: 50%; transform: translateY(-50%);"></i>
						</span>
						}
					</td>
					}
					<td>
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'customId1AltItem' + bidAltItemIdx"
							[name]="'customId1AltItem' + bidAltItemIdx" formControlName="CustomId1" />
						}@default {
						{{bidItemAlternate.value.CustomId1}}
						}
						}
					</td>
					<td>
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'customId2AltItem' + bidAltItemIdx"
							[name]="'customId2AltItem' + bidAltItemIdx" formControlName="CustomId2" />
						}@default {
						{{bidItemAlternate.value.CustomId1}}
						}
						}

					</td>
					<td>
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'descriptionAltItem' + bidAltItemIdx"
							[name]="'descriptionAltItem' + bidAltItemIdx" formControlName="Description" />
						}@default {
						{{bidItemAlternate.value.Description}}
						<div>
							<span class="badge text-bg-info">Replacement Alt. for Item {{ bidItem.value.CustomId1
								}}</span>
						</div>
						}
						}

					</td>
					<td>
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						<input type="text" class="form-control" [id]="'unitAltItem' + bidAltItemIdx"
							[name]="'unitAltItem' + bidAltItemIdx" formControlName="Unit" />
						}@default {
						{{bidItemAlternate.value.Unit}}
						}
						}
					</td>
					<td class="text-end">

						@if( (isEdit || isBid || bidItemAlternate.value.IsQtyEditable) &&
						bidItemAlternate.value.UnitPriceSetting !== UNIT_PRICE_SETTINGS.PERCENTAGE){

						@if(bidItemAlternate.get('Qty')?.valid && (isEdit || bidItemAlternate.value.IsQtyEditable)){
						<input type="text" class="form-control" [id]="'qtyAltItem' + bidAltItemIdx"
							[name]="'qtyAltItem' + bidAltItemIdx"
							[ngClass]="{'bg-warning-subtle': !bidItemAlternate.get('Qty')?.valid}" formControlName="Qty"
							[currencyMask]="currencyMaskNaturalOptions" />

						@if( bidItemAlternate.value.IsQtyEditable){
						<div>
							<span class="badge text-bg-info">Bidder to Input Qty</span>
						</div>
						}

						}@else {
						{{bidItemAlternate.value.Qty}}
						}

						}@else {
						{{bidItemAlternate.value.Qty}}
						}
					</td>
					<td class="text-end">
						@if(bidItemAlternate.get('BidderPrice')?.disabled){
						<span class="badge text-bg-info">Bid Item Used</span>
						}@else {
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT){
						@if(bidItemAlternate.value.UnitPriceSetting === UNIT_PRICE_SETTINGS.PERCENTAGE){
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">%</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder=""
								formControlName="UnitPrice" [id]="'unitPriceAltItem' + bidAltItemIdx"
								[name]="'unitPriceAltItem' + bidAltItemIdx" [currencyMask]="currencyMaskNaturalOptions"
								[maxlength]="settings.maxCurrencyLength">
						</div>
						}@else {
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder=""
								[id]="'unitPriceAltItem' + bidAltItemIdx" [name]="'unitPriceAltItem' + bidAltItemIdx"
								formControlName="UnitPrice" [currencyMask]="currencyMaskOptions"
								[maxlength]="settings.maxCurrencyLength">
						</div>
						}

						@if(bidItemAlternate.value.NotRequired){
						<div>
							<span class="badge text-bg-info">Bidder Not Required</span>
						</div>

						}

						}@case (BIDFORM_VIEWS.BID) {
						@if(bidItemAlternate.value.UnitPriceSetting === UNIT_PRICE_SETTINGS.FIXED){
						<span>{{ bidItemAlternate.value.UnitPrice | currency}}</span>
						}@else {
						@if(bidItemAlternate.value.UnitPriceSetting === UNIT_PRICE_SETTINGS.PERCENTAGE){
						<div class="input-group">
							<span class="input-group-text">%</span>
							<input type="text" class="form-control"
								[ngClass]="{'bg-warning-subtle': !bidItemAlternate.get('BidderPrice')?.valid}"
								placeholder="" [id]="'bidderPriceAltItem' + bidAltItemIdx"
								[name]="'bidderPriceAltItem' + bidAltItemIdx" formControlName="BidderPrice"
								[currencyMask]="currencyMaskNaturalOptions">
						</div>
						}@else {
						<div class="input-group">
							<span class="input-group-text">$</span>
							<input type="text" class="form-control"
								[ngClass]="{'bg-warning-subtle': !bidItemAlternate.get('BidderPrice')?.valid}"
								formControlName="BidderPrice" placeholder="Enter Bid Amount"
								[id]="'bidderPriceAltItem' + bidAltItemIdx"
								[name]="'bidderPriceAltItem' + bidAltItemIdx" formControlName="BidderPrice"
								[currencyMask]="currencyMaskOptions" />
						</div>
						}

						@if(bidItemAlternate.value.NotRequired){
						<div>
							<span class="badge text-bg-info">Not Required</span>
						</div>

						}
						}
						}
						@default {

						<span>
							{{bidItemAlternate.value.BidderPrice | currency }}
						</span>

						}
						}


						@if(isBid || isEdit){
						<div>
							@switch(bidItemAlternate.value.UnitPriceSetting){
							@case(UNIT_PRICE_SETTINGS.MIN){
							<span class="badge text-bg-info">
								Min: {{bidItemAlternate.value.UnitPrice | currency }}
							</span>
							}
							@case(UNIT_PRICE_SETTINGS.MAX){
							<span class="badge text-bg-info">
								Max: {{bidItemAlternate.value.UnitPrice | currency }}
							</span>
							}@case(UNIT_PRICE_SETTINGS.FIXED){
							<span class="badge text-bg-info">
								Fixed Unit Price
							</span>
							}@case(UNIT_PRICE_SETTINGS.PERCENTAGE){
							<span class="badge text-bg-info">
								Max: {{ bidItemAlternate.value.UnitPrice }} % of Total Bid
							</span>

							@if(altPercentMaxAllowedError){
							<div style="color: red">
								You can only bid up to {{ altPercentMaxAllowedError | currency}}
							</div>
							}
							}
							}
						</div>
						}
						}
					</td>
					<td class="text-end">
						@switch(bidView()){
						@case(BIDFORM_VIEWS.EDIT ){
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<div class="px-3">
									<div class="mb-3" (click)="$event.stopPropagation()">
										<select class="form-select mb-1" aria-label="Default select example"
											[id]="'unitPriceSettingAltItem' + bidAltItemIdx"
											[name]="'unitPriceSettingAltItem' + bidAltItemIdx"
											formControlName="BidderPrice" formControlName="UnitPriceSetting">
											<option [ngValue]="null">None</option>
											<option [value]="UNIT_PRICE_SETTINGS.FIXED">Fixed</option>
											<option [value]="UNIT_PRICE_SETTINGS.MAX">Max</option>
											<option [value]="UNIT_PRICE_SETTINGS.MIN">Min</option>
											<option [value]="UNIT_PRICE_SETTINGS.PERCENTAGE">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check" (click)="$event.stopPropagation()">
										<input type="checkbox" class="form-check-input"
											[id]="'bidItemAltDropdownCheck' + bidAltItemIdx"
											name="'bidItemAltDropdownCheck' + bidAltItemIdx"
											formControlName="NotRequired">
										<label class="form-check-label"
											[for]="'bidItemAltDropdownCheck' + bidAltItemIdx">
											Not required.
										</label>
									</div>
								</div>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<div class="px-3">
									<div class="form-check" (click)="$event.stopPropagation()">
										<input type="checkbox" class="form-check-input"
											[id]="'bidAltItemQtyDropdownCheck' + bidAltItemIdx"
											[name]="'bidAltItemQtyDropdownCheck' + bidAltItemIdx"
											formControlName="IsQtyEditable">
										<label class="form-check-label"
											[for]="'bidAltItemQtyDropdownCheck' + bidAltItemIdx">
											Allow bidder to set qty.
										</label>
									</div>
								</div>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">More</h5>
								</li>
								@if(useDisabledOnBidFormItem() && !altIsNew){
								<a class="dropdown-item text-danger"
									(click)="disableAlternate(formId, sectionId, bidItemId, alternateId)">
									Disable Alternate
								</a>
								}@else {
								<a class="dropdown-item text-danger"
									(click)="deleteAlternate(form.value.FormId, section.value.SectionId, bidItem.value.BidItemId, bidItemAlternate.value.AlternateId)">Delete
									Alternate</a>
								}

							</ul>
						</div>
						}
						@case (BIDFORM_VIEWS.BID) {
						{{bidItemAlternate.value.ExtensionPrice | currency }}
						}
						@default {
						{{bidItemAlternate.value.ExtensionPrice | currency }}
						}
						}
					</td>
				</tr>
				}
				}
				<tr>
					<td [attr.colspan]="colSize" class="text-end">
						@if(!isEdit){
						Subtotal: {{ section.value.Total | currency}}
						}
					</td>
				</tr>

			</tbody>
			}
		</table>

	</section>
	}
	}@placeholder {
	<app-bid-form-skeleton></app-bid-form-skeleton>
	}
	}
</form>

<!-- bid forms (hide/show) -->


}

<ng-template #errorItem>
	<div [innerHTML]="latestErrorItem"></div>
</ng-template>


<div class="modal fade  modal-lg" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
	aria-hidden="true" #bidFormPreviewModal>
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Import Bid Form</h5>
				<button type="button" class="close ml-auto" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<app-bidder-import-info #importResponseInfo></app-bidder-import-info>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="hideModal()">Cancel
					Import</button>
				<button type="button" class="btn btn-primary" (click)="finalImport()">Finish Import</button>
			</div>
		</div>
	</div>
</div>