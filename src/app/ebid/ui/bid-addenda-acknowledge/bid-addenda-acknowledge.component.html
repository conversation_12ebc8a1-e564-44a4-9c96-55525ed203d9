
<header class="d-flex justify-content-between align-items-center mb-3">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div class="d-flex justify-content-end">
			<ng-content select="[grab]"></ng-content>
		</div>
	}
	<h5 class="d-flex justify-content-middle page-title m-0">		
		Addenda Acknowledgement
	</h5>

	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
	
		<div class="d-flex justify-content-end">
			<ng-content select="[remove]"></ng-content>
		</div>
	}
	@else if(bidView() === BIDFORM_VIEWS.BID){
	<!-- bidder: header -->
	<div>
		@if(acknowledgeAddendaFormGroup?.valid){
		<span class="badge text-bg-success">Completed</span>
		}@else {
		<span class="badge text-bg-danger">Not Completed</span>
		}
	</div>
	}
</header>

@if(isLoading()){
	<section class="mb-4 bid-form placeholder-glow">
		<div class="placeholder col-12" style="height: 30px;"></div>
	</section>
}@else {

<form [formGroup]="acknowledgeAddendaFormGroup">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<p>This section is auto created if you have any addendas or revisions in your documents.</p>
		<p>You cannot modify this section, except for positioning it in your bid folder</p>
		
		<ul class="list-group">
			@for (item of acknowledgeAddendaFormGroup.get('Acknowledgements')?.controls; track $index) {	
				<form [formGroup]="item">
					<li class="list-group-item">
						<div class="row d-flex align-items-center">
							<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
								{{ item.value.DocumentName }}
							</div>
							<div class="col-12 col-md-6 d-flex justify-content-md-end">
								<div class="form-check">
									<input type="checkbox" class="form-check-input" [id]="$index + 'isRequired'" [name]="$index + 'isRequired'" formControlName="IsRequired">
									<label class="form-check-label" [for]="$index + 'isRequired'">Required</label>
								</div>
							</div>
						</div>
					</li>
				</form>		
		
			}
		</ul>
		}@else if(bidView() === BIDFORM_VIEWS.BID){
			<p>To complete this portion of the bid you must acknowledge all the addenda. Do not forget to save.</p>
			<p>I acknowledge I have read and understand the following addenda:</p>
			<ul class="list-group">
				@for (item of acknowledgeAddendaFormGroup.get('Acknowledgements')?.controls; track $index) {	
					<form [formGroup]="item">
						<li class="list-group-item">
							<div class="row d-flex align-items-center">
								<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
									{{ item.value.DocumentName }}
								</div>
								<div class="col-12 col-md-6 d-flex justify-content-md-end">
									@if(item.value.AcknowledgeAt){					
										<span class="mx-4">
											{{ item.value.AcknowledgeAt | date: 'short' }}
										</span>	
										
										<i class="fas fa-check fa-lg text-success" aria-hidden="true"></i>
									}@else {
										<button class="btn btn-outline-success" (click)="acknowledgeAddenda(item.value.DocumentId)">									
											Acknowledge
										</button>
									}	
								
								</div>
							</div>
						</li>
					</form>		
			
				}
			</ul>
		
		}@else if(bidView() === BIDFORM_VIEWS.VIEW){
			<ul class="list-group">
				@for (item of acknowledgeAddendaFormGroup.get('Acknowledgements')?.controls; track $index) {	
					<form [formGroup]="item">
						<li class="list-group-item">
							<div class="row d-flex align-items-center">
								<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
									{{ item.value.DocumentName }}
								</div>
								<div class="col-12 col-md-6 d-flex justify-content-md-end">			
									@if(item.value.AcknowledgeAt){					
										<span class="mx-4">
											{{ item.value.AcknowledgeAt | date: 'short' }}
										</span>	
										
										<i class="fas fa-check fa-lg text-success" aria-hidden="true"></i>
									}@else {
										<i class="fas fa-times fa-lg text-danger" aria-hidden="true"></i>
									}															
									
								</div>
							</div>
						</li>
					</form>		
			
				}
			</ul>
		}
</form>
}