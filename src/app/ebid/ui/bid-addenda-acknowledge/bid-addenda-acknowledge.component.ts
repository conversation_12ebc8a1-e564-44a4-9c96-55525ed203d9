import { CommonModule } from '@angular/common';
import { Component, effect, inject, Injector, input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { BidViews, EBidSectionTypes } from '../../interfaces/ebid';
import { SectionBase } from '../../interfaces/section-base';
import { BidderBidInfo } from '../../interfaces/bidder-bid-info';
import { AddendaAcknowledgeService } from '../../data-access/addenda-acknowledge.service';

@Component({
  selector: 'app-bid-addenda-acknowledge',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './bid-addenda-acknowledge.component.html',
  styleUrl: './bid-addenda-acknowledge.component.css'
})
export class BidAddendaAcknowledgeComponent extends SectionBase implements OnInit, OnDestroy {
  addendaAckService = inject(AddendaAcknowledgeService);
  addendaAckSection = input.required<FormGroup>();
  bidView = this.addendaAckService.bidView;
  bidderInfoData = this.addendaAckService.bidderInfoData;  
  isLoading = this.addendaAckService.isLoading;  
  acknowledgeAddendaFormGroup = this.addendaAckService.acknowledgeAddendaFormGroup;

  constructor() {
    super(EBidSectionTypes.ADDENDA_ACKNOWLEDGE);

    effect(() => {
      this.addendaAckService.addendaAckSection.set(this.addendaAckSection());
    });
  }


  ngOnInit(): void {
    this.addendaAckService.startEffects();
  }
  ngOnDestroy(): void {    
    this.addendaAckService.stopEffects();
  }
  acknowledgeAddenda(documentId: string) {
    this.addendaAckService.acknowledgeAddenda(documentId);
  }

}