@let isEdit = bidView() === BIDFORM_VIEWS.EDIT;
@let isBid = bidView() === BIDFORM_VIEWS.BID;
@let isView = bidView() === BIDFORM_VIEWS.VIEW;
@let cawmType = workOrderSectionFormGroup?.value.WorkOrderType === WORK_ORDER_SECTION_TYPES.CAWM;
@let smType = workOrderSectionFormGroup?.value.WorkOrderType === WORK_ORDER_SECTION_TYPES.SM;
@let valid = workOrderSectionFormGroup?.valid;
<!-- header -->
<header class="d-flex justify-content-between align-items-center mb-3">
	@if(bidView() === BIDFORM_VIEWS.EDIT)
	{
		<!-- engineer: header -->
		<div class="d-flex justify-content-end">
			<ng-content select="[grab]"></ng-content>
		</div>
	}
	<h5 class="page-title m-0">Work Order</h5>
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div>
			<ng-content select="[remove]"></ng-content>
		</div>
	}@else if(bidView() === BIDFORM_VIEWS.BID){
		<!-- bidder: header -->
		@if(valid){
			<div><span class="badge text-bg-success">Completed</span></div>
		}@else {
			<div><span class="badge text-bg-danger">Incompleted</span></div>
		}
	}
</header>
@if(isLoading()){
	<section class="mb-4 bid-form placeholder-glow">
		<div class="placeholder col-12" style="height: 30px;"></div>
	</section>
}@else {
<!-- all 3 views -->
<form [formGroup]="workOrderSectionFormGroup">


	@if(isEdit){
		@if(!workOrderSectionFormGroup?.valid)
		{
			<div class="text-danger">
				<i class="fas fa-exclamation-triangle"></i>
				Work order is missing values.
			</div>	
		}
	<!-- engineer: main area -->
	<div class="row">
		<div class="col-12 col-lg-3 mb-2 mb-lg-0">
			<label for="workOrderTypeSelect" class="form-label">Type</label>
			<select class="form-control" id="workOrderTypeSelect" name="workOrderType" formControlName="WorkOrderType">
				<option value="sm">
					Straight Multiplier
				</option>
				<option value="cawm">
					Contractor Allowance with Multiplier
				</option>
			</select>
			@if(workOrderSectionFormGroup.get('WorkOrderType')?.errors?.required)
			{
				<div class="text-danger">
					<i class="fas fa-exclamation-triangle"></i>
					Required
				</div>	
			}
		</div>
		<div class="col-12 col-lg-3 mb-2 mb-lg-0">
			<label for="allowancesSelect" class="form-label">Decimal Places Allowed</label>
			<select class="form-control" id="allowancesSelect" name="decimalValue" formControlName="DecimalValue">
				@for (allowance of numberOfAllowances; track $index) {
					<option value="{{ allowance }}">{{ allowance }}</option>
				}
			</select>
			@if(workOrderSectionFormGroup.get('DecimalValue')?.errors?.required)
			{
				<div class="text-danger">
					<i class="fas fa-exclamation-triangle"></i>
					Required
				</div>	
			}
		</div>
		@if(cawmType){
		<div class="col-12 col-lg-3 mb-2 mb-lg-0">
			<div>
				<label for="fixedCurrency" class="form-label">Fixed</label>
				<div class="input-group">
					<span class="input-group-text">$</span>
					<input type="text" class="form-control" id="fixedCurrency" name="fixed" [currencyMask]="currencyMaskDefaultOptions"
						formControlName="Fixed" />
				</div>
				@if(workOrderSectionFormGroup.get('Fixed')?.errors?.required)
				{
					<div class="text-danger">
						<i class="fas fa-exclamation-triangle"></i>
						Required
					</div>	
				}
			</div>
		</div>
		<div class="col-12 col-lg-3 mb-2 mb-lg-0">
		
			<label for="allowance" class="form-label">Allowances</label>
			<div class="input-group">
				<span class="input-group-text">$</span>
			<input type="text" class="form-control" id="allowance" name="allowances" [currencyMask]="currencyMaskDefaultOptions"
				formControlName="Allowances" />
				@if(workOrderSectionFormGroup.get('Allowances')?.errors?.required)
				{
					<div class="text-danger">
						<i class="fas fa-exclamation-triangle"></i>
						Required
					</div>	
				}
			</div>
			
		</div>
		}
	</div>
	}@else if(bidView() === BIDFORM_VIEWS.BID){
	<!-- bidder: main area -->
	<div class="row">
		@if(smType){
		<div class="col-12 col-md-8 col-lg-6">
			<label class="form-label" for="userValueSmType">Enter your factor (Example: 0.9835 or 1.025)</label>
			<input type="text" class="form-control" id="userValueSmType" name="userValue"
				[ngClass]="{'bg-warning-subtle': workOrderSectionFormGroup?.value.UserValue === null}"
				placeholder="" formControlName="UserValue" [currencyMask]="currencyMaskUserInpuOptions">

			@if(workOrderSectionFormGroup.get('UserValue')?.errors?.required)
			{
				<div class="text-danger">
					<i class="fas fa-exclamation-triangle"></i>
					Required
				</div>	
			}
				
		</div>
		}@else if(cawmType){
		<div class="col-12 d-flex align-items-center">
			<div class="me-1">${{ workOrderSectionFormGroup?.get('Fixed')?.value | number }}</div>
			<div class="me-1">x</div>
			<div class="me-1">
				<input style="display:inline;max-width: 200px;" name="userValue" [currencyMask]="currencyMaskUserInpuOptions" type="text"
					class="form-control" formControlName="UserValue" />	
				@if(workOrderSectionFormGroup.get('UserValue')?.errors?.required)
				{
					<div class="text-danger">
						<i class="fas fa-exclamation-triangle"></i>
						Required
					</div>	
				}			
			</div>
			<div class="me-1">+</div>
			<div>${{ workOrderSectionFormGroup?.value.Allowances | number }}</div>
			<div class="me-1">=</div>
			<div>{{ (workOrderSectionFormGroup?.value.Total || 0) | currency }}</div>
		</div>
		}@else {
			<div class="alert alert-info" role="alert">
				Work Order section not found.
			</div>
		}
	</div>
	<div class="text-secondary small mt-3">
		Enter up to <span class="badge text-bg-info">{{ workOrderSectionFormGroup?.value.DecimalValue}}</span> decimal places.
	</div>
	}@else if(bidView() === BIDFORM_VIEWS.VIEW){
	<!-- report: main area -->
	@if(smType){
		<div>{{workOrderSectionFormGroup?.value.UserValue | number }}</div>
	}@else if(cawmType){
		<span class="me-2">{{ workOrderSectionFormGroup?.value.Fixed| currency }}</span>
		<span class="me-2">x</span>
		<span class="me-2" style="font-weight: 700;">{{workOrderSectionFormGroup?.value.UserValue | number }}</span>
		<span class="me-2">+</span>
		<span>{{ workOrderSectionFormGroup?.value.Allowances | currency }}</span>
		<span class="me-2">=</span>
		<span>{{ (workOrderSectionFormGroup?.value.Total || 0) | currency }}</span>
		
	}
	}@else {
		<div class="alert alert-danger" role="alert">
			Work Order view not found.
		</div>
	}
</form>
}