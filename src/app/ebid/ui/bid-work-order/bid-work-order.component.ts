import { ChangeDetectorRef, Component, effect, inject, Injector, input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BidViews, EBidSectionTypes, WorkOrderSectionTypes } from '../../interfaces/ebid';
import { SectionBase } from '../../interfaces/section-base';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgxCurrencyDirective, NgxCurrencyInputMode } from 'ngx-currency';
import { BidderBidInfo } from '../../interfaces/bidder-bid-info';
import { WorkOrderService } from '../../data-access/work-order-service';

@Component({
  selector: 'app-bid-work-order',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgxCurrencyDirective],
  templateUrl: './bid-work-order.component.html',
  styleUrl: './bid-work-order.component.css'
})
export class BidWorkOrderComponent extends SectionBase implements OnInit, OnD<PERSON>roy {
  workOrderService = inject(WorkOrderService);
  public readonly WORK_ORDER_SECTION_TYPES: typeof WorkOrderSectionTypes = WorkOrderSectionTypes;
  bidView = this.workOrderService.view;
  bidderInfoData = this.workOrderService.bidderInfoData;
  workOrderSection = input.required<FormGroup>();  
  isLoading = this.workOrderService.isLoading;
  currencyMaskUserInpuOptions = { prefix: '', thousands: ',', decimal: '.', nullable: true, allowNegative: true, align: "", precision: 2, suffix: "", inputMode: NgxCurrencyInputMode.Natural };
  currencyMaskNaturalOptions = { prefix: '', thousands: ',', decimal: '.', nullable: true, allowNegative: true, align: "", precision: 2, suffix: "", inputMode: NgxCurrencyInputMode.Natural };

  currencyMaskDefaultOptions = { prefix: '', thousands: ',', decimal: '.', nullable: true, allowNegative: true, align: "", precision: 2, suffix: "", inputMode: NgxCurrencyInputMode.Natural };
  numberOfAllowances = [1, 2, 3, 4];
  workOrderSectionFormGroup = this.workOrderService.workOrderSectionFormGroup;

  constructor() {
    super(EBidSectionTypes.WORK_ORDER);

    effect(() => {
      this.workOrderService.workOrderSection.set(this.workOrderSection());
    });

    effect(() => {
      if (this.workOrderService.precision()) {
        this.currencyMaskUserInpuOptions.precision = this.workOrderService.precision();
        this.currencyMaskNaturalOptions.precision = this.workOrderService.precision();
      }
    });
  }

  ngOnInit(): void {
    this.workOrderService.startEffects();
  }
  ngOnDestroy(): void {
    this.workOrderService.stopEffects();
  }


}