import { Component, effect, inject, Injector, input, OnDestroy, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { BidderBidInfo, RequiredUploadInfo } from '../../interfaces/bidder-bid-info';
import { BidViews, EBidSectionTypes } from '../../interfaces/ebid';
import { SectionBase } from '../../interfaces/section-base';
import { CommonModule } from '@angular/common';
import { RequiredUploadService, RequiredUploadSetting } from '../../data-access/required-uploads.service';
import { FileSize } from 'src/app/shared/utils/pipes/files-pipe';
import { MultiSelectModule } from 'primeng/multiselect';
import { ToastrService } from 'ngx-toastr';
import { v4 } from 'uuid';

@Component({
    selector: 'app-bid-required-uploads',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, FileSize, MultiSelectModule, FormsModule],
    templateUrl: './bid-required-uploads.component.html',
    styleUrl: './bid-required-uploads.component.css'
})
export class BidRequiredUploadsComponent extends SectionBase implements OnInit, OnDestroy {

  requiredUploadService = inject(RequiredUploadService);  
  bidView = this.requiredUploadService.bidView;
  bidderInfoData = this.requiredUploadService.bidderBidInfoData;
  requiredUploadSection = input.required<FormGroup>();  
  isLoading = this.requiredUploadService.isLoading
  selectedDocuments = signal<Array<RequiredUploadSetting>>([]);
  toastrService = inject(ToastrService);
  requiredUploadsFormGroup = this.requiredUploadService.requiredUploadsFormGroup;
  requiredUploadNewFormGroup = 	new FormGroup({
		requiredUploadId: new FormControl(v4(), Validators.required),
		name: new FormControl('', Validators.required),
		notes: new FormControl('')
	});
  requiredUploadsSettings = this.requiredUploadService.requiredUploadsSettings;
  constructor() {
    super(EBidSectionTypes.REQUIRED_UPLOADS);
    
    effect(() => {
      this.requiredUploadService.requiredUploadSection.set(this.requiredUploadSection());
    });

    effect(() => {
      if(this.requiredUploadService.resetAddForm()){
        this.requiredUploadNewFormGroup.patchValue({
          requiredUploadId: v4(),
          name: '',
          notes: ''
        });        
      }
    })
  }
  ngOnInit(): void {
    this.requiredUploadService.startEffects();
  }
  ngOnDestroy(): void {    
    this.requiredUploadService.stopEffects();
  }

  addRequiredUploadInfoToForm(requiredUploadInfo: RequiredUploadInfo){
    this.requiredUploadService.addRequiredUploadInfoToForm(requiredUploadInfo);
  }

  downloadFile(key:string, fileName:string, fileGroup: FormGroup){
    this.requiredUploadService.downloadFile(key, fileName, fileGroup);
  }

  removeUpload(requiredUploadId: string, uploadId: string){
    this.requiredUploadService.removeUpload(requiredUploadId, uploadId);
  }

  addRequiredUploadInfo(requiredUploadId: string, uploadId: string, documentName: string, key: string, s3Bucket: string, createdAt:Date, size: number, comment: string){
    this.requiredUploadService.addRequiredUploadInfo(requiredUploadId, uploadId, documentName, key, s3Bucket, createdAt, size, comment);
   
  }

  onFileChange(event: any, requiredUploadId: string){
    this.requiredUploadService.onFileChange(event, requiredUploadId);
  }

  addRequiredUpload(){
    this.requiredUploadService.addRequiredUpload(v4(), this.requiredUploadNewFormGroup.get('name')?.value  as string, this.requiredUploadNewFormGroup.get('notes')?.value as string);
  }

  removeRequiredUpload(requiredUploadId: string){
    this.requiredUploadService.removeRequiredUpload(requiredUploadId);
  }

  setIsEditting(requiredUploadId: string, isEditting: boolean){
    this.requiredUploadService.setIsEditting(requiredUploadId, isEditting);
  }

  addDocumentsFromSettings(){
    for(let setting of this.selectedDocuments()){
      this.addFromExisting(setting);  
      
    }
  }

  sectionsSelected(event: any){
    this.selectedDocuments.set(event.value);
    
  }

  addFromExisting(ruSetting: RequiredUploadSetting){
    this.requiredUploadService.addRequiredUploadFromExisting(ruSetting);
  }
}