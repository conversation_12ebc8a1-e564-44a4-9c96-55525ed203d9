
	<header class="d-flex justify-content-between align-items-center mb-3">
		@if(bidView() === BIDFORM_VIEWS.EDIT){
			<!-- engineer: header -->
			<div class="d-flex justify-content-end">
				<ng-content select="[grab]"></ng-content>
			</div>
		}
		<h5 class="page-title m-0">Mandatory Documents</h5>
		@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div class="d-flex justify-content-end">
			<ng-content select="[remove]"></ng-content>
		</div>
		}
		@else if(bidView() === BIDFORM_VIEWS.BID && !isLoading()){
		<!-- bidder: header -->
		<div>
			@if(requiredUploadsFormGroup?.valid){
			<span class="badge text-bg-success">Completed</span>
			}@else {
			<span class="badge text-bg-danger">Not Completed</span>
			}
		</div>
		}
	</header>
	@if(isLoading()){
		<section class="mb-4 bid-form placeholder-glow">
			<div class="placeholder col-12" style="height: 30px;"></div>
		</section>
	}@else {
	<form [formGroup]="requiredUploadsFormGroup">
		@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: main area -->
		<p>List all documents that bidders must upload to complete their bid submission (e.g., bid bond, forms,
			statement of qualifiications, conflict on interest questionnaire, etc. )</p>
		
	
		@if(requiredUploadsFormGroup.get('RequiredUploads')?.hasError('minLengthArray')){
			<div class="text-danger">
				<i class="fas fa-exclamation-triangle"></i>
				Must have at least 	{{requiredUploadsFormGroup.get('RequiredUploads')?.errors?.minLengthArray?.minLength }}  
				required upload added to this section or remove this section from the bid folder.
			</div>
		}
		<div class="bg-light p-3 mb-3">
			<div class="form-check mb-3">
				<input class="form-check-input" type="checkbox" name="onlyPDF" id="onlyPDF" formControlName="OnlyPDF">
				<label class="form-check-label" for="onlyPDF">
					Restrict to PDF Files Only
				</label>
			</div>
			<form [formGroup]="requiredUploadNewFormGroup" (submit)="addRequiredUpload()">
				<pre>{{ selectedDocuments | json }}</pre>
				<div class=" col-12 col-md-6 mb-3">
					<label for="exampleInputEmail1" class="form-label">Add From Existing</label>


					<div>
						<p-multiSelect 
						[options]="requiredUploadsSettings()"	
						display="none"
						optionLabel="name"	
						[filter]="false"
						[showToggleAll]="true"
						[showHeader]="false"
						style="width: 300px;"
						[displaySelectedLabel]="false"	
						(onChange)="sectionsSelected($event)"						
						placeholder="Select Documents" [disabled]="isLoading()">
					
					</p-multiSelect>
						<button class="btn btn-outline-secondary" type="button" id="button-addon2" (click)="addDocumentsFromSettings()" style="margin-bottom: 12px;">Add</button>
					</div>
				</div>
				<div class="mb-3">
					<label for="exampleInputEmail1" class="form-label">Add New</label>
					<input type="text" class="form-control" placeholder="Document name" aria-label="name"
						aria-describedby="button-addon2" formControlName="name">
				</div>
				<div class="mb-3">
					<textarea class="form-control" id="exampleFormControlTextarea1" rows="3"
						placeholder="Notes/Instructions" formControlName="notes" aria-label="Notes"
						aria-describedby="button-addon2"></textarea>
				</div>
				<div class="d-flex justify-content-end">
					<button class="btn btn-outline-secondary" type="submit" id="button-addon2"
						[disabled]="!requiredUploadNewFormGroup.valid">Add New</button>
				</div>
			</form>
		</div>
		<div>
			<h6>Mandatory Document List</h6>
			<ul class="list-group">
				@for(requiredUpload of requiredUploadsFormGroup?.get('RequiredUploads')?.controls; track $index){
				<li class="list-group-item">
					@if(requiredUpload.value.IsEditting){
					<form [formGroup]="requiredUpload">
						<div class="mb-3">
							<input type="text" class="form-control" placeholder="Document Name" aria-label="name"
								aria-describedby="button-addon2" formControlName="Name">
						</div>
						<div class="mb-3">
							<textarea class="form-control" id="exampleFormControlTextarea1" rows="3" placeholder="Notes/Instructions" aria-label="Notes"
							aria-describedby="button-addon2" formControlName="Notes"></textarea>
						</div>
						<div class="d-flex justify-content-end">
							<button class="btn btn-outline-dark" type="button" id="button-addon2"
								(click)="setIsEditting(requiredUpload.value.RequiredUploadId, false)"
								[disabled]="!requiredUpload.valid">Close</button>
						</div>
					</form>
					}@else {
					<div class="d-flex justify-content-between align-items-start">
						<div>
							<h6>{{requiredUpload.get('Name')?.value}}</h6>
							<p class="small text-secondary m-0">{{requiredUpload.value.Notes}}</p>
						</div>
						<div class="btn-group">
							<button type="button" class="btn btn-outline-dark"
								(click)="setIsEditting(requiredUpload.value.RequiredUploadId, true)">Edit</button>
							<button type="button" class="btn btn-outline-danger"
								(click)="removeRequiredUpload(requiredUpload.value.RequiredUploadId)">Remove</button>
						</div>
					</div>
					}
				</li>
				}
			</ul>
		</div>
		}@else if(bidView() === BIDFORM_VIEWS.BID){
			@let pdfOnly = requiredUploadsFormGroup?.value?.OnlyPDF;
			@if(pdfOnly){
				<div class="alert alert-info">
					PDF's only is allowed for uploads. 
				</div>
			}
		
		<!-- bidder: main area -->
			@for(requiredUpload of requiredUploadsFormGroup?.get('RequiredUploads')?.controls; track $index){
				<div class="border-bottom py-3">				
				<div class="d-flex justify-content-between align-items-start">
					@if(requiredUpload?.value.FileUploadInfo.IsProgressing){						
						<div class="col-9 mb-1 mb-md-0 col-md-5">
							{{ requiredUpload.value.FileUploadInfo.Name }}
						</div>
						<div class="col-3 mb-1 mx-2 mb-md-0 col-md-2 text-end">
							{{ requiredUpload.value.FileUploadInfo.Size  | filesize }}
						</div>
						<div class="col-9 mb-1 mb-md-0 col-md-4">
							<div class="progress" style="height:30px;">
								<div class="progress-bar bg-success" role="progressbar"
									[style.width]="requiredUpload.value.FileUploadInfo.UploadProgress + '%'" aria-valuemin="0"
									aria-valuemax="100"></div>
							</div>
						</div>
						<div class="col-3 mb-1 mb-md-0 col-md-1 text-end">
							@if(requiredUpload.value.FileUploadInfo.UploadProgress < 100 && requiredUpload.value.FileUploadInfo.IsProgressing){								
								{{ requiredUpload.value.FileUploadInfo.UploadProgress }}%							
							}@else if(requiredUpload.value.FileUploadInfo.UploadProgress === 100 && requiredUpload.value.FileUploadInfo.IsComplete){
									<i class="fas fa-check text-success"></i>
							}			
						</div>
					}@else {
						<div>
							<h6>{{requiredUpload.value.Name}}</h6>
							<div>{{requiredUpload.value.Notes}}</div>
						</div>
						<div class="btn-group">
							<label class="btn btn-outline-success btn-sm">Upload File			
								<input type="file" class="form-control required d-none" [name]="'upload-' + requiredUpload.value.RequiredUploadId" 
								(change)="onFileChange($event, requiredUpload.value.RequiredUploadId)" [accept]=" pdfOnly ? '.pdf' : ''">
							</label>
						</div>
					}
				</div>
				@for (item of requiredUpload.get('Uploads')?.controls; track $index) {
					<form [formGroup]="item">		
						<div class="my-4">
							<!-- {{ $index + 1 }} -->
							<button class="btn btn-link" (click)="downloadFile(item.value.Key, item.value.DocumentName, item)" [disabled]="item.value.IsDownloading">
								@if(item.value.IsDownloading){
									<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}
								{{item.value.DocumentName}}
							</button>
							<button class="btn btn-outline-danger"
								(click)="removeUpload(requiredUpload.value.RequiredUploadId, item.value.UploadId)">Remove</button>
							<div class="my-4">
								<textarea class="form-control" [name]="'comment-' + item.value.UploadId"
									formControlName="Comment" placeholder="Add an optional comment with your document."></textarea>
							</div>
						</div>
					</form>
				}
		</div>
			}
		}@else if(bidView() === BIDFORM_VIEWS.VIEW){
		<!-- report: main area -->
			@for(requiredUpload of requiredUploadsFormGroup?.get('RequiredUploads')?.controls; track $index){
			<div class="border-bottom py-3">
				<div class="mb-2">
					<h6 class="mb-1">{{requiredUpload.value.Name}}</h6>
					<div>{{requiredUpload.value.Notes}}</div>
				</div>
				@for (item of requiredUpload.get('Uploads')?.controls; track $index) {
					<div class="mb-2">
						<!-- 		{{ $index + 1 }}. -->
						<button class="btn btn-link m-0 p-0"
							(click)="downloadFile(item.value.Key, item.value.DocumentName, item)">
							@if(item.value.IsDownloading){
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
							{{item.value.DocumentName}}
						</button>
					</div>
					<div class="text-muted small">
						@if(item.value.Comment){
							{{item.value.Comment}}
						}
					</div>
				}
			</div>
			}
		
		}
	</form>
	}