<header class="d-flex justify-content-between align-items-center mb-3">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div class="d-flex justify-content-end">
			<ng-content select="[grab]"></ng-content>
		</div>
	}

	<h5 class="page-title m-0">Disclaimer</h5>
	
	@if(bidView() === BIDFORM_VIEWS.EDIT){
	<!-- engineer: header -->
	<div>
		<ng-content select="[remove]"></ng-content>
	</div>
	}
	@else if(bidView() === BIDFORM_VIEWS.BID){
	<!-- bidder: header -->
	<div>
		@if(acknowledgeFormGroup?.valid){
		<span class="badge text-bg-success">Completed</span>
		}@else {
		<span class="badge text-bg-danger">Not Completed</span>
		}
	</div>
	}
</header>

@if(isLoading()){
	<section class="mb-4 bid-form placeholder-glow">
		<div class="placeholder col-12" style="height: 30px;"></div>
	</section>
}@else {

<form [formGroup]="acknowledgeFormGroup">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
	<!-- engineer: main area -->
	<p>The Disclaimer allows you to communicate essential terms, conditions, or requirements that contractors must
		read and acknowledge before submitting a bid. </p>
	<div class="mb-3">
		<button type="button" class="btn btn-outline-dark" (click)="importDisclaimer()" [disabled]="settingDisclaimer()?.length <= 0">Import Standard Disclaimer</button>
	</div>
	<div class="form-group mb-3">
		<!-- <p-editor [style]="{ height: '300px'}" id="disclaimer1"  name="disclaimer1" formControlName="disclaimer"/> -->
		<app-custom-quill name="disclaimer" class="w-100" theme="snow" [modules]="quillModulesConfig" [s3Info]="s3Prefix()"
			[s3Prefix]="s3Prefix()" height="300px" format="html" formControlName="Disclaimer"></app-custom-quill>
	</div>
	<div class="d-flex justify-content-end">
		<button type="button" class="btn btn-outline-dark btn-sm" (click)="clear()">Clear</button>
	</div>
	}@else if(bidView() === BIDFORM_VIEWS.BID){
	<!-- bidder: main area -->
	<div ng-switch="view">
		<div class="mb-4">Please read the disclaimer message carefully and check the box to acknowledge your
			agreement before submitting your bid.</div>
			<div class="p-3 bg-light mb-3" [innerHTML]="acknowledgeFormGroup.value?.Disclaimer"></div>			
		@if(acknowledgeFormGroup.value?.AcknowledgedAt){
		<div class="d-flex justify-content-end align-items-center mb-2">
			<i class="fas fa-check text-success me-2" aria-hidden="true"></i>
			<span class="me-1">Message accepted.</span>
		</div>
		<div class="d-flex justify-content-end align-items-center">
			<button type="button" class="btn btn-outline-danger" (click)="undo()">Undo</button>
		</div>
		}@else {
		<div class="d-flex justify-content-end">
			<button type="button" class="btn btn-outline-dark" (click)="accept()">I Accept</button>
		</div>
		}
	</div>
	}@else if(bidView() === BIDFORM_VIEWS.VIEW){
	<!-- report: main area -->
	<div class="p-3 bg-light mb-3" [innerHTML]="acknowledgeFormGroup.value?.Disclaimer"></div>
	<div class="d-flex justify-content-end align-items-center">
		@if(acknowledgeFormGroup.value.AcknowledgedAt){
			<i class="fas fa-check text-success me-2" aria-hidden="true"></i>
			<span>Message accepted.</span>
		}@else {
			<i class="fas fa-times text-danger me-2" aria-hidden="true"></i>
			<span>Message not accepted.</span>
		}
	</div>
	}
</form>
}