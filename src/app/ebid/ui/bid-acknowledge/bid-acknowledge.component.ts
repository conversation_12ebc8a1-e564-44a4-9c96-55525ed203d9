import { Component, effect, HostListener, inject, input, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { SectionBase } from '../../interfaces/section-base';
import { BidViews, EBidSectionTypes } from '../../interfaces/ebid';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BidderBidInfo } from '../../interfaces/bidder-bid-info';
import { CommonModule } from '@angular/common';
import { quillModules } from 'src/app/shared/utils/quill-modules';
import { CustomQuillComponent } from 'src/app/shared/ui/custom-quill/custom-quill.component';
import { AcknowledgeService } from '../../data-access/acknowledge.service';

@Component({
  selector: 'app-bid-acknowledge',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, CustomQuillComponent],
  templateUrl: './bid-acknowledge.component.html',
  styleUrl: './bid-acknowledge.component.css'
})
export class BidAcknowledgeComponent extends SectionBase implements OnInit, OnDestroy {
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
      event.preventDefault();

    }else if(event.ctrlKey && event.key.toLocaleLowerCase() === 'z' && event.shiftKey){
      event.preventDefault();
 
    }   
  }

  acknowledgeService = inject(AcknowledgeService);
  acknowledgeSection = input.required<FormGroup>();  
  bidView = this.acknowledgeService.bidView;
  bidderInfoData = this.acknowledgeService.bidderInfoData;
  s3Prefix = this.acknowledgeService.s3Prefix;  
  isLoading = input<boolean>(false);
  quillModulesConfig = quillModules;
  acknowledgeFormGroup = this.acknowledgeService.acknowledgeFormGroup;
  settingDisclaimer = this.acknowledgeService.bidSettingDisclaimer;
  constructor() {
    super(EBidSectionTypes.ACKNOWLEDGE);

    effect(() => {
      this.acknowledgeService.acknowledgeSection.set(this.acknowledgeSection());
    });
  }
  ngOnInit(): void {
    this.acknowledgeService.startEffects();
  }
  ngOnDestroy(): void {
    this.acknowledgeService.stopEffects();
  }

  accept() {
    this.acknowledgeService.accept();    
  }

  undo() {
    this.acknowledgeService.undo();  
  }
  clear() {
    this.acknowledgeService.clear();  
  }

  importDisclaimer() {
    this.acknowledgeService.importDisclaimer();
  }
}