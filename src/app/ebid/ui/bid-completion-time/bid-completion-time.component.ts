import { ChangeDetectionStrategy, ChangeDetectorRef, Component, effect, inject, Injector, input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CompletionTime, CompletionTimeOptions } from '../../interfaces/completion-time-section';
import { SectionBase } from '../../interfaces/section-base';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { BidderBidInfo } from '../../interfaces/bidder-bid-info';
import { CompletionTimeService } from '../../data-access/completion-time.service';
import { DropdownModule } from 'primeng/dropdown';
import { v4 } from 'uuid';
import { BidViews, EBidSectionTypes } from '../../interfaces/ebid';


@Component({
  selector: 'app-bid-completion-time',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, DropdownModule],
  templateUrl: './bid-completion-time.component.html',
  styleUrl: './bid-completion-time.component.css'  
})
export class BidCompletionTimeComponent extends SectionBase implements OnInit, OnDestroy {
  completionTimeService = inject(CompletionTimeService);  
  completionTimeSection = input.required<FormGroup>();
  bidView = this.completionTimeService.bidView;
  bidderInfoData = this.completionTimeService.bidderInfoData;
  isLoading = this.completionTimeService.isLoading;    
  completionTypes = this.completionTimeService.completionTypes;
  defaultCompletionTimes = this.completionTimeService.defaultCompletionTimes
  completionTimeOptions = this.completionTimeService.completionTimeOptions;
  bidderCompletionTimeOptions = this.completionTimeService.bidderCompletionTimeOptions;
  completionTimeFormGroup = this.completionTimeService.completionTimeFormGroup;
  completionTimeAddFormGroup = 	new FormGroup({
		completionTimeOption: new FormControl('', Validators.required),
		completionType: new FormControl('', Validators.required),
		maxDays: new FormControl(null),
	});

  public readonly COMPLETION_TIME_OPTIONS: typeof CompletionTimeOptions = CompletionTimeOptions;  
  
  constructor() {
    super(EBidSectionTypes.COMPLETION_TIME);
    effect(() => {
      this.completionTimeService.completionTimeSection.set(this.completionTimeSection());
    });
  }
  ngOnDestroy(): void {    
    this.completionTimeService.stopEffects();
  }
  ngOnInit(): void {    ;
    this.completionTimeService.startEffects();
    this.completionTimeService.getCompletionTypes();
  }

  blockDecimal(event: KeyboardEvent): void {
    if (event.key === '.') {
      event.preventDefault();
    }
  }

  remove(completionTimeId: string) {    
   this.completionTimeService.remove(completionTimeId);
  }

  addTime(): void {
    if (this.completionTimeAddFormGroup.valid) {
		  var completionTime = {
			CompletionTimeId: v4(),
			CompletionTimeOption: this.completionTimeAddFormGroup.get('completionTimeOption')?.value,
			CompletionType: this.completionTimeAddFormGroup.get('completionType')?.value,
			MaxDays: this.completionTimeAddFormGroup.get('maxDays')?.value
		  } as CompletionTime;
	
      this.completionTimeService.addTime(completionTime);
		
		}
    
  }

}