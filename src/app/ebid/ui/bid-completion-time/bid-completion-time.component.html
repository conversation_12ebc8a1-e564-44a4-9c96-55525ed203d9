<header class="d-flex justify-content-between align-items-center mb-3">
	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div class="d-flex justify-content-end">
			<ng-content select="[grab]"></ng-content>
		</div>
	}

	<h5 class="page-title">Completion Time</h5>

	@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: header -->
		<div>
			<ng-content select="[remove]"></ng-content>
		</div>
	}@else if(bidView() === BIDFORM_VIEWS.BID){
	<!-- bidder: header -->
		@if(completionTimeFormGroup?.valid){
			<div><span class="badge text-bg-success">Completed</span></div>
		}@else {
			<div><span class="badge text-bg-danger">Incompleted</span></div>
		}
	}
</header>

@if(isLoading()){
	<section class="mb-4 bid-form placeholder-glow">
		<div class="placeholder col-12" style="height: 30px;"></div>
	</section>
}@else {
	<form [formGroup]="completionTimeFormGroup">
		@if(bidView() === BIDFORM_VIEWS.EDIT){
		<!-- engineer: main area -->
		<p>Select the specific completion milestones (e.g., Complete, Substantially Complete, 50% Complete) you require and
			indicate whether bidders should enter their timeline in calendar days or work days.</p>
		@if(completionTimeFormGroup.get('CompletionTimes')?.hasError('minLengthArray')){
			<div class="text-danger">
				<i class="fas fa-exclamation-triangle"></i>
				Must have at least 	{{completionTimeFormGroup.get('CompletionTimes')?.errors?.minLengthArray?.minLength }}  
				required upload added to this section or remove this section from the bid folder.
			</div>
		}
		<div class="mb-3">
			<button type="button" class="btn btn-outline-dark" data-bs-toggle="modal" data-bs-target="#exampleModal">
				Add
			</button>
		</div>
		<div class="">
			<h6>Requested Completion Times</h6>
			<ul class="list-group">
				<li class="list-group-item">
					<div class="row fw-bold d-none d-lg-flex">
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							Completion Type
						</div>
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							Timeline Type
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0">
							Max Days
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0 d-flex justify-content-end">
	
						</div>
					</div>
				</li>
				@for (item of completionTimeFormGroup.get('CompletionTimes')?.controls; track $index) {
				<li class="list-group-item">
					<div class="row">
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							{{ item.get('CompletionType').value }}
						</div>
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							{{ item.get('CompletionTimeOption').value }}
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0">
							{{ item.get('MaxDays').value }}
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0 d-flex justify-content-lg-end">
							<button type="button" class="btn btn-outline-danger btn-sm"
								(click)="remove(item.get('CompletionTimeId')?.value)">Remove</button>
						</div>
					</div>
				</li>
				}@empty {
				<li class="list-group-item">
					<div class="row">
						<div class="col-12">
							No completion time requirements have been added.
						</div>
					</div>
				</li>
				}
	
			</ul>
		</div>
		}@else if(bidView() === BIDFORM_VIEWS.BID){
		<div class="mb-4">Enter your completion time(s).
		</div>
	
		<div class="">
			<h6>Requested Completion Times</h6>
			<ul class="list-group">
				<li class="list-group-item">
					<div class="row fw-bold d-none d-lg-flex">
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							Completion Type
						</div>
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							Timeline Type
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0">
							Max Days
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0 d-flex justify-content-end">
							User Input Time
						</div>
					</div>
				</li>
				@for (item of completionTimeFormGroup.get('CompletionTimes')?.controls; track $index) {
				<form [formGroup]="item">
					<li class="list-group-item">
						<div class="row">					
							<div class="col-12 col-lg-4 mb-1 mb-lg-0">
								{{ item.get('CompletionType').value }}
							</div>
							<div class="col-12 col-lg-4 mb-1 mb-lg-0">
								@if(item.get('CompletionTimeOption').value === COMPLETION_TIME_OPTIONS.USER_DECIDES){
									<select class="form-control" name="userCompletionTime" formControlName="UserCompletionTime">
										@for (item of bidderCompletionTimeOptions; track $index) {
										<option [value]="item">
											{{ item}}
										</option>
										}
									</select>
								}@else {
									{{ item.get('CompletionTimeOption').value }}
								}
	
							</div>
							<div class="col-12 col-lg-2 mb-1 mb-lg-0">
								{{ item.get('MaxDays').value }}
							</div>
							<div class="col-12 col-lg-2 mb-1 mb-lg-0 d-flex justify-content-lg-end">
								<input type="number" class="form-control" [name]="$index + 'userTime'"
									(keydown)="blockDecimal($event)" formControlName="UserTime"  [ngClass]="{'bg-warning-subtle': !item.get('UserTime')?.valid}" />
							</div>
						</div>
					</li>
				</form>
				}@empty {
				<li class="list-group-item">
					<div class="row">
						<div class="col-12">
							No completion time requirements have been added.
						</div>
					</div>
				</li>
				}
	
			</ul>
		</div>
		}@else if(bidView() === BIDFORM_VIEWS.VIEW){
		<ul class="list-group">
			<li class="list-group-item">
				<div class="row fw-bold d-none d-lg-flex">
					<div class="col-12 col-lg-4 mb-1 mb-lg-0">
						Completion Type
					</div>
					<div class="col-12 col-lg-4 mb-1 mb-lg-0">
						Timeline Type
					</div>
					<div class="col-12 col-lg-2 mb-1 mb-lg-0">
						Max Days
					</div>
					<div class="col-12 col-lg-2 mb-1 mb-lg-0 d-flex justify-content-end">
						User Input Time
					</div>
				</div>
			</li>
			@for (item of completionTimeFormGroup.get('CompletionTimes')?.controls; track $index) {
			<form [formGroup]="item">
				<li class="list-group-item">
					<div class="row">
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							{{ item.get('CompletionType').value }}
						</div>
						<div class="col-12 col-lg-4 mb-1 mb-lg-0">
							{{ item.get('UserCompletionTime').value }}
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0">
							{{ item.get('MaxDays').value }}
						</div>
						<div class="col-12 col-lg-2 mb-1 mb-lg-0 d-flex justify-content-lg-end">
							{{ item.get('UserTime').value }}
						</div>
					</div>
				</li>
			</form>
			}@empty {
			<li class="list-group-item">
				<div class="row">
					<div class="col-12">
						No completion time requirements have been added.
					</div>
				</div>
			</li>
			}
	
		</ul>
		}
	</form>
}




<form [formGroup]="completionTimeAddFormGroup" (submit)="addTime()">
	<!-- Modal -->
	<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h1 class="modal-title fs-5" id="exampleModalLabel">Add Completion Time Requirement</h1>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="mb-3">
						<label for="completionTypeDropDown" class="form-label">Completion Type</label>
						<p-dropdown id="completionTypeDropDown" [options]="completionTypes()" formControlName="completionType"
							placeholder="Select a Completion Type" [editable]="true" [checkmark]="false"
							name="completionTypeDropDown" class="completion-type-dropdown"></p-dropdown>					

						<div id="emailHelp" class="form-text">Select one of our standard completion types or add your
							own.</div>
					</div>
					<div class="mb-3">
						<label for="completionTimeFrame" class="form-label">Timeline Type</label>
						<select class="form-control" name="completionTimeFrame" id="completionTimeFrame" formControlName="completionTimeOption">
							@for (item of completionTimeOptions; track $index) {
							<option [value]="item">
								{{ item}}

							</option>
							}
						</select>
					</div>
					<div class="col-7">
						<label for="maxDays" class="form-label">Maximum Allowed Days (Optional)</label>
						<input type="number" class="form-control" id="maxDays" (keydown)="blockDecimal($event)"
							name="maxDays" formControlName="maxDays" aria-describedby="maxdays">
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
					<button type="submit" class="btn btn-primary" data-bs-dismiss="modal"
						[disabled]="!completionTimeAddFormGroup.valid">Add</button>
				</div>
			</div>
		</div>
	</div>
</form>