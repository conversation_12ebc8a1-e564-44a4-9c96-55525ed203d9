input.ng-invalid.ng-dirty {
	border: 2px solid red;
	background-color: #ffe6e6;
  }
  
  input.ng-invalid.ng-dirty:focus {
	outline: none;
	box-shadow: 0 0 5px red;
  }

/* Assuming the inner input box has a class like 'p-dropdown-label-container' */
::ng-deep .p-dropdown {
    height: 42px;
    display: flex;	
	padding-left: 15px;
    align-items: center; /* Optional: to vertically center the text */
}

::ng-deep .p-dropdown-items {
  padding-left: 0px;
}

::ng-deep .p-dropdown-item span {	
	padding: 5px;
}