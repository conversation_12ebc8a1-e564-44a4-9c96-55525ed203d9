/**
 * This component is used to display the bid form view. It will display the bid form section information.
 * 
 */

import { Component, computed, effect, input, signal } from '@angular/core';
import { BidFormSectionInfo, FormInfo } from '../../interfaces/bidder-bid-info';
import { BidForm, BidFormSection, BidSection, UnitPriceSettings } from '../../interfaces/bid-form-section';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-bid-form-view',
  imports: [CommonModule],
  templateUrl: './bid-form-view.component.html',
  styleUrl: './bid-form-view.component.css'
})
export class BidFormViewComponent {

  bidFormSection = input.required<BidFormSection | undefined>();
  bidFormSectionInfo = input.required<BidFormSectionInfo | undefined>();

bidForm = signal<any>(undefined);

  bidFormInfo = effect(() => {



    if(this.bidFormSection() && this.bidFormSectionInfo()) {
      const forms = {
        Forms: Array<any>()
      };

      var bfForms = this.bidFormSection()?.Forms as Array<BidForm>;
      var bfFormsInfo = this.bidFormSectionInfo()?.Forms as Array<FormInfo>;
      for(let mainForm of bfForms) {
        let formInfo = bfFormsInfo.find((form) => form.FormId === mainForm.FormId);

        if(formInfo){
          const fForm = {
            FormId: mainForm.FormId,
            FormName: mainForm.Title,
            FormTotal: formInfo.FormTotal,
            Sections: Array<any>()
          } as any;

          for(let section of mainForm.Sections){
            let sectionInfo: any = formInfo.Sections?.find((x:any) => x.SectionId === section.SectionId);

            if(sectionInfo){
              const fSection = {
                SectionId: section.SectionId,
                SectionName: section.Title,
                SectionTotal: sectionInfo.SectionTotal,                
              }
        
              fForm.Sections.push(fSection);
            }
          }

          forms.Forms.push(fForm);
        }   
      }

      this.bidForm.set(forms);  
    }


  });
}
