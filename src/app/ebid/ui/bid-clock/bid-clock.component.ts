import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, Injector, input, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { tap } from 'rxjs';
import { HelperTools } from 'src/app/shared/utils/helper-tools';
import { BidClockService } from '../../data-access/bid-clock.services';
import { BaseEffectsService } from 'src/app/shared/interfaces/abstract-effects-service';

@Component({
  selector: 'app-bid-clock',
  imports: [CommonModule, FormsModule],
  standalone: true,
  templateUrl: './bid-clock.component.html',
  styleUrl: './bid-clock.component.css'
})
export class BidClockComponent extends BaseEffectsService implements OnInit, OnDestroy {

  clockComponentInjector = inject(Injector);
  ngOnDestroy(): void {
    this.stopEffects();

  }
  ngOnInit(): void {
  
  }


  constructor() {
    super();
  }

  clockService = inject(BidClockService);  

  timeLeft = this.clockService.timeLeft;
  timeInterval: any = null;
  startDate = this.clockService.startDate;

  daysLeft= this.clockService.daysLeft;
  monthsLeft= this.clockService.monthsLeft;
  yearsLeft= this.clockService.yearsLeft;
  hoursLeft= this.clockService.hoursLeft;
  minutesLeft= this.clockService.minutesLeft;
  secondsLeft= this.clockService.secondsLeft;
  isBidExpiringSoon = computed(() => this.timeLeft() <= 60 * 5); // 5 minutes


}
