<div class="my-4" [ngClass]="{'clock-pulsate': isBidExpiringSoon()}">					

	@if(yearsLeft() === 0 && monthsLeft() === 0 && daysLeft() === 0 && hoursLeft() === 0 && minutesLeft() === 0 && secondsLeft() === 0){
		Bid is Over
	}@else {
		<span>Time Left: </span>
		@if(yearsLeft() > 0){
			<span>{{ yearsLeft() }}yr </span>
		}
		@if(monthsLeft() > 0){
			<span class="span">{{ monthsLeft() }}mth </span>
		}

		@if(daysLeft() > 0){
			<span>{{ daysLeft() }}d </span>
		}

		@if(hoursLeft() > 0){
			<span>{{ hoursLeft() }}h </span>
		}

		@if(minutesLeft() > 0){
			<span>{{ minutesLeft() }}m </span>
		}
			
		<span>{{ secondsLeft() }}s </span>
	}
	
		
		<!-- {{ yearsLeft() }} years | {{ monthsLeft() }} months | {{ daysLeft() }} days | {{ hoursLeft() }} hours | {{ minutesLeft() }} minutes | {{ secondsLeft() }} seconds -->
</div>