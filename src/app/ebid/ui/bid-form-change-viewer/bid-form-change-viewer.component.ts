import { CommonModule } from '@angular/common';
import { Component, effect, inject, input, OnChanges, SimpleChanges } from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { EBidService } from 'src/app/ebid/data-access/ebid.service';
import { EBid } from '../../interfaces/ebid';
import { HelperTools } from 'src/app/shared/utils/helper-tools';

@Component({
    selector: 'app-bid-form-change-viewer',
    imports: [CommonModule, FormsModule],
    templateUrl: './bid-form-change-viewer.component.html',
    styleUrl: './bid-form-change-viewer.component.css'
})
export class BidFormChangeViewerComponent implements OnChanges {
  ngOnChanges(changes: SimpleChanges): void {
    console.log("change");
    // if(changes.eBidFormGroup){
    //   console.log(changes.eBidFormGroup.currentValue);
    // }
  }
  ebidService = inject(EBidService); 
  changeData = input.required<any>({});
  eBidRaw: EBid | null = null;

  formGroupEffect = effect(() => {
    if (this.changeData()) {
      console.log(this.changeData());
    }
  });  
}


