<app-bid-form-view [bidFormSection]="bidFormSection()" [bidFormSectionInfo]="response()?.BidFormSectionInfo"></app-bid-form-view>

@if(response()?.Critical?.length > 0) {
	@for (item of response()?.Critical; track $index) {			
		<div class="alert alert-danger">			
			{{item}}
		</div>
	}
}


<div ngbAccordion>
	<div ngbAccordionItem>
		<h2 ngbAccordionHeader>
			<button ngbAccordionButton>Success ({{response()?.Success?.length || 0}})</button>
		</h2>
		<div ngbAccordionCollapse>
			<div ngbAccordionBody>				
				@for (item of response()?.Success; track $index) {						
					<div class="text-success">						
						{{item}}
					</div>
				}@empty {
					<i>
						No Success Messages
					</i>
				}
			</div>
		</div>
	</div>
	<div ngbAccordionItem>
		<h2 ngbAccordionHeader>
			<button ngbAccordionButton>Warnings ({{response()?.Warnings?.length || 0}})</button>
		</h2>
		<div ngbAccordionCollapse>
			<div ngbAccordionBody>
				@for (item of response()?.Warnings; track $index) {
					<div class="text-warning">						
						{{item}}
					</div>
				}@empty {
					<i>
						No Warning Messages
					</i>
				}
			</div>
		</div>
	</div>
	<div ngbAccordionItem>
		<h2 ngbAccordionHeader>
			<button ngbAccordionButton>Information ({{response()?.Infos?.length || 0}})</button>
		</h2>
		<div ngbAccordionCollapse>
			<div ngbAccordionBody>
				@for (item of response()?.Infos; track $index) {
					<div class="text-info">												
						{{item}}
					</div>
				}@empty {
					<i>
						No Info Messages
					</i>
				}
			</div>
		</div>
	</div>
	<div ngbAccordionItem>
		<h2 ngbAccordionHeader>
			<button ngbAccordionButton>Errors ({{response()?.Errors?.length || 0}})</button>
		</h2>
		<div ngbAccordionCollapse>
			<div ngbAccordionBody>
				@for (item of response()?.Errors; track $index) {
					<div class="text-danger mb-1">												
						{{item}}
					</div>
				}@empty {
					<i>
						No Error Messages
					</i>
				}
			</div>
		</div>
	</div>
</div>