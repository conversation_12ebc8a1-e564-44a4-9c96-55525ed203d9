import { Component, input, model } from '@angular/core';
import { BidderInfoImportResponse } from '../../data-access/bidform-service';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { BidFormSection } from '../../interfaces/bid-form-section';
import { BidFormViewComponent } from '../bid-form-view/bid-form-view.component';

@Component({
  selector: 'app-bidder-import-info',
  imports: [CommonModule, NgbAccordionModule, BidFormViewComponent],
  templateUrl: './bidder-import-info.component.html',
  styleUrl: './bidder-import-info.component.css'
})
export class BidderImportInfoComponent {
  bidFormSection = model<BidFormSection>( {} as BidFormSection);
  response = model<BidderInfoImportResponse>( {} as BidderInfoImportResponse);
}
