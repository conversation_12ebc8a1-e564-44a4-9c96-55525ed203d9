import { Component, effect, inject, Injector, input, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BidViews,  EBidSectionTypes } from 'src/app/ebid/interfaces/ebid';
import { BidFormComponent } from "../bid-form/bid-form.component";
import { CommonModule } from '@angular/common';
import { BidCompletionTimeComponent } from '../bid-completion-time/bid-completion-time.component';
import { BidWorkOrderComponent } from '../bid-work-order/bid-work-order.component';
import { BidAcknowledgeComponent } from '../bid-acknowledge/bid-acknowledge.component';
import { BidRequiredUploadsComponent } from '../bid-required-uploads/bid-required-uploads.component';
import { BidRequiredDownloadsComponent } from '../bid-required-downloads/bid-required-downloads.component';
import { BidAddendaAcknowledgeComponent } from '../bid-addenda-acknowledge/bid-addenda-acknowledge.component';
import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList } from '@angular/cdk/drag-drop';
import { EBidMoveCommand } from '../../interfaces/bid-form-section';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EBidFolderService } from '../../data-access/bid-folder.service';
import { EBidHistoryService } from '../../data-access/ebid-history.service';
import { BaseEffectsService } from 'src/app/shared/interfaces/abstract-effects-service';
import { bool } from 'aws-sdk/clients/signer';
@Component({
  selector: 'app-ebid-folder',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, BidFormComponent,
    BidCompletionTimeComponent, BidWorkOrderComponent, BidAcknowledgeComponent, BidRequiredUploadsComponent,
    BidRequiredDownloadsComponent, BidAddendaAcknowledgeComponent, CdkDrag, CdkDropList, CdkDragHandle],
  providers: [],
  templateUrl: './bid-folder.component.html',
  styleUrl: './bid-folder.component.css'
})
export class EBidFolder extends BaseEffectsService implements OnInit, OnDestroy {
  

  //INJECTS  

  eBidHistoryService = inject(EBidHistoryService);
  eBidFolderService = inject(EBidFolderService);

  //INPUTS
  ebidFormGroup = this.eBidFolderService.ebidFormGroup;
  eBid = this.eBidFolderService.eBid;
  view = this.eBidFolderService.view;
  projectId = this.eBidFolderService.projectId;
  bidderBidInfoData = this.eBidFolderService.bidderBidInfoData;
  isLoading = this.eBidFolderService.isLoading;
  showBidFormImport = input<boolean>(true);
  useDisabledOnBidFormItem = input<boolean>(false);
  allowFullBidFormDelete = input<boolean>(false);
  //SIGNALS
  sectionSetupComplete = signal<boolean>(false);

  modalService = inject(NgbModal);
  public readonly EBID_SECTIONS_TYPES: typeof EBidSectionTypes = EBidSectionTypes;
  public readonly BIDFORM_VIEWS: typeof BidViews = BidViews;


  modalBidFormSection: FormGroup | undefined = {} as FormGroup;

  effectsInjector = inject(Injector);
  constructor() {
    super();

  }
  ngOnInit(): void {
    this.eBidFolderService.startEffects();
  }


  drop(data: any[], event: CdkDragDrop<string[]>) {
    const moveBidItemCommand = new EBidMoveCommand(data, event.previousIndex, event.currentIndex);
    this.eBidHistoryService.executeCommand(moveBidItemCommand);
    this.ebidFormGroup.markAsUntouched();
    this.ebidFormGroup.markAsDirty();
  }

  ngOnDestroy(): void {
    this.stopEffects();
    this.eBidFolderService.stopEffects();
  }

  removeSection(sectionId: string) {
    this.eBidFolderService.removeSection(sectionId);
  }

}