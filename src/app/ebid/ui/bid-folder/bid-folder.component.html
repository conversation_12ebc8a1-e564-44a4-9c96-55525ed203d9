

@let sections = ebidFormGroup.get('sections')?.controls;

<div class="mb-2">	
	<div cdkDropList (cdkDropListDropped)="drop(sections, $event)">
		<form [formGroup]="ebidFormGroup">			
			@for (section of sections; track $index) {	
				@let sectionId = section.value.section?.SectionId;
				@let sectionType = section.value.section?.SectionType;
				<div class="my-4" cdkDrag  [cdkDragPreviewContainer]="'parent'">					
					<section class="border mb-3 p-3">						
						@if(sectionType === EBID_SECTIONS_TYPES.BID_FORM){
							<app-bid-form [bidFormSection]="section" [useDisabledOnBidFormItem]="useDisabledOnBidFormItem()" [allowFullBidFormDelete]="allowFullBidFormDelete()" [showBidFormImport]="showBidFormImport()">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle grab></i>									
								<button class="btn btn-outline-danger btn-sm" (click)="removeSection(sectionId)" remove>Remove</button> 															
							</app-bid-form>
						}@else if(sectionType === EBID_SECTIONS_TYPES.COMPLETION_TIME){
							<app-bid-completion-time [completionTimeSection]="section">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle  grab></i>	
								<button class="btn btn-outline-danger btn-sm" (click)="removeSection(sectionId)" remove>Remove</button> 
							</app-bid-completion-time>
						}@else if(sectionType === EBID_SECTIONS_TYPES.WORK_ORDER){
							<app-bid-work-order [workOrderSection]="section">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle  grab></i>	
								<button class="btn btn-outline-danger btn-sm" (click)="removeSection(sectionId)" remove>Remove</button> 
							</app-bid-work-order>
						}@else if(sectionType === EBID_SECTIONS_TYPES.ACKNOWLEDGE){
							<app-bid-acknowledge [acknowledgeSection]="section" [isLoading]="isLoading()">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle  grab></i>	
								<button class="btn btn-outline-danger btn-sm" (click)="removeSection(sectionId)" remove>Remove</button> 
							</app-bid-acknowledge>
						}@else if(sectionType === EBID_SECTIONS_TYPES.REQUIRED_UPLOADS){
							<app-bid-required-uploads [requiredUploadSection]="section">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle grab></i>	
								<button class="btn btn-outline-danger btn-sm" (click)="removeSection(sectionId)" remove>Remove</button> 
							</app-bid-required-uploads>
						}@else if(sectionType === EBID_SECTIONS_TYPES.REQUIRED_DOWNLOADS){
							<app-bid-required-downloads  [requiredDownloadsSection]="section">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle  grab></i>	
								<button class="btn btn-outline-danger btn-sm" (click)="removeSection(sectionId)" remove>Remove</button>							
							</app-bid-required-downloads>
						}@else if(sectionType === EBID_SECTIONS_TYPES.ADDENDA_ACKNOWLEDGE){
							<app-bid-addenda-acknowledge  [addendaAckSection]="section">
								<i class="fas fa-grip-vertical fa-lg text-secondary" cdkDragHandle  grab></i>	
							</app-bid-addenda-acknowledge>
						}
						@else {
							<p>Section Not Found {{sectionType}}</p>
						}		
					</section>
				</div>	
			}
		</form>
	</div>			
</div>		


