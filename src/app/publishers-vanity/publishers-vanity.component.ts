import { Component, inject } from '@angular/core';
import { PublisherProjectsComponent } from '../bid-opportunities/publisher/ui/publisherProjects/publisherProjects.component';
import { ActivatedRoute } from '@angular/router';
import { PublishersService } from '../account/shared/data-access/publishers.service';

@Component({
  selector: 'app-publishers-vanity',
  imports: [PublisherProjectsComponent],
  templateUrl: './publishers-vanity.component.html',
  styleUrl: './publishers-vanity.component.css'
})
export class PublishersVanityComponent {
  private aroute = inject(ActivatedRoute);
  private publishersService = inject(PublishersService);
  isLoading = this.publishersService.isLoading;
  publisherProfileName = this.publishersService.publisherName;
  vanityName = this.publishersService.publisherVanityName;

  constructor() {
    this.aroute.params.subscribe(params => {
      const vanityName = decodeURIComponent(params['vanityName']);
      this.publishersService.publisherVanityName.set(vanityName);
    });
  }
}
