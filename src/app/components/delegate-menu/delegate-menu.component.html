<div class="dropdown">
	<a class="btn btn-outline-dark text-dark dropdown-toggle" href="javascript:void(0)" role="button"
		data-bs-toggle="dropdown" aria-expanded="false">
		Switch Accounts
	</a>
	<ul class="dropdown-menu">
		@if(delegateInfoLoading()){
		<div class="p-1 placeholder-glow" style="height: 50px;">
			<span class="placeholder col-12" style="height: 100%;"></span>
		</div>
		}@else{
		<li>
			<h5 class="dropdown-header">Select An Account</h5>
		</li>
		<li style="cursor: pointer;" (click)="removeCurrentDelegate()">
			<a class="dropdown-item" [ngClass]="{'bg-primary text-dark': !delegateNavStore()?.currentDelegateUserId}">
				My Account
				<div class="small text-dark">{{currentUser?.FirstName}} {{currentUser?.LastName}}</div>
			</a>
		</li>
		@if(delegateNavStore()?.accountsAccess?.length > 0 ){
		@for (accountInfo of delegateNavStore()?.accountsAccess; track $index) {
		<li style="cursor: pointer;" (click)="accessDelegate(accountInfo)">
			<a class="dropdown-item"
				[ngClass]="{'bg-primary text-dark': delegateNavStore()?.currentDelegateUserId === accountInfo?.UserId}">
				{{accountInfo.CompanyName}}
				<div class="small text-dark">{{accountInfo.FirstName}} {{ accountInfo.LastName}}</div>
			</a>
		</li>
		}
		}
		}
	</ul>
</div>