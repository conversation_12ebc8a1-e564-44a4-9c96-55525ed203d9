import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DelegateService } from 'src/app/account/user/shared/data-access/delegate.service';
import { UserDelegateStore } from 'src/app/account/user/shared/interfaces/delegate';
import { EmployeeService } from 'src/app/construction/shared/data-access/employee.service';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { DelegateNavStore, MyUserInfo, DelegateUserNavInfo } from '../main-header/main-header.component';

@Component({
    selector: 'app-delegate-menu',
    imports: [CommonModule],
    templateUrl: './delegate-menu.component.html',
    styleUrl: './delegate-menu.component.css'
})
export class DelegateMenuComponent implements OnInit {
  delegateService = inject(DelegateService);
  employeeService = inject(EmployeeService);
  accountService = inject(AccountService);
  authService = inject(AuthService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  delegateInfoLoading = computed(() => this.delegateService.delegateAccountInfoLoading() || this.delegateService.delegateInfoLoading());
  delegateNavStore = this.delegateService.delegateNavInfo;
  currentUser: MyUserInfo | null = null;
  removeDelegateUrl: string | null = null;  
  userDelegateStore = this.delegateService.userDelegateStore;

  constructor() {
    this.delegateService.initialize.set(true); 

    //TODO: this should be moved to auth service
    effect(() => {
      if (this.authService.identityToken()) {
        const idInfoSection = this.authService.identityToken()?.split('.')[1];
          var tInfo = JSON.parse(atob(idInfoSection as string));

          if (tInfo) {
            this.currentUser = new MyUserInfo(tInfo['FirstName'], tInfo['LastName']);
          }
      }
    });
  }
  
  ngOnInit(): void {
    
  }

  // loadDelegateInfo(store: UserDelegateStore) {
  //   this.delegateInfoLoading = true;
  //   var ids = new Array<string>();

  //   if (store.CurrentDelegation) {
  //     ids.push(store.CurrentDelegation)
  //   }

  //   for (let del of store.MyDelegations) {
  //     ids.push(del);
  //   }

  //   if (ids.length > 0) {
  //     this.accountService.GetAccounts(ids).subscribe({
  //       next: (accounts: CivCastAccount[]) => {
       
  //         if(!this.delegateNavStore){
  //           this.delegateNavStore = new DelegateNavStore();
  //         }
  //         this.delegateNavStore.currentDelegateUserId = store.CurrentDelegation;

  //         this.delegateNavStore.accountsAccess = new Array<DelegateUserNavInfo>();
  //         for (let account of accounts) {
  //           this.delegateNavStore.accountsAccess.push(
  //             new DelegateUserNavInfo(account.CognitoUserId, account.Profile?.FirstName, account.Profile?.LastName, account.Profile?.Company?.Name)
  //           );
  //         }
       

  //         this.delegateInfoLoading = false;
       
  //       },
  //       error: (err) => {
  //         this.delegateInfoLoading = false;
  //         console.log(err);
  //       },
  //       complete: () => {
  //         this.delegateInfoLoading = false;
  //       }
  //     });
  //   }else{
  //     this.delegateInfoLoading = false;
  //   }
  // }



  removeCurrentDelegate() {
    if(this.delegateNavStore()){
      // this.delegateNavStore.currentDelegateUserId = null;
      this.router.navigate(['users', 'delegation', 'change-delegation'], { queryParams: { did: "root" }, skipLocationChange: true, relativeTo: this.aRoute });
    }else{
      console.log("DelegateNavStore is null");
    }
  }

  async accessDelegate(account: DelegateUserNavInfo) {
    if(this.delegateNavStore()){
      // this.delegateNavStore.currentDelegateUserId = account.UserId;
      this.router.navigate(['users', 'delegation', 'change-delegation'], { queryParams: { did: account.UserId }, skipLocationChange: true, relativeTo: this.aRoute });
    }else{
      console.log("DelegateNavStore is null");
    }
  }
}
