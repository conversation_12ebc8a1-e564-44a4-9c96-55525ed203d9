import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
    selector: 'app-loading',
    templateUrl: './app-loading.component.html',
    styleUrls: ['./app-loading.component.css'],
    imports: [CommonModule]
})
export class AppLoadingComponent implements OnInit {

  @Input() isLoaded: boolean = false;
  
  constructor(    
    private aRoute: ActivatedRoute, 
    private authService: AuthService) { 


    this.authService.isLoggedIn.subscribe({
        next:(result) => {   
          if(result !== null){
            this.isLoaded = true; 
          }
        },
        error: (err) => {
          this.isLoaded = true;
        }  
    });
  }


  ngOnInit(): void {
  }

}
