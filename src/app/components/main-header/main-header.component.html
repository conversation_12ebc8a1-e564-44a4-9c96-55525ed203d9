<div class="p-3 bg-dark d-flex justify-content-between align-items-center">
    <!-- left side of header -->
    <div class="d-flex align-items-center">
        <!-- logo -->
        <a href="/" style="text-decoration: none;"><div class="montserrat fw-bold fs-3 text-white me-3">CIVCAST</div></a>
        
        <!-- mobile nav -->
        @if(!isLoading){
            <div class="dropdown d-md-none">
                <a class="text-white text-decoration-none dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    Hubs
                </a>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    @for (hub of hubs; track $index) {
                        @if(!hub.IsHidden){
                            <li>
                                <a class="dropdown-item" routerLink="{{hub.Route}}">
                                    {{hub.Name}}
                                </a>
                            </li>
                        }  
                    }            
                </ul>
            </div>
            <!-- desktop nav -->
            <div class="d-none d-md-flex align-items-center">	
                @for (hub of hubs; track $index) {
                    @if(!hub.IsHidden){
                        <a class="text-white text-decoration-none me-3" routerLink="{{hub.Route}}">
                            {{hub.Name}}
                        </a>   
                    }
                }                   
            </div>
        }@else {
            
        }

    </div>
    <!-- right side of header -->
    <div class="d-flex">
        @if(!user && !isLoading){
            <a routerLink="/signin" class="text-white me-3 text-decoration-none">Sign In</a>
        }

        @if(user){
            <div class="dropdown">
                <a class="text-white dropdown-toggle text-decoration-none" href="#" role="button" id="dropdownMenuLink"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user"></i>
                </a>
                
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink">
                    <div class="p-1 placeholder-glow" style="height: 50px;" *ngIf="delegateInfoLoading">
                        <span class="placeholder col-12" style="height: 100%;"></span>
                    </div>
                    @if(!delegateInfoLoading){
                        @if(delegateNavStore?.accountsAccess?.length > 0 ){
                            <li>
                                <h5 class="dropdown-header">Select An Account</h5>
                            </li>
                            <li style="cursor: pointer;" (click)="removeCurrentDelegate()">
                                <a class="dropdown-item" [ngClass]="{'bg-primary text-white': !delegateNavStore?.currentDelegateUserId}">
                                    My Account
                                    <div class="small">{{currentUser?.FirstName}} {{currentUser?.LastName}}</div>
                                </a>				
                            </li>
                            <li style="cursor: pointer;" *ngFor="let accountInfo of delegateNavStore?.accountsAccess" (click)="accessDelegate(accountInfo)">				
                                <a class="dropdown-item" [ngClass]="{'bg-primary text-white': delegateNavStore?.currentDelegateUserId === accountInfo?.UserId}">
                                    {{accountInfo.CompanyName}}
                                    <div class="small">{{accountInfo.FirstName}} {{ accountInfo.LastName}}</div>
                                </a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                        }
               
                        @if(!delegateNavStore?.currentDelegateUserId){
                            <li><a class="dropdown-item" href="#">Profile</a></li>
                            <li><a class="dropdown-item" routerLink="user/settings/account">Account Settings</a></li>
                            <li><a class="dropdown-item" routerLink="user/settings/dbe-certs">DBE Certifications</a></li>
                        }
                        <li><a class="dropdown-item" routerLink="user/settings/people">Users</a></li>
                        <li><a class="dropdown-item" routerLink="user/settings/access/roles">Roles</a></li>
                        <li><a class="dropdown-item" routerLink="user/settings/payment">Payment</a></li>
                        <li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
        
                        <li><a class="dropdown-item" routerLink="signout">Sign Out</a></li>     
                  
                    }
                    <ng-container *ngIf="">
                        <ng-container *ngIf="">
                       
                        </ng-container>                
                   
                      
                  
                    </ng-container>
    
                </ul>
            </div>
        }

    </div>
</div>