import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { HubService } from 'src/app/shared/data-access/hub-service';
import { UserDelegateStore } from 'src/app/shared/interfaces/delegate';
// import { DelegateService } from 'src/app/user/shared/data-access/delegate.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { AccountService } from 'src/app/shared/data-access/account-service';
// import { AccountsSettingsService } from 'src/app/user/shared/data-access/accounts-settings-service';
import { AccountSettings } from 'src/app/account/user/shared/interfaces/account-settings';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { HubInfo } from 'src/app/shared/interfaces/hub-info';
import { CommonModule } from '@angular/common';
import { DelegateService } from 'src/app/account/user/shared/data-access/delegate.service';
import { AccountsSettingsService } from 'src/app/account/user/shared/data-access/accounts-settings-service';

@Component({
    selector: 'app-main-header',
    templateUrl: './main-header.component.html',
    styleUrls: ['./main-header.component.css'],
    imports: [CommonModule, RouterLink]
})
export class MainHeaderComponent implements OnInit {

  user: any = null;
  isLoading: boolean = true;
  delegateInfoLoading: boolean = false;
  delegateNavStore: DelegateNavStore | null = null;
  currentUser: MyUserInfo | null = null;
  removeDelegateUrl: string | null = null;
  accountSettings: AccountSettings | null = null;
  hubs: Array<HubInfo> = new Array<HubInfo>();
  constructor(
    private authService: AuthService,
    private delegateService: DelegateService,
    private accountService: AccountService,
    private hubService: HubService,    
    private router: Router,
    private accountsSettingsService: AccountsSettingsService,
    private aRoute: ActivatedRoute) {
    this.hubService.hubUpdate.subscribe(result => {
      if(result){
        this.setupHub();
      }
      
    });

    this.hubs = new Array<HubInfo>();
    this.hubs = this.hubService.getHubs();
    this.authService.isLoggedIn.subscribe(
      {
        next: (result) => {
          if(result !== null){
            if(result){
              this.loadNavMenu();
              this.setupHub();
              this.user = result;
              this.isLoading = false;
              this.getCurrentUser();            
            }else{
              this.isLoading = false;
            }
          }
       
        },
        error: (err) => {
          this.isLoading = false;
        }
      }    
    )
  }



  ngOnInit(): void {    
    this.setupLinks();
  
  }

  setupHub() {
    this.accountsSettingsService.getSettings().subscribe(result => {      
      this.accountSettings = result;
      if(this.accountSettings){        
        for(let hub of this.hubs){
          hub.IsHidden = this.accountsSettingsService.isSettingHidden(hub.Id, this.accountSettings);
        }
      }
    });



  }

  // loadUser() {
  //   this.isLoading = true;
  //   this.authService.isUserLoggedIn().subscribe(
  //     {
  //       next: (user) => {
  //         this.user = user;
  //       },
  //       error: (err) => {
  //         this.user = null;
  //         console.log(err);
  //         this.isLoading = false;
  //       },
  //       complete: () => {
  //         this.isLoading = false;
  //       }
  //     });
  // }

  setupLinks() {
    this.removeDelegateUrl = `/user/delegation/change-delegation?did=root`
  }

  getDelegateUrl(account: DelegateUserNavInfo) {
    return `/user/delegation/change-delegation?did: ${account.UserId}`;
  }

  loadNavMenu() {
    if (!this.delegateNavStore) {
      this.loadDelegateStore();
    }
  }

  maxRecursion = 3;
  currentRecursion = 0;
  loadDelegateStore() {
    // this.delegateInfoLoading = true;
    // this.delegateService.GetUserDelegateStore().subscribe(
    //   {
    //     next: (store) => {
    //       if(!store){
    //         if(this.currentRecursion <= this.maxRecursion){
    //           setTimeout(() => {
    //             this.currentRecursion++;
    //             this.loadDelegateStore();
    //           }, 5000);
    //         }else{
    //           console.log("Recursion max met. Cannot find store info.");
    //           this.delegateInfoLoading = false;
    //         }
    //       }else{
    //         this.delegateNavStore = new DelegateNavStore();
    //         this.delegateNavStore.currentDelegateUserId = store.CurrentDelegation;
    //         this.loadDelegateInfo(store);
    //       }
    //     },
    //     error: (err) => {
    //       console.log(err);
    //       this.delegateInfoLoading = false;
    //     }
    //   }
    // );
  }

  loadDelegateInfo(store: UserDelegateStore) {
    this.delegateInfoLoading = true;
    var ids = new Array<string>();

    if (store.CurrentDelegation) {
      ids.push(store.CurrentDelegation)
    }

    for (let del of store.MyDelegations) {
      ids.push(del);
    }

    if (ids.length > 0) {
      this.accountService.GetAccounts(ids).subscribe({
        next: (accounts: CivCastAccount[]) => {
          if(this.delegateNavStore){
            this.delegateNavStore.accountsAccess = new Array<DelegateUserNavInfo>();
            for (let account of accounts) {
              this.delegateNavStore.accountsAccess.push(
                new DelegateUserNavInfo(account.CognitoUserId, account.Profile?.FirstName, account.Profile?.LastName, account.Profile?.Company?.Name)
              );
            }
          }else{
            console.log("DelegateNavStore is null");
          }
       
        },
        error: (err) => {
          console.log(err);
        },
        complete: () => {
          this.delegateInfoLoading = false;
        }
      });
    } else {
      this.delegateInfoLoading = false;
    }
  }

  getCurrentUser() {
    this.authService.getIdentityToken().subscribe({
      next: (token) => {
        if(token){
          var tInfo = JSON.parse(atob(token.split('.')[1]));

          if (tInfo) {
            this.currentUser = new MyUserInfo(tInfo['FirstName'], tInfo['LastName']);
          }
        }else{
          console.log("token is null");
        }
      }
    })
  }

  removeCurrentDelegate() {
    if(this.delegateNavStore){
      this.delegateNavStore.currentDelegateUserId = null;
      this.router.navigate(['user', 'delegation', 'change-delegation'], { queryParams: { did: "root" }, skipLocationChange: true, relativeTo: this.aRoute.parent });
    }else{
      console.log("DelegateNavStore is null");
    }

  }

  async accessDelegate(account: DelegateUserNavInfo) {
    if(this.delegateNavStore){
      this.delegateNavStore.currentDelegateUserId = account.UserId;
      this.router.navigate(['user', 'delegation', 'change-delegation'], { queryParams: { did: account.UserId }, skipLocationChange: true, relativeTo: this.aRoute.parent });
    }else{
      console.log("DelegateNavStore is null");
    }

  }

}

export class DelegateUserNavInfo {

  constructor(userId: string, firstName: string, lastName: string, companyName: string) {
    this.FirstName = firstName;
    this.LastName = lastName;
    this.UserId = userId;
    this.CompanyName = companyName;
  }

  UserId: string;
  FirstName: string;
  LastName: string;
  CompanyName: string;
}

export class DelegateNavStore {
  currentDelegateUserId: string | null = null;
  accountsAccess: Array<DelegateUserNavInfo> = new Array<DelegateUserNavInfo>();

}

export class MyUserInfo {

  constructor(firstName: string, lastName: string) {
    this.FirstName = firstName;
    this.LastName = lastName;
  }
  FirstName: string;
  LastName: string;
}