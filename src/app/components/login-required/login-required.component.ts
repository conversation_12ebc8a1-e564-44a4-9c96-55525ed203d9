import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
    selector: 'app-login-required',
    imports: [CommonModule],
    templateUrl: './login-required.component.html',
    styleUrl: './login-required.component.css'
})
export class LoginRequiredComponent {
  authService = inject(AuthService);
  signUp() {
    this.authService.signUp();
  }

  signIn() {
    this.authService.logIn();
  }
}
