import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { HomeAdvertisementComponent } from '../home-advertisement/home-advertisement.component';
import { AppLoadingComponent } from '../app-loading/app-loading.component';

@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.css'],
    imports: [CommonModule, HomeAdvertisementComponent, AppLoadingComponent]
})
export class HomeComponent implements OnInit {
  isLoaded: boolean = false;
  isLoggedIn: boolean = false;
  message: string = '';
  constructor(
    private authService: AuthService) {

    this.authService.isLoggedIn.subscribe({
      next: (result) => {
        if(result !== null){
          console.log("Login Request on Page");    
          this.isLoggedIn = result;
          this.isLoaded = true;      
        }
      }, error: (err) => {
        this.isLoaded = true;
      }
    });
  }

  ngOnInit() { }
}
