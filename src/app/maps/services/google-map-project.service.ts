import { effect, inject, Injectable, signal } from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";
import { <PERSON><PERSON><PERSON>tizer, SafeResourceUrl } from "@angular/platform-browser";

@Injectable()
export class GoogleMapProjectService {
  sanitizer = inject(DomSanitizer);
  longitude = signal<number | null>(null);
  latitude = signal<number | null>(null);
  url = signal<string | null>(null);
  errorText = signal<string | null>(null);
  apiKey = signal<string | null>(null);
  touched = signal<boolean>(false);
  isDirty = signal<boolean>(false);

  constructor() {


  }


  geocodeAddress(address: string): void {
    const geocoder = new google.maps.Geocoder();


    if (!address) {
      this.errorText.set('Please enter an address to geocode.');
      return;
    }

    geocoder.geocode({ address: address }, (results: any, status: string) => {
      if (results && results.length > 0) {
        const location = results[0];
        console.log('Geocoding results:', location);
        const latLng = location.geometry.location;

        this.latitude.set(latLng.lat());
        this.longitude.set(latLng.lng());
        this.errorText.set(null);
        this.touched.set(true);
        this.isDirty.set(true);
      } else {
        this.errorText.set('Need more details to find the location. Please be more specific.');
      }
    });


  }

  GetMyMapsSafeUrl(url: string): SafeResourceUrl | null {
    // Validate that the URL contains 'www.google.com/maps'
    if (!url.includes('www.google.com/maps')) {
      console.error('Invalid URL: Does not contain "www.google.com/maps"');
      this.errorText.set('Invalid URL. The URL must contain "www.google.com/maps".');
      return null;
    }

    // Check if the URL is an <iframe> string
    if (url.includes('<iframe')) {
      const div = document.createElement('div');
      div.innerHTML = url;
      const iframe = div.querySelector('iframe');
      if (iframe && iframe.src.includes('www.google.com/maps')) {
        console.log('Extracted embed URL from iframe:', iframe.src);
        return this.sanitizeUrl(iframe.src);
      } else {
        console.error('No valid embed URL found in the iframe.');
        this.errorText.set('No valid embed URL found in the iframe.');
        return null;
      }
    }

    // Check if the URL is a direct Google Maps link
    if (url.startsWith('https://www.google.com/maps/')) {
      console.log('Detected standard Google Maps URL:', url);
      return this.sanitizeUrl(url);
    }

    this.touched.set(true);
    this.isDirty.set(true);

    return null;
  }
  private sanitizeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  convertToEmbedUrl(googleMapsUrl: string, apiKey: string) {
    try {
      // Case 0: Handle full <iframe> HTML string
      if (googleMapsUrl.includes('<iframe')) {
        const div = document.createElement('div');
        div.innerHTML = googleMapsUrl;
        const iframe = div.querySelector('iframe');
        if (iframe && iframe.src.includes('https://www.google.com/maps/embed')) {
          return iframe.src;
        }
      }

      if (googleMapsUrl.startsWith('https://www.google.com/maps/embed?pb=')) {
        console.warn("Embed URL with 'pb=' does not support API keys; returning as-is.");
        this.errorText.set('Embed URL with "pb=" does not support API keys; returning as-is.');
        return googleMapsUrl;
      }
      // Attempt to treat input as a URL
      const url = new URL(googleMapsUrl);

      // Case 1: Directions (e.g., /dir/Start/End)
      if (url.pathname.startsWith('/maps/dir/')) {
        const pathParts = url.pathname.split('/');
        const origin = decodeURIComponent(pathParts[3] || '');
        const destination = decodeURIComponent(pathParts[4] || '');
        return `https://www.google.com/maps/embed/v1/directions?key=${apiKey}&origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}`;
      }

      // Case 2: Place (e.g., /place/Some+Location)
      const placeMatch = url.pathname.match(/\/place\/([^/]+)/);
      if (placeMatch) {
        const place = decodeURIComponent(placeMatch[1]);
        return `https://www.google.com/maps/embed/v1/place?key=${apiKey}&q=${encodeURIComponent(place)}`;
      }

      // Case 3: Coordinates via @lat,lng
      const atCoords = url.pathname.match(/@([-0-9.]+),([-0-9.]+)/);
      if (atCoords) {
        const lat = atCoords[1];
        const lng = atCoords[2];
        return `https://www.google.com/maps/embed/v1/view?key=${apiKey}&center=${lat},${lng}&zoom=17&maptype=roadmap`;
      }

      // Case 4: Query param (e.g., ?query=lat,lng or place)
      if (url.searchParams.has("query")) {
        const query = url.searchParams.get("query");
        if (/^-?\d+\.\d+,-?\d+\.\d+$/.test(query as string)) {
          return `https://www.google.com/maps/embed/v1/view?key=${apiKey}&center=${query}&zoom=17&maptype=roadmap`;
        } else {
          return `https://www.google.com/maps/embed/v1/place?key=${apiKey}&q=${encodeURIComponent(query as string)}`;
        }
      }

      this.touched.set(true);
      this.isDirty.set(true);

      // Fallback
      return `https://www.google.com/maps/embed/v1/view?key=${apiKey}&center=0,0&zoom=1`;
    } catch (err) {
      console.error("Invalid input for Google Maps conversion:", err);
      this.errorText.set('Invalid input for Google Maps conversion. Please check the URL and try again.');
      return '';
    }
  }
}