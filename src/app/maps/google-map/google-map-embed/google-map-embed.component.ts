import { Component, effect, inject, input, signal } from '@angular/core';
import { SafeUrl, DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { GoogleMapProjectService } from '../../services/google-map-project.service';

@Component({
  selector: 'app-google-map-embed',
  imports: [],
  templateUrl: './google-map-embed.component.html',
  styleUrl: './google-map-embed.component.css'
})
export class GoogleMapEmbedComponent {
  googleMapProjectService = inject(GoogleMapProjectService);
  url = input.required<string>();
  apiKey = input<string>();
  sanitizer = inject(DomSanitizer);
  iframeSafe = signal<SafeResourceUrl | null>(null);
  errorText = signal<string | null>(null);


  constructor() {
    effect(() => {
      try {
        const src = this.GetMyMapsSafeUrl(this.url());

        if (src) {
          this.errorText.set(null);
          this.iframeSafe.set(src);
        } else {
          this.errorText.set('Invalid URL. Please check and try again.');
        }

      } catch (err) {
        this.errorText.set('There was an error.');
        console.error("Error processing Google Maps URL:", err);
      }
    })
  }

  private GetMyMapsSafeUrl(url: string): SafeResourceUrl | null {
    return this.googleMapProjectService.GetMyMapsSafeUrl(url);
  }  

  convertToEmbedUrl(googleMapsUrl: string, apiKey: string) {
    return this.googleMapProjectService.GetMyMapsSafeUrl(googleMapsUrl);
  }
}