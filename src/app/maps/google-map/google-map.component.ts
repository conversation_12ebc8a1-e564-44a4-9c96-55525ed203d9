import { CommonModule } from '@angular/common';
import { Component, effect, ElementRef, inject, input, output, signal, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { GoogleMapProjectService } from '../services/google-map-project.service';
import { GoogleMapEmbedComponent } from './google-map-embed/google-map-embed.component';
declare const google: any;

@Component({
  selector: 'app-google-map',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, GoogleMapsModule, GoogleMapEmbedComponent],
  templateUrl: './google-map.component.html',
  styleUrl: './google-map.component.css'
})
export class GoogleMapComponent {
  // Signals for state
  @ViewChild('googlemap', { static: true }) mapContainer!: ElementRef;
  gooleMapProjectService = inject(GoogleMapProjectService);
  sanitizer = inject(DomSanitizer);
  defaultCenter = input<{ lat: number; lng: number }>({ lat: 0, lng: 0 });
  address = signal<string | null>(null);  
  latitude = this.gooleMapProjectService.latitude;
  longitude = this.gooleMapProjectService.longitude;
  url = this.gooleMapProjectService.url;
  zoom = signal(15);
  center = signal<{ lat: number; lng: number }>({ lat: 0, lng: 0 });  
  results = signal<any[]>([]);
  apiKeyInput = input.required<string>();
  apiKey = this.gooleMapProjectService.apiKey;
  mode = signal<string>('find');
  addressForm = new FormGroup({
    address: new FormControl(``, Validators.required)
  });

  rawUrlForm = new FormGroup({
    rawUrl: new FormControl<string | null>(null)    
  });
  
  customMapUrl = this.gooleMapProjectService.url;
  errorText = signal<string | null>(null);
  selectedMarkerOptions: google.maps.MarkerOptions = {
    clickable: true
  };
  options: google.maps.MapOptions = { clickableIcons: false, gestureHandling: 'auto', streetViewControl: false, fullscreenControl: false, zoom: 15 };
  markerPositions = signal<google.maps.LatLngLiteral[]>([]);
  constructor() {
    effect(() => {
      if(this.latitude() && this.longitude()) {
        this.center.set({
          lat: this.latitude() as number,
          lng: this.longitude() as number
        });

        this.markerPositions.update((positions) => {
          positions = [];
          positions.push({ lat: this.latitude() as number, lng: this.longitude() as number });
          return [...positions];
        });
      }
    });

    effect(() => {
      this.apiKey.set(this.apiKeyInput());
    })
  }

  changeMode(mode: string): void {
    this.mode.set(mode);
  }

  setUrl() {
    const url = this.rawUrlForm.get('rawUrl')?.value;
    if (url) {
      this.gooleMapProjectService.url.set(url);
      this.gooleMapProjectService.errorText.set(null);
    } else {
      this.gooleMapProjectService.errorText.set('Please enter a valid URL.');
    }
  }

  // Geocode function using the address signal
  geocodeAddress(): void {
    this.gooleMapProjectService.geocodeAddress(this.addressForm.get('address')?.value as string);
  }

  addMarker(event: any) {
    this.latitude.set(event.latLng.lat());
    this.longitude.set(event.latLng.lng());

    this.address.set(null);
    this.addressForm.get('address')?.setValue('');
  }

  clearMarkers() {
    this.latitude.set(null);
    this.longitude.set(null);
    this.markerPositions.set([]);
    this.errorText.set(null);
    this.gooleMapProjectService.isDirty.set(true);
    this.gooleMapProjectService.touched.set(true);
  }

  clearUrl() {
    this.rawUrlForm.get('rawUrl')?.setValue(null);
    this.url.set(null);
    this.errorText.set(null);
    this.gooleMapProjectService.isDirty.set(true);
    this.gooleMapProjectService.touched.set(true);
  }

}
