<div class="mb-3">

	<div class="btn-group">
		<button class="btn btn-outline-secondary" [ngClass]="{'active': mode() === 'find'}" (click)="changeMode('find')">Find Address</button>
		<button class="btn btn-outline-secondary" [ngClass]="{'active': mode() === 'add'}" (click)="changeMode('add')">Add Google My Maps Url</button>
	</div>

	@if(mode() === 'find'){
		<div>
			Type location information to find the location on the map. To find the exact location enter as much details or the full address.			
		</div>
		<div>
			ex: <i>hee haws Tomball TX</i> or <i>Houston Tx</i> or just <i>Texas</i>.							
		</div>
		<div>
			You can also click the map to set the location marker.
		</div>

		<form [formGroup]="addressForm" (submit)="geocodeAddress()">
			<div class="btn-group my-2" style="width: 100%">
				<input type="text"  class="form-control" formControlName="address"
					placeholder="Start typing an address..." />
				<button type="submit" class="btn btn-outline-secondary" [disabled]="!addressForm.valid">Find Address</button>
				<button type="button" class="btn btn-outline-secondary" (click)="clearMarkers()" [disabled]="markerPositions()?.length <= 0">Clear Marker</button>
			</div>
		</form>


		<google-map #googlemap [options]="options" [center]="center()" height="400px" width="100%" (mapClick)="addMarker($event)">
			@for (markerPosition of markerPositions(); track $index) {
				<map-marker [position]="markerPosition" [options]="markerOptions" [options]="selectedMarkerOptions"></map-marker>
			}
		</google-map>
	}@else {

		@let helpSection = false;

		<p class="mt-4">
			Add a google my maps url.  To find out more about Google My Maps, visit the
			<a href="https://www.google.com/maps/about/mymaps/" target="_blank">Google My Maps</a> page.	
			<button class="btn btn-sm btn-link" (click)="helpsSection = !helpsSection" [attr.aria-expanded]="helpsSection">
				{{ helpsSection ? 'Hide' : 'Show' }} Help Section
			</button>
		</p>
	
		@if(helpsSection) {
			<div class="row">
				<div class="col-6">
					<img class="img-fluid" src="assets/images/google_my_maps_embed_view.png" alt="Google My Maps Embed Link" />
				</div>
				<div class="col-6">
					<img class="img-fluid" src="assets/images/google_my_maps_embed.png" alt="Google My Maps Embed Link" />
				</div>
			
			</div>
		}

		<form [formGroup]="rawUrlForm" (submit)="setUrl()">
			<div class="btn-group my-2" style="width: 100%" >
				<input type="text" class="form-control" formControlName="rawUrl"
					placeholder="Insert Google My Maps Url..." />
				<button type="submit" class="btn btn-outline-secondary">Set Url</button>	
				<button type="button" class="btn btn-outline-secondary" (click)="clearUrl()" [disabled]="!url()">Clear Marker</button>			
			</div>
			<i>{{ customMapUrl() }}</i>
		</form>

		@if(customMapUrl()) {
			<app-google-map-embed [url]="customMapUrl()" [apiKey]="apiKey()"></app-google-map-embed>		
		}	
	}	

	@if(errorText()) {
		<div class="alert alert-danger">
			{{ errorText()}}
		</div>
	}
</div>




