import { ChangeDetectorRef, Component, Input, OnInit, TemplateRef } from '@angular/core';
import { cmLoadingAnimationTypes, CMLoadingConfig, ICMLoadingConfig } from '../cm-loader-config';
import { CMLoaderService } from '../cm-loader-service';

@Component({
    selector: 'cm-loading',
    templateUrl: './cm-loading.component.html',
    styleUrls: ['./cm-loading.component.css', './cm-loading-animation-styles.css'],
    standalone: false
})
export class CMLoadingComponent implements OnInit {
  @Input() show: boolean = false;
  @Input() config: ICMLoadingConfig = new CMLoadingConfig();
  @Input() template: TemplateRef<any> | null = null;
  private defaultConfig: ICMLoadingConfig = {
    animationType: cmLoadingAnimationTypes.threeBounce,
    backdropBackgroundColour: 'rgba(0, 0, 0, 0.3)',
    backdropBorderRadius: '0px',
    fullScreenBackdrop: false,
    primaryColour: '#ffffff',
    secondaryColour: '#ffffff',
    tertiaryColour: '#ffffff'
  };
  public ngxLoadingAnimationTypes = cmLoadingAnimationTypes;

  constructor(private LoadingService: CMLoaderService, private changeDetectorRef: ChangeDetectorRef) { }

  ngOnInit() {
    this.setupConfig();
  }

  private setupConfig(): void {
    for (const option in this.defaultConfig) {
      if (typeof this.config[option] === 'boolean') {
        if (this.config[option] != null) {
          continue;
        }

        this.config[option] = this.LoadingService.loadingConfig[option] != null ? this.LoadingService.loadingConfig[option] : this.defaultConfig[option];
      } else {
        if (this.config[option] != null) {
          continue;
        }

        this.config[option] = this.LoadingService.loadingConfig[option] != null ? this.LoadingService.loadingConfig[option] : this.defaultConfig[option];
      }
    }
  }

  public setShow(show: boolean): void {
    this.show = show;
    this.changeDetectorRef.markForCheck();
  }

}
