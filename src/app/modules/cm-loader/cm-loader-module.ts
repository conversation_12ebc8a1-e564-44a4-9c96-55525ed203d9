import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { ICMLoadingConfig } from './cm-loader-config';
import { CMLoadingComponent } from './cm-loading/cm-loading.component';

@NgModule({
  imports: [CommonModule],
  declarations: [CMLoadingComponent],
  exports: [CMLoadingComponent]
})
export class CMLoaderModule {
  static forRoot(loadingConfig: ICMLoadingConfig): ModuleWithProviders<CMLoaderModule> {
    return {
      ngModule: CMLoaderModule,
      providers: [{ provide: 'loadingConfig', useValue: loadingConfig }]
    };
  }
}