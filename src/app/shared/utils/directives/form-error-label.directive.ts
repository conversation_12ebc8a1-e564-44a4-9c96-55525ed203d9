import { Directive, ElementRef, Renderer2, effect, input, signal } from '@angular/core';
import { FormControl, ValidationErrors } from '@angular/forms';

@Directive({
  selector: '[appFormErrorLabel]',
  standalone: true
})
export class FormErrorLabelDirective {

  formControlInfo = input.required<FormControl>();
  errors = input.required<ValidationErrors>();
  label = input.required<string>();
  extraMessage = input<string>("");
  elementsAdded = new Array<any>();
  bindToNextParent = input<boolean>(false);

  errors2 = signal<ValidationErrors | null>(null);


  errorEffect = effect(() => {      
    this.clearChildElements(); 
    this.removeRedWrapper();   

      if(this.formControlInfo() && this.errors()){
        if(this.formControlInfo() != null && this.formControlInfo()?.touched || this.formControlInfo()?.dirty){
          var errors = this.formControlInfo()?.errors as ValidationErrors;
          Object.keys(errors).forEach(keyError => {
            this.addErrorLabel(keyError, errors[keyError]);        
          });

          if(this.el.nativeElement.validationMessage){
            this.addErrorLabel('custom', `${this.el.nativeElement.validationMessage}`);
            
          }

          if(this.extraMessage()){
            this.addErrorLabel('custom', `${this.extraMessage()}`);
          }

        }
      }
    
    
 
  })
  constructor(private el: ElementRef, private renderer: Renderer2) { }  

  addErrorLabel(error: string, errorData: any) {
    // Create a new element
    const newElement = this.renderer.createElement('div');
    newElement.style.color = 'red';


    let errorLabel = "error";

    if(error === 'required'){
        errorLabel = `${this.label()} is required`;
      
    }else if(error === 'minlength'){
      errorLabel = `${this.label()} is not long enough. ${errorData.actualLength}/${errorData.requiredLength}`;
    }else if(error === 'maxlength'){
      errorLabel = `${this.label()} is too long. ${errorData.actualLength}/${errorData.requiredLength}`;
    }else if(error === 'pattern'){
      errorLabel = `${this.label()} is not a properly formatted.`;
    }else if(error === 'email'){
      errorLabel = `Not a valid email.`;
    }else if(error === 'custom'){
      errorLabel = errorData;
    }

    // Set the innerHTML of the new element
    this.renderer.setProperty(newElement, 'innerHTML', errorLabel);

    this.addRedWrapper();   

   // Append the new element to the parent of the host element
   const parent = this.bindToNextParent() ? this.el.nativeElement.parentNode.parentNode : this.el.nativeElement.parentNode;
   this.renderer.appendChild(parent, newElement);
   this.elementsAdded.push(newElement);
  }

  addRedWrapper(){
    this.renderer.setStyle(this.el.nativeElement, 'border-color', '#c00000');
    this.renderer.setStyle(this.el.nativeElement, 'border-width', '3px');
    this.renderer.setStyle(this.el.nativeElement, 'background-color', '#ffdddd');
  }

  removeRedWrapper() {     
    this.renderer.removeStyle(this.el.nativeElement, 'border-color');
    this.renderer.removeStyle(this.el.nativeElement, 'border-width');
    this.renderer.removeStyle(this.el.nativeElement, 'background-color');
   }

  clearChildElements() {
    for (let child of this.elementsAdded) {
      this.renderer.removeChild(this.bindToNextParent() ? this.el.nativeElement.parentNode.parentNode : this.el.nativeElement.parentNode, child);
    }

    this.elementsAdded = [];
  }

}
