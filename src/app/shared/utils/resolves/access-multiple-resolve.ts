import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { AccessResponse } from '../../../models/access/access-response';
import { AccessService } from '../../data-access/access-service';

export const AccessMultipleResolve: ResolveFn<AccessResponse> = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
  accessControlService: AccessService = inject(AccessService)
): Observable<AccessResponse> => {
  return new Observable<AccessResponse>(obs => {      
    var action = route.data["action"];
    var resource = route.data["resource"];
    accessControlService.CheckAccess(resource, action).subscribe(result => {
      obs.next(result);
      obs.complete();
    });
  });
}