// filepath: f:\Source\Repos\civcast-app\src\app\shared\resolvers\publishername.resolver.ts
import { Injectable } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class PublisherNameResolver implements Resolve<string> {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): string {
    const publisherName = route.params['publishername'];
    if (!publisherName) {
      return '';
    }

    // Re-encode or rewrite the publishername parameter
    return decodeURIComponent(encodeURIComponent(publisherName.trim()));
  }
}