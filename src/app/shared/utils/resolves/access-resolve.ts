import { inject } from '@angular/core';
import { ResolveFn, ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { AccessRequest } from '../../../models/access/access-request';
import { AccessResponse } from '../../../models/access/access-response';
import { AccessService } from '../../data-access/access-service';

export const AccessResolve: ResolveFn<AccessResponse> = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
  accessControlService: AccessService = inject(AccessService)
): Observable<AccessResponse> => {
  return new Observable<AccessResponse>(obs => {      
          if(route.data['actionRequest']){
              var request = route.data['actionRequest'] as AccessRequest;      
              accessControlService.CheckAccessByRequest(request).subscribe(result => {
                obs.next(result);
                obs.complete();
              });
          }else{
              obs.next(undefined);
              obs.complete();
          }  
      });
}
