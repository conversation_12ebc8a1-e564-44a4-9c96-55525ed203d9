import { DefaultUrlSerializer, UrlSerializer, UrlTree } from '@angular/router';

export class CustomUrlSerializer extends DefaultUrlSerializer {
  override parse(url: string): UrlTree {
    // Preprocess the URL to encode special characters
    // const encodedUrl = url.replace(/\(/g, '%28').replace(/\)/g, '%29');
    // return super.parse(encodedUrl);

	const encodedUrl = url
	.replace(/\(/g, '%28')
	.replace(/\)/g, '%29')

  return super.parse(encodedUrl);
  }
}

// .replace(/\(/g, '%28')
// .replace(/\)/g, '%29')
// .replace(/ /g, '%20')
// .replace(/"/g, '%22')
// .replace(/#/g, '%23')
// .replace(/%/g, '%25')
// .replace(/&/g, '%26')
// .replace(/'/g, '%27')
// .replace(/\*/g, '%2A')
// .replace(/\+/g, '%2B')
// .replace(/,/g, '%2C')
// .replace(/\//g, '%2F')
// .replace(/:/g, '%3A')
// .replace(/;/g, '%3B')
// .replace(/</g, '%3C')
// .replace(/=/g, '%3D')
// .replace(/>/g, '%3E')
// .replace(/\?/g, '%3F')
// .replace(/@/g, '%40')
// .replace(/\[/g, '%5B')
// .replace(/\\/g, '%5C')
// .replace(/\]/g, '%5D')
// .replace(/\^/g, '%5E')
// .replace(/`/g, '%60')
// .replace(/{/g, '%7B')
// .replace(/\|/g, '%7C')
// .replace(/}/g, '%7D')
// .replace(/~/g, '%7E');