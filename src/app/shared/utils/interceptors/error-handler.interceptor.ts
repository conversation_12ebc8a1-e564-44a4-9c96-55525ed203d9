import { Injectable } from '@angular/core';
import { HttpRequest, <PERSON>ttpH<PERSON><PERSON>, HttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {

  constructor(private toastrService: ToastrService) { }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

    let handled: boolean = false;

    return next.handle(request).pipe(tap(() => [], (returnedError: any) => {
      if (returnedError instanceof HttpErrorResponse) {
        this.toastrService.toastrConfig.preventDuplicates = true;
        let errorMessage = null;
        let userMessage = null;
        if (returnedError.status >= 500 && returnedError.status <= 510 || returnedError.status === 400 ) {     
            errorMessage = `Error Status ${returnedError.status}: ${returnedError.error} - ${returnedError.error.message}`;
            this.toastrService.error(errorMessage);
        }
      }

      return next.handle(request);
    }));
  }

  private handleServerSideError(error: HttpErrorResponse): boolean {
    let handled: boolean = false;

    switch (error.status) {
      case 401:
        this.toastrService.warning("Authentication may be expired.");
        //this.authenticationService.localLogOut(`You are not authorized on an endpoint. Please login again. ${error.status} - ${error.message}`);
        handled = true;
        break;
      case 403:
        this.toastrService.warning("Authentication may be expired.");
        //this.authenticationService.localLogOut(`You are not authorized on an endpoint. Please login again. ${error.status} - ${error.message}`);
        handled = true;
        break;
    }

    return handled;
  }
}