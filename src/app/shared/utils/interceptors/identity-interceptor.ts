import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable, mergeMap } from 'rxjs';
import { Injectable } from '@angular/core';
import { AuthService } from '../../data-access/auth.service';
@Injectable()
export class IdentityInterceptor implements HttpInterceptor {
  constructor(private auth: AuthService) { }

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    return this.auth.getIdentityToken().pipe(mergeMap((result: string | null) => {
      if (result) {
        const authReq = req.clone({ headers: req.headers.append('x-identity-token', result) });
        return next.handle(authReq);
      }
      return next.handle(req);
    })
    )
  };

}