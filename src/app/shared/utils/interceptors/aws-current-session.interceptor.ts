import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest } from '@angular/common/http';
import { Observable, from } from 'rxjs';
import { catchError, mergeMap, switchMap } from 'rxjs/operators';
import { fetchAuthSession } from 'aws-amplify/auth';

/* As the AWS Amplify SDK does not notify when the current access token expires
 * we have to check the current token on every HTTP call.
 * Amplify Auth.currentSession() checks the access token and refreshes it if required.
 */
@Injectable()
export class AWSCurrentSessionInterceptor implements HttpInterceptor {
  constructor() {}

  //I Had to change this to make sure returns happen or the request is sent twice if an error occurs.
  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return from(fetchAuthSession()).pipe(
      mergeMap((result:any) => {
      return next.handle(request);
    }),
      catchError((err, caught)=> {
        return next.handle(request);
      })
    );
  }
}