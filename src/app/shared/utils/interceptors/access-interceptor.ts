import { HttpInter<PERSON>, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { tap } from 'rxjs/operators';
@Injectable()
export class AccessInterceptor implements HttpInterceptor {
  constructor(private toastrService: ToastrService) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {    
    return next.handle(req).pipe(tap(() => [], (error: any) => {
        if(error instanceof HttpErrorResponse){         
            if(error.status === 442){                        
                if(error.error){
                    if(error.error.ErrorMessage && error.error.Description){
                        this.toastrService.error(`${error.error.ErrorMessage}. You are missing a required policy to access certain features on this page. Some features may be inaccessible.`,`${error.error.Description}` );
                    }else{
                        this.toastrService.error("You are missing a required policy to access certain features on this page. Some features may be inaccessible.", "Missing Permission");
                    }
                    
                    
                }else{
                    this.toastrService.error("You are missing a required policy to access certain features on this page. Some features may be inaccessible.", "Missing Permission");
                    console.log("Policy access error, check error logs for more detail");
                }                    
            }                  
        }       

        return next.handle(req);
    }));    
  }
}