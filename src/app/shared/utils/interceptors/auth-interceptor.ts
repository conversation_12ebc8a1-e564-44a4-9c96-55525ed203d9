import { HttpInterceptor, HttpRequest, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpEvent } from '@angular/common/http';
import { AuthService } from '../../data-access/auth.service';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable, mergeMap } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private auth: AuthService) {}
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return this.auth.getIdentityToken().pipe(mergeMap((result: string | null) => {
      if (result) {
        var xAuthHeader = req.headers.get("x-auth");
        if(xAuthHeader){
          if(xAuthHeader === "false"){
            return next.handle(req);
          }
        }
        
        if(!req.url.startsWith(environment.global_policy_url) && !req.url.startsWith(environment.Defaults.Roles_Url)){
          const authReq = req.clone({ headers: req.headers.set('Authorization', 'Bearer ' + result) });
          return next.handle(authReq);
        }
      }  

      return next.handle(req);
    })
    )
  };
}