import { <PERSON>ttpInter<PERSON>, HttpRequest, <PERSON>ttpH<PERSON><PERSON>, HttpEvent } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { DelegateLocalStorageService } from '../../data-access/delegate-localstorage-service';

@Injectable()
export class DelegateInterceptor implements HttpInterceptor {
  constructor(private delegateService: DelegateLocalStorageService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    
    if(request.url !== "/delegation/user/delegated"){
      var delegateID = this.delegateService.GetEncryptedDelegatedId();
      
      if(delegateID !== null){                  
          let drequest = request.clone({
            headers: request.headers.set('x-delegated-token', delegateID)
          });

          return next.handle(drequest);

      }else{            
          return next.handle(request);
      }
    
    }else{
      return next.handle(request);
    }
  }
}
