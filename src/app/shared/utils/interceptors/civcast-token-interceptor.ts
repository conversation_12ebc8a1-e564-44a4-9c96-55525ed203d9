import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { Injectable, inject } from '@angular/core';
import { catchError, delay, mergeMap, switchMap } from 'rxjs/operators';
import { CivCastTokenService } from '../../interfaces/civcast-token/civcast-token-service';
import { AuthService } from '../../data-access/auth.service';

@Injectable()
export class CivCastTokenInterceptor implements HttpInterceptor {
	authService = inject(AuthService);
	constructor(private civcastTokenService: CivCastTokenService) { }

	intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

		if(req.url.endsWith('civcast/token')) {
			return next.handle(req);
		}

		return this.authService.getAccessToken().pipe(
			switchMap(token => {
				if (token) {
					return this.civcastTokenService.getTokenAlt().pipe(
						switchMap(result => {
							if (result) {
								const authReq = req.clone({ headers: req.headers.append('x-civcast-token', result.split('.')[0]) });
								return next.handle(authReq);
							}
			
							return next.handle(req);
						}),
						catchError((err) => {
							return next.handle(req);
						}));
				}

				return next.handle(req);
			}),
			catchError((err) => {
				return next.handle(req);
			}));
	}
}