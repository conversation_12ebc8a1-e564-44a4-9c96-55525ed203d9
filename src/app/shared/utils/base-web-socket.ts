import { inject, Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { AuthService } from '../data-access/auth.service';

export class BaseWebSocketService {
	authService = inject(AuthService);
	private subject: Subject<MessageEvent> | null = null;
	private ws: WebSocket = {} as WebSocket;
  
	constructor() {}

	destroy(){
		this.subject?.unsubscribe();
		this.disconnect();		
		this.ws = {} as WebSocket;
	}
  
	public connect(url: string): Subject<MessageEvent> {
	  if (!this.subject) {

		this.subject = this.create(url);
	  }
	  return this.subject;
	}
  
	private create(url: string): Subject<MessageEvent> {
	  this.ws = new WebSocket(url);	  
  
	  const observable = new Observable(observer => {
		this.ws.onmessage = observer.next.bind(observer);
		this.ws.onerror = observer.error.bind(observer);
		this.ws.onclose = observer.complete.bind(observer);
		return this.ws.close.bind(this.ws);
	  });
  
	  const observer = {
		next: (data: Object) => {
		  if (this.ws.readyState === WebSocket.OPEN) {
			this.ws.send(JSON.stringify(data));
		  }
		}
	  };
  
	  return Subject.create(observer, observable);
	}
  
	public sendDataMessage(action: string, data: any){
		if(this.ws.readyState === WebSocket.OPEN){	
			var messageInfo = {
				action: action,
				data: data
			};
			this.sendMessage(JSON.stringify(messageInfo));
		}
	}

	public sendMessage(message: any) {
	  if (this.ws.readyState === WebSocket.OPEN) {
		var messageInfo = {
			action: "sendmessage",
			data: message
		};

		this.ws.send(JSON.stringify(messageInfo));
	  } else {
		console.error('WebSocket is not open. Ready state is:', this.ws.readyState);
	  }
	}

	public disconnect() {
		if (this.ws) {
		  this.ws.close();		  
		  
		  this.subject = null;
		}
	  }
  }