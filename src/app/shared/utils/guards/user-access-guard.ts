import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from "@angular/router";
import { map, Observable } from "rxjs";
import { AccessEffects } from "../../interfaces/access";
import { AccessService } from "../../data-access/access-service";

@Injectable({
  providedIn: 'root'
})
export class UserAccessGuard {
  constructor(private accessService: AccessService, private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const resource = route.data['resource'] as string;
    const action = route.data['action'] as string;

    return this.accessService.CheckAccess(resource, action).pipe(
      map(result => {
        if (result.Access === AccessEffects.Allow) {
          return true;
        } else {
          this.router.navigate(['/unauthorized']);
          return false;
        }
      })
    );
  }
}
