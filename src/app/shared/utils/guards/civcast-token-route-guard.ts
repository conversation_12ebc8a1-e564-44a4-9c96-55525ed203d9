import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { CivCastTokenService } from '../../interfaces/civcast-token/civcast-token-service';

@Injectable({
  providedIn: 'root'
})
export class CivCastTokenRouteGuard  {
  constructor(private civcastTokenService: CivCastTokenService) {}
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return new Observable<boolean>(obs => {
			this.civcastTokenService.getToken().subscribe({
			next: (value) => {
				if(value){
					obs.next(true);
				}else{
					obs.next(false);
				}
	
				obs.complete();
			},
			error: (err) => {
				console.log(err);
				obs.next(true);				
			}
		});

    });	
  }
}
