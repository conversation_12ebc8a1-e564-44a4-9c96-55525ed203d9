import { HttpClient } from '@angular/common/http';
import { Injectable, EventEmitter } from '@angular/core';
import { CanActivate, Router, ActivatedRoute, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { observeOn } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
    providedIn: 'root'
  })
export class DelegateGuard implements CanActivate {
  constructor(private router: Router, private httpClient: HttpClient) {}
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return new Observable<boolean>(obs => {

         return this.IsDelegateAllowed().subscribe(result => {            
            if(result){
                obs.next(true);
            }else{
                this.router.navigate(['/delegate-unauthorized']);
                obs.next(false);
            }
         });
    });
  }

  IsDelegateAllowed(): Observable<boolean>{
      var obs = new Subject<boolean>();
    this.httpClient.get<CheckDelegationResponse>(`${environment.services_root_endpoints.delegation}/delegation/user/validate`).subscribe(result=>{
        console.log(result);
        obs.next(result.IsValid);
    });

    return obs.asObservable();
  }
}

export interface CheckDelegationResponse{
    IsDelegated: boolean;
    IsNotDelegated:boolean;
    AccountId:string;
    DelegateId:string;
    IsValid:boolean;
}
