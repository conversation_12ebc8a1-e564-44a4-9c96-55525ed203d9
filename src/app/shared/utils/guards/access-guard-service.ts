import { Injectable } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { AccessService } from '../../data-access/access-service';
import { AccessEffects, AccessRequest } from 'src/app/shared/interfaces/access';

@Injectable({
  providedIn: 'root'
})
export class AccessGuard implements CanActivate {
  constructor(private router: Router, private aRoute: ActivatedRoute, private accessService: AccessService, private toastrService: ToastrService) {}
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return new Observable<boolean>(obs => {
        var request = route.data['accessRequest'] as AccessRequest;
        var result = this.accessService.CheckAccessByRequest(request).subscribe(result => {
            if(result.Access === AccessEffects.Allow){                
                this.accessService.LatestRouteAccess = result;
                obs.next(true);
            }else{
                this.router.navigate(['unauthorized'], {skipLocationChange:true});
                obs.next(false);
            }
            
            obs.complete();            
        }, error => {
            console.log(error);
            this.toastrService.error(`There was an error getting access to the route.`);
            obs.error();
        });
    });
  }
}
