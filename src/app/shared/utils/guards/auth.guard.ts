import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router, CanActivate } from '@angular/router';
import { Observable } from 'rxjs';
import {
  fetchAuthSession,
  getCurrentUser
} from 'aws-amplify/auth';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate  {
  constructor(private router: Router) {}
  canActivate(): Observable<boolean> {
    return new Observable<boolean>(obs => {
      fetchAuthSession()
        .then(result => {
          if (!result) {
            this.router.navigate(['/unauthorized']);
            obs.next(false);
          } else {
            obs.next(true);
          }
          //this.authService.activateSignIn(result);
        })
        .catch(() => {
          obs.next(false);
          this.router.navigate(['/unauthorized']);
        });
    });
  }
}
