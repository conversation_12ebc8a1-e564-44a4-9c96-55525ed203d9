
import { ActivatedRouteSnapshot, RouterStateSnapshot, CanActivateFn, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import {
	fetchAuthSession,
	getCurrentUser
} from 'aws-amplify/auth';
import { inject } from '@angular/core';


export const checkLoginRequired: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
	const router = inject(Router);
	return new Observable<boolean>(obs => {
		getCurrentUser()
			.then(result => {
				if (!result) {
					router.navigate(['/login-required']);
					obs.next(false);
				} else {
					obs.next(true);
				}
			})
			.catch(() => {
				router.navigate(['/login-required']);
				obs.next(false);
			});
	});
}