import { v4 } from "uuid";
import { fromZonedTime, toZonedTime } from 'date-fns-tz';

export class HelperTools{
	static fontSize = "x-small";
	
	static wrapImagesWithLinks(htmlString: string, imageClickableLink: boolean = true, imageClickableLinkPosition: string = "bottom"): string {
		return htmlString.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, (match, p1) => {
			var finalImage = '';
		  var link =  `<div><a href="${p1}" target="_blank"><img loading="lazy" src="${p1}" alt="image" width=300></a>`;
		  if(imageClickableLink){
			if(imageClickableLinkPosition === "top"){
				finalImage =`<div><a href="${p1}" target="_blank">View Image</a></div>`
				finalImage += link;
			}else if(imageClickableLinkPosition === "bottom"){
				finalImage = link;
				finalImage += `<div><a href="${p1}" target="_blank">View Image</a></div>`
			}	 
		  }

		  return finalImage + '</div>';
		  
		});
	  }

	  static stripHtmlTags(input: string): string {
		return input.replace(/<\/?[^>]+(>|$)/g, '').trim();
	  }

	  static generateUUID(){
		return v4();
	  }
	  static CopyObject(obj: any): any {
		return JSON.parse(JSON.stringify(obj));
	  }
	
	  static toPascalCase(obj: any): any {
		if (Array.isArray(obj)) {
		  return obj.map(v => HelperTools.toPascalCase(v));
		} else if (obj !== null && obj !== undefined && obj.constructor === Object) {
		  return Object.keys(obj).reduce((result, key) => {
			const newKey = key.replace(/(^\w|_\w)/g, match => match.replace('_', '').toUpperCase());
			result[newKey] = HelperTools.toPascalCase(obj[key]);
			return result;
		  }, {} as any);
		}
		return obj;
	  }

	  static getUTCDate(): Date{
		return new Date(Date.UTC(
			new Date().getUTCFullYear(),
			new Date().getUTCMonth(),
			new Date().getUTCDate(),
			new Date().getUTCHours(),
			new Date().getUTCMinutes(),
			new Date().getUTCSeconds(),
			new Date().getUTCMilliseconds()
		  ));
	  }

	  static convertToTimeZoneDate(year: number, month: number, day: number, hour:number, minute:number, timezoneInfo:string): Date{

		let timeZone = undefined;
		const upperTimeZone = timezoneInfo.toLocaleUpperCase();
		if(upperTimeZone === TZInfo.EST || upperTimeZone === TZInfo.EDT){
			timeZone = 'America/New_York';
		}else if(upperTimeZone === TZInfo.CST || upperTimeZone === TZInfo.CDT){
			timeZone = 'America/Chicago';			
		} else if (upperTimeZone === TZInfo.MST || upperTimeZone === TZInfo.MDT) {
			timeZone = 'America/Denver';
		  } else if (upperTimeZone === TZInfo.PST || upperTimeZone === TZInfo.PDT) {
			timeZone = 'America/Los_Angeles';
		  }else{
			throw new Error("Invalid timezone");
			
		  }
		
		const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hour).padStart(2,'0')}:${String(minute).padStart(2,'0')}:00`;
	  
		// Convert the date from us timezone to computer timezone
		const utcDate = fromZonedTime(dateStr, timeZone);
	  
		return utcDate;

	

	  }

	  static downloadFile(fileURL: string, fileName: string = "download") {
		const link = document.createElement('a');
		link.href = fileURL;
		link.download = fileName;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	  }

	  static getBrowserTimezone(): string {
		const now = new Date();
		const offset = now.getTimezoneOffset(); // Get timezone offset in minutes
		const hoursOffset = -offset / 60; // Convert offset to hours

		// Map timezone offsets to common timezone abbreviations
		// Note: Since object keys must be unique, use a switch statement for clarity
		switch (hoursOffset) {
			case -5:
				// Could be EST or CDT
				// Check if DST is in effect
				return HelperTools.isDST(now) ? "CDT" : "EST";
			case -6:
				return HelperTools.isDST(now) ? "MDT" : "CST";
			case -7:
				return HelperTools.isDST(now) ? "PDT" : "MST";
			case -8:
				return "PST";
			case -4:
				return "EDT";
			default:
				return "Unknown Timezone";
		}
	  }

	  // Helper method to determine if DST is in effect
	  private static isDST(date: Date): boolean {
		const january = new Date(date.getFullYear(), 0, 1).getTimezoneOffset();
		const july = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
		return Math.max(january, july) !== date.getTimezoneOffset();
	  }
}  


export enum TZInfo
{	
	EST = "EST",
	EDT = "EDT",
	CST = "CST",
	CDT = "CDT",
	MST = "MST",
	MDT = "MDT",
	PST = "PST",
	PDT = "PDT"
}