import { Signal } from "@angular/core";
import { AbstractControl, FormArray, ValidationErrors, ValidatorFn } from "@angular/forms";

export class FormTools {
	

static minLengthArray(min: number) {
	return (control: AbstractControl): ValidationErrors | null => {
	  if (control instanceof FormArray) {
		return control.length >= min ? null : { minLengthArray: { valid: false, minLength: min } };
	  }
	  return null;
	};
  }

  static atLeastOneRequiredValidator(controlName: string): ValidatorFn {
    return (formArray: AbstractControl): ValidationErrors | null => {
      const controls = (formArray as any).controls;
      const isValid = controls.some((control: AbstractControl) => control.get(controlName)?.value);
      return isValid ? null : { atLeastOneRequired: true };
    };
  }

  static maxPercentValidator(percentage:number, total: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
  
      // Ensure the value is a number
      if (value == null || isNaN(value)) {
        return null;
      }

      const decimalPercentage = percentage / 100;
      const maxTotal = Math.round((decimalPercentage * (total - value)) / (1 - decimalPercentage) * 100) / 100;
  

      // Check if the value is 10% or less of the total
      if (value > maxTotal) {
        return { exceedsTenPercent: { value, maxAllowed: maxTotal } };
      }  
      
 3
      // Validation passed
      return null;
    };
  }  
}
