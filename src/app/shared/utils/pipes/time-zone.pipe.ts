import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'timeZonePipe',
  standalone: true  
})
export class TimeZonePipe implements PipeTransform {
  private datePipe: DatePipe;
  constructor() {
    this.datePipe = new DatePipe('en-US');
  }
  transform(value: Date, ...args: Array<string>): unknown {        
    var d = this.datePipe.transform(value, args[0], args[1]);
    return d;
  }

}
