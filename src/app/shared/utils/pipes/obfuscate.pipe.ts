import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'obfuscate',
  standalone: true  
})
export class ObfuscatePipe implements PipeTransform {
  constructor() {

  }
  transform(obj: any, ...args: Array<string>): unknown {   
	var value = obj;
	if(value){
		if(args.length > 0){						
			value = this.obfuscateString(value, args[0]);	
			
		}
	
	}



    return value;
  }

  obfuscateString(input: string, key: string): string {
	let result = '';
	for (let i = 0; i < input.length; i++) {
	  const charCode = input.charCodeAt(i);
	  const keyCode = key.charCodeAt(i % key.length);
	  result += String.fromCharCode(charCode ^ keyCode);
	}
	return result;
  }

}




