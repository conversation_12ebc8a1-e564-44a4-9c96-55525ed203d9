import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'bidDatePipe',
  standalone: true  
})
export class BidDatePipe implements PipeTransform {
  private datePipe: DatePipe;
  constructor() {
    this.datePipe = new DatePipe('en-US');
  }
  transform(obj: any, ...args: Array<string>): unknown {   
	var dateValue = null;
	if(obj && obj.Year && obj.Month && obj.Day &&  typeof obj.Hour === 'number' && typeof obj.Minute === 'number'){
		var hour =  obj.Hour > 12 ? obj.Hour - 12 : (obj.Hour === 0) ? 12 : obj.Hour
		var paddedHour = hour.toString().padStart(2, '0');
		var paddedMinute = obj.Minute.toString().padStart(2, '0');
		if(args.length === 0){
			dateValue = `${obj.Month + 1}/${obj.Day}/${obj.Year} ${hour}:${paddedMinute} ${obj.Hour >= 12 ? 'PM' : 'AM'}`;
		}else{
			if(args[0]==='date'){
				dateValue = `${obj.Month + 1}/${obj.Day}/${obj.Year}`;
			}else if(args[0]==='time'){
				dateValue = `${hour}:${paddedMinute} ${obj.Hour >= 12 ? 'PM' : 'AM'}`;
			}else{
				dateValue = `${obj.Month + 1}/${obj.Day}/${obj.Year} ${hour}:${paddedMinute} ${obj.Hour >= 12 ? 'PM' : 'AM'}`;
			}
			
		}
	}

    return dateValue;
  }

}
