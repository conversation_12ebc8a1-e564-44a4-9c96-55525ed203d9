import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'groupBy',
  standalone: true,
  pure: false
})
export class GroupByPipe implements PipeTransform {

  transform(value: any[], field: string): any {
    if (!value) return [];

	const groupedObj = value.reduce((prev, cur) => {
		// Split the field by dots to support nested properties
		const keys = field.split('.');
		// Reduce the keys to navigate through the nested objects

    

		const fieldValue = keys.reduce((acc, key) => (acc)? acc[key] : "None", cur);
	
		if (!prev[fieldValue]) {
		  prev[fieldValue] = [cur];
		} else {
		  prev[fieldValue].push(cur);
		}
		return prev;
	  }, {});
	
	  return Object.keys(groupedObj).map(key => ({ key, value: groupedObj[key] }));

    // const groupedObj = value.reduce((prev, cur) => {
    //   if (!prev[cur[field]]) {
    //     prev[cur[field]] = [cur];
    //   } else {
    //     prev[cur[field]].push(cur);
    //   }
    //   return prev;
    // }, {});

    // return Object.keys(groupedObj).map(key => ({ key, value: groupedObj[key] }));
  }

}