import { PipeTransform, Pipe } from '@angular/core';

@Pipe({
	name: 'phoneFormatter',
	standalone: true  
  })
export class PhonesPipe implements PipeTransform {
	transform(phoneNumber: string): string {
		// Remove all non-numeric characters
		const cleaned = ('' + phoneNumber).replace(/\D/g, '');
	
		// Check if the input is of correct length
		const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
	
		if (match) {
		  // Format the number as (*************
		  return `(${match[1]}) ${match[2]}-${match[3]}`;
		}
	
		return phoneNumber;
	  }
}
