import { DOCUMENT } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { Inject, Injectable, Renderer2 } from "@angular/core";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: 'root'
})
export class ScriptLoaderService {


  constructor(  @Inject(DOCUMENT) private document: Document, private client: HttpClient) { 

  }

  loadKnownScripts(renderer: Renderer2){
	this.loadGoogleMapsScript(renderer);
  }

  loadGoogleMapsScript(renderer: Renderer2){
	this.loadJsScript(renderer, `https://maps.googleapis.com/maps/api/js?key=${environment.google.maps.apiKey}`)
  }

  public loadJsScript(renderer: Renderer2, src: string): HTMLScriptElement {
    const script = renderer.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    renderer.appendChild(this.document.body, script);
    return script;
  }
}