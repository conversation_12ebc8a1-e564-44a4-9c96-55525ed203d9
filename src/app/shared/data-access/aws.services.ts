import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

/**
 * @deprecated The class should not be used
 */
@Injectable({
    providedIn: 'root'
  })
  export class AWSService {
    constructor(private http:HttpClient){}

    StepFunctionDescribe(arn: string): Observable<any>{
        return this.http.get(`/aws/stepfunctions/describe/${arn}`);
        
    }

  }