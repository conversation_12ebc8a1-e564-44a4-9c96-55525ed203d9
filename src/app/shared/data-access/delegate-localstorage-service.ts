import { HttpClient } from "@angular/common/http";
import { Injectable, signal } from "@angular/core";
import { Observable, Subject } from "rxjs";
import { environment } from "src/environments/environment";


@Injectable({
	providedIn: 'root'
})
export class DelegateLocalStorageService {

	delegatedId = signal<string | null>(null);

	constructor(private client: HttpClient){

	}

	//This function will get the current delegation from local store.
	//If local store is empty will try to pull and store the id. 
	//It will only store the id if they are delegated to local store
	//
	public GetEncryptedDelegatedId(): string | null {
		var obs = new Subject<string>();
		var storedId = this.GetDelegationIdFromClientStore();
		// if( storedId == null){

		// }else{
		//   return Observable.of(storedId);    
		// }
		return storedId;

	}

	public CreateAndGetDelegatedId(): Observable<string | null> {
		var obs = new Subject<string | null>();

		this.client.get<any>(`${environment.services_root_endpoints.delegation}/delegation/user/delegated`).subscribe(result => {
			if (result.EDelegatedId !== "") {
				this.SetDelegationIdentityToClientStore(result.EDelegatedId);
				obs.next(result.EDelegatedId)
				this.delegatedId.set(result.EDelegatedId);
			}
			else {
				this.SetDelegationIdentityToClientStore('');
				obs.next(null);
				this.delegatedId.set(null);
			}

			obs.complete();

		}, err => {
			obs.error(err);
		});

		return obs.asObservable();
	}

	public GetDelegationIdFromClientStore(): string | null {
		let id = localStorage.getItem("cm-delegated-identity");
		return id;
	}

	private SetDelegationIdentityToClientStore(delegationId: string) {
		localStorage.setItem("cm-delegated-identity", delegationId);
	}

	public ClearDelegationIdFromClientStore() {
		localStorage.removeItem("cm-delegated-identity");
	}
}