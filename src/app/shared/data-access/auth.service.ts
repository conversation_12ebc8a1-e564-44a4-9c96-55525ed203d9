import { effect, Injectable, signal } from '@angular/core';
import {
  fetchAuthSession,
  getCurrentUser
} from 'aws-amplify/auth';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { deprecate, isNull } from 'util';

@Injectable({
  providedIn: 'root'  
})
export class AuthService {
  isLoggedIn = new BehaviorSubject<boolean | null>(null);  
  isLoggedInSignal = signal<boolean | null>(null);
  subId = signal<string | null>(null);
  identityToken = signal<string | null>(null);  
  constructor(private client: HttpClient) {
    
  }

  loginEffect= effect(() => {
    if(this.isLoggedInSignal()){
      var session = fetchAuthSession().then(result => {
        this.subId.set(result.userSub as string);
        this.identityToken.set(result.tokens?.idToken?.toString() as string);
        
      });      
    }
  });
  

  public getAccessToken(): Observable<string | null> {
    var obs = new Subject<string | null>();

    fetchAuthSession()
      .then(data => {
        var token = data.tokens?.accessToken.toString() as string;
        obs.next(token);
        obs.complete();
      })
      .catch(err => {
        obs.next(null);
        obs.complete();
      });

    return obs.asObservable();
  }

  /**
   * @deprecated This method is deprecated. Use SubId Signal instead.
   */
  public async getSubId(): Promise<string> {    // Your code here
  
    var session = (await fetchAuthSession()).userSub as string;
    this.subId.set(session);
    return session;
  }

    /**
   * @deprecated This method is deprecated. Use Identity Signal instead.
   */
  public getIdentityToken(): Observable<string | null> {
    var obs = new Subject<string | null>();

    fetchAuthSession()
      .then(data => {
        var token = data.tokens?.idToken?.toString() as string;
        obs.next(token);
        obs.complete();
      })
      .catch(err => {
        obs.next(null);
        obs.complete();
      });

    return obs.asObservable();
  }

  public DownloadFile(url: string, data: any): Observable<any> {
    var obs = new Subject<any>();
    this.getAccessToken().subscribe(result => {
      var res = this.client.post(url, data, {
        headers: { 'Content-Type': 'application/json', Authorization: 'Bearer ' + result },
        responseType: 'blob'
      });

      obs.next(res);
    });

    return obs.asObservable();
  }
  
  public logIn() {    
    var userManagementUrl = encodeURI(environment.id_server + '/login?redirectUrl=' + environment.BaseSiteUrl + '/auth/validate');
    window.location.replace(userManagementUrl);    
  }

  public logInRedirect(redirectUrl:string) {    
    var userManagementUrl = encodeURI(environment.id_server + '/login?redirectUrl=' + redirectUrl + '/auth/validate');
    window.location.replace(userManagementUrl);    
  }

  public logOut() {
    var userManagementUrl = encodeURI(environment.id_server + '/logout?redirectUrl=' + environment.BaseSiteUrl + '/auth/callback/signout');
    window.location.replace(userManagementUrl);
  }

  public signUp(){
    var userManagementUrl = encodeURI(environment.id_server + '/register?redirectUrl=' + environment.BaseSiteUrl + '/auth/validate');
    window.location.replace(userManagementUrl);  
  }

  public localLogOut(message: any){
    localStorage.clear();
    window.location.replace(`${environment.BaseSiteUrl}/login?message=${message}`); 
  }
}
