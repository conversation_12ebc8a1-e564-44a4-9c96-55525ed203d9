﻿import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { UserProfile } from '../../models/user-profile';

@Injectable(
  {
    providedIn: 'root'
  }
)
export class UserSharedService {
  CachedProfiles: Array<UserProfile> = new Array<UserProfile>();

  constructor(private client: HttpClient) {}

  public GetUserActivityPaged(    
    currentPage: number,
    orderBy: string,
    isReversed: boolean,
    searchValue: string
  ): Observable<any> {    
    
    var url = `${environment.services_root_endpoints.history}/history/account`

    if(searchValue){
      var encValue = encodeURIComponent(searchValue);
      url += `?search=${encValue}`;
    }

    
    if(url.indexOf("?") === -1){
      url += `?current-page=${currentPage}`
    }else{
      url += `&current-page=${currentPage}`
    }
    
    
  
    return this.client.get<any>(url);    
  }

  public GetProfile(userId: string): Observable<UserProfile> {
    return new Observable<UserProfile>(obs => {
      const userProfile = this.GetProfileFromCache(userId);

      if (userProfile === null) {
        this.client.get(`${environment.services_root_endpoints.accounts}/accounts/${userId}`).subscribe(
          response => {
            const userProfile = response as UserProfile;
            this.AddProfileToCache(userProfile);
            obs.next(userProfile);
            obs.complete();
          },
          err => {
            obs.error(err);
          }
        );
      } else {
        obs.next(userProfile);
        obs.complete();
      }
    });
  }

  private AddProfileToCache(userProfile: UserProfile): void {
    const hasProfile = this.CachedProfiles.filter(x => x.UserId === userProfile.UserId);
    if (hasProfile.length <= 0) {
      this.CachedProfiles.push(userProfile);
    }
  }

  private GetProfileFromCache(userId: string): UserProfile | null {
    const hasProfile = this.CachedProfiles.filter(x => x.UserId === userId);
    if (hasProfile.length > 0) {
      return hasProfile[0];
    }

    return null;
  }

  public PrintCache(): void {
    for (const item of this.CachedProfiles) {
      console.log(item);
    }
  }
}
