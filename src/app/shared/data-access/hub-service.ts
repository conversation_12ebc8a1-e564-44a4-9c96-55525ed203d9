import { Injectable } from "@angular/core";
import { HubRoots } from "../interfaces/hub-root-constants";
import { HubInfo } from "../interfaces/hub-info";
import { BehaviorSubject } from "rxjs";

@Injectable({
	providedIn: 'root'
  })
export class HubService {
	hubUpdate = new BehaviorSubject<any>(null);
	hubs: Array<HubInfo> = [];
	getHubs() {
		if(!this.hubs || this.hubs.length <= 0){
			this.hubs = new Array<HubInfo>();
			this.hubs.push(new HubInfo(HubRoots.advertisement.route, HubRoots.advertisement.name, HubRoots.advertisement.id, HubRoots.advertisement.description));
			this.hubs.push(new HubInfo(HubRoots.construction.route, HubRoots.construction.name, HubRoots.construction.id, HubRoots.construction.description));
			this.hubs.push(new HubInfo(HubRoots.bid_opportunities.route, HubRoots.bid_opportunities.name, HubRoots.bid_opportunities.id, HubRoots.bid_opportunities.description));
		}

		return this.hubs;
	}

	getHub(hubId:string): HubInfo{
		return this.getHubs().filter(x => x.Id === hubId)[0];
	}

	getDefaultHub(): HubInfo{
		return this.getHub(HubRoots.default_hub_id);
		
	}
}

