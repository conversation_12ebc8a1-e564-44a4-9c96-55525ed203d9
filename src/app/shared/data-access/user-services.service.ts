import { Injectable, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { Auth } from 'aws-amplify';
import { Observable } from 'rxjs';
import { CMUser, User, UserProfile } from '../interfaces/user';
import { environment } from 'src/environments/environment';


@Injectable({
  providedIn: 'root'
})
export class UserService {
  storedUser: User | undefined;

  user = signal<User | null>(null);
  cmUser = signal<CMUser | null>(null);
  storedProfiles = signal<Array<UserProfile>>([]);

  constructor(private client: HttpClient, private authService: AuthService) {}

  public getDelegates(): Observable<Response> | null {
    return null;
  }

  public getDelegate(id: number | string): Observable<Response> | null {
    return null;
  }
  
  //todo: test id token info
  public getUser(): Observable<User> {
    return new Observable<User>(obs => {
      if (this.storedUser == null) {
        this.authService.getIdentityToken().subscribe(
          idToken => {
            if(idToken){
              var token = JSON.parse(atob(idToken.split('.')[1]));
              Auth.currentUserInfo().then(
                result => {
                  var user = new User();
                  user._id = result.attributes.sub;
  
                  obs.next(user);
                  obs.complete();
                },
                err => {
                  obs.error(err);
                }
              );
            }else{
              obs.next(undefined);
            }
     
          },
          err => {
            obs.error(err);
          }
        );
      } else {
        obs.next(this.storedUser);
      }
    });
  }

  public getUserClaims(): Observable<User> {
    return new Observable<User>(obs => {
      obs.next(new User());
    });
  }

  public searchUsers(search: string): Observable<Response> | null {
    var searchData = {
      data: search
    };

    return null;
    //return this.authService.AuthPost("/user/search", searchData);
  }

  private CreateUserBasedOnClaims(data: any): User {
    let user: User = new User();
    return user;
  }

  public GetProfile(userId: string): Observable<UserProfile> {
    return new Observable<UserProfile>(obs => {
      const userProfile = this.storedProfiles().find(x => x.UserId === userId);
      if(userProfile){
        obs.next(userProfile);
        obs.complete();
      }else{
        this.client.get(`${environment.services_root_endpoints.accounts}/accounts/${userId}`).subscribe(
          {
            next: (profile) => {
              const userProfile = profile as UserProfile;
              this.storedProfiles.update((result) => [...this.storedProfiles(), userProfile]);
              obs.next(userProfile);
              obs.complete();
            },
            error: (err) => {
              obs.error(err);
            }
          }      
        );
      }
    });
  }
}
