import { Injectable } from "@angular/core";
import { NgbDate, NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import moment from 'moment';
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root'
})

export class MomentDateFormatterService {
    format(date: NgbDate | null): string {
        if (!date) return '';
        let mdt = moment([date.year, date.month - 1, date.day]);
        if (!mdt.isValid()) return '';
        return mdt.format(environment.date_format);
    }

    parse(date: string): NgbDate | null{
        if(!date) return null;
        var data = date.split('-'); 

        if(data.length === 3){
            try {
                var nDate = new NgbDate(parseInt(data[2].trim()), parseInt(data[0].trim()), parseInt(data[1].trim()));    

                return nDate;
            } catch (error) {
                console.log(error);
            }
            
        }

        return null;

    }
}