import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { HubRoots } from 'src/app/shared/interfaces/hub-root-constants';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class HubGuardService  {
  constructor(private router: Router, private authService: AuthService) {}
	canActivateChild(childRoute: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree | Observable<boolean | UrlTree> | Promise<boolean | UrlTree> {
		return new Observable<boolean>(obs => {
			this.authService.isUserLoggedIn().subscribe(
				{
				  next: (user) => {
					obs.next(true);
				  },
				  error: (err) => {			
					if(childRoute.parent){
						var root = childRoute.parent.data['root'];
						if(root){
							this.router.navigate([root, HubRoots.ad_root]);						
							obs.next(true);
						}else{
							obs.next(false);
						}
						
					}else{
						obs.next(false);
					}			
					
				  },
				  complete: () => {
					
				  }
		  
				});
		});
	}
}
