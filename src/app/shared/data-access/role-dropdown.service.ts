import { HttpClient } from "@angular/common/http";
import { computed, inject, Inject, Injectable, resource, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { UserRoleStore } from "src/app/models/user-role-store";

import { environment } from "src/environments/environment";

@Injectable({
	providedIn: 'root'
})
export class RoleDropdownService {
	client = inject(HttpClient);
	initializeTeamDropDown = signal<boolean>(false);
	roles = computed(() => this.rolesDropDownResource.value()?.Roles ?? []);
	isLoading = computed(() => this.rolesDropDownResource.isLoading());
	
	

	private rolesDropDownResource = rxResource({
			request: () => this.initializeTeamDropDown(),
			loader: (request) => {
				if (request.request) {
			  		return this.client.get<UserRoleStore>(`${environment.services_root_endpoints.acl}/user-role-store`);
				}
	
				return of(null);
			}
		});
}