﻿import { Injectable, EventEmitter, Output, Directive } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmModalComponent } from '../ui/confirm/confirm.modal.component';

@Directive()
@Injectable({
  providedIn: 'root'
})
export class ConfirmService {
  @Output() close: EventEmitter<any> = new EventEmitter();
  //private modalRef;
  constructor(private modal: NgbModal) {}

  open(question: string, header: string = 'Confirm', isStatic: boolean = true): NgbModalRef {
    var ref = this.modal.open(ConfirmModalComponent, { 
      size: 'lg',
      backdrop: isStatic ? 'static': false,
      windowClass: 'confirm-dialog' // Added a custom class for additional styling if needed
    });
    ref.componentInstance.confirmQuestion = question;
    ref.componentInstance.header = header;
    return ref;
  }
}
