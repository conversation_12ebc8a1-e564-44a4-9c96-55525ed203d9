import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable, inject, signal } from "@angular/core";
import { Observable, of } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class GlobalDataService {

  client = inject(HttpClient);
  states = signal<Array<State> | null>(null);
  counties = signal<Array<County> | null>(null);
  timeZones = signal<Array<TimeZone> | null>(null);
  companyTypes = signal<Array<CompanyType> | null>(null);
  constructor() {
    this.getStates();
    this.getCounties();
    this.getCompanyTypes();
    this.getTimeZones();
   }

  // signalStates(){
  //   if(this.states() == null){
    
  //   }    

  //   if(this.counties() == null){
  //     this.getCounties().subscribe();
  //   }
  // }
  private getStates() {

    if(!this.states()){
      const headers = new HttpHeaders({
        'ContentType': 'application/json'
      });

      this.client.get<Array<State>>('./assets/data/states.json', { headers }).subscribe({
        next: (states) => {
          this.states.set(states);
        },
        error: (err) => {
          console.log(err);
        }
      });
    }
  }

  private getCompanyTypes(){

    if(!this.companyTypes()){
      const headers = new HttpHeaders({
        'ContentType': 'application/json'
      });
      this.client.get<Array<CompanyType>>('./assets/data/company-types.json', { headers }).subscribe({
        next: (companyTypes) => {
          this.companyTypes.set(companyTypes);
        },
        error: (err) => {
          console.log(err);
        }
      });
      
    }

  }

  private getCounties() {   
    if(!this.counties()){
      const headers = new HttpHeaders({
        'ContentType': 'application/json'
      });

      this.client.get<Array<County>>('./assets/data/counties.json', { headers }).subscribe({
        next: (counties) => {
          this.counties.set(counties);       
      
        },
        error: (err) => {

          console.log(err);
        }
      });
    }
  }

  private getTimeZones() {
    if(!this.timeZones()){
      const headers = new HttpHeaders({
        'ContentType': 'application/json'
      });
      this.client.get<Array<TimeZone>>('./assets/data/timezones.json', { headers }).subscribe({
        next: (timeZones) => {
          this.timeZones.set(timeZones);
        },
        error: (err) => {
          console.log(err);
        }
      });
    }

  }

//   getStateCounties(stateAbbreviation: string): Observable<Array<County>> {
//     return new Observable<Array<County>>(obs => {
//       this.getCounties().subscribe({
//         next: (counties) => {
//           let stateCounties = new Array<County>();
          
//           if(counties){
//             stateCounties = counties.filter(x => x.State === stateAbbreviation);          
//           }

//           obs.next(stateCounties);
//           obs.complete();
//         },
//         error: (err) => {
//           obs.error(err);
//           console.log(err);
//         }
//       });    
//     });
//   }
 }

export interface State {
  StateId: number;
  Name: string;
  Abbreviation: string;
}

export interface County {
  CountyId: number;
  State: string;
  StateId: number;
  Name: string;
  Latitude: number;
  Longitude: number;
  ADM2: string | null;
}

export interface TimeZone {
  ZoneId: string;
  Name: string;
  Value: string;
}

export interface CompanyType {
  CompanyTypeId: number;
  Name: string;
  Order: number;
}