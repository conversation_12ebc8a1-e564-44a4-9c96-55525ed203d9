import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { UserPolicyStore } from "src/app/models/user-policy-store";
import { UserRoleStore } from "src/app/models/user-role-store";
import { environment } from "src/environments/environment";
import { AccessRequest, AccessResponse } from "../interfaces/access";


@Injectable({
    providedIn: 'root'
})
export class AccessService {

    public LatestRouteAccess: AccessResponse = {} as AccessResponse;
    constructor(private client: HttpClient) { }
    public CheckAccess(resource: string, action: string): Observable<AccessResponse> {
        var request: AccessRequest = {
            Action: action,
            Resource: resource
        };

        return this.client.post<AccessResponse>(`${environment.services_root_endpoints.acl}/access`, request);
    }

    public CheckAccessMulti(accessRequest: Array<AccessRequest>){
        return this.client.post<AccessResponse>(`${environment.services_root_endpoints.acl}/access`, accessRequest);
    }

    public GetAccessMulti(accessRequest: Array<AccessRequest>){
        return this.client.patch<Array<AccessResponse>>(`${environment.services_root_endpoints.acl}/access/accesses`, accessRequest);
    }

    public CheckAccessByRequest(accessRequest: AccessRequest): Observable<AccessResponse> {

        return this.client.post<AccessResponse>(`${environment.services_root_endpoints.acl}/access`, accessRequest);
    }

    public AddRoleUserStore(delegateId: string, roleId: string): Observable<any> {

        var request = {
            DelegateId: delegateId,
            RoleId: roleId
        };

        return this.client.patch(`${environment.services_root_endpoints.acl}/user-store/add-user-policy-store-role`, request);
    };

    public InviteAddRoleToUserStore(token: string, accountId:string, delegateId: string, roleId: string): Observable<any> {

        var request = {
            AccountId: accountId,
            DelegateId: delegateId,
            RoleId: roleId,
            Token: token
        };

        return this.client.patch(`${environment.services_root_endpoints.acl}/user-store/invite-add-user-policy-store-role`, request);
    };

  
    public RemoveRoleUserStore(delegateId: string, roleId: string): Observable<any> {

        var request = {
            DelegateId: delegateId,
            RoleId: roleId
        };

        return this.client.patch(`${environment.services_root_endpoints.acl}/user-store/remove-user-policy-store-role`, request);
    };

    public GetRoles(): Observable<UserRoleStore> {
        return this.client.get<UserRoleStore>(`${environment.services_root_endpoints.acl}/user-role-store`);
    }

    public GetUserStores(userIds: Array<string>): Observable<GetUsersPolicyStoresResponse>{
        var request = {
            UserIds: userIds
        };

        return this.client.post<GetUsersPolicyStoresResponse>(`${environment.services_root_endpoints.acl}/user-store/get-users-policies`, request);
    }

    public GetUserStore(delegateId: string): Observable<UserPolicyStore> {
        return this.client.get<UserPolicyStore>(`${environment.services_root_endpoints.acl}/user-store/${delegateId}`);
    }
}

export class GetUsersPolicyStoresResponse{
    UserPolicyStores: Array<UserPolicyStore> = [];
}


interface AccessRequestData {
    request: AccessRequest;    
    response: AccessResponse;
}