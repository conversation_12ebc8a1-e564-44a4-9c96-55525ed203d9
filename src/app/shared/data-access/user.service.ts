﻿import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import {
  fetchAuthSession,
  getCurrentUser
} from 'aws-amplify/auth';
import { Observable, Subject } from 'rxjs';
import { CMUser, User } from '../interfaces/user';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  storedUser: User | undefined;

  user = new Subject<User>();
  cmUser = new Subject<CMUser | null>();

  constructor(private client: HttpClient, private authService: AuthService) {}

  public getDelegates(): Observable<Response> | null {
    return null;
  }

  getClaimsUser(): Observable<User> {
    return this.user.asObservable();
  }

  getCMUser(): Observable<CMUser | null> {
    return this.cmUser.asObservable();
  }

  setClaimsUser(claimsUser: User) {
    this.user.next(claimsUser);
  }

  setCMUser(cmUser: CMUser) {
    this.cmUser.next(cmUser);
  }

  public getDelegate(id: number | string): Observable<Response> | null {
    return null;
  }
  
  //todo: test id token info
  public getUser(): Observable<User> {
    return new Observable<User>(obs => {
      if (this.storedUser == null) {
        this.authService.getIdentityToken().subscribe(
          idToken => {
            if(idToken){
              var token = JSON.parse(atob(idToken.split('.')[1]));
              fetchAuthSession().then(
                result => {
                  var user = new User();
                  user._id = result.userSub as string;
  
                  obs.next(user);
                  obs.complete();
                },
                err => {
                  obs.error(err);
                }
              );
            }else{
              obs.next(undefined);
            }
     
          },
          err => {
            obs.error(err);
          }
        );
      } else {
        obs.next(this.storedUser);
      }
    });
  }

  public getUserClaims(): Observable<User> {
    return new Observable<User>(obs => {
      obs.next(new User());
    });
  }

  public searchUsers(search: string): Observable<Response> | null {
    var searchData = {
      data: search
    };

    return null;
    //return this.authService.AuthPost("/user/search", searchData);
  }

  private CreateUserBasedOnClaims(data: any): User {
    let user: User = new User();
    return user;
  }
}
