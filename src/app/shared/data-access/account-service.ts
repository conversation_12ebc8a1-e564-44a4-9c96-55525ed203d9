import { Injectable, signal } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { UserProfile } from 'src/app/shared/interfaces/user';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { rxResource } from '@angular/core/rxjs-interop';


@Injectable({
  providedIn: 'root'
})
export class AccountService {
  CachedProfiles: Array<UserProfile> = new Array<UserProfile>();
  // initialize = signal<boolean>(false);

  // private accountResource = rxResource({
  //   request: () => this.initialize(),
  //   loader: (request) => {
  //     if (request.request) {
  //       return this.client.get<AccountDelegateStore>(`${environment.services_root_endpoints.delegation}/delegation/account`);
  //     }

  //     return of(null);
  //   }
  // });
    

  constructor(private client: HttpClient) {}

  public FindAccounts(search: any): Observable<Array<CivCastAccount>> {
    return this.client.get<Array<CivCastAccount>>(`${environment.services_root_endpoints.accounts}/accounts?user-name=${search.Search}&company-name=${search.Search}`);    
  }
  public GetProfile(userId: string): Observable<UserProfile> {
    return new Observable<UserProfile>(obs => {
      const userProfile = this.GetProfileFromCache(userId);

      if (userProfile === null) {
        this.client.get(`${environment.services_root_endpoints.accounts}/accounts/${userId}`).subscribe(
          response => {
            const userProfile = response as UserProfile;
            this.AddProfileToCache(userProfile);
            obs.next(userProfile);
            obs.complete();
          },
          err => {
            obs.error(err);
          }
        );
      } else {
        obs.next(userProfile);
        obs.complete();
      }
    });
  }

  public GetAccounts(userIds: Array<string>): Observable<Array<CivCastAccount>>{
    return this.client.post<Array<CivCastAccount>>(`${environment.services_root_endpoints.accounts}/accounts`, userIds);
  }

  public GetAccountsByCivCastUserIds(civCastUserIds: Array<string>): Observable<Array<CivCastAccount>>{
    return this.client.patch<Array<CivCastAccount>>(`${environment.services_root_endpoints.accounts}/accounts/get-accounts-civcastuserids`, civCastUserIds);
  }
  
  public GetAccountsFilter(filterOptions: AccountServiceFilterOptions): Observable<any>{

    var url = `${environment.services_root_endpoints.accounts}/accounts`;
    var params = [];

    if(filterOptions.username && filterOptions.username.trim()){
        params.push(`user-name=${filterOptions.username}`);
    }

    if(filterOptions.companyName && filterOptions.companyName.trim()){
        params.push(`company-name=${filterOptions.companyName}`);
    }

    if(filterOptions.projections && filterOptions.projections.length > 0){
        var projections = filterOptions.projections.join(',');
        params.push(`projections=${projections}`);
    }

    if(filterOptions.name && filterOptions.name.trim()){            
        params.push(`name=${filterOptions.name}`);
    }

    if(filterOptions.limit && filterOptions.limit.trim()){
        params.push(`limit=${filterOptions.limit}`);
    }
    

    var c = 0;
    for(let option of params){
        if(c === 0){
            url += "?";
        }else{
            url += "&";
        }

        url += option;
        c++;
    }

    return this.client.get<any>(url);
}

  public GetAccountByIdentity(): Observable<CivCastAccount>{
    return new Observable(obs => {
      var item = localStorage.getItem("cm-delegated-identity-info");    

      if(item){
        var civcastAccount = JSON.parse(item) as CivCastAccount;
        obs.next(civcastAccount);
        obs.complete();        
      }else{
        this.client.get<CivCastAccount>(`${environment.services_root_endpoints.accounts}/accounts/identity`).subscribe(result => {
          localStorage.setItem("cm-delegated-identity-info", JSON.stringify(result));
          obs.next(result);
          obs.complete();
        });
      }
    });
  }
  

  public RemoveAccountIdentity(){
    localStorage.removeItem("cm-delegated-identity-info");
  }

  private AddProfileToCache(userProfile: UserProfile): void {
    const hasProfile = this.CachedProfiles.filter(x => x.UserId === userProfile.UserId);
    if (hasProfile.length <= 0) {
      this.CachedProfiles.push(userProfile);
    }
  }

  private GetProfileFromCache(userId: string): UserProfile | null {
    const hasProfile = this.CachedProfiles.filter(x => x.UserId === userId);
    if (hasProfile.length > 0) {
      return hasProfile[0];
    }

    return null;
  }

  public PrintCache(): void {
    for (const item of this.CachedProfiles) {
      console.log(item);
    }
  }
}

export interface AccountServiceFilterOptions{
  username: string;
  companyName: string;
  name: string;    
  projections: Array<string>;
  limit: string;
}