﻿import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NavService {
  showNavIcon: boolean = false;
  showDelegation: boolean = false;
  isNavOpen: boolean = true;
  isNavOpenTest = new Subject<boolean>();
  isNavOpenTest$ = this.isNavOpenTest.asObservable();
  navOpenState: string = 'active';
  resetNav = new Subject<any>();
  resetNav$ = this.resetNav.asObservable();
  _updateRecentProjects = new Subject<any>();
  updateRecentProjects =  this._updateRecentProjects.asObservable();
}
