import { HttpClient, HttpEvent, HttpEventType, HttpHeaders, HttpRequest } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Observable, Subject, Subscription } from "rxjs";
import { map, takeUntil } from "rxjs/operators";
import { environment } from "src/environments/environment";

@Injectable({
	providedIn: 'root'
  })
  export class LambdaAWSService {
	constructor(private client: HttpClient, private router: Router) {}

	getUploadPresignedUrl(key: string, contentType: string): Observable<PresignedUrlResponse>{
		var request: PresignedUrlRequest = {
			Key: key,
			ContentType: contentType
		};

		return this.client.post<PresignedUrlResponse>(`${environment.services_root_endpoints.lambdaaws}/presignedurl`, request);
	}

	getDownloadPresignedUrl(key: string, fileName: string): Observable<PresignedUrlResponse>{		
		var result = this.client.get<PresignedUrlResponse>(`${environment.services_root_endpoints.lambdaaws}/presignedurl?key=${encodeURIComponent(key)}&filename=${encodeURIComponent(fileName)}`);
		return result;
	}

	uploadFilePresignedUrl(key: string, file:File): Observable<any>{
		var obs = new Subject();
		this.getUploadPresignedUrl(key, file.type).subscribe(result => {
			var headers = new HttpHeaders().set("x-auth","false").set("Content-Type", file.type);

			this.client.put(result.PresignedUrl, file, {headers: headers}).subscribe(fileResult => {
				obs.next(fileResult);
				obs.complete();
			}, err => {
				obs.error(err);
			});
		}, err => {
			obs.error(err);
		});

		return obs.asObservable();
	}

	// uploadFileWithSignedURLWithProgress(url: string, file: File): { observable: Observable<any>, subscription: Subscription } {
	// 	const headers = new HttpHeaders().set("x-auth", "false").set("Content-Type", file.type);
	
	// 	// Create the observable for the upload process
	// 	const observable = this.client.put(url, file, { headers, reportProgress: true, observe: 'events' }).pipe(
	// 		map((event: any) => {
	// 			if (!event) return;
	// 			switch (event.type) {
	// 				case HttpEventType.UploadProgress:
	// 					// Calculate and return the upload progress
	// 					return Math.round((event.loaded / event.total) * 100);
	// 				case HttpEventType.Response:
	// 					// Return the response body on completion
	// 					return event.body;
	// 			}
	// 		})
	// 	);
	
	// 	// Subscribe to the observable to start the upload and allow cancellation
	// 	const subscription = observable.subscribe({
	// 		next: progress => console.log('Upload progress:', progress),
	// 		error: err => console.error('Upload error:', err),
	// 		complete: () => console.log('Upload complete')
	// 	});
	
	// 	// Return both the observable (for progress tracking) and the subscription (for cancellation)
	// 	return { observable, subscription };
	// }

	uploadFileWithSignedURLWithProgress(url: string, file: File): Observable<string> {
		// const headers = { 'Content-Type': file.type };\
		var headers = new HttpHeaders().set("x-auth", "false").set("Content-Type", file.type );

		return this.client.put(url, file, { headers, reportProgress: true, observe: 'events' }).pipe(
			map((event: any) => {
				if (!event) return;
				switch (event.type) {
					case HttpEventType.UploadProgress:
						return Math.round((event.loaded / event.total) * 100);
					case HttpEventType.Response:
						return event.body;
				}
			})
		);
	}
  }

  export class PresignedUrlRequest{
	constructor(key: string, contentType: string){
		this.Key = key;
		this.ContentType = contentType;
	}
	Key: string;
	ContentType: string;
  }

  export interface PresignedUrlResponse{
	Bucket: string;
	PresignedUrl: string;
  }