import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { forkJoin, lastValueFrom, Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Role } from '../../models/user-role-store';

@Injectable({
	providedIn: 'root'
})
export class DefaultSyncService {

	constructor(private client: HttpClient) { }

	async verifyAndSync() {
		this.GetDelegationValidationResponse().subscribe(result => {
			if(result.AccountId === result.DelegateId){
				forkJoin(this.syncDefaultRoles(), this.syncDefaultUserProject()).subscribe(result => {
					console.log("sync complete");
				}, err => {
					console.log("Sync Error", err);
				});
			}
		});
	}

	syncDefaultUserProject(): Observable<any> {
		var obs = new Subject<any>();

		this.getUserProjectStore().subscribe(async store => {
			try {			
				if (store) {
					if (!store.SyncDate) {
						var result = await lastValueFrom(forkJoin([this.syncDefaultUserProjectComponentStore(), this.getDefaultUserProjectComponentsInfo()]));

						var list = new Array<Observable<any>>();

						if(result){
							for(let comp of result[1].Components){
								var hasComp = store.Components.filter((x: { ComponentIdentifier: any; }) => x.ComponentIdentifier === comp.ComponentIdentifier);
								if(hasComp.length <= 0){
									list.push(this.client.post<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, comp));
								}
							}
	
						}
				
						await lastValueFrom(forkJoin(list));
					}
				}
	
			} catch (error) {

			}
		});

		return obs.asObservable();
	}

	syncDefaultRoles(): Observable<any> {
		var obs = new Subject<any>();
		this.getUserRoleStore().subscribe(async store => {
			try {				
				if (store) {
					if (!store.SyncDate) {
						//sync date first 
						var result = await lastValueFrom(forkJoin([this.getDefaultRoles(), this.syncDefaultRoleStore()]));
	
						var roles = result[0];
	
						var list = new Array<Observable<Role>>();
						for (var role of roles) {
							var hasRole = store.Roles.filter((x: { Id: string; }) => x.Id === role.Id);
	
							if (hasRole.length <= 0) {
								var request = {
									Role: role
								}
	
								list.push(this.client.patch<Role>(`${environment.services_root_endpoints.acl}/user-role-store/add-role`, request));
							}
						}
	
						await lastValueFrom(forkJoin(list));
	
					}
				}
	
	
			} catch (error) {

			}
		});

		return obs.asObservable();

	}

	syncDefaultUserProjectComponentStore() {
		var request = {
			FunctionName: "sync"
		};

		return this.client.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, request);
	}


	syncDefaultRoleStore() {
		var request = {
			FunctionName: "sync"
		};

		return this.client.patch<any>(`${environment.services_root_endpoints.acl}/user-role-store/sync`, request);
	}



	syncUserProjectComponents(){
		var patchOptions = {
			FunctionName: "sync"

		  }
		  return this.client.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, patchOptions);
	}

	getUserProjectStore(): Observable<any> {
		return this.client.get<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`);
	}

	getUserRoleStore(): Observable<any> {
		return this.client.get<any>(`${environment.services_root_endpoints.acl}/user-role-store`);
	}

	GetDelegationValidationResponse(): Observable<any>{
		return this.client.get<any>(`${environment.services_root_endpoints.delegation}/delegation/user/validate`);			
	}

	getDefaultRoles(): Observable<Array<Role>> {
		const headers = new HttpHeaders({
			'ContentType': 'application/json'
		});
		return this.client.get<Array<Role>>(environment.Defaults.Roles_Url, { headers });
	}

	getDefaultUserProjectComponentsInfo(): Observable<any>{
		const headers = new HttpHeaders({
		  'ContentType': 'application/json'
		});

		return this.client.get('./assets/data/user-project-default-settings.json', { headers });
	  }  
}