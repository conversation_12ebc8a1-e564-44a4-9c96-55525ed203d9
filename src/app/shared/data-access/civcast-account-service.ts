import { computed, inject, Injectable, signal } from '@angular/core';
import { Observable, of, Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { AccountProfile, AllowedProfile } from '../interfaces/account-profile';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { rxResource } from '@angular/core/rxjs-interop';

@Injectable({
	  providedIn: 'root'
})
export class CivCastAccountService {
  AccountProfile = computed(() => this.accountProfileResource.value());
  client = inject(HttpClient);
  authService = inject(AuthService);
  subId = this.authService.subId;
  userProfiles = signal<Array<AllowedProfile> | null>(null);
  allowedProfilesSubscription = new Subscription();
  isLoading = computed(() => this.accountProfileResource.isLoading());

  constructor(){}

  accountProfileResource = rxResource({
	request: () => this.subId(),
	loader: (request) => {
		if(request.request){
			return this.client.get<AccountProfile>(`${environment.services_root_endpoints.accounts}/accounts/${request.request}`);
		}

		return of(null);
		}
	});

  destroy(){
	this.allowedProfilesSubscription?.unsubscribe();	
  }

  getUserProfiles(cognitoIds:Array<string>){
	this.allowedProfilesSubscription?.unsubscribe();

	this.allowedProfilesSubscription = this.client.post<Array<AllowedProfile>>(`${environment.services_root_endpoints.civcast}/users`,cognitoIds).subscribe(
		{
			next: response => {
				this.userProfiles.set(response);
			},
			error: err => {
				console.error(err);
			}
		}
	);
  }

  public GetCivCastUserSettings(): Observable<any> {
	  return new Observable<any>(obs => {
		this.client.get<any>(`${environment.services_root_endpoints.civcast}/users?filter=user-settings`).subscribe(
			response => {	
			  obs.next(response);
			  obs.complete();
			},
			err => {
			  obs.error(err);
			}
		  );
	  });
  }
}

export class AccountServiceFilterOptions{
  username!: string;
  companyName!: string;
  name!: string;    
  projections: Array<string>=[];
  limit!: string;
}