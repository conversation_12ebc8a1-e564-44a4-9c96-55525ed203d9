import { EffectRef } from "@angular/core";

export class EffectRegistrar{
	private effects: Array<EffectRef> = new Array<EffectRef>();	
	registerEffect(effect: EffectRef){
		if(effect){
			this.effects.push(effect);
		}

		if(this.effects.length > 30){
			console.log("Possible (30) Too many effects registered", this.effects.length);
		}else if(this.effects.length > 100){
			console.log("Possible (100) Too many effects registered", this.effects.length);
		}
	}

	stopEffects(){
		for(let effect of this.effects){				
			effect.destroy();
		}
		
		this.effects = new Array<EffectRef>();
	}
}