﻿export interface Delegate {
  UserId: string;
  IsEnabled: boolean;
  CreateDate: Date | null;
  Deactivated: Date | null;
  Title: string;
}

export interface UserDelegateStore {
  Id: string | null;
  UserId: string;
  CurrentDelegation: string | null;
  MyDelegations: Array<string>;
}

export interface AccountDelegateStore {
  Id: string | null;
  UserId: string;
  Users: Array<Delegate>;
}


export class DelegateUserNavInfo {
  constructor(userId: string, firstName: string, lastName: string, companyName: string) {
    this.FirstName = firstName;
    this.LastName = lastName;
    this.UserId = userId;
    this.CompanyName = companyName;
  }

  UserId: string;
  FirstName: string;
  LastName: string;
  CompanyName: string;
}

export class DelegateNavStore {
  currentDelegateUserId: string | null = null;
  accountsAccess: Array<DelegateUserNavInfo> = new Array<DelegateUserNavInfo>();

}

export class MyUserInfo {

  constructor(firstName: string, lastName: string) {
    this.FirstName = firstName;
    this.LastName = lastName;
  }
  FirstName: string;
  LastName: string;
}