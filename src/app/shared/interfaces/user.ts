import { Delegate } from "src/app/shared/interfaces/delegate";

export class CMUser {
	DelegatedUsers: Array<Delegate> = [];
	MyDelegations: Array<Delegate> = [];
	CurrentDelegation: Delegate | null = null;
  
	constructor() {}
  }
  
  export interface UserProfile {
	UserId: string;
	FirstName: string;
	LastName: string;
	Company: any;
	Email: string;
  }
  

  export class User {
	_id!: string;
	userId!: string;
	profile!: UserProfile;
	data: any;
  
  }
  