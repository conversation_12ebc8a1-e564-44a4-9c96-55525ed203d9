import { HttpClient } from "@angular/common/http";
import { Injectable, inject } from "@angular/core";
import moment from "moment";
import { Observable, Subject, Subscription, catchError, delay, delayWhen, map, of, retryWhen, take, tap, timer } from "rxjs";
import { environment } from "src/environments/environment";
import { CivCastToken } from "./civcast-token";

@Injectable({
	providedIn: 'root'
})
export class CivCastTokenService {

	client = inject(HttpClient);
	isTokenGenerating = false;
	currentGeneratingToken: Subscription | null = null;
	constructor() { }

	getTokenAlt(): Observable<string | null> {

		return new Observable<string | null>(observer => {
			const token = localStorage.getItem('civcast-token');
				if (token && !this.isTokenExpired(token)) {
					observer.next(token);
					observer.complete();
				} else {
					this.createTokenAlt().subscribe({
						next: (value) => {
							if (value && value.Token) {						
							
								localStorage.setItem("civcast-token", value.Token);
			
								var info = value.Token.split('.')[1];
								localStorage.setItem("civcast-token-info", atob(info));

								observer.next(value.Token);
														
							} else {
								observer.error(new Error('No token found'));
								observer.next(null);
							}

							observer.complete();
						},
						error: (err) => {
							observer.error(err);
						}
					});
					//observer.error(new Error('No token found'));
				}
		});
	}

	getToken(): Observable<string | null> {
		return new Observable<string | null>(observer => {
			if (this.isTokenGenerating) {
				observer.error(new Error('Token is generating'));
			} else {
				const token = localStorage.getItem('civcast-token');
				if (token && !this.isTokenExpired(token)) {
					observer.next(token);
					observer.complete();
				} else {
					this.createToken().subscribe({
						next: (value) => {
							if (value) {
								observer.next(value);
								observer.complete();
							} else {
								observer.error(new Error('No token found'));
							}
						},
						error: (err) => {
							observer.error(new Error('No token found'));
						}
					});
					//observer.error(new Error('No token found'));
				}
			}
		}).pipe(
			retryWhen(errors =>
				errors.pipe(
					// Use delay to wait for a while before retrying
					delayWhen( x => timer(2000)), // Wait for 5 seconds before retrying
					tap(() => {
						if (!this.isTokenGenerating) {
							throw new Error('Token is ready');
						}
					}),
					take(2),
					tap(() => {
						throw new Error("Token should be ready now");
						
					})

				)
			),
			catchError(err => {
				if (err.message === 'Token is ready') {
					return this.getToken(); // Token is ready, retry getting the token
				} else {
					// Handle other errors here
					return of(null);
				}
			})
		);
	}

	createTokenAlt() {
		return this.client.get<CivCastTokenResponse>(`${environment.services_root_endpoints.civcast}/token`);
	}

	createToken(): Observable<string | null> {
		var obs = new Subject<string | null>();
		this.isTokenGenerating = true;

		if (this.currentGeneratingToken) {
			this.currentGeneratingToken.unsubscribe();
			this.currentGeneratingToken = null;
		}

		this.currentGeneratingToken = this.client.get<CivCastTokenResponse>(`${environment.services_root_endpoints.civcast}/token`).subscribe({
			next: (value) => {
				if (value) {
					if (value.Token) {
						localStorage.setItem("civcast-token", value.Token);

						var info = value.Token.split('.')[1];
						localStorage.setItem("civcast-token-info", atob(info));
					}
					else {
						localStorage.removeItem("civcast-token");
					}

					this.isTokenGenerating = false;
					obs.next(value.Token);
					obs.complete();
				} else {
					this.isTokenGenerating = false;
					obs.next(null)
					obs.complete();
				}

			},
			error: (err) => {
				this.isTokenGenerating = false;

				obs.next(null)
				obs.complete();
			}
		});

		return obs.asObservable();
	}

	isTokenExpired(token: string) {
		if (token) {
			var tokenInfo = JSON.parse(atob(token.split('.')[1])) as CivCastToken;

			if (tokenInfo) {
				var nDate = moment();
				var cDate = moment(tokenInfo.Exp);

				if (tokenInfo) {

					var nDate = moment();
					var cDate = moment(tokenInfo.Exp);

					if (nDate < cDate) {
						return false;
					}

					return true;
				}

				return true;
			} else {
				return false;
			}
		} else {
			throw new Error("Token is not valid");
		}


	}

	getTokenInfo() {
		return localStorage.getItem("civcast-token-info");
	}
}

export interface CivCastTokenResponse {
	Token: string;
}