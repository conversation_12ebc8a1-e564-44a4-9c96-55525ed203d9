export enum AccessEffects{
    Allow = "Allow",
    Deny = "Deny",
    ReadOnly = "ReadOnly"
}

export interface AccessRequest{    
    Action:string;
    Resource:string;        
}

export interface AccessResponse{
    Access:string;    
    Policy:Policy
}

export interface Policy{
    Id:string;
    Name:string;
    Description:string;
    Version: string;
    ACL:ACL;
}

export interface ACL{
    Id:string;
    Statements:Array<ACLItem>;
}

export interface ACLItem{
    Effect:string;
    Actions:Array<string>;
    Resources:Array<string>;
}