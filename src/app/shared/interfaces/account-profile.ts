
export interface CivCastAccount{
    Id: string;
    CognitoUserId: string;
    Profile: AccountProfile;
    Username: string;
    IsOnList: boolean;
    IsActive: boolean;
}

export interface AccountProfile{
    FirstName: string;
    LastName: string;
    Company: Company;
    Email: string;
    Username: string;
}

export interface AllowedProfile{
    FirstName: string;
    LastName: string;
    CompanyName: string;
    Email: string;
    Phone: string;
    Address: string;    
    CognitoUserId: string;
    UserId: string;
}

export interface Company{
    Name: string;
    Phone: string;
    Fax: string;
    Address: Address;
    CompanyType: CompanyType;
	Website: string;
}

export interface CompanyType{
    CompanyTypeId: number;
    Name: string;
    Order: number;
}

export interface Address{
    Name: string;
    Address1: string;
    Address2: string;
    City: string;
    State: State;
    Zip: string;
}

export interface State{
    StateId: number;
    Name: string;
    Abbreviation: string;
}