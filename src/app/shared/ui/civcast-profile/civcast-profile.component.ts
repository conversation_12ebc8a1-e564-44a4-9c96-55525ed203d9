import { Component, computed, effect, inject, OnDestroy, signal } from '@angular/core';
import { CivCastAccountService } from '../../data-access/civcast-account-service';
import { AllowedProfile } from '../../interfaces/account-profile';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { PhonesPipe } from '../../utils/pipes/phone.pipe';
import { DbeCertificationsService } from 'src/app/account/shared/data-access/dbe-certifications-service';
import { DBECertificationStatic, DocumentInfo } from 'src/app/account/shared/interfaces/dbe-certifications.model';
import { LambdaAWSService, PresignedUrlResponse } from 'src/app/shared/data-access/lambdaaws.service';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs/operators';

// New interface to merge static and user-specific certification data
export interface UserCertificationDisplay {
  cert: DBECertificationStatic; // Static certification info
  documents: DocumentInfo[];    // User-specific documents
}

@Component({
  selector: 'app-civcast-profile',
  imports: [CommonModule, PhonesPipe],
  templateUrl: './civcast-profile.component.html',
  styleUrl: './civcast-profile.component.css'
})
export class CivcastProfileComponent implements OnDestroy {


  cognitoUserId = signal<string | null>(null);
  civcastAccountService = inject(CivCastAccountService);
  dbeCertificationsService = inject(DbeCertificationsService);
  currentProfile: AllowedProfile | null = null;
  aRoute = inject(ActivatedRoute);
  isAccountLoading = this.civcastAccountService.isLoading;
  isDBECertsLoading = this.dbeCertificationsService.isLoading;
  userCertifications: UserCertificationDisplay[] = [];  // Updated type

  ngOnDestroy(): void {
    this.civcastAccountService.destroy();
  }

  accountSignal = toSignal(toObservable(this.civcastAccountService.userProfiles).pipe(
    tap(userProfiles => {
      if (userProfiles) {
        const allowedProfiles = userProfiles as AllowedProfile[];
        if (allowedProfiles.length > 0) {
          this.currentProfile = allowedProfiles[0];
        }        
      }
    })
  ));

  cognitoUserIdSignal = toSignal(toObservable(this.cognitoUserId).pipe(
    tap(userId => {
      if (userId) {
        this.civcastAccountService.getUserProfiles([userId]);
        this.dbeCertificationsService.userId.set(userId);
      }
    })
  ));

  constructor(private lambdaAWSService: LambdaAWSService) {
    this.aRoute.params.subscribe((params) => {
      this.cognitoUserId.set(params['userId']);
    });

    effect(()=> {
      if(this.cognitoUserId() && this.dbeCertificationsService.userDBECerts() && this.dbeCertificationsService.staticCertifications() && this.civcastAccountService.userProfiles()){
        this.userCertifications = [];
        const staticCerts = this.dbeCertificationsService.staticCertifications() as DBECertificationStatic[];
        const userCerts = this.dbeCertificationsService.userDBECerts();
        if (userCerts && staticCerts.length > 0) {
            const certMap = new Map(staticCerts.map(cert => [cert.DBECertId, cert]));

            this.userCertifications = userCerts.map(userCert => ({
                cert: certMap.get(userCert.DBECertId) as DBECertificationStatic,
                documents: userCert.DBECertDocuments
            }));
        }
      }
    
    });
       

  }

  fetchPresignedUrlForDocument(doc: DocumentInfo): void {
    this.lambdaAWSService.getDownloadPresignedUrl(doc.S3Key, doc.FileName).subscribe({
      next: (presignedUrl: PresignedUrlResponse) => {
        window.open(presignedUrl.PresignedUrl);
      },
      error: (err: any) => console.error('Error retrieving presigned URL:', err)
    });
  }

  // private loadUserCertifications(): void {
  //   //this.dbeCertificationsService.loadStaticCertifications();
  //   //this.dbeCertificationsService.getDBECerts();


  // }
}
