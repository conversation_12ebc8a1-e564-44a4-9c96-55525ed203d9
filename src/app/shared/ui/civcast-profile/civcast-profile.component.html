<div class="container">
	<h3>User Profile</h3>
	<div class="card my-4">
		<h5 class="card-header">Contact</h5>
		<div class="card-body">
			@if(isAccountLoading()){
			<div class="col-12 placeholder-glow">
				<div class="col-12 col-lg-2">
					<span class="placeholder col-12"></span>
				</div>
				<div class="col-12 col-lg-1">
					<span class="placeholder col-12"></span>
				</div>
				<div class="col-12 col-lg-2">
					<span class="placeholder col-12"></span>
				</div>
				<div class="col-12 col-lg-6">
					<span class="placeholder col-12"></span>
				</div>
				<div class="col-12 col-lg-1">
					<span class="placeholder col-12"></span>
				</div>
			</div>
			}@else {
				<div>
					{{ currentProfile?.FirstName}} {{ currentProfile?.LastName }}
				</div>
				<div>
					<a class="d-block text-truncate" href='tel:{{ currentProfile?.Phone }}'>
						{{ currentProfile?.Phone | phoneFormatter }}
					</a>
				</div>
				<div>
					<a class="d-block text-truncate" href='mailto:{{ currentProfile?.Email }}'>{{ currentProfile?.Email |
						lowercase }}</a>

				</div>
				<div>
					{{ currentProfile?.CompanyName }}
				</div>
				<div>
					<a class="d-block text-truncate" href='https://maps.google.com?q={{ currentProfile?.Address }}'
						target="_blank">{{ currentProfile?.Address }}</a>
				</div>
			}
		</div>
	</div>
	<!-- DBE Certifications and Documentation Section -->
	<div class="card my-4">
		<h5 class="card-header">DBE Certifications and Documentation</h5>
		<div class="card-body">
			@if(isDBECertsLoading()){
				<div class="col-12 placeholder-glow">
					<div class="col-12 col-lg-2">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-1">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-2">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-6">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-1">
						<span class="placeholder col-12"></span>
					</div>
				</div>
			}@else{
				<!-- Show certifications if available, otherwise display "No Certifications" -->

				@for (cert of userCertifications; track $index) {
					<div class="d-flex align-items-center my-2">
						<i class="fas fa-check-circle text-success me-2" style="position: relative; top: 2px;"></i>
						<strong>{{ cert.cert.Abbreviation }}</strong>
						<span>&nbsp;({{ cert.cert.Name }})</span>
						@for (doc of cert.documents; track $index) {
							<span class="ms-3">
								<button type="button" class="btn btn-link"
								(click)="fetchPresignedUrlForDocument(doc)">{{ doc.FileName }}</button>
							</span>	
						}
					</div>
				}@empty {
					<!-- Message if no certifications are available -->
					<ng-template #noCerts>
						<div class="text-muted">No Certifications</div>
					</ng-template>
				}
			}

		</div>
	</div>
</div>