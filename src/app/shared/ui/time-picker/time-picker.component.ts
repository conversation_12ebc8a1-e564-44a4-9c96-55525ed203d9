import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, forwardRef } from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormsModule, NG_VALUE_ACCESSOR, NgForm, ReactiveFormsModule } from '@angular/forms';
import { NgbDate, NgbModule, NgbTimepicker, NgbTimeStruct } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment';
import { v4 } from 'uuid';

const pad = (i: number): string => i < 10 ? `0${i}` : `${i}`;
@Component({
    selector: 'app-time-picker',
    templateUrl: './time-picker.component.html',
    styleUrls: ['./time-picker.component.css'],
    imports: [CommonModule, NgbModule, NgbTimepicker, FormsModule, ReactiveFormsModule],
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => TimePickerComponent),
            multi: true
        }
    ]
})
export class TimePickerComponent implements OnInit, OnChanges, ControlValueAccessor {

  @Input("required") required: boolean = false;
  @Input() time: NgbTimeStruct | null = null;
  @Input() placeholderTitle: string = "Select Time";
  @Input() name: string = v4();
  timeName: string = v4();
  @Input() disabled: boolean = false;
  @Output() timeChange: EventEmitter<NgbTimeStruct | null> = new EventEmitter<NgbTimeStruct | null>();
  @ViewChild("timepickerelement") timepickerelement: ElementRef | null = null;  
  @ViewChild("timepicker") timepicker: NgbTimepicker | null = null;
  timeDisplay: string | null = null;
  showTimePicker: boolean = false;
  currentId: string = v4();
  currentId2: string = v4();
  rawTime: any;
  defaultNumbers: Array<number> = [];
  control = new FormControl();
  time2: NgbTimeStruct | null = null;

  changeHour(hour: number){
    if(this.time){
      this.time.hour = hour;
    }
    
    this.setDisplayTime();
  }

  onChange = (time: NgbTimeStruct) => {
    this.control.setValue(time);
    this.setDisplayTime();     
  };
  onTouched = () => {
    this.showTimePicker = true;
   };

   setChange(evt:any){  
    this.time = evt;
    this.setDisplayTime();
    }

  constructor() {
    for (let i = 1; i <= 12; i++) {
      this.defaultNumbers.push(i);
    }    
  }
  writeValue(obj: any): void {
    this.time = obj;
    this.control.setValue(obj);
    this.setDisplayTime();
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
    this.control.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
    this.control.valueChanges.subscribe(fn);
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
  ngOnChanges(changes: SimpleChanges): void {
    const { time } = changes;
    if (time) {
      if (time.currentValue != time.previousValue) {
        this.setDisplayTime();
      }
    }
  }

  ngOnInit(): void {
    this.currentId = v4();
    this.currentId2 = v4();

    if (!this.name) {
      this.name = v4();
    }

    this.time2 = { hour: 13, minute: 30, second: 0};
  }
  setDisplayTime() {
    let time = null;
    if (!this.time) {
      this.timeDisplay = null;
    } else {
      time = moment(`${pad(this.time.hour)}:${pad(this.time.minute)}`, ["HH:mm"]).format("hh:mm A");
      this.timeDisplay = time;
      this.control.setValue(this.time);
    }

    this.timeChange.next(this.time);
    if(this.time){
      this.onChange(this.time);
    }


  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    this.showTimePicker = false;
  }

  setHour(number: number) {

    if (!this.time) {
      var sValue = 0;

      if (number >= 1 && number <= 7) {
        sValue += 12;
      } else {
        sValue = 0;
      }

      this.time = {
        hour: number + sValue,
        minute: 0,
        second: 0
      }
    } else {
      this.time = {
        hour: number,
        minute: this.time.minute,
        second: 0
      };
    }


    this.setDisplayTime();
    
  }

  setMinute(minute: number) {
    if (!this.time) {
      this.time = {
        hour: 0,
        minute: 0,
        second: 0
      }
    }

    this.time = {
      hour: this.time.hour,
      minute: minute,
      second: 0
    };
    
    if (this.time.hour) {
      this.showTimePicker = false;
    }
    
    this.setDisplayTime();
  }

  clear() {
    this.time = null;
    this.setDisplayTime();
  }
}
