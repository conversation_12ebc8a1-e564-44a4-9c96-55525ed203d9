.ngb-tb div{
	background-color: aquamarine !important;
}

  .readonly{
	  background-color:rgb(230, 230, 230);
  }

  .box{
	z-index: 1001; 
	position:absolute; 
	background-color: white; 
	padding:5px; 
	border: 1px solid rgb(161, 161, 161); 
	width: 300px;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
	animation-duration: 0.5s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;	
	border-top-left-radius: 0;
	border-bottom-left-radius: 10px;
	border-top-right-radius: 10px;
	border-bottom-right-radius: 10px;
	padding: 20px;
  }

  @-webkit-keyframes fadeIn {
	0% {opacity: 0;}
	100% {opacity: 1;}
 }
 
 @keyframes fadeIn {
	0% {opacity: 0;}
	100% {opacity: 1;}
 }
 
 .fadeIn {
	-webkit-animation-name: fadeIn;
	animation-name: fadeIn;
 }

 .invalid-form-control {
	border-color: #c00000;
	border-width: 3px;
	background-color: #ffdddd;
  }
