<div id="timepickerelement" name="timepickerelement" #timepickerelement (click)="$event.stopPropagation()">
	<div class="input-group">
		<input class="form-control readonly" [id]="timeName" [name]="timeName" [(ngModel)]="timeDisplay"
			[placeholder]="placeholderTitle" autocomplete="off" (keydown)="$event.preventDefault()" [ngClass]="{'invalid-form-control': control.errors?.['required'] && (control?.dirty || control?.touched)}"
			[required]="required">
		<button type="button" class="btn btn-outline-secondary" (click)="showTimePicker = !showTimePicker; $event.stopPropagation()"
			type="button">
			<i class="fa fa-clock" aria-hidden="true"></i>
		</button>
	</div>


	<div id="showtimepickerelement" *ngIf="showTimePicker" class="box fadeIn">		
		<ngb-timepicker #timePicker [id]="name" [name]="name" [formControl]="control" [meridian]="true" [required]="required"></ngb-timepicker>

			<div class="row">
				<div class="col-8">
					<div class="row">
						<div class="col-4" *ngFor="let number of defaultNumbers">
							<button type="button" class="btn btn-light btn-sm w-100 my-1" (click)="setHour(number)">
								{{ number }}
							</button>
						</div>
					</div>
				</div>
				<div class="col-4">
					<button type="button" class="btn btn-primary btn-sm w-100 my-1" (click)="setMinute(00)">
						00
					</button>
					<button type="button" class="btn btn-primary btn-sm w-100 my-1" (click)="setMinute(15)">
						15
					</button>
					<button type="button" class="btn btn-primary btn-sm w-100 my-1" (click)="setMinute(30)">
						30
					</button>
					<button type="button" class="btn btn-primary btn-sm w-100 my-1" (click)="setMinute(45)">
						45
					</button>
				</div>
			</div>
			

		<button type="button" class="btn btn-danger btn-sm m-2 float-start" (click)="clear(); showTimePicker = false">Clear</button>
		<button type="button" class="btn btn-secondary btn-sm m-2 float-end" (click)="showTimePicker = false">Close</button>
	</div>	

</div>