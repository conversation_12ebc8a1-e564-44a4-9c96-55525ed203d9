﻿import { Component, OnInit, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'civcast-confirm',
    templateUrl: './confirm.modal.component.html',
    styleUrls: ['./confirm.css'],
    standalone: false
})
export class ConfirmModalComponent {
  @Input() confirmQuestion: string = "";
  @Input() header:string = "Confirm";
  constructor(private activeModal: NgbActiveModal) {}

  yes() {
    this.activeModal.close('yes');
  }

  no() {
    this.activeModal.close('no');
  }

  cancel() {
    this.activeModal.dismiss('cancel');
  }
}
