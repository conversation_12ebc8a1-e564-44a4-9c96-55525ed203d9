import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, forwardRef } from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormsModule, NG_VALUE_ACCESSOR, NgForm, ReactiveFormsModule } from '@angular/forms';
import moment from 'moment';
import { v4 } from 'uuid';
import { CalendarModule } from 'primeng/calendar';

const pad = (i: number): string => i < 10 ? `0${i}` : `${i}`;
@Component({
    selector: 'app-time-picker-alt',
    templateUrl: './time-picker.component.html',
    styleUrls: ['./time-picker.component.css'],
    imports: [CommonModule, FormsModule, ReactiveFormsModule, CalendarModule],
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => TimePickerAltComponent),
            multi: true
        }
    ]
})
export class TimePickerAltComponent implements OnInit, OnChanges, ControlValueAccessor {

  @Input("required") required: boolean = false;
  @Input() time: Date | null = null;
  @Input() placeholderTitle: string = "Select Time";
  @Input() name: string  = v4();
  timeName: string = v4();
  @Input() disabled: boolean = false;
  @Output() timeChange: EventEmitter<Date | null> = new EventEmitter<Date | null>();
  // @ViewChild("timepickerelement") timepickerelement: ElementRef | null = null;  
  // @ViewChild("timepicker") timepicker: NgbTimepicker | null = null;
  timeDisplay: string | null = null;
  showTimePicker: boolean = false;
  currentId: string = v4();
  currentId2: string = v4();
  rawTime: any;
  defaultNumbers: Array<number> = [];
  control = new FormControl();

  changeHour(hour: number){
    if(this.time){
      //this.time.hour = hour;
      this.time.setHours(hour);
    }else{
      this.time = new Date();
      this.time.setHours(hour);
    }
    
    this.setDisplayTime(this.time);
  }

  onChange = (time: Date) => {
    this.control.setValue(time);
    this.setDisplayTime(time);     
  };
  onTouched = () => {
    this.showTimePicker = true;    
   };

  constructor() {
    for (let i = 1; i <= 12; i++) {
      this.defaultNumbers.push(i);
    }    
  }
  writeValue(obj: any): void {
    let d = null;
    if(obj){
      d = new Date(obj);
    }
    this.time = d;
    this.control.setValue(d);
    this.setDisplayTime(d);
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
    this.control.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
    this.control.valueChanges.subscribe(fn);
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
  ngOnChanges(changes: SimpleChanges): void {
    const { time } = changes;
    if (time) {
      if (time.currentValue != time.previousValue) {
        this.setDisplayTime(time.currentValue);
      }
    }
  }

  setChange(evt:any){  
  this.time = evt;
  this.setDisplayTime(this.time);
  }

  ngOnInit(): void {
    this.currentId = v4();
    this.currentId2 = v4();

    if (!this.name) {
      this.name = v4();
    }

    ///this.time2 = { hour: 13, minute: 30, second: 0};
  }

  public showTimeDisplay(time: Date | null){    
    if(time){
      this.timeDisplay = moment(`${pad(time.getHours())}:${pad(time.getMinutes())}`, ["HH:mm"]).format("hh:mm A");
      this.time = time;
      this.control.setValue(time, {onlySelf: true, emitEvent: false, emitModelToViewChange: false, emitViewToModelChange: false});
    }    
  }

  setAMPM(){
    if(this.time){
      if(this.time.getHours() >= 12){
        this.time.setHours(this.time.getHours() - 12);
      }else{
        this.time.setHours(this.time.getHours() + 12);
      }
    }

    this.setDisplayTime(this.time);
  }

  public setDisplayTime(optionTime: Date | null) {
    let time = optionTime;

    if(optionTime){
      time = optionTime;
    }

    if (!time) {
      this.timeDisplay = null;
    } else {
      if(!(time instanceof Date)){      
        time = new Date(time);  
      }

      this.timeDisplay = moment(`${pad(time.getHours())}:${pad(time.getMinutes())}`, ["HH:mm"]).format("hh:mm A");         

      this.control.setValue(optionTime, {onlySelf: true});
    }

    // this.timeChange.next(this.time);
    
    // if(this.time){
    //   this.onChange(this.time);
    // }


  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    this.showTimePicker = false;
  }

  setHour(number: number) {
    if(this.time){
      this.time.setHours(number);
    }
    
    if (!this.time) {
      var sValue = 0;

      if (number >= 1 && number <= 7) {
        sValue += 12;
      } else {
        sValue = 0;
      }
      this.time = new Date();

      this.time.setHours(number + sValue)
 
    } else {
         this.time.setHours(number)
    }


    this.setDisplayTime(this.time);
    
  }

  setMinute(minute: number) {

    if(this.time){
      this.time.setMinutes(minute);
    }else{
      this.time = new Date();
      this.time.setMinutes(minute);
    }

    
      this.setDisplayTime(this.time);
  }

  clear() {
    this.time = null;
    this.setDisplayTime(this.time);
    this.control.setValue(null);
  }
}
