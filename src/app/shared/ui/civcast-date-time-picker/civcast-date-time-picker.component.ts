import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { DatePickerModule } from 'primeng/datepicker';
import { Popover, PopoverModule } from 'primeng/popover';

@Component({
  selector: 'app-civcast-date-time-picker',
  templateUrl: './civcast-date-time-picker.component.html',
  styleUrls: ['./civcast-date-time-picker.component.css'],
  imports: [CommonModule, FormsModule, ReactiveFormsModule, DatePickerModule,CalendarModule, PopoverModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: CivCastDateTimePicker,      
      multi: true
    }
  ]
})
export class CivCastDateTimePicker implements ControlValueAccessor, OnInit {
  ngOnInit(): void {
    
  }
  setDisabledState?(isDisabled: boolean): void {
    
  }
  @ViewChild('timePanel') timePanel!: Popover;

  date: Date | null = null;
  time: Date | null = null;

  hours = Array.from({ length: 12 }, (_, i) => i + 1); // [1..12]
  minuteOptions = [0, 15, 30, 45];

  selectedHour: number | null = null;
  selectedMinute: number | null = null;
  meridian: 'AM' | 'PM' | null = null;

  private onChange = (_: any) => {};
  private onTouched = () => {};

  writeValue(value: Date | null): void {
    if (value) {
      this.date = new Date(value);
      this.time = new Date(value);
  
      const rawHour = this.time.getHours();
      this.meridian = rawHour >= 12 ? 'PM' : 'AM';
      this.selectedHour = rawHour % 12 || 12;
      this.selectedMinute = this.time.getMinutes();
    } else {
      // Reset internal state if form control is null
      this.date = null;
      this.time = null;
      this.selectedHour = null;
      this.selectedMinute = null;
      this.meridian = null;
    }
  }
  get formattedTime(): string {
    if (!this.date || !this.time) return '';
    const hours = this.time.getHours();
    const minutes = this.time.getMinutes();
    const meridian = hours >= 12 ? 'PM' : 'AM';
    const displayHour = hours % 12 || 12;
    const paddedMinutes = minutes.toString().padStart(2, '0');
    return `${displayHour}:${paddedMinutes} ${meridian}`;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onDateChange(date: Date) {
    this.date = date;

    if (!this.time) {
      this.time = new Date();
      this.time.setHours(14, 0, 0, 0); // 2:00 PM
  
      this.selectedHour = 2;
      this.selectedMinute = 0;
      this.meridian = 'PM';
    }

    this.emitDateTime();
  }

  onTimeChange(timeVal: Date) {
    this.time = timeVal;
    const rawHour = timeVal.getHours();
    this.meridian = rawHour >= 12 ? 'PM' : 'AM';
    this.selectedHour = rawHour % 12 || 12;
    this.selectedMinute = timeVal.getMinutes();
    this.emitDateTime();
  }

  onHourButtonClick(hour: number) {
    if (!this.date) {
      this.date = new Date();
    }

    this.selectedHour = hour;

    if(this.selectedMinute === null) {
      this.selectedMinute = 0;
    }

    if(this.meridian === null) {
      this.meridian = hour >= 12 ? 'PM' : 'AM';
    }

    this.updateTimeFromSelection();
    this.emitDateTime();
  }

  onMinuteButtonClick(minute: number) {
    if (!this.date) {
      this.date = new Date();
    }

    this.selectedMinute = minute;
    this.updateTimeFromSelection();
    this.emitDateTime();
  }

  setMeridian(mer: 'AM' | 'PM') {
    if (!this.date) {
      this.date = new Date();
    }

    this.meridian = mer;
    this.updateTimeFromSelection();
    this.emitDateTime();
  }

  private updateTimeFromSelection() {
    const updatedTime = this.time ? new Date(this.time) : new Date();
    let hour = this.selectedHour ? this.selectedHour % 12 : null;
    if (this.meridian === 'PM') hour ? hour += 12 : hour;
    updatedTime.setHours(hour ?? 0, this.selectedMinute ?? 0);
    this.time = updatedTime;
  }

  private emitDateTime() {
    if (!this.date || !this.time) return;
  
    const combined = new Date(this.date);
    combined.setHours(this.time.getHours(), this.time.getMinutes(), 0, 0);
    this.onChange(combined);
  }

  clear() {
    this.date = null;
    this.time = null as any;
    this.selectedHour = null;
    this.selectedMinute = null;
    this.meridian = null;
  
    this.onChange(null); // Emit null to form
  }
}