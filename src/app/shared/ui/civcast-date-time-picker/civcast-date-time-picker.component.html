<div class="row">
	<div class="col-6">
		<label class="form-label" style="display: block;">Date</label>
		<!-- Date Picker -->
		<p-datepicker
		[(ngModel)]="date"
		[showTime]="false"
		[showIcon]="true"
		(onSelect)="onDateChange($event)"
		placeholder="Select Date">
		</p-datepicker>
	</div>
	<div class="col-6">
		<label class="form-label">Time</label>
		<div class="input-group">
			<input
				type="text"
				[value]="formattedTime"
				readonly
				(click)="timePanel.toggle($event)"
				class="form-control" />
			<!-- Button to trigger time selection dropdown -->
			<button class="btn btn-outline-secondary" type="button" (click)="timePanel.toggle($event)">
				<i class="fa fa-clock" aria-hidden="true"></i>
			</button>
		</div>
	  
		<!-- Time Selection Dropdown -->
		<p-popover #timePanel [dismissable]="true">
		  <div class="time-panel-content">
			<!-- PrimeNG Time Picker (12hr) -->
			<p-datepicker
			  [(ngModel)]="time"
			  [timeOnly]="true"
			  hourFormat="12"
			  (onSelect)="onTimeChange($event)">
			</p-datepicker>
	  
	
			<div class="row mt-2">
				<div class="col-8">
					<div class="row">
						@for(h of hours; track $index){
							<div class="col-4">
								<button type="button" class="btn btn-light btn-sm w-100 my-1" (click)="onHourButtonClick(h)"
								[class.active]="selectedHour === h">
									{{ h }}
								</button>
							</div>
						}
						
					</div>
					<div class="row">
						<div class="col-12">
							<button type="button" class="btn btn-light btn-sm w-100 my-1" (click)="clear()">
								Clear
							</button>
						</div>
					</div>
				</div>
				<div class="col-4">
					@for (m of minuteOptions; track $index) {
						<button type="button" class="btn btn-light btn-sm w-100 my-1" (click)="onMinuteButtonClick(m)" 
						[class.active]="selectedMinute === m">
							{{ m | number:'2.0' }}
						</button>
					}
					<button (click)="setMeridian('AM')" class="btn  btn-sm w-100 my-1" [class.active]="meridian === 'AM'">AM</button>
					<button (click)="setMeridian('PM')" class="btn  btn-sm w-100 my-1" [class.active]="meridian === 'PM'">PM</button>				
				</div>				
			</div>
		  </div>
		</p-popover>
	</div>


  </div>