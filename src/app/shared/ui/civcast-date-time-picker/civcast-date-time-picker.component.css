  
  .time-panel-content {
	display: flex;
	flex-direction: column;
	gap: 1rem;
	padding: 0.5rem;
	width: 300px;
  }
  
  .hour-button-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 0.5rem;
  }
  
  .ampm-toggle {
	display: flex;
	gap: 1rem;
  }
  
  .minute-button-grid {
	display: flex;
	gap: 0.5rem;
	flex-wrap: wrap;
  }
  
  button.active {
	background-color: #007bff;
	color: white;
  }

 ::ng-deep p-datepicker .p-inputtext {
	height: 36px !important;
	line-height: 30px;
  }