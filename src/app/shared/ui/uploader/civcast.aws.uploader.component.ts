﻿import { Component, OnInit, Input, OnDestroy, EventEmitter, Output } from '@angular/core';
import { <PERSON><PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subject, Observable, Subscription } from 'rxjs';
import { MaxFileError } from './maxfile.error';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'civcast-aws-uploader',
    imports: [CommonModule],
    templateUrl: './civcast.aws.uploader.component.html'
})
export class CivCastAWSUploader implements OnDestroy {
  public filesList: Array<File> = new Array<File>();
  private unsubscribe: Subject<void> = new Subject();  
  @Output() selectedFiles = new EventEmitter<FileList>();
  @Output() uploadError = new EventEmitter<Error>();
  @Output() finishUploadButtonEvent = new EventEmitter();
  @Input() allowDragDrop: boolean = false;
  @Input() maxFiles: number = 0;
  @Input("accept-info") acceptfiles: string = "image/png, image/jpeg, image/gif";

  @Input() allowFolderFiles: boolean = true;
  constructor() {}

  onDrop(event: DragEvent) {
    this.preventEvent(event);

    if (!event.dataTransfer || !event.dataTransfer.files) {
      return;
    }

    this.runFiles(event.dataTransfer.files);
  }

  onDragEnter(event: Event) {
    this.preventEvent(event);
  }

  onDragOver(event: any) {
    this.preventEvent(event);
  }

  runFiles(files: FileList) {
    this.selectedFiles.next(files);
  }

  onChange(event: any) {
    let eventObj: any = event;
    let target: HTMLInputElement = <HTMLInputElement>eventObj.target;

    if(target.files){
      let files: FileList = target.files;

      if (this.maxFiles > 0) {
        if (files.length > this.maxFiles) {
          this.uploadError.next(new MaxFileError('Too many files. You are allowed to upload ' + this.maxFiles + ' at a time'));
          return;
        }
      }
  
      this.runFiles(files);
      target.value = '';
    }

    this.executeFinishButton();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  executeFinishButton() {
    this.finishUploadButtonEvent.next(null);
  }

  private preventEvent(event: any): void {
    event.stopPropagation();
    event.preventDefault();
  }
}
