﻿<div *ngIf="allowDragDrop" class="d-none d-md-block d-lg-block mb-3">
  <div
    class="py-5"
    style="border: 1px dashed rgba(0, 0, 0, 0.125);"
    (drop)="onDrop($event)"
    (dragenter)="onDragEnter($event)"
    (dragover)="onDragOver($event)"
  >
      <div class="text-center"><i class="fas fa-camera fa-3x"></i></div>
      <div class="text-center">Drag and drop photos here.</div>
  </div>
</div>
<div>
  <label class="btn btn-outline-dark me-2">
    Choose File
    <input
      #civcastfileuploader
      type="file"
      (change)="onChange($event)"
      multiple
      style="visibility: hidden; max-width: 0;"
      accept="{{acceptfiles}}"
    />
  </label>
  <label class="btn btn-outline-dark" *ngIf="allowFolderFiles">
    Choose Folder
    <input
      #civcastfileuploader
      type="file"
      style="visibility: hidden; max-width: 0;"
      (change)="onChange($event)"
      multiple
      webkitdirectory
      directory
      accept="{{acceptfiles}}"
    />
  </label>
</div>
