<div  [id]="id">	
	<div [hidden]="images()?.length > 0">	
		<p-editor #editorView [style]="{ height: height() }" [id]="quillId" (onTextChange)="textChanged($event)" [formControl]="control" />
	</div>
	@if(images()?.length > 0){
	
	<div class="card mt-2 card ">
		<div class="card-header">
			Adding Image to Editor
		</div>
		<div class="card-body">
			@for (imageInfo of images(); track $index) {
			<div class="col-12">
				<div class="col-3">
					<div style="max-width: 200px">
						<img src="{{ imageInfo.image }}" alt="image" class="img-thumbnail" />
					</div>
				</div>
				<div class="col-9">
					@if(imageInfo?.isTooLarge){
						<div class="alert alert-danger" role="alert">
							Image is too large ({{imageInfo.size | filesize}}). Please upload image with size less than {{maxImageSize()| filesize}}.
						</div>
						<button class="btn btn-danger" (click)="removeImage(imageInfo)">
							<i class="fa fa-trash"></i>
							Delete
						</button>
					}@else{
						<div class="row align-items-center">	
							<div class="col-9 mb-1 mb-md-0 col-md-4">
								<div class="progress" style="height:30px;">
									<div class="progress-bar bg-success progress-bar-striped progress-bar-animated "
										role="progressbar" [style.width]="imageInfo?.uploadProgress + '%'" aria-valuemin="0"
										aria-valuemax="100">
										@if(imageInfo?.isComplete){	
											Complete
										}@else {
										}									
									</div>								
								</div>
								<small>{{imageInfo?.uploadProgress }} / 100</small>
							</div>
							<div class="col-3 mb-1 mb-md-0 col-md-1 text-end">
								@if(imageInfo?.uploadProgress < 100 && imageInfo?.isProgressing)
								{
									<span>
										{{ imageInfo?.uploadProgress }}%
									</span>
									
								}@else if(imageInfo?.uploadProgress >= 100){
									<!-- <span>
										<i class="fa fa-check text-success"></i>									
									</span> -->
								}
								</div>
							</div>
						}
					</div>
				</div>
				}
			</div>
		</div>
	
		}
</div>
