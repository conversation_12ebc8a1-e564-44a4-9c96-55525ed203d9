import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, forwardRef, inject, input, OnChanges, signal, SimpleChanges, ViewChild, viewChild } from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormsModule, NG_VALUE_ACCESSOR, NgForm, ReactiveFormsModule } from '@angular/forms';
import { Editor, EditorModule } from 'primeng/editor';
import Quill from 'quill';
import { Delta, Op } from 'quill/core';
import { LambdaAWSService } from '../../data-access/lambdaaws.service';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { v4 } from 'uuid';
import { FileSize } from '../../utils/pipes/files-pipe';

@Component({
    selector: 'app-custom-quill',
    imports: [CommonModule, FormsModule, ReactiveFormsModule, EditorModule, FileSize],
    templateUrl: './custom-quill.component.html',
    styleUrl: './custom-quill.component.css',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => CustomQuillComponent),
            multi: true
        }
    ]
})
export class CustomQuillComponent implements AfterViewInit, ControlValueAccessor {

  quillEditor = viewChild<Editor>("editorView");
  awsService = inject(LambdaAWSService);
  s3Info = input.required<EditorS3Info>();
  maxImageSize = input<number>(10 * 1024 * 1024);
  s3Prefix = input.required<string>();
  control = new FormControl();
  isDisabled = false;
  issues: string[] | null = null;
  name = input<string>(v4());
  height = input<string>('150px');
  id = v4();
  quillId = v4();
  images = signal<ImageInfo[] | null>(null);
  eventEmitted = input<boolean>(false);
  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  writeValue(obj: any): void {
    this.control.setValue(obj, { emitEvent: this.eventEmitted() });  
  }
  registerOnChange(fn: any): void {
    this.onChange = fn;
    this.control.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
    this.control.valueChanges.subscribe(fn);
  }
  setDisabledState?(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  @ViewChild('editorView', {static: true}) editorView!: Editor;


  ngAfterViewInit(): void {
  }

  exportQuillData(){
    const quill = this.quillEditor()?.getQuill() as Quill;
    console.log(quill.getContents());
    console.log(quill.getSemanticHTML());
    console.log(quill.getText());
    console.log(this.control.value);
  }
  
  textChanged(evt:any){

    evt.delta.ops.forEach((op:any) => {
      if(op.insert && typeof op.insert === 'object' && op.insert.image){        
        console.log("is image"); 

        let imageInfo = new ImageInfo(op.insert.image);

        this.uploadAndCreateImage(imageInfo).subscribe({
          next: (imageInfo:ImageInfo) => {
          
            this.replaceImageInQuill(imageInfo.url as string, op, false);            
            this.addText("");        
            
            setTimeout(() => {
              this.images.update((images) => {        
                return images?.filter((image:ImageInfo) => image.key !== imageInfo.key) as ImageInfo[];
              });
            }, 2000);
            
          },
          error: (err:any) => {
            console.log(err);
          }
        });
      }
    });
  }
  insertTempImage(){

  }

  removeTempImage(){
  }

  removeImage(image: ImageInfo){
    this.images.update((images) => {        
      return images?.filter((img:ImageInfo) => img.key !== image.key) as ImageInfo[];
    });
  }

  addText(text:string){
    const quill = this.quillEditor()?.getQuill() as Quill;
    quill.insertText(quill.getLength(), text, 'user');
  }

  addLink(url:string, text:string){
    const quill = this.quillEditor()?.getQuill() as Quill;
    var delta = {
      ops: [
        {retain: quill.getLength()},
        {insert: text, attributes: {link: url, font: 'serif', size: 'small'}},
      ]
    } as Delta;
    
    quill.updateContents(delta, 'user');
  }

  removeImageInQuill(image:string, emit: boolean = true): void {
    if(!image){
      return;
    }

    const quill = this.quillEditor()?.getQuill() as Quill;            
    if(quill){

      const delta = quill.getContents(0, quill.getLength()); // Get the text to be replaced

      var dd = [...delta.ops];

      let idx = 0;
      for(let item of dd){
        if(item.insert && typeof item.insert === 'object' && item.insert['image']){
          if(item.insert['image'] === image){
            dd.splice(idx, 1);
          }
        }

        idx++;
      }

      if(emit){
        quill.setContents(dd, 'user');  
      }else{
        quill.setContents(dd, 'silent');  
      }
    }
  }

  replaceImageInQuill(url: string, op:any, emit: boolean = true): void {
    if(!url){
      return;
    }

    const quill = this.quillEditor()?.getQuill() as Quill;            
    if(quill){

      const delta = quill.getContents(0, quill.getLength()); // Get the text to be replaced

      var dd = [...delta.ops];

      let idx = 0;
      for(let item of dd){
        if(item.insert && typeof item.insert === 'object' && item.insert['image']){
          if(item.insert['image'] === op.insert.image){
            dd.splice(idx, 1, {
              insert: { image: url },                                            
              attributes: { width: '300' }
            });
          }
        }

        idx++;
      }

      if(emit){
        quill.setContents(dd, 'user');  
      }else{
        quill.setContents(dd, 'silent');  
      }
    }
  }

  uploadAndCreateImage(imageInfo: ImageInfo): Observable<ImageInfo> {

    this.images.set([...(this.images() || []), imageInfo]);

    return new Observable<ImageInfo>((observer) => {
      var fileData = this.getFile(imageInfo.image);
      imageInfo.size = fileData.size;
      if(fileData.size > this.maxImageSize()){
        imageInfo.isTooLarge = true;
        observer.error(`Image size is too large. Max size is ${this.maxImageSize() / 1024 / 1024} MB`);
        this.removeImageInQuill(imageInfo.image, false);
        return;
      }

      const key = `${this.s3Prefix()}/${v4()}-${fileData.name}`;
      var pSignURL = this.awsService.getUploadPresignedUrl(key, fileData.type);
  
      pSignURL.subscribe(url => {
        var headers = new Headers();
        headers.append('Content-Type', fileData.type);
        this.awsService.uploadFileWithSignedURLWithProgress(url.PresignedUrl, fileData).subscribe(
          {
            next: (event) => {
              if (event && typeof event === 'number') {
                this.images.update((images) => {
                  return images?.map((img:ImageInfo) => {
                    if(img.image === imageInfo.image){
                      img.uploadProgress = +event;
                    }
                    return img;
                  }) as ImageInfo[];
                });
              } else if (imageInfo.uploadProgress === 100 && !event) {
               
                this.images.update((images) => {
                  return images?.map((img:ImageInfo) => {
                    if(img.image === imageInfo.image){
                      img.isProgressing = false;
                      img.isComplete = true;
                      img.url = `${environment.QAImages.Location}/${key}`;
                      img.key = key;
                    }
                    return img;
                  }) as ImageInfo[];
                });
       
                
                observer.next(imageInfo);
                observer.complete();              
  
              }
            }, error: (err) => {
              observer.error(err);
              console.log(err);
            }
          })
      }); 
    });
       
  }

  private getFile(base64: string): File {
    const byteString = atob(base64.split(',')[1]);
    const mimeString = base64.split(',')[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    var blob = new Blob([ab], { type: mimeString });
    return new File([blob], `image.${this.getExtensionFromMimeType(mimeString)}`, { type: blob.type });
  }

  private getExtensionFromMimeType(mimeType: string): string {
    const mimeTypes: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/bmp': 'bmp',
      'image/webp': 'webp'
    };
    return mimeTypes[mimeType] || 'png'; // Default to 'png' if MIME type is not found
  }
}

export class EditorS3Info {
  constructor(bucket: string, prefix: string) {
    this.bucket = bucket;
    this.prefix = prefix;
  }
  bucket: string;
  prefix:string;
  
}

export class ImageInfo {
  constructor(image: string) {
    this.image = image;
  }
  image: string;
  isProgressing: boolean = false;
  uploadProgress: number = 0;
  isComplete: boolean = false;
  key: string | null = null;
  url: string | null = null;
  isTooLarge: boolean = false;
  size: number = 0;
}