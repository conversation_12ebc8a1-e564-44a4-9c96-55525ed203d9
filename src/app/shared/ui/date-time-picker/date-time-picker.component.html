<div class="input-group">
	<input class="form-control readonly" [footerTemplate]="footerTemplate"
		[placeholder]="placeholderTitle" [name]="dateName" [id]="dateName" (dateSelect)="onDateSelect($event)"
		ngbDatepicker #d="ngbDatepicker" (change)="executeFilter()" [formControl]="control"
		[ngClass]="{'invalid-form-control': control.errors?.['required'] && (control?.dirty || control?.touched)}"
		[required]="required" (keydown)="$event.preventDefault();" autocomplete="off">
	<button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
		<i class="fa fa-calendar" aria-hidden="true"></i>
	</button>
</div>

<ng-template #footerTemplate>
	<hr class="my-0">
	<button type="button" class="btn btn-primary btn-sm m-2 float-start" (click)="today(); d.close()">Today</button>
	<button type="button" class="btn btn-danger btn-sm m-2 float-start" (click)="clearDate(); d.close()">Clear</button>
	<button type="button" class="btn btn-secondary btn-sm m-2 float-end" (click)="d.close()">Close</button>
</ng-template>