import { CommonModule } from '@angular/common';
import { Component, EventEmitter, forwardRef, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormsModule, NG_VALUE_ACCESSOR, NgForm, ReactiveFormsModule } from '@angular/forms';
import { NgbDate, NgbDatepickerModule, NgbDateStruct, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { bool } from 'aws-sdk/clients/signer';
import { v4 } from 'uuid';
import { MomentDateFormatterService } from '../../data-access/moment-date-formatter-service';

@Component({
    selector: 'app-date-time-picker',
    templateUrl: './date-time-picker.component.html',
    styleUrls: ['./date-time-picker.component.css'],
    imports: [CommonModule, FormsModule, NgbDatepickerModule, ReactiveFormsModule],
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => DateTimePickerComponent),
            multi: true
        }
    ]
})
export class DateTimePickerComponent implements OnInit, OnChanges, ControlValueAccessor {
  selectedDate: NgbDateStruct | null = null;
  @Input() placeholderTitle: string = "Select Date";
  @Input() required: bool = false;
  @Input() name: string = v4();
  currentId: string;
  dateName: string = v4();
  @Input() date: NgbDate | null = null;
  @Output() dateChange: EventEmitter<NgbDateStruct | null> = new EventEmitter<NgbDateStruct | null>();
  @Output("on-date-change") onDateChange: EventEmitter<NgbDateStruct | null> = new EventEmitter<NgbDateStruct | null>();
  @Input() disabled: boolean = false;
  control = new FormControl();
  
  onChange = (date: NgbDate) => {
    this.date = date;
    this.control.setValue(date);
  };
  onTouched = () => {};

  constructor(
    public formatter: MomentDateFormatterService) {
    this.currentId = v4();
  }
  writeValue(obj: any): void {
    let d = null;
    if (obj) {
      d = new NgbDate(obj.year, obj.month, obj.day);
    }

    this.date = d;      
    this.control.setValue(d);      
  }
  
  registerOnChange(fn: any): void {
    this.onChange = fn;
    this.control.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
    this.control.valueChanges.subscribe(fn);
  }
  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['date']) {
      this.date = changes['date'].currentValue;
      this.control.setValue(this.date);
    }
  }

  ngOnInit(): void {
    if (!this.name) {
      this.name = v4();
    }
  }

  onDateSelect(dateInfo: NgbDate) {
    this.dateChange.emit(dateInfo);
    this.onDateChange.emit(dateInfo);
  }

  executeFilter() {

  }

  today() {
    var cDate = new Date();
    var nDate = new NgbDate(cDate.getFullYear(), cDate.getMonth() + 1, cDate.getDate());
    this.date = nDate;
    this.dateChange.emit(nDate);
    this.onDateChange.emit(nDate);
  }

  clearDate() {
    this.date = null;
    this.dateChange.emit(null);
    this.onDateChange.emit(null);
  }
}
