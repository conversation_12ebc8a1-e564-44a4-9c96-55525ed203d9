import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, forwardRef } from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormsModule, NG_VALUE_ACCESSOR, NgForm, ReactiveFormsModule } from '@angular/forms';
import { DatePickerModule } from 'primeng/datepicker';
import { v4 } from 'uuid';

@Component({
    selector: 'app-prime-calendar-custom',
    templateUrl: './prime-calendar-custom.component.html',
    styleUrls: ['./prime-calendar-custom.component.css'],
    imports: [CommonModule, FormsModule, ReactiveFormsModule, DatePickerModule],
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => PrimeCalendarCustomComponent),
            multi: true
        }
    ]
})
export class PrimeCalendarCustomComponent implements ControlValueAccessor {
@Output() dateChange = new EventEmitter<string | null>();
@Input() name = v4();
@Input() hoursToAddOnSelection = 0;
  control = new FormControl();
  isDisabled = false;

  writeValue(value: any): void {
    let d = null;
   if(value){
      d = new Date(value);
   }
    this.control.setValue(d);
  }

  registerOnChange(fn: any): void {
    this.control.valueChanges.subscribe(fn);
  }

  registerOnTouched(fn: any): void {
    this.control.valueChanges.subscribe(fn);
  }
  constructor() { }

  bindFilter(event: any) {  
    var newDate = new Date(event);
    newDate.setHours(newDate.getHours() + this.hoursToAddOnSelection);
    this.dateChange.emit(newDate.toISOString());
    this.control.setValue(newDate);
  }

  setDisabledState?(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  blur(){
    this.dateChange.emit(this.control.value);
  }
  clear(){
    this.control.setValue(null);
    this.dateChange.emit(null);
  }

}
