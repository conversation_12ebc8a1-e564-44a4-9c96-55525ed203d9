/* .p-datepicker table td {
    padding: 0.2rem !important;
  }
  
 .p-datepicker table td > span {
    width: 1.7rem;
    height: 1.7rem;
  }  
  
.p-datepicker table {
    font-size: 10pt;
    margin: -0.143rem 0;
  }

  ::ng-deep .p-inputtext {
    padding: 0.4rem;
  }

  ::ng-deep .p-datepicker .p-datepicker-header {
    padding: 0.4rem;
  }

 ::ng-deep .p-datepicker-title button {
   margin-left: 5px;
  }
   */
  /* ::ng-deep .p-datepicker-buttonbar{
    padding: 5px;
  }

  ::ng-deep .p-datepicker-buttonbar button{
    padding: 5px;
  }
 */

  /* ::ng-deep .p-button{
    color: #212529;
    border: 1px solid #ced4da;
    background: #ced4da;

  }

  ::ng-deep .p-button:enabled:hover {
    color: #ced4da;
    border: 1px solid #adb5bd;
    background: #212529;
  }

  ::ng-deep .p-calendar{
    height: 40px;
  } */