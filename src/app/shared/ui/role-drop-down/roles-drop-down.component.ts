import { Component, inject, signal } from '@angular/core';
import { MultiSelectModule } from 'primeng/multiselect';
import { RoleDropdownService } from '../../data-access/role-dropdown.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-roles-drop-down',
  imports: [CommonModule, FormsModule, MultiSelectModule],
  templateUrl: './roles-drop-down.component.html',
  styleUrl: './roles-drop-down.component.css'
})
export class RolesDropDownComponent {
  roleDropdownService = inject(RoleDropdownService);
  roles = this.roleDropdownService.roles;
  isLoading = this.roleDropdownService.isLoading;
  selectedRole:string = '';  
  router = inject(Router);
  aRouter = inject(ActivatedRoute);

  constructor() {
    this.roleDropdownService.initializeTeamDropDown.set(true);

    this.aRouter.queryParams.subscribe(params => {
      if (params['role']) {
        this.selectedRole = params['role'];
      } else {
        this.selectedRole = '';
      }
    });
  }

  onItemSelect(role: string | null) {
    let fRole = role;
    if(!fRole) {
      fRole = null;
    }

    this.router.navigate([], {
      queryParams: { page: 1, role: fRole },
      queryParamsHandling: 'merge'
    });    

    console.log(event);
  }
}
