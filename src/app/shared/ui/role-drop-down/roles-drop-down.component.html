<!-- <p-multiSelect size="small" [options]="roles()" optionLabel="Title" [(ngModel)]="selectedRoles"
	placeholder="Select Projects" [filter]="true" filterPlaceholder="Search..." [showToggleAll]="false"
	(onChange)="onItemSelect($event)" style="width: 100%;" [loading]="isLoading()"
	[selectedItemsLabel]="'{0} projects selected'" [showToggleAll]="true" [maxSelectedLabels]="1" [filterBy]="'Name'">
	
	<ng-template let-role pTemplate="item">
		<div class="d-flex align-items-center">
			<span class="me-2">{{ role.Name }}</span>
		</div>
	</ng-template>
</p-multiSelect> -->
	
<label for="rolesDropDown" class="form-label">Roles</label>

	<select id="rolesDropDown"  [(ngModel)]="selectedRole" (ngModelChange)="onItemSelect($event)" name="rolesDropDown" class="form-select">
	<option [value]=""></option>
		
	@for (item of roles(); track $index) {
		<option [value]="item.Id">
			{{ item.Name }}
		</option>	
	} -->
</select>