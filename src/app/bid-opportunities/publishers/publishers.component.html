<div class="container p-4 mb-4">
    <!-- page title -->
    <h1 class="fs-5 mb-3">Publishers</h1>
    <!-- search -->
    <section class="col-12 col-md-6 col-xl-4 mb-3">
        <div class="input-group">
            <span class="input-group-text" id="basic-addon1">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="Search by organization name" [value]="searchText"
                (input)="onSearchChange($event.target.value)" />
        </div>
    </section>
<!-- publishers -->
    <!-- publishers -->
    <section class="card p-3">
        <div class="row">
                @if (isLoading()) {
                @for (i of [1, 2, 3]; track i) {
                <div class="col-12 col-md-4 mb-3">
                    <div class="placeholder-glow mb-3 ">
                        <span class="placeholder w-100"></span>
                    </div>
                    <div class="placeholder-glow mb-3 ">
                        <span class="placeholder w-100"></span>
                    </div>
                    <div class="placeholder-glow mb-3 ">
                        <span class="placeholder w-100"></span>
                    </div>
                </div>
                }
                }@else {
                @if (publishers()?.length === 0) {
                <div class="col-12">
                    <div class="alert alert-info" role="alert">
                            No publishers.
                        </div>
                </div>
                } @else {
                <div class="col-12 col-lg-4 d-flex flex-column">
                    @for (publisher of column1Publishers(); track publisher.PublisherName) {
                    <div class="d-flex flex-column">
                        <a href="javascript:void(0)" class="publisher-link display-block text-truncate mb-3"
                            (click)="navigateToPublisherProjects(publisher.PublisherName)">
                            {{ publisher.PublisherName }}
                        </a>
                    </div>
                    }
                </div>
                <div class="col-12 col-lg-4 d-flex flex-column">
                    @for (publisher of column2Publishers(); track publisher.PublisherName) {
                    <div class="d-flex flex-column">
                        <a href="javascript:void(0)" class="publisher-link display-block text-truncate mb-3"
                            (click)="navigateToPublisherProjects(publisher.PublisherName)">
                            {{ publisher.PublisherName }}
                        </a>
                    </div>
                    }
                </div>
                <div class="col-12 col-lg-4 d-flex flex-column">
                    @for (publisher of column3Publishers(); track publisher.PublisherName) {
                    <div class="d-flex flex-column">
                        <a href="javascript:void(0)" class="publisher-link display-block text-truncate mb-3"
                            (click)="navigateToPublisherProjects(publisher.PublisherName)">
                            {{ publisher.PublisherName }}
                        </a>
                    </div>
                    }
                </div>
                }
                }
            </div>
    </section>
</div>