import { Component, OnInit, computed, effect, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { PublishersService } from 'src/app/account/shared/data-access/publishers.service';
import { Publisher } from 'src/app/account/shared/interfaces/publishers.model';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'app-publishers',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './publishers.component.html',
    styleUrls: ['./publishers.component.css']
})
export class PublishersComponent implements OnInit {
    private publishersService = inject(PublishersService);
    private router = inject(Router);
    private toastr = inject(ToastrService);

    publishers = this.publishersService.publishers;
    filteredPublishers = signal<Publisher[]>([]);
    column1Publishers = signal<Publisher[]>([]);
    column2Publishers = signal<Publisher[]>([]);
    column3Publishers = signal<Publisher[]>([]);

    searchText: string = '';
    isLoading = this.publishersService.isLoading;
    isSaving = false;

    ngOnInit(): void {        
        this.publishersService.initializePublishers.set(true);
    }

    constructor() {
        effect(()=> {            
            const columnSize = Math.ceil(this.filteredPublishers().length / 3);
            const publishers1 = this.filteredPublishers().slice(0, columnSize);
            const publishers2 = this.filteredPublishers().slice(columnSize, columnSize * 2);
            const publishers3 = this.filteredPublishers().slice(columnSize * 2, columnSize * 3);

            this.column1Publishers.set(publishers1);
            this.column2Publishers.set(publishers2);
            this.column3Publishers.set(publishers3);
        });

        effect(() => {
            this.filteredPublishers.set(this.publishersService.publishers() || []);
        });
    }



    navigateToPublisher(publisherId: string): void {
        if (!publisherId) {
            console.error('No publisher ID provided for navigation.');
            return;
        }
        this.router.navigate(['/publishers', publisherId]);
    }

    getColumnPublishers(columnIndex: number): Publisher[] {
        const columnSize = Math.ceil(this.filteredPublishers.length / 3);
        const startIndex = columnIndex * columnSize;
        const endIndex = startIndex + columnSize;
        return this.filteredPublishers()?.slice(startIndex, endIndex) as Publisher[];
    }

    trackByPublisherId(index: number, publisher: Publisher): string {
        return publisher.PublisherId;
    }

    onSearchChange(value: string): void {
        this.searchText = value;
        this.filterPublishers();
    }

    filterPublishers(): void {
        const lowerCaseSearchText = this.searchText.toLowerCase();
        const filtered = this.publishers()?.filter((publisher) =>
            publisher.PublisherName.toLowerCase().includes(lowerCaseSearchText)
        );

        this.filteredPublishers.set(filtered || []);
    }

    navigateToPublisherProjects(publisherName: string): void {
        if (!publisherName) {
            console.error('No publisher name provided for navigation.');
            return;
        }

        this.router.navigate(['/bid-opportunities/publishers/projects'], {
            queryParams: { name: publisherName },
        }).catch((err) => console.error('Navigation error:', err));
    }
}