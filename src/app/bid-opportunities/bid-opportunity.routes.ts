import { Route } from '@angular/router';
import { BidOpportunityComponent } from './bid-opportunity.component';
import { check<PERSON><PERSON><PERSON><PERSON>equired } from '../shared/utils/guards/activations'
import { EBidProjectService } from '../bid-adv/shared/data-access/bids-ebid-project.service';
import { BidBidderFolderService } from './shared/data-access/bid-bidderfolder.service';
import { EBidService } from '../ebid/data-access/ebid.service';
import { RequiredDownloadsService } from '../ebid/data-access/required-downloads.service';
import { BidOverService } from './shared/data-access/bid-over.service';
import { BidsHistoryService } from '../bid-adv/shared/data-access/bids-history-service';
import { BidOpsProjectsService } from './shared/data-access/bid-ops-projects-service';
import { BidOpsDocumentService } from './shared/data-access/bid-ops-document.service';
import { DownloadHistoryService } from './shared/data-access/download-history.service';
import { EBidFolderService } from '../ebid/data-access/bid-folder.service';
import { AddendaAcknowledgeService } from '../ebid/data-access/addenda-acknowledge.service';
import { BidFormService } from '../ebid/data-access/bidform-service';
import { CompletionTimeService } from '../ebid/data-access/completion-time.service';
import { RequiredUploadService } from '../ebid/data-access/required-uploads.service';
import { WorkOrderService } from '../ebid/data-access/work-order-service';
import { AcknowledgeService } from '../ebid/data-access/acknowledge.service';
import { EBidHistoryService } from '../ebid/data-access/ebid-history.service';
import { BidAdvService } from '../bid-adv/shared/data-access/bid.service';
export const BID_OPPORTUNITIES_ROUTES: Route[] = [
	{ path: '', redirectTo: 'projects', pathMatch: 'full' },
	{
		path: '',
		providers: [BidOpsProjectsService],
		component: BidOpportunityComponent,
		children: [
			{
				path: 'projects',
				loadComponent: () => import('./bids/bids.component').then(m => m.BidOpportunitiesComponent)
			},
			{
				path: 'projects/:projectId',
				providers: [],
				loadComponent: () => import('./bid/bid.component').then(m => m.BidComponent),
				children: [
					{
						path: '',
						pathMatch: 'full',
						redirectTo: 'summary'
					},
					{
						path: 'summary',
						loadComponent: () => import('./bid/summary/summary.component').then(m => m.SummaryComponent)
					},
					{
						path: 'documents',
						loadComponent: () => import('./bid/documents/documents.component').then(m => m.DocumentsComponent)
					},
					{
						path: 'planholders',
						loadComponent: () => import('./bid/planholders/planholders.component').then(m => m.PlanholdersComponent)
					},
					{
						path: 'qa',
						loadComponent: () => import('./bid/qa/qa.component').then(m => m.QaComponent)
					},
					{
						path: 'e-bid',
						loadComponent: () => import('./bid/e-bid/e-bid.component').then(m => m.EBidComponent),
						children: [
							{
								path: '',
								pathMatch: 'full',
								redirectTo: 'bid-folder'
							},
							{
								path: 'bid-folder',
								loadComponent: () => import('./bid/e-bid/bid-folder/bid-folder.component').then(m => m.BidFolderComponent)
							},
							{
								path: 'bid-over',
								loadComponent: () => import('./bid/e-bid/bid-over/bid-over.component').then(m => m.BidOverComponent)
							}
						]
					}
				]
			},
			{
				path: 'download-history',
				canActivate: [checkLoginRequired],
				loadComponent: () => import('../account/download-history/download-history.component').then(m => m.DownloadHistoryComponent)
			},
			{
				path: 'project-not-found',
				loadComponent: () => import('./project-not-found/project-not-found.component').then(m => m.ProjectNotFoundComponent)

			},
			{
				path: 'project-not-visible',
				loadComponent: () => import('./project-not-visible/project-not-visible.component').then(m => m.ProjectNotVisibleComponent)

			},
			{
				path: 'private-login',
				loadComponent: () => import('./private-project-login/private-project-login.component').then(m => m.PrivateProjectLoginComponent)
			},
			{
				path: 'users/:userId',
				loadComponent: () => import('../shared/ui/civcast-profile/civcast-profile.component').then(m => m.CivcastProfileComponent)
			},
			{
				path: 'favorites',
				canActivate: [checkLoginRequired],
				loadComponent: () => import('./favorites/favorites.component').then(m => m.FavoritesComponent)
			},
			{
				path: 'publishers',
				loadComponent: () => import('./publishers/publishers.component').then(m => m.PublishersComponent)
			},
			{
				path: 'publishers/projects',
				loadComponent: () => import('./publisher/publisher.component').then((m) => m.PublisherComponent)
			},
		]
	}
];