import { Component, inject, OnInit, signal } from '@angular/core';
import { PublishersService } from 'src/app/account/shared/data-access/publishers.service';
import { PublisherProjectsComponent } from './ui/publisherProjects/publisherProjects.component';
import { ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-publisher',
  imports: [PublisherProjectsComponent, FormsModule],  
  templateUrl: './publisher.component.html',
  styleUrl: './publisher.component.css'
})
export class PublisherComponent {

  publishersService = inject(PublishersService);
  aRoute = inject(ActivatedRoute);
  isLoading = this.publishersService.isLoading;
  publisherName = this.publishersService.publisherName;
  searchText = this.publishersService.searchText;
  constructor() {
    this.aRoute.queryParams.subscribe(queryParams => {
      if (queryParams['name']){
        const publisherName = decodeURIComponent(queryParams['name']);
        this.publishersService.publisherProfileName.set(publisherName);
      }else if (queryParams['id']){
        const id = decodeURIComponent(queryParams['id']);
        this.publishersService.publisherId.set(id);
      }
    });
   
  }

  
  filterProjects(text: string): void {
    this.publishersService.searchText.set(text);
  }

}
