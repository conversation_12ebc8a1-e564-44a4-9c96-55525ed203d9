<!-- Project List -->
<section>
	<table class="table">
		<thead>
			<tr class="d-none d-lg-table-row">
				<th scope="col">
					<span class="text-nowrap clickable" (click)="setSortColumn('BidDetailsBidDate')">
						<u>Bid Date</u>
						@if (sortColumn() === 'BidDetailsBidDate') {
						@if (sortDirection() === 'asc') {
						<i class="fas fa-caret-up"></i>
						} @else {
						<i class="fas fa-caret-down"></i>
						}
						}
					</span>
				</th>
				<th scope="col">
					<span class="text-nowrap clickable" (click)="setSortColumn('BidDetailsState')">
						<u>State</u>
						@if (sortColumn() === 'BidDetailsState') {
						@if (sortDirection() === 'asc') {
						<i class="fas fa-caret-up"></i>
						} @else {
						<i class="fas fa-caret-down"></i>
						}
						}
					</span>
				</th>
				<th scope="col">
					<span class="text-nowrap clickable" (click)="setSortColumn('BidDetailsCounty')">
						<u>County</u>
						@if (sortColumn() === 'BidDetailsCounty') {
						@if (sortDirection() === 'asc') {
						<i class="fas fa-caret-up"></i>
						} @else {
						<i class="fas fa-caret-down"></i>
						}
						}
					</span>
				</th>
				<th scope="col">
					<span class="text-nowrap clickable" (click)="setSortColumn('ProjectTitle')">
						<u>Project Name</u>
						@if (sortColumn() === 'ProjectTitle') {
						@if (sortDirection() === 'asc') {
						<i class="fas fa-caret-up"></i>
						} @else {
						<i class="fas fa-caret-down"></i>
						}
						}
					</span>
				</th>
			</tr>
		</thead>
		<tbody>
			<!-- loading -->
			@if (isLoading()) {
			@for (i of [1, 2, 3, 4, 5]; track i) {
			<tr>
				<td class="placeholder-glow">
					<span class="placeholder w-100"></span>
				</td>
				<td class="placeholder-glow">
					<span class="placeholder w-100"></span>
				</td>
				<td class="placeholder-glow">
					<span class="placeholder w-100"></span>
				</td>
				<td class="placeholder-glow">
					<span class="placeholder w-100"></span>
				</td>
			</tr>
			}
			}@else{
			<!-- projects -->
			@for (project of filteredProjects(); track project.ProjectId) {
			<tr>
				<td class="d-lg-none">
					<div class="mb-1 fw-bold">{{ formatDate(project.BidDetailsBidDate) }}</div>
					<div class="mb-1">{{ project.BidDetailsCounty }}, {{ project.BidDetailsState }}</div>
					<div (click)="navigateToProject(project.ProjectId)">{{ project.ProjectTitle }}</div>
				</td>
				<td class="d-none d-lg-table-cell align-middle">{{ formatDate(project.BidDetailsBidDate) }}</td>
				<td class="d-none d-lg-table-cell align-middle">{{ project.BidDetailsState }}</td>
				<td class="d-none d-lg-table-cell align-middle">{{ project.BidDetailsCounty }}</td>
				<td class="d-none d-lg-table-cell align-middle">
					<a (click)="navigateToProject(project.ProjectId)">{{project.ProjectTitle }}</a>
				</td>
			</tr>
			}@empty {
			<tr>
				<td colspan="4">
					<div class="alert alert-info m-0" role="alert">
						No projects found.
					</div>
				</td>
			</tr>
			}
			}
		</tbody>
	</table>
</section>