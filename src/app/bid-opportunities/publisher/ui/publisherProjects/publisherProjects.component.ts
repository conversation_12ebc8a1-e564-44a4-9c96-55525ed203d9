import { Component, OnInit, computed, effect, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { PublishersService } from 'src/app/account/shared/data-access/publishers.service';
import { PublisherProject, Publisher } from 'src/app/account/shared/interfaces/publishers.model';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-publisher-projects',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './publisherProjects.component.html',
  styleUrls: ['./publisherProjects.component.css'],
})
export class PublisherProjectsComponent {
  private publishersService = inject(PublishersService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  sortColumn = this.publishersService.sortColumn;
  sortDirection = this.publishersService.sortDirection;
  publisherName = this.publishersService.publisherName;
  projectId: string = '';
  isLoading = this.publishersService.isLoading;
  projects = this.publishersService.publisher

  filteredProjects = this.publishersService.filteredProjects;

  constructor() {

  }

  setSortColumn(column: string): void {
    if (this.sortColumn() === column) {
      this.publishersService.sortDirection.set(this.sortDirection() === 'asc' ? 'desc' : 'asc');
    } else {
      this.publishersService.sortColumn.set(column);
      this.publishersService.sortDirection.set('asc');
    }
  }

  navigateToProject(projectId: string): void {
    this.router.navigate(['/bid-opportunities/projects', projectId, 'summary']);
  }

  formatDate(date: string | null): string {
    if (!date) return 'TBA';
    const parsedDate = new Date(date);
    return parsedDate.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
    });
  }
}