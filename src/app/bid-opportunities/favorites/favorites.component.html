<div class="container p-4 mb-4">
    <!-- header -->
    <h1 class="fs-5 mb-3"><i class="fas fa-lg fa-star text-warning me-2"></i>Favorites</h1>
    <!-- search -->
    <section class="row mb-3">
        <div class="col-12 col-md-6 col-xl-4 mb-3 mb-md-0">
            <div class="input-group">
                <span class="input-group-text" id="basic-addon1">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" placeholder="Search by Project Name or County"
                    [value]="searchText" (input)="onSearchChange($event.target.value)" />
            </div>
        </div>
        <div class="col-12 col-md-6 col-xl-4">
            <app-date-time-picker placeholderTitle="Filter by Bid Date" (on-date-change)="onBidDateChange($event)"
                [date]="bidDateFilter" [(date)]="bidDateFilter">
            </app-date-time-picker>
        </div>
    </section>
    <!-- favorites -->
    <section class="mb-3">
        <table class="table">
            <thead>
                <tr class="d-none d-lg-table-row">
                    <th scope="col">
                        <a href="#" class="fw-bold text-dark text-nowrap">Bid Date</a>
                    </th>
                    <th scope="col">
                        <a href="#" class="fw-bold text-dark text-nowrap">State</a>
                    </th>
                    <th scope="col">
                        <a href="#" class="fw-bold text-dark text-nowrap">County</a>
                    </th>
                    <th scope="col">
                        <a href="#" class="fw-bold text-dark text-nowrap">Name</a>
                    </th>
                    <th scope="col">
                    </th>
                </tr>
            </thead>
            <tbody>
                <!-- loading -->
                @if (isLoading) {
                @for (i of [1, 2, 3]; track i) {
                <tr>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                </tr>
                }
                } @else {
                <!-- no favorites -->
                @if(filteredFavorites.length === 0) {
                <tr>
                    <td colspan="5">
                        <div class="alert alert-info m-0" role="alert">
                            You don't have any favorites yet.
                        </div>
                    </td>
                </tr>
                }
                @if(filteredFavorites.length > 0) {
                @for (favorite of filteredFavorites; track favorite.ProjectId) {
                <tr>
                    <td class="d-lg-none">
                        <div class="mb-1 fw-bold">{{ favorite.BidDetailsBidDate }}</div>
                        <div class="mb-1">{{ favorite.BidDetailsCounty }}, {{ favorite.BidDetailsState }}</div>
                        <div class="mb-1">
                            <a [routerLink]="['/bid-opportunities/projects', favorite.ProjectId]"
                            [queryParams]="{ privateId: favorite.ProjectUniqueId, 'return-url': '/bid-opportunities/favorites' }">
                            {{ favorite.ProjectTitle }}
                        </a>
                        </div>
                        <div class="mb-1">
                            <button class="btn btn-outline-danger"
                            (click)="removeFavorite(favorite.ProjectId); $event.stopPropagation()"
                            [disabled]="isSaving">
                            @if (isSaving) {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            } Remove
                        </button>
                        </div>
                    </td>
                    <td class="d-none d-lg-table-cell align-middle">{{ favorite.BidDetailsBidDate }}</td>
                    <td class="d-none d-lg-table-cell align-middle">{{ favorite.BidDetailsState }}</td>
                    <td class="d-none d-lg-table-cell align-middle">{{ favorite.BidDetailsCounty }}</td>
                    <td class="d-none d-lg-table-cell align-middle">
                        <a [routerLink]="['/bid-opportunities/projects', favorite.ProjectId]"
                            [queryParams]="{ privateId: favorite.ProjectUniqueId, 'return-url': '/bid-opportunities/favorites' }">
                            {{ favorite.ProjectTitle }}
                        </a>
                    </td>
                    <td class="d-none d-lg-table-cell align-middle text-lg-end">
                        <button class="btn btn-outline-danger"
                            (click)="removeFavorite(favorite.ProjectId); $event.stopPropagation()"
                            [disabled]="isSaving">
                            @if (isSaving) {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            } Remove
                        </button>
                    </td>
                </tr>
                }
                }
                }
            </tbody>
        </table>
    </section>
    <!-- footer -->
    <footer class="d-flex justify-content-end">
        [Insert Pagination]
    </footer>
</div>