import { Component, OnInit, inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { FavoritesService } from 'src/app/account/shared/data-access/favorites.service';
import { Favorite } from 'src/app/account/shared/interfaces/favorites.model';
import { DateTimePickerComponent } from 'src/app/shared/ui/date-time-picker/date-time-picker.component'; // Adjust the import path as needed
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-favorites',
  standalone: true,
  imports: [CommonModule, DateTimePickerComponent, RouterLink],
  templateUrl: './favorites.component.html',
  styleUrls: ['./favorites.component.css'],
})
export class FavoritesComponent implements OnInit {
  favorites: Favorite[] = [];
  filteredFavorites: Favorite[] = [];
  searchText: string = '';
  bidDateFilter: NgbDateStruct | null = null; 
  isLoading = true; // Track loading state
  isSaving = false; // Track saving state

  private favoritesService = inject(FavoritesService);
  private router = inject(Router);
  private cdr = inject(ChangeDetectorRef);
  private toastr = inject(ToastrService);
  
  ngOnInit(): void {
    this.loadFavorites();
  }

  private loadFavorites(): void {
    this.isLoading = true;
    this.favoritesService.getFavorites().subscribe({
      next: (favorites) => {
        console.log('Loaded favorites', favorites);
        this.favorites = favorites;
        this.filteredFavorites = favorites;
        this.isLoading = false;
        this.cdr.detectChanges(); 
      },
      error: (err) => {
        console.error('Failed to load favorites', err);
        this.isLoading = false;
      },
    });
  }

  removeFavorite(projectId: string): void {
    this.isSaving = true;
    this.favoritesService.removeFavorite(projectId).subscribe({
      next: () => {
        console.log('Project removed from favorites successfully!');
        this.favorites = this.favorites.filter(fav => fav.ProjectId !== projectId);
        this.filterFavorites(); 
        this.toastr.success('Project removed from favorites'); 
        this.isSaving = false;
      },
      error: (err) => {
        console.error('Failed to remove project from favorites', err);
        this.isSaving = false;
      },
    });
  }

  filterFavorites(): void {
    const lowerCaseSearchText = this.searchText.toLowerCase();
    const bidDateString =
      this.bidDateFilter &&
      `${this.bidDateFilter.year}-${String(this.bidDateFilter.month).padStart(2, '0')}-${String(this.bidDateFilter.day).padStart(2, '0')}`;
    
    this.filteredFavorites = this.favorites.filter((favorite) => {
      const matchesSearch =
        !this.searchText ||
        favorite.ProjectTitle.toLowerCase().includes(lowerCaseSearchText) ||
        favorite.BidDetailsCounty.toLowerCase().includes(lowerCaseSearchText);

      const matchesBidDate =
        !this.bidDateFilter || 
        favorite.BidDetailsBidDate === bidDateString; 

      return matchesSearch && matchesBidDate;
    });
  }

  onSearchChange(value: string): void {
    this.searchText = value;
    this.filterFavorites();
  }

  onBidDateChange(value: NgbDateStruct | null): void {
    if (!value) {
      this.bidDateFilter = null; 
    } else {
      this.bidDateFilter = value; 
    }
    this.filterFavorites();
  }

  navigateToProject(projectId: string): void {
    if (!projectId) {
      console.error('No project ID provided for navigation.');
      return;
    }
    this.router.navigate(['/bid-opportunities/projects', projectId, 'summary']);
  }

  trackByProjectId(index: number, favorite: Favorite): string {
    return favorite.ProjectId;
  }
}