import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { DevBarComponent } from "../shared/ui/dev-bar/dev-bar.component";
import { BidOpsPrivateProjectService } from './shared/data-access/bid-private-project.service';
import { BidOpsProjectsService } from './shared/data-access/bid-ops-projects-service';


@Component({
    selector: 'app-bid-opportunity',
    templateUrl: './bid-opportunity.component.html',
    styleUrls: ['./bid-opportunity.component.css'],
    imports: [CommonModule, RouterModule, DevBarComponent]
})
export class BidOpportunityComponent implements OnInit {

    aRouter = inject(ActivatedRoute);
    bidOpsProjectService = inject(BidOpsProjectsService);

    constructor() { 

    }
    ngOnInit(): void {
        this.bidOpsProjectService.initialize.set(true);
    }


}
