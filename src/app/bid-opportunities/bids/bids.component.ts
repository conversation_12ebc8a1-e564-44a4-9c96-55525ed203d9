import { Component, OnInit, inject, effect, OnDestroy, computed, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {  NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { BidOpsProjectsService } from '../shared/data-access/bid-ops-projects-service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BidOpportunitiesFilterComponent } from './ui/bid-opportunities-filter/bids-filter.component';
import { BidOpsGridInfo} from '../shared/interfaces/bid';
import { SortIconComponent } from 'src/app/shared/ui/sort-icon/sort-icon.component';
import { NgbModal, ModalDismissReasons } from '@ng-bootstrap/ng-bootstrap';
import { PrivateProjectLoginComponent } from '../private-project-login/private-project-login.component';
import { BidOpsPrivateProjectService } from '../shared/data-access/bid-private-project.service';
@Component({
    selector: 'app-bid-opportunities',
    templateUrl: './bids.component.html',
    styleUrls: ['./bids.component.css'],
    imports: [CommonModule,        
        FormsModule,
        ReactiveFormsModule,        
        RouterModule,
        BidOpportunitiesFilterComponent,
        NgbPagination,
        SortIconComponent]
})
export class BidOpportunitiesComponent implements OnInit, OnDestroy {
  bidOpsProjectService = inject(BidOpsProjectsService);
  bidOpsPrivateProjectService = inject(BidOpsPrivateProjectService);
  modalService = inject(NgbModal);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  projectsGrid = this.bidOpsProjectService.projects;
  isLoading = this.bidOpsProjectService.isProjectsLoading;
  limit = this.bidOpsProjectService.limit;
  currentPage = this.bidOpsProjectService.currentPage;
  sortOrder = this.bidOpsProjectService.sortOrder;
  sortBy = this.bidOpsProjectService.sortBy;
  search = this.bidOpsProjectService.search;
  totalCount = computed(() => this.projectsGrid()?.TotalCount ?? 0);

  openModal() {
    this.bidOpsPrivateProjectService.openModal();
  }

  ngOnDestroy(): void {
    
  }

  ngOnInit() {
  
  }


  trackById(index: number, item: BidOpsGridInfo): string {
    return item.ProjectId;
  }

  changePage(pageInfo: number) {
    if (pageInfo !== this.currentPage()) {
      this.router.navigate([], {
        queryParams: {currentPage: pageInfo},
        queryParamsHandling: "merge"
      });
    }    
    
  }

  setOrder(sortBy: string) {

    this.router.navigate([], {
      queryParams: {sortBy: sortBy, sortOrder: this.sortOrder() === 'desc' ? 'asc' : 'desc'},
      queryParamsHandling: "merge"
    });

  }
}
