<!-- header -->
<header class="bg-light mb-3">
	<div class="container p-4">
		<!-- page title -->
		<h1 class="fs-5 mb-3">Bid Opportunities</h1>
		<!-- filters -->
		<app-bid-opportunities-filter [disable]="isLoading()">
		</app-bid-opportunities-filter>
	</div>
</header>
<div class="container px-4 mb-3">
	<!-- nav -->
	<nav class="d-flex align-items-center justify-content-end">
		<a class="custom-link me-4" routerLink="../favorites">Favorites</a>
		<a class="custom-link me-4" routerLink="../publishers">Browse by Publisher</a>
		<a class="custom-link" (click)="openModal()" style="cursor: pointer;">Private Bids</a>
	</nav>
	<!-- bid opportunities -->
	<section class="mb-3">
		<table class="table">
			<thead>
				<tr class="d-none d-lg-table-row">
					<th scope="col" style="width: 120px">
						<span class="text-nowrap text-underline clickable me-1"
							(click)="setOrder('BidDetails.BidDateTimeInfo.Date')"><u>Bid Date</u></span>
						<app-sort-icon [CurrentSortOrder]="sortOrder" [CurrentSortBy]="sortBy()"
							SortBy="BidDetails.BidDateTimeInfo.Date"></app-sort-icon>
					</th>
					<th scope="col" style="width: 100px">
						<span class="text-nowrap text-underline clickable me-1"
							(click)="setOrder('Location.State.Abbreviation')"><u>State</u></span>
						<app-sort-icon [CurrentSortOrder]="sortOrder()" [CurrentSortBy]="sortBy()"
							SortBy="Location.State.Abbreviation"></app-sort-icon>
					</th>
					<th scope="col" style="width: 120px">
						<span class="text-nowrap text-underline clickable me-1"
							(click)="setOrder('Location.County.Name')"><u>County</u></span>
						<app-sort-icon [CurrentSortOrder]="sortOrder()" [CurrentSortBy]="sortBy()"
							SortBy="Location.County.Name"></app-sort-icon>
					</th>
					<th scope="col">
						<span class="text-nowrap text-underline clickable me-1"
							(click)="setOrder('ProjectTitle')"><u>Name</u></span>
						<app-sort-icon [CurrentSortOrder]="sortOrder()" [CurrentSortBy]="sortBy()"
							SortBy="ProjectTitle"></app-sort-icon>
					</th>
					<th scope="col">
					</th>
				</tr>
			</thead>
			<tbody>
				@if(isLoading())
				{
				@for (item of [].constructor(limit()); track $index) {
				<tr class="placeholder-glow">
					<td><span class="placeholder w-100"></span></td>
					<td><span class="placeholder w-100"></span></td>
					<td><span class="placeholder w-100"></span></td>
					<td><span class="placeholder w-100"></span></td>
					<td><span class="placeholder w-100"></span></td>
				</tr>
				}
				}
				@else {
				@for (project of projectsGrid()?.Projects; track $index)
				{
				<tr>
					<td class="d-flex flex-column d-lg-none">
						@if(project.IsTBA){
						<span class="fw-bold">TBA</span>
						}@else {
						<span class="fw-bold">{{ project.BidDate | date: 'M/dd/yyyy' }}</span>
						}
						<span>{{ project.County }} County, TX</span>
						<a href="javascript:void(0)" [ngClass]="{'visited': project.IsClicked }"
							[routerLink]="[project.ProjectId]">{{
							project.ProjectTitle }}</a>
						<span *ngIf="project.IsBid">
							<span class="badge bg-warning">eBid</span>
						</span>
					</td>
					<td class="d-none d-lg-table-cell">
						@if(project.IsTBA){
						TBA
						}@else {
						{{ project.BidDate | date: 'M/dd/yyyy' }}
						}
					</td>
					<td class="d-none d-lg-table-cell">
						<span>{{ project.State }}</span>
					</td>
					<td class="d-none d-lg-table-cell">
						<span>{{ project.County }}</span>
					</td>
					<td class="d-none d-lg-table-cell">
						<a href="javascript:void(0)" [ngClass]="{'visited': project.IsClicked }"
							[routerLink]="[project.ProjectId]">{{
							project.ProjectTitle }}</a>
					</td>
					<td class="d-none d-lg-table-cell text-end">
						<span *ngIf="project.IsBid">
							<span class="badge bg-warning">eBid</span>
						</span>
					</td>
				</tr>
				}
				@empty {
				<tr>
					<td colspan="5">
						<div class="alert alert-info m-0" role="alert">
							There are no projects that match your search criteria.
						</div>
					</td>
				</tr>
				}
				}
			</tbody>
		</table>
	</section>
	<!-- footer -->
	<footer class="container d-flex justify-content-end">
		@if(totalCount() > limit())
		{
		<ngb-pagination [collectionSize]="totalCount()" [pageSize]="limit()" [page]="currentPage()" [rotate]="true"
			[maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)" [disabled]="isLoading()">
			<ng-template ngbPaginationFirst>First</ng-template>
			<ng-template ngbPaginationPrevious>Previous</ng-template>
			<ng-template ngbPaginationNext>Next</ng-template>
			<ng-template ngbPaginationLast>Last</ng-template>
		</ngb-pagination>
		}
	</footer>
</div>