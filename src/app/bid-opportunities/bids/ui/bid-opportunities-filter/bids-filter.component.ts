import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, ViewChild, inject } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { County, GlobalDataService } from 'src/app/shared/data-access/global-data-service';
import { BidOpsProjectsService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-projects-service';
import { DateTimePickerComponent } from 'src/app/shared/ui/date-time-picker/date-time-picker.component';

@Component({
    selector: 'app-bid-opportunities-filter',
    templateUrl: './bids-filter.component.html',
    styleUrls: ['./bids-filter.component.css'],
    providers: [NgForm],
    imports: [CommonModule, FormsModule, DateTimePickerComponent]
})
export class BidOpportunitiesFilterComponent implements OnInit {
  @Input("disable") disable: boolean = false;
  @Output("filter-change") filterChangeEvent: EventEmitter<any> = new EventEmitter<any>();
  globalDataService = inject(GlobalDataService);
  public formatter = inject(MomentDateFormatterService);
  bidOpsProjectsService = inject(BidOpsProjectsService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  searchInputDelay = 600;
  searchInputChange = new Subject<string>();
  projectTitleInputChange = new Subject<string>();
  internalIdInputChange = new Subject<string>();
  publisherInputChange = new Subject<string>();
  workTypeInputChange = new Subject<string>();
  selectedDate: NgbDateStruct | null = null;
  showAdvanceFilters: boolean = false;
  selectedCounties: Array<County> = [];
  states = this.globalDataService.states;
  counties = this.globalDataService.counties;
  internalId = this.bidOpsProjectsService.internalId;
  projectTitle = this.bidOpsProjectsService.projectTitle;
  state = this.bidOpsProjectsService.state;
  county = this.bidOpsProjectsService.county;
  publisher = this.bidOpsProjectsService.publisher;  
  typeOfWork = this.bidOpsProjectsService.typeOfWork;
  hideTBA = this.bidOpsProjectsService.hideTBA;
  selectedTime: any | null = null;
  search = this.bidOpsProjectsService.search;
  timeOptions: Array<any> = [];  
  bidOpsProjectService = inject(BidOpsProjectsService);
  constructor() {

    this.setupTimeOptions();
    this.setupSearchInputs();    
    this.initialize();

    this.aRoute.queryParams.subscribe(params => {

      if(params['hideTBA']){
        this.bidOpsProjectsService.hideTBA.set(params['hideTBA'] ? true : false);
      }else{
        
        var hideTBAInfo = localStorage.getItem("hideTBA");
        if (hideTBAInfo) {
          this.router.navigate([], {
            queryParams: { hideTBA: true },
            skipLocationChange: false,
            replaceUrl: true, 
            queryParamsHandling: 'merge'
          });
          this.bidOpsProjectService.hideTBA.set(true);
        }else{
          this.bidOpsProjectService.hideTBA.set(false); 
        }
      }

      if(params['currentPage']){
        this.bidOpsProjectService.currentPage.set(parseInt(params['currentPage']));
      }

      if(params['limit']){
        this.bidOpsProjectService.limit.set(parseInt(params['limit']));
      }else{
        this.bidOpsProjectService.limit.set(50);
      }

      if(params['sortBy']){
        this.bidOpsProjectService.sortBy.set(params['sortBy']);
      }else{
        this.bidOpsProjectService.sortBy.set("BidDetails.BidDateTimeInfo.Date");
        this.router.navigate([], {
          queryParams: { sortBy: "BidDetails.BidDateTimeInfo.Date" },
          skipLocationChange: false,
          replaceUrl: true, 
          queryParamsHandling: 'merge'
        });
      }

      if(params['sortOrder']){
        this.bidOpsProjectService.sortOrder.set(params['sortOrder']);
      }else{
        this.bidOpsProjectService.sortOrder.set("desc");
        this.router.navigate([], {
          queryParams: { sortOrder: "desc" },
          skipLocationChange: false,
          replaceUrl: true, 
          queryParamsHandling: 'merge'
        });
      }

      if(params['search']){
        this.bidOpsProjectService.search.set(params['search']);        
      }else{
        this.bidOpsProjectService.search.set(null);
      }

      if(params['bidDateStart']){
        this.bidOpsProjectService.bidStart.set(params['bidDateStart']);         
      }else{
        this.bidOpsProjectService.bidStart.set(null); 
        
      }

      if(params['bidDateEnd']){
        this.bidOpsProjectService.bidEnd.set(params['bidDateEnd']);        
      }else{
        this.bidOpsProjectService.bidEnd.set(null);
      }

      if(params['releaseDateStart']){
        this.bidOpsProjectService.releaseDateStart.set(params['releaseDateStart']);
      }else{
        this.bidOpsProjectService.releaseDateStart.set(null);
      }

      if(params['releaseDateEnd']){
        this.bidOpsProjectService.releaseDateEnd.set(params['releaseDateEnd']);
      }else{
        this.bidOpsProjectService.releaseDateEnd.set(null);
      }

      if(params['internalId']){
        this.bidOpsProjectService.internalId.set(params['internalId']);
      }else{
        this.bidOpsProjectService.internalId.set(null);
      }

      if(params['state']){
        this.bidOpsProjectService.state.set(params['state']);
        this.getCounties(params['state']);
      }else{
        this.bidOpsProjectService.state.set(null);
      }

      if(params['county']){
        this.bidOpsProjectService.county.set(params['county']);
      }else{
        this.bidOpsProjectService.county.set(null);
      }

      if(params['projectTitle']){
        this.bidOpsProjectService.projectTitle.set(params['projectTitle']);
      }else{
        this.bidOpsProjectService.projectTitle.set(null);
      }

      if(params['publisher']){
        this.bidOpsProjectService.publisher.set(params['publisher']);        
      }else{
        this.bidOpsProjectService.publisher.set(null);
      }

      if(params['typeOfWork']){
        this.bidOpsProjectService.typeOfWork.set(params['typeOfWork']);
      }else{
        this.bidOpsProjectService.typeOfWork.set(null);
      }

      
      if(params['filterTitle']){
        const toInfo = this.timeOptions.find(x => x.title === params['filterTitle']);
        this.selectedTime = toInfo;

        if(params['filterTitle']==='currentdate'){
          var d = new Date(params['bidDateStart']);          
          var p = { year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate() };
          this.selectedDate = {...p} as NgbDateStruct;
  
        }
          
      }else{
        this.selectedTime = null;
        var defaultOption = this.timeOptions[0]
        this.selectedTime = defaultOption;
        this.bidOpsProjectService.bidStart.set(defaultOption.startDate);
    
        this.router.navigate([], {
          queryParams: {
            bidDateStart: defaultOption.startDate, 
            bidDateEnd: defaultOption.endDate,
            releaseDateStart: null, 
            releaseDateEnd: null,
            filterTitle: defaultOption.title
          },
          skipLocationChange: false,
          replaceUrl: true,
          queryParamsHandling: 'merge'
        })
      }
    });

 

  }

  initialize(){
  

  }

  ngOnInit(): void {

  }


  setupSearchInputs(){
   this.searchInputChange
      .pipe(debounceTime(this.searchInputDelay), distinctUntilChanged())
      .subscribe(value => {
        let search = null;

        if(value){
          search = value;
        }

        this.router.navigate([], {
          queryParams: { search: search  },
          queryParamsHandling: "merge"
        });
      });

    this.projectTitleInputChange
      .pipe(debounceTime(this.searchInputDelay), distinctUntilChanged())
      .subscribe(value => {
        let projectTitle = null;
        if (value ) {
          projectTitle = value;
        }

        this.router.navigate([], {
          queryParams: { projectTitle: projectTitle },
          queryParamsHandling: "merge"
        });
      });

    this.internalIdInputChange
      .pipe(debounceTime(this.searchInputDelay), distinctUntilChanged())
      .subscribe(value => {
        if (value === '') {
          this.bidOpsProjectService.internalId.set(null);
        }else{
          this.bidOpsProjectService.internalId.set( value);
        }

        this.router.navigate([], {
          queryParams: { internalId: this.bidOpsProjectService.internalId() },
          queryParamsHandling: "merge"
        });
      });

    this.publisherInputChange
      .pipe(debounceTime(this.searchInputDelay), distinctUntilChanged())
      .subscribe(value => {
        if (value === '') {
          this.bidOpsProjectService.publisher.set(null);
        }else{
          this.bidOpsProjectService.publisher.set(value);
        }

        this.router.navigate([], {
          queryParams: { publisher: this.bidOpsProjectService.publisher()  },
          queryParamsHandling: "merge"
        });
      });

    this.workTypeInputChange
      .pipe(debounceTime(this.searchInputDelay), distinctUntilChanged())
      .subscribe(value => {
        if (value === '') {
          this.bidOpsProjectService.typeOfWork.set(null);
        }else{
          this.bidOpsProjectService.typeOfWork.set(value);
        }

        this.router.navigate([], {
          queryParams: { typeOfWork: this.bidOpsProjectService.typeOfWork() },
          queryParamsHandling: "merge"
        });
      });
  }

  setupTimeOptions(){
    var nDate = new Date(new Date().setHours(0, 0, 0, 0));

    var currentFilter = {
      title: 'Currently Bidding',
      type : 'BidDate',
      startDate: `${nDate.getFullYear()}-${nDate.getMonth() + 1}-${nDate.getDate()}`,
      endDate: null
    }

    var newTodayFilter = {
      title: 'Added Today',
      type : 'ReleaseDate',
      startDate: `${nDate.getFullYear()}-${nDate.getMonth() + 1}-${nDate.getDate()}`,
      endDate: `${nDate.getFullYear()}-${nDate.getMonth() + 1}-${nDate.getDate()}`,
    }

    var startOfWeek = new Date(nDate.setDate(nDate.getDate() - nDate.getDay()));
    var endOfWeek = new Date(nDate.setDate(nDate.getDate() - nDate.getDay() + 6));
    
    var newThisWeekFilter = {
      title: 'Added This Week',
      type : 'ReleaseDate',
      startDate: `${startOfWeek.getFullYear()}-${startOfWeek.getMonth() + 1}-${startOfWeek.getDate()}`,
      endDate: `${endOfWeek.getFullYear()}-${endOfWeek.getMonth() + 1}-${endOfWeek.getDate()}`,
    }

    // First day of the current month
    var startOfMonth = new Date(nDate.getFullYear(), nDate.getMonth(), 1);
    // Last day of the current month
    var endOfMonth = new Date(nDate.getFullYear(), nDate.getMonth() + 1, 0);

    var newThisMonthFilter = {
      title: 'Added This Month',
      type : 'ReleaseDate',
      startDate: `${startOfMonth.getFullYear()}-${startOfMonth.getMonth() + 1}-${startOfMonth.getDate()}`,
      endDate: `${endOfMonth.getFullYear()}-${endOfMonth.getMonth() + 1}-${endOfMonth.getDate()}`,
    }

    var allFilter = {
      title: 'All (Archives)',
      type : null,
      startDate: null,
      endDate: null
    }

    this.timeOptions.push(currentFilter);
    this.timeOptions.push(newTodayFilter);
    this.timeOptions.push(newThisWeekFilter);
    this.timeOptions.push(newThisMonthFilter);    
    this.timeOptions.push(allFilter);

  }

  selectTime(option:any){
    if(option.type === "BidDate"){
      this.router.navigate([], {
        queryParams: { 
          bidDateStart: option.startDate,
          bidDateEnd: option.endDate,
          releaseDateStart: null, 
          releaseDateEnd: null,
          filterTitle: option.title
        },
        queryParamsHandling: "merge"
      });
    }else if(option.type === "ReleaseDate"){
      this.router.navigate([], {
        queryParams: { 
          bidDateStart: null, 
          bidDateEnd: null,
          releaseDateStart: option.startDate, 
          releaseDateEnd: option.endDate,
          filterTitle: option.title
        },
        queryParamsHandling: "merge"
      });
    
    }else{
      this.router.navigate([], {
        queryParams: { 
          bidDateStart: null, 
          bidDateEnd: null,
          releaseDateStart: null, 
          releaseDateEnd: null,
          filterTitle: option.title
        },
        queryParamsHandling: "merge"
      });
    
    }

    this.selectedDate = null;
  }



  onDateSelect(dateInfo: any) {
    this.selectedDate = dateInfo;    
    if(this.selectedDate?.day && this.selectedDate?.month && this.selectedDate?.year){
    var d = `${this.selectedDate?.year}-${this.selectedDate?.month}-${this.selectedDate?.day}`
    this.router.navigate([], {          
      queryParams: { 
        bidDateStart: d,
        bidDateEnd: d,
        filterTitle: 'currentdate'
      },
      queryParamsHandling: "merge"          
    });
  }else{
    this.router.navigate([], {          
      queryParams: { 
        bidDateStart: null,
        bidDateEnd: null,
        filterTitle: null
      },
      queryParamsHandling: "merge"          
    });
  }
    
    // this.bidDateChange.next(this.selectedDate);

  }

  getState(state:string){
    this.router.navigate([], {
      queryParams: { state: state, county: null },
      queryParamsHandling: "merge"
    });

    this.getCounties(state);
  }

  getCounty(county:string){
    this.router.navigate([], {
      queryParams: { county: county },
      queryParamsHandling: "merge"
    });    
  }

  getCounties(state: string) {

    if(this.states()){
      this.selectedCounties = [...this.counties()?.filter(x => x.State === state) as Array<County>];
    }
  }

  setTBA(isTBA: boolean) {

    if (isTBA) {        
      localStorage.setItem("hideTBA", "yes");      
    } else {
      localStorage.removeItem("hideTBA");
    }   

    this.router.navigate([], {
      queryParams: { hideTBA: isTBA ? true : null },
      queryParamsHandling: 'merge'
    })

  }

  clearFilters() {
    this.initialize();

    this.router.navigate([], {
      queryParams: { 
        projectTitle: null,
        internalId: null,
        state: null,
        county: null,
        publisher: null,
        typeOfWork: null        
      },
      queryParamsHandling: "merge"
    });


  }
}