<!-- main filters -->
<div class="row">
	<!-- search by name, county, or id name -->
	<div class="col-12 col-md-6 col-xl-3 mb-2 mb-xl-0">
		<div class="input-group">
			<span class="input-group-text" id="basic-addon1"><i class="fas fa-search"></i></span>
			<input type="text" name="search" id="search" class="form-control" name="UserSearch"
				placeholder="Search by Name, County, or ID" [ngModel]="search()"
				(ngModelChange)="this.searchInputChange.next($event)" />
		</div>
	</div>
	<!-- bid date -->
	<div class="col-12 col-md-6 col-xl-3 mb-2 mb-xl-0">
		<app-date-time-picker (on-date-change)="onDateSelect($event)" [(date)]="selectedDate"></app-date-time-picker>
	</div>
	<!-- currently bidding / archives -->
	<div class="col-12 col-md-6 col-xl-2 mb-2 mb-xl-0">
		<select class="form-select" name="tfSelect" id="tfSelect" [(ngModel)]="selectedTime"
			(ngModelChange)="selectTime($event)">
			<option *ngFor="let option of timeOptions; let i = index;" [ngValue]="option">
				{{ option?.title }}
			</option>
		</select>
	</div>
	<!-- buttons and hide tba -->
	<div class="col-12 col-md-6 col-xl-4 mb-2 mb-xl-0 d-flex align-items-center">
		<a class="btn btn-outline-dark text-dark text-nowrap me-2" data-bs-toggle="collapse" href="#showadvancefilters"
			role="button" aria-expanded="false">Advanced Search</a>
		<a class="btn btn-primary text-white me-2" href="javascript:void(0)" role="button" (click)="clearFilters()">Clear</a>
		<div class="form-check">
			<input type="checkbox" class="form-check-input" name="HideTBA" [ngModel]="hideTBA()"
				(ngModelChange)="setTBA($event)" />
			<label class="form-check-label small" for="flexCheckDefault">
				Hide TBA
			</label>
		</div>
	</div>
</div>
<!-- advanced filters -->
<div class="collapse mt-3" id="showadvancefilters" #advancedFilters>
	<div class="border rounded bg-white p-3 mt-3">
		<div class="row">
			<!-- name -->
			<div class="col-12 col-md-4 mb-3">
				<div>
					<label for="myStateSelect" class="form-label">Name</label>
					<input type="text" class="form-control" name="ProjectTitle" [ngModel]="projectTitle()"
						(ngModelChange)="this.projectTitleInputChange.next($event)" />
				</div>
			</div>
			<!-- state -->
			<div class="col-12 col-md-4 mb-3">
				<div>
					<label for="StateInfo" class="form-label">State</label>
					<select class="form-select" name="StateInfo" id="StateInfo" [ngModel]="state()"
						(ngModelChange)="getState($event);">
						@for (option of states(); track $index) {
						<option [ngValue]="option.Abbreviation">
							{{ option.Abbreviation }}
						</option>
						}
					</select>
				</div>
			</div>
			<!-- county -->
			<div class="col-12 col-md-4 mb-3">
				<label for="CountyInfo" class="form-label">County (Requires State Selection)</label>
				<select class="form-select" name="CountyInfo" id="CountyInfo" [ngModel]="county()"
					(ngModelChange)="getCounty($event)" [disabled]="!state()">
					@for (option of selectedCounties; track $index) {
					<option [value]="option?.Name">
						{{ option?.Name }}
					</option>
					}
				</select>
			</div>
			<!-- id -->
			<div class="col-12 col-md-4 mb-3">
				<label for="ProjectInternalId" class="form-label">Project No.</label>
				<input type="text" class="form-control" id="ProjectInternalId" name="ProjectInternalId"
					[ngModel]="internalId()" (ngModelChange)="this.internalIdInputChange.next($event)" />
			</div>
			<!-- publisher -->
			<div class="col-12 col-md-4 mb-3">
				<label for="Publisher" class="form-label">Publisher</label>
				<input type="text" class="form-control" id="Publisher" name="Publisher"
					[ngModel]="publisher()" (ngModelChange)="this.publisherInputChange.next($event)" />
			</div>
			<!-- type -->
			<div class="col-12 col-md-4 mb-3">
				<label for="Type" class="form-label">Type</label>
				<input type="text" class="form-control" id="ProjectWorkType" name="ProjectWorkType"
					[ngModel]="typeOfWork()" (ngModelChange)="this.workTypeInputChange.next($event)" />
			</div>
		</div>
	</div>
</div>