<div>
	<h5 class="page-title text-muted fs-6">Plan Holders</h5>
	<p>When you download or order bid documents, plans, addenda, or revisions, you will be added to the plan holders
		list
		and automatically receive notifications of any new addenda or revisions.</p>
</div>
<!------------------>
@if(project()){
	<!-- engineer has turn plan holders off -->
	@if(!project()?.Permissions?.ShowPlanholdersList){
		<div class="alert alert-info">
			<i class="fas fa-info-circle me-2"></i>Planholders for this project are not available for viewing.
		</div>
	}@else {
		<!-- plan holders -->
		@if(isAuthorized()){
			<app-planholders-info></app-planholders-info>
		}
		<!-- user is not logged in -->
		@else{
			<div class="alert alert-info">
				<i class="fas fa-info-circle me-2"></i>You must log in to see this page.
			</div>
		}		
	}
}
