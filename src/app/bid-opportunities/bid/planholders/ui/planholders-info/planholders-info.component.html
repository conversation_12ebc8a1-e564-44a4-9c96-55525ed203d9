<!-- top part -->
 @if(planholdersBidding()){
	<div class="border rounded bg-light-subtle p-3 mb-3">

		@if(!userAsPlanholder()){
			<div class="form-check">
				<input type="checkbox" class="form-check-input" id="planHoldersAdd" [checked]="isInterested()"
					(change)="trackInterested()" [disabled]="actionLoading()">
				<label class="form-check-label" for="planHoldersAdd">Add me to the plan holders list without
					downloading
					plans.</label>
			</div>
		}@else {
			<div class="form-check">
				<input type="checkbox" class="form-check-input" id="notBiddingCheck" [checked]="!isBidding()"
					(change)="trackProjectBidding()" [disabled]="actionLoading()">
				<label class="form-check-label" for="notBiddingCheck">I am not bidding and do not wish to
					receive
					quotes.</label>
			</div>
		}
	</div>
	<div class="d-flex justify-content-end mb-3">
		<button class="btn btn-link p-0" (click)="downloadExcel()" [disabled]="isDownloadingExcel()">			
			@if(isDownloadingExcel()){
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}				
			
			Download Excel
		</button>
	</div>
 }

<!-- plan holders -->
<ul class="list-group mb-3">
	<!-- Filter -->
	<li class="list-group-item bg-light">
		<div class="input-group">
			<span class="input-group-text" id="basic-addon1">
				<i class="fas fa-search" aria-hidden="true"></i>
			</span>
			<input type="text" class="form-control" name="search" [ngModel]="search()"
			(ngModelChange)="this.searchInputChange.next($event)" placeholder="Search by name or company">
			<button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown"
				aria-haspopup="true" aria-expanded="false">
				<i class="fas fa-sort-alpha-down" aria-hidden="true"></i>
			</button>

			<ul class="dropdown-menu dropdown-menu-end">
				@for (sort of phSorts; track  $index) {
					<li>
						<a class="dropdown-item" href="javascript:void(0)" (click)="setSort(sort.value)">
							{{ sort.name }}
				
							@if(currentSort() === sort.value){
								<span>*</span>
							}
						</a>
					</li>
				}
			</ul>
		</div>
	</li>
	<!-- Change Company Type -->

	@if(userAsPlanholder()){
		<li class="list-group-item bg-light-subtle">
			<p>
				Update your company type for this project. For instance, if your profile lists you as a
				subcontractor
				but you intend to bid as a general contractor, update your company type accordingly.
			</p>
			<div class="row">
				<div class="col-4">
					<div class="input-group">				
						<select class="form-control" name="changeCompanyType" [ngModel]="userCompanyType()"
							[disabled]="isUpdating" (ngModelChange)="updateUserCompanyType($event)">
							@for (companyType of companyTypes(); track  $index) {
								<option [ngValue]="companyType">
									{{ companyType }}
								</option>
							}
						</select>
						<button class="btn btn-primary" (click)="updatePlanholder()">
							@if(userIsUpdating()){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}	
							Update
						</button>
					</div>
				</div>
			</div>
		</li>
	}
	<!-- Skeleton -->
	 @if(isLoading()){
		<app-planholder-skeleton></app-planholder-skeleton>
	 }@else {
		@for ( ph of planholdersBidding(); track $index) {
			<li class="list-group-item">
				<div class="row align-items-center">
					<!-- Company Name and Type -->
					<div class="col-12 col-lg-4 col-xl-4">
						<div class="fw-bold">{{ ph.CompanyName }}</div>
						<div>{{ ph.CompanyType.Name }}</div>
					</div>
					<!-- First and Last Name -->
					<div class="col-12 col-lg-2 col-xl-3">
						{{ ph.FirstName }} {{ ph.LastName }}
					</div>
					<!-- Contact Info (Phone and Email) -->
					<div class="col-12 col-lg-3 col-xl-3">
						<div>
							<a class="text-decoration-none" href="tel:{{ ph.Phone }}">{{
								ph.Phone | phoneFormatter }}</a>
						</div>
						<div>
							<a class="d-block text-truncate text-decoration-none"
								href='mailto:{{ ph.Email | obfuscate: _k()  }}'>{{ ph.Email |
								obfuscate: _k() | lowercase }}</a>
						</div>
					</div>
					<!-- Full Profile -->
					<div class="col-12 col-lg-3 col-xl-2 d-flex justify-content-lg-end align-items-center">
						@if (ph.CognitoUserId && !ph.InfoIsHidden) {
							<div>
								<a class="btn btn-outline-secondary mt-1 mt-lg-0"
									[routerLink]="['/bid-opportunities', 'users', ph.CognitoUserId]"
									target="_blank">Full Profile</a>
							</div>
						}

						<!-- @if(ph.InfoIsHidden){
							<div class="text-danger mt-1 mt-lg-0" style="font-size: 0.8rem;">
								<i class="fas fa-exclamation-triangle fa-1x mx-2"></i>Info is hidden
							</div>
						} -->
					
					</div>
					<!-- DBE Certifications -->
					<div class="p-2 rounded">
						@for (cert of ph.DBECerts; track $index) {		

							<span class="badge bg-primary mx-1">
								{{ cert }}<<<<<
							</span>
						}			
					</div>
				</div>
			</li>
		}@empty {
			<li class="list-group-item">
				This project doesn't have any plan holders yet.
			</li>
		}
	 }
</ul>
<!-- pagination -->
 @if(totalBidding() > limit()){
	<div class="d-flex justify-content-end mb-3">
		<ngb-pagination [collectionSize]="totalBidding()" [pageSize]="limit()" [page]="currentPage()" [rotate]="true"
			[maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)">
			<ng-template ngbPaginationFirst>First</ng-template>
			<ng-template ngbPaginationPrevious>Previous</ng-template>
			<ng-template ngbPaginationNext>Next</ng-template>
			<ng-template ngbPaginationLast>Last</ng-template>
		</ngb-pagination>
	</div>
 }

<!-- not bidding list -->
 @if(planholdersNotBidding() && planholdersNotBidding()?.length > 0){
	<ul class="list-group mb-3">
		<li class="list-group-item bg-danger fw-bold" style="color: white">
			Not Bidding
		</li>
		@for (ph of planholdersNotBidding(); track $index) {
			<li class="list-group-item">
				<div class="row">
					<div class="col-12">
						<div class="fw-bold">{{ ph.CompanyName }}</div>
						<div>{{ ph.FirstName }} {{ ph.LastName }}</div>
					</div>
				</div>
			</li>
		}
	</ul>	
 }