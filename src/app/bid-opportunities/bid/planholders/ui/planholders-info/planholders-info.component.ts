import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, input, On<PERSON><PERSON>roy, OnInit, signal, viewChild } from '@angular/core';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { Subject, debounceTime, distinctUntilChanged, of, tap } from 'rxjs';
import { BidOpsPlanholdersService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-planholders.service';
import { Planholder } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { ObfuscatePipe } from 'src/app/shared/utils/pipes/obfuscate.pipe';
import { PlanholderSkeletonComponent } from '../planholder-skeleton/planholder-skeleton.component';
import {  GlobalDataService } from 'src/app/shared/data-access/global-data-service';
import { PhonesPipe } from 'src/app/shared/utils/pipes/phone.pipe';
import { CivcastProfileComponent } from 'src/app/shared/ui/civcast-profile/civcast-profile.component';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
  selector: 'app-planholders-info',
  standalone: true,
  imports: [CommonModule, FormsModule, NgbPaginationModule, ObfuscatePipe, PlanholderSkeletonComponent, PhonesPipe, RouterLink],
  templateUrl: './planholders-info.component.html',
  styleUrls: ['./planholders-info.component.css']
})
export class PlanholdersInfoComponent implements OnInit, OnDestroy {
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  searchInputChange = new Subject<string>();
  globalDataService = inject(GlobalDataService);
  bidOpsPlanholdersService = inject(BidOpsPlanholdersService);    
  bidOpsProjectService = inject(BidOpsProjectService);  
  authService = inject(AuthService);
  project = this.bidOpsProjectService.project;
  limit = this.bidOpsPlanholdersService.limit;
  planholdersResponse = this.bidOpsPlanholdersService.planholdersResponse;
  userProfileComponent = viewChild(CivcastProfileComponent);
  currentSort = this.bidOpsPlanholdersService.sortBy;  
  isDownloadingExcel = this.bidOpsPlanholdersService.isDownloadingExcel;
  phSorts = this.bidOpsPlanholdersService.phSorts;
  currentPage = this.bidOpsPlanholdersService.page;
  search = this.bidOpsPlanholdersService.search;
  actionLoading = this.bidOpsPlanholdersService.actionLoading;
  userIsUpdating = this.bidOpsPlanholdersService.userIsUpdating;
  isLoading = this.bidOpsPlanholdersService.isLoading;
  planholdersBidding = computed(() => this.bidOpsPlanholdersService.planholdersResponse()?.BiddingPlanholders);
  planholdersNotBidding = computed(() => this.bidOpsPlanholdersService.planholdersResponse()?.NotBiddingPlanholders);
  userCompanyType = computed(() => this.planholdersResponse()?.UserAsPlanholder?.CompanyType?.Name);
  totalBidding = computed(() => this.planholdersResponse()?.BiddingTotal ?? 0);  
  companyTypes = computed(() => this.globalDataService.companyTypes()?.map(x => x.Name));
  _k = computed(() => this.planholdersResponse()?._k ?? "");
  isInterested = computed(() => this.planholdersResponse()?.UserAsPlanholder?.Actions?.some(x => x.toLowerCase() === "interested"));
  isBidding = computed(() => !this.planholdersResponse()?.UserAsPlanholder?.NotBidding);
  userAsPlanholder = computed(() => this.bidOpsPlanholdersService.planholdersResponse()?.UserAsPlanholder);
  selectedCompanyType = signal<string | null>(null);

  constructor() {
    this.initializeSearchInputChange();
    this.initializeQueryParams();   

    effect(() => {
      if(this.bidOpsProjectService.projectId() && this.authService.isLoggedInSignal() && this.project()){ 
        if(this.project()?.Permissions.ShowPlanholdersList){
          this.bidOpsPlanholdersService.projectId.set(this.bidOpsProjectService.projectId());
        }        
      }
    })
  }
  ngOnDestroy(): void {
    this.bidOpsPlanholdersService.stopEffects();  
  }

  ngOnInit(): void {
    this.bidOpsPlanholdersService.startEffects();
  }




  private initializeQueryParams(): void {
    this.aRoute.queryParamMap.subscribe((params) => {
      if (params.has('sortBy')) {
        this.currentSort.set(params.get('sortBy') as string);
      }else{
        this.currentSort.set('DateCreated');
      }

      if (params.has('page')) {
        this.currentPage.set(parseInt(params.get('page') as string));
      }

      if (params.has('search')) {
        this.search.set(params.get('search') as string);
      }else{
        this.search.set(null);
      }

      if(params.has('sortOrder')) {
        this.bidOpsPlanholdersService.sortOrder.set(params.get('sortOrder') as string);
      }else{
        this.bidOpsPlanholdersService.sortOrder.set('asc');
      }

      if(params.has('limit')) {
        this.bidOpsPlanholdersService.limit.set(parseInt(params.get('limit') as string));
      }

    });
  }

  private initializeSearchInputChange(): void {
    this.searchInputChange.pipe(debounceTime(400), distinctUntilChanged()).subscribe(value => {
      if (value.length >= 3 || value.length === 0) {
        this.router.navigate([], {
          queryParams: { search: value || null, page: 1 },
          queryParamsHandling: "merge"
        });
      }
    });  
  }


  updatePlanholder(): void {
    if (this.selectedCompanyType()) {      
      this.bidOpsPlanholdersService.updatePlanholder(this.selectedCompanyType() as string);
    }
  }

  trackInterested(): void {
    const value = this.isInterested() ? 'not-interested' : 'Interested';
    this.bidOpsPlanholdersService.addAction(value);
  }

  trackProjectBidding(): void {
    const value = this.isBidding() ? 'not-bidding' : 'bidding';
    this.bidOpsPlanholdersService.addAction(value);
  }

  downloadExcel(): void {
    this.bidOpsPlanholdersService.getExcel();
  }

  setSort(sort: string): void {
    this.router.navigate([], {
      queryParams: { sortBy: sort ,page: 1 },
      queryParamsHandling: "merge"
    });
  }

  updateUserCompanyType(companyType: string): void {
    this.selectedCompanyType.set(companyType);    
  }
  changePage(page: number): void {

      this.router.navigate([], {
        queryParams: { page: page },
        queryParamsHandling: "merge"
      });
    
  }

  private handleError(message: string, error: any): void {
    console.error(message, error);
    alert(`${message}. Please try again.`);
  }


  trackByIndex(index: number): number {
    return index;
  }
}

export class PlanholderInfo {
  constructor(public Planholder: Planholder) { }
  IsInterested = false;
}