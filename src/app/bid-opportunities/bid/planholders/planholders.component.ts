import { Component, inject } from '@angular/core';
import { BidOpsPlanholdersService } from '../../shared/data-access/bid-ops-planholders.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { BidOpsProjectService } from '../../shared/data-access/bid-ops-project.service';
import { PlanholdersInfoComponent } from './ui/planholders-info/planholders-info.component';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
    selector: 'app-planholders',
    imports: [PlanholdersInfoComponent],
    templateUrl: './planholders.component.html',
    styleUrl: './planholders.component.css'
})
export class PlanholdersComponent {

  bidOpsProjectService = inject(BidOpsProjectService);
  authService = inject(AuthService);
  project = this.bidOpsProjectService.project;
  isAuthorized = this.authService.isLoggedInSignal;


}
