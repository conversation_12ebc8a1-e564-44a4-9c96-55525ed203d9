<div class="row">
    <!-- details ----------------------->
    <div class="col-12 col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Details</h2>
                <div class="row mb-1">
                    <div class="col-4 col-md-5 col-xl-4">ID: </div>
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.InternalId }}</div>
                </div>
                <div class="row mb-1">
                    <div class="col-4 col-md-5 col-xl-4 col-md-4">Estimate:</div>
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.DetailsEstimate }}</div>
                </div>
                <div class="row mb-1">
                    <div class="col-4 col-md-5 col-xl-4">Type:</div>
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.TypeOfWork }}</div>
                </div>
                <div class="row mb-1">
                    <div class="col-4 col-md-5 col-xl-4">Owner:</div>
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.Owner }}</div>
                </div>
                <!--row: Low Bid Info------------>
                @if( project()?.LowBidName){
                <div class="row mb-1">
                    <div class="col-4 col-md-5 col-xl-4">Low Bid Name: </div>
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.LowBidName }}</div>
                </div>
                }
                @if(project()?.LowBidAmount){
                <div class="row mb-1">
                    <div class="col-4 col-md-5 col-xl-4 col-md-4">Low Bid Amount:</div>
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.LowBidAmount }}</div>
                </div>
                }
            </div>
        </div>
    </div>
    <!-- location ---------------------->
    <div class="col-12 col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Location</h2>
                <div class="mb-1">{{ project()?.Location?.County?.Name }} County,
                    {{project()?.Location?.State?.Abbreviation
                    }}</div>
                @if((project()?.Location?.MapData?.Latitude && project()?.Location?.MapData?.Longitude) || project()?.CustomMapUrl){
                <div class="mb-1" ng-show="project.Location.MapData">
                    <a class="custom-link" href="javascript:void(0)" (click)="showMap()">
                        @if(showGoogleMap){
                        Hide Google Map
                        }@else {
                        Show Google Map
                        }
                    </a>
                </div>
                <div>
                    <a class="custom-link"
                        href="https://map.google.com/?q={{project()?.Location?.MapData?.Latitude}},{{project()?.Location?.MapData?.Longitude}}"
                        target='_blank'>
                        Open Map On Google
                    </a>
                </div>
                }
            </div>
        </div>
    </div>
    <!-- map --------------------------->
    @if(showGoogleMap){
        <div class="col-12">
            <div class="card mb-3">
                <div class="card-body">
                    @if(project()?.Location?.MapData?.Latitude && project()?.Location?.MapData?.Longitude){
                    <google-map
                        [center]="{lat: project()?.Location?.MapData?.Latitude, lng: project()?.Location?.MapData?.Longitude}"
                        #googlemap [options]="options" height="500px" width="100%">
                        @for (markerPosition of markerPositions; track $index) {
                        <map-marker [position]="markerPosition" [options]="markerOptions"></map-marker>
                        }
                    </google-map>
                    }

                    @if(project()?.CustomMapUrl && googleMapApiKey){
                        <div class="mt-2" >
                            <app-google-map-embed [url]="project()?.CustomMapUrl" [apiKey]="googleMapApiKey"></app-google-map-embed>
                        </div>
                    }
                </div>
            </div>
        </div>

     
    }
    <!-- scope ------------------------->
    <div class="col-12 mb-3">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Scope</h2>
                <div [innerHTML]="project()?.Scope"></div>
            </div>
        </div>
    </div>
    <!-- pre-bid ----------------------->
    <div class="col-12 col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Pre-Bid Meeting</h2>
                <div class="mb-2">
                    @if(project()?.IsTBA){
                    <span>TBA</span>
                    }@else{
                    <span>{{ project()?.PreBidDetails?.BidDateTimeInfo | bidDatePipe : 'date' }}, </span>
                    }
                    @if(project()?.IsTBA){
                    <span>TBA</span>
                    }@else {
                    <span>
                        {{ project()?.PreBidDetails?.BidDateTimeInfo | bidDatePipe : 'time'}}
                    </span>
                    }
                </div>
                <div class="mb-2">
                    {{ project()?.PreBidDetails?.Location }}
                </div>
                <div class="mb-2">
                    {{ project()?.PreBidDetails?.Notes }}
                </div>
                @if(sanitizedPreBidMeetingLink){
                <div>
                    <a [href]="sanitizedPreBidMeetingLink" target="_blank">Pre-Bid Meeting</a>
                </div>
                }
            </div>
        </div>
    </div>
    <!-- bid opening ------------------->
    <div class="col-12 col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Bid Opening</h2>
                <div class="mb-1">
                    @if(project()?.IsTBA){
                    <span>TBA</span>
                    }@else{
                    <span>{{ project()?.BidDetails?.BidDateTimeInfo | bidDatePipe : 'date' }},</span>
                    }
                    <span ng-hide="project.Permissions.IsTBA">
                        {{ project()?.BidDetails?.BidDateTimeInfo | bidDatePipe : 'time'}}
                        {{ project()?.BidDetails?.BidDateTimeInfo?.TimeZone?.ZoneId}}
                    </span>
                </div>
                <div class="row mb-1">
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.BidDetails?.Location }}</div>
                </div>
                <div class="row mb-1">
                    <div class="col-8 col-md-7 col-xl-8">{{ project()?.BidDetails?.Notes }}</div>
                </div>
                @if(sanitizedBidOpeningLink){
                <div class="row mb-1">
                    <div class="col-12">
                        <a [href]="sanitizedBidOpeningLink" target="_blank">Bid Opening</a>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
    <!-- contact company --------------->
    <div class="col-12 col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Contact Company</h2>
                @if(project()?.ContactInfo?.Company?.Name){
                <div class="rowv mb-1">
                    <div class="col-12">{{ project()?.ContactInfo?.Company?.Name}}</div>
                </div>
                }
                @if(project()?.ContactInfo?.Company?.Address?.Address1 ||
                project()?.ContactInfo?.Company?.Address?.Address2){
                <div class="row mb-1">
                    <div class="col-12">{{ project()?.ContactInfo?.Company?.Address?.Address1}}</div>
                    <div class="col-12">{{ project()?.ContactInfo?.Company?.Address?.Address2}}</div>
                </div>
                }
                <div class="row mb-1">
                    <div class="col-12">
                        @if(project()?.ContactInfo?.Company?.Address?.City)
                        {
                        {{ project()?.ContactInfo?.Company?.Address?.City }}
                        }

                        @if( project()?.ContactInfo?.Company?.Address?.State?.Abbreviation ||
                        project()?.ContactInfo?.Company?.Address?.Zip){
                        ,
                        }

                        @if( project()?.ContactInfo?.Company?.Address?.State?.Abbreviation){
                        {{ project()?.ContactInfo?.Company?.Address?.State?.Abbreviation }}
                        }

                        @if(project()?.ContactInfo?.Company?.Address?.Zip){
                        {{ project()?.ContactInfo?.Company?.Address?.Zip }}
                        }
                    </div>
                </div>
                @if(sanitizedWebsiteLink){
                <div class="row mb-1">
                    <div class="col-12">
                        <a class="text-decoration-none" [href]="sanitizedWebsiteLink" target="_blank">Company
                            Website</a>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
    <!-- contact person ---------------->
    <div class="col-12 col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Contact Person</h2>
                <div class="mb-2">{{ project()?.ContactInfo?.FirstName }} {{
                    project()?.ContactInfo?.LastName }}</div>
                <div class="mb-2">
                    {{ project()?.ContactInfo?.Company?.Phone }}
                </div>
                <div class="mb-2">
                    {{ project()?.ContactInfo?.Company?.Fax }}
                </div>
                <div>
                    {{ project()?.ContactInfo?.Email }}
                </div>
            </div>
        </div>
    </div>
    <!-- bid package notes ------------->
    <div class="col-12 mb-3">
        <div class="card mb-3">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Bid Package Notes</h2>
                <div class="text-muted">{{ project()?.Notes }}</div>
            </div>
        </div>
    </div>
    <!-- additional notes -------------->
    <div class="col-12 mb-3">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title fs-6 page-title">Additional Notes</h2>
                <div class="text-muted">{{ project()?.AdditionalNotes }}</div>
            </div>
        </div>
    </div>
</div>