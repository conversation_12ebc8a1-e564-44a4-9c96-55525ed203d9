import { CommonModule } from '@angular/common';
import { Component, effect, inject, input } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { GoogleMapsModule } from '@angular/google-maps';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { tap } from 'rxjs';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { GoogleMapEmbedComponent } from 'src/app/maps/google-map/google-map-embed/google-map-embed.component';
import { GoogleMapProjectService } from 'src/app/maps/services/google-map-project.service';
import { BidDatePipe } from 'src/app/shared/utils/pipes/biddate.pipe';
import { TimeZonePipe } from 'src/app/shared/utils/pipes/time-zone.pipe';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-summary-info',
    imports: [CommonModule, GoogleMapsModule, BidDatePipe, GoogleMapEmbedComponent],
    templateUrl: './summary-info.component.html',
    styleUrl: './summary-info.component.css'
})
export class SummaryInfoComponent {
  project = input<BidOpsProject | null>(null)  
  aRoute = inject(ActivatedRoute);
  sanitizer = inject(DomSanitizer);
  showGoogleMap = false;  
  options: google.maps.MapOptions = {clickableIcons: false, gestureHandling: 'none', fullscreenControl: false, zoom: 15 };
  markerPositions: google.maps.LatLngLiteral[] = [];
  // markerOptions: google.maps.marker.AdvancedMarkerElement = {} as google.maps.marker.AdvancedMarkerElement;
  sanitizedBidOpeningLink: SafeResourceUrl | null = null;  
  sanitizedPreBidMeetingLink: SafeResourceUrl | null = null;  
  sanitizedWebsiteLink: SafeResourceUrl | null = null;  
  googleMapApiKey = environment.google.maps.apiKey;
  projectEffect = toSignal(toObservable(this.project).pipe(
    tap((project) => {
      if(project){
        this.markerPositions = [{ lat: this.project()?.Location?.MapData?.Latitude as number, lng: this.project()?.Location?.MapData?.Longitude as number }];
        if(this.project()?.BidOpeningLink) {this.sanitizedBidOpeningLink = this.sanitizer.bypassSecurityTrustResourceUrl(this.project()?.BidOpeningLink as string) as string};
        if(this.project()?.PreBidMeetingLink) {this.sanitizedPreBidMeetingLink = this.sanitizer.bypassSecurityTrustResourceUrl(this.project()?.PreBidMeetingLink as string) as string};
        if(this.project()?.ContactInfo?.Company?.Website) {this.sanitizedWebsiteLink = this.sanitizer.bypassSecurityTrustResourceUrl(this.project()?.ContactInfo?.Company?.Website as string) as string};
        
      }
    })
  )); 

  constructor() {
   
  }

  showMap(){
    this.showGoogleMap = !this.showGoogleMap;
  }
}
