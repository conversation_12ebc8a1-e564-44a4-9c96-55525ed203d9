import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BidOpsProjectService } from '../../shared/data-access/bid-ops-project.service';
import { BidSummarySkeletonComponent } from './ui/bid-summary-skeleton/bid-summary-skeleton.component';
import { SummaryInfoComponent } from './ui/summary-info/summary-info.component';

@Component({
    selector: 'app-summary',
    imports: [CommonModule, BidSummarySkeletonComponent, SummaryInfoComponent],
    standalone: true,
    templateUrl: './summary.component.html',
    styleUrl: './summary.component.css'
})
export class SummaryComponent implements OnInit {
  bidOpsProjectService = inject(BidOpsProjectService);

  project = this.bidOpsProjectService.project;

  projectLoading = this.bidOpsProjectService.projectLoading;


  ngOnInit(): void {

    
  }
}
