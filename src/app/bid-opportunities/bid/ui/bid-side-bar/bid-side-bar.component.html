
	<!-- side nav -->
	<div class="col-12 d-none d-lg-block col-lg-3 col-xl-2">
		<nav id="links">
			<ul class="list-group">
				<li class="list-group-item"><a class="text-decoration-none" routerLink="summary">Bid Notice 2</a></li>
				<li class="list-group-item"><a class="text-decoration-none" href="#">E-Bid</a></li>
				<li class="list-group-item"><a class="text-decoration-none" routerLink="documents">Docs<span
							class="badge text-bg-secondary ms-1">10</span></a></li>
				@if(project()?.ShowPlanholders){
				<li class="list-group-item"><a class="text-decoration-none" href="#">Plan Holders <span
							class="badge text-bg-secondary ms-1">50</span></a></li>
				}
				<li class="list-group-item"><a class="text-decoration-none" href="#">Q/A<span
							class="badge text-bg-secondary ms-1">100</span></a></li>
				<li class="list-group-item"><a class="text-decoration-none" href="#">Paper Plans</a></li>
			</ul>
		</nav>
	</div>