import { Component, inject, input, OnInit, OnChanges, SimpleChanges, computed, effect } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { CommonModule } from '@angular/common';
import { FavoritesService } from 'src/app/account/shared/data-access/favorites.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { BidBidderFolderService } from 'src/app/bid-opportunities/shared/data-access/bid-bidderfolder.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { BidOpsPlanholdersService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-planholders.service';
import { BidsOpsQAService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project-qa.service';
import { BidOpsDocumentService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-document.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { GoogleMapProjectService } from 'src/app/maps/services/google-map-project.service';

@Component({
    selector: 'app-bid-main',
    imports: [CommonModule, RouterOutlet, RouterLink, NgbPopover],
    standalone: true,
    providers: [ GoogleMapProjectService],
    templateUrl: './bid-main.component.html',
    styleUrl: './bid-main.component.css'
})
export class BidMainComponent implements OnInit, OnChanges {
  bidFolderService = inject(BidBidderFolderService);
  bidOpsProjectService = inject(BidOpsProjectService);
  bidOpsPlanholdersService = inject(BidOpsPlanholdersService);
  bidOpsQuestionsService = inject(BidsOpsQAService);
  bidOpsBidDocsService = inject(BidOpsDocumentService);
  authService = inject(AuthService);
  disableHyperlink = input<boolean>(false);
  project = this.bidOpsProjectService.project;
  isFavorite = false;
  private favoritesService = inject(FavoritesService);
  private toastr = inject(ToastrService);
  private favoritesSubscription: Subscription | null = null;
  private router = inject(Router);

  eBid = this.bidFolderService.eBid;
  isEBidStarted = this.bidFolderService.isBidStarted;
  startDateInfo = computed(() =>  this.bidFolderService.startDate);
  eBidLoading = this.bidFolderService.isLoading;
  
  formattedStartDate = computed(() => {
    const startDate = this.bidFolderService.startDate();
    if (startDate) {
      return new Date(startDate).toLocaleString();
    }
    return '';
  });

  constructor(){
    effect(() => {
      if(this.authService.isLoggedInSignal()){
        this.loadFavorites();
      }
    });
  }
  ngOnInit(): void {

  }

  ngOnChanges(changes: SimpleChanges): void {

  }

  ngOnDestroy(): void {
    this.favoritesSubscription?.unsubscribe();
  }

  preLoadPlanholders(){
    this.bidOpsPlanholdersService.projectId.set(this.project()?.Id as string);
  }

  preLoadQuestions(){
    this.bidOpsQuestionsService.projectId.set(this.project()?.Id as string);
  }

  preLoadBidDocs(){
    this.bidOpsBidDocsService.projectId.set(this.project()?.Id as string);
  }

  private loadFavorites(): void {
    this.favoritesSubscription = this.favoritesService.getFavorites().subscribe({
      next: (favorites) => {
        const currentProject = this.project() as BidOpsProject;
        if (currentProject) {
          this.isFavorite = favorites.some(fav => fav.ProjectId === currentProject.Id);
        } else {
          this.isFavorite = false;
        }
      },
      error: (err) => {
        console.error('Failed to load favorites', err);
      }
    });
  }

  addToFavorites(): void {
    const currentProject = this.project(); // Dereference the signal to get the actual value
    if (!currentProject) {
      console.error('No project data available for adding to favorites.');
      return;
    }

    this.favoritesService.addFavorite(currentProject.Id).subscribe({
      next: () => {
        console.log('Project added to favorites successfully!');
        this.toastr.success('Project added to favorites.');
        this.isFavorite = true;
      },
      error: (err) => {
        console.error('Failed to add project to favorites', err);
        this.toastr.error('Failed to add project to favorites. Please try again.');
      },
    });
  }

  removeFromFavorites(): void {
    const currentProject = this.project(); // Dereference the signal to get the actual value
    if (!currentProject) {
      console.error('No project data available for removing from favorites.');
      return;
    }

    this.favoritesService.removeFavorite(currentProject.Id).subscribe({
      next: () => {
        console.log('Project removed from favorites successfully!');
        this.toastr.success('Project removed from favorites.');
        this.isFavorite = false;
      },
      error: (err) => {
        console.error('Failed to remove project from favorites', err);
        this.toastr.error('Failed to remove project from favorites. Please try again.');
      },
    });
  }

  navigateToPublisherProjects(projectId: string): void {
    if (!projectId) {
        console.error('No project ID provided for navigation.');
        return;
    }

    this.router.navigate(['/bid-opportunities/publishers/projects'], {
        queryParams: { id: projectId },
    }).catch((err) => console.error('Navigation error:', err));
  }
}