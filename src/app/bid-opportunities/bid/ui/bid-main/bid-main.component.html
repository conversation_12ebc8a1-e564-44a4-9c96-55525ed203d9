<!-- page header -->
<header class="bg-light p-4">
	<div class="container">
		<!-- page title -->
		@if(!project())
		{
		<div class="placeholder-glow">
			<span class="col-12 placeholder" style="height: 100%;"></span>
		</div>
		}@else
		{
		<div class="d-flex justify-content-between">
			<h1 class="fs-5 me-3">{{ project()?.ProjectTitle }}</h1>
			@if(eBid()){
			<div class="d-inline-block"
				[ngbPopover]="eBidLoading() ? '' : !eBid()?.IsEnabled  ? 'E-Bidding is not available at this time' : (!isEBidStarted() ? 'E-Bidding has not started yet. Bid Begins on ' + formattedStartDate() : '')"
				[placement]="'left'" triggers="mouseenter:mouseleave">
				<button class="btn btn-primary" routerLink="e-bid"
					[disabled]="!eBid() && (!eBid()?.IsEnabled || !isEBidStarted())">
					E-Bid
					@if(!eBidLoading()){
					<i class="fal fa-info-circle ms-1" *ngIf="!eBid()?.IsEnabled || !isEBidStarted()"></i>
					<!-- <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i> -->
					}
				</button>
			</div>
			}
		</div>
		}
		<!-- publisher -->
		@if(!project()){
		<div class="placeholder-glow">
			<span class="col-2 placeholder" style="height: 100%;"></span>
		</div>
		}@else {
		<div class="mb-3">
			<a (click)="navigateToPublisherProjects(project()?.CreatedByUserId)">
				{{ project()?.Creator || project()?.Owner || 'Projects By This Publisher' }}
			</a>
		</div>
		}
		<!-- links -->
		<div class="row">
			<div class="col-6 col-lg-3 mb-3">
				<div class="d-flex justify-content-between align-items-center bg-white border rounded d-block p-3">
					<a routerLink="documents" class="custom-link">
						<div>
							<div>Bid Documents</div>
							<h4 class="text-dark text-decoration-none">
								@if(project()){
								{{ project()?.DocumentCount }}
								}@else{
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}

							</h4>
						</div>
					</a>
					<div>
						<div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
							style="height: 40px; width: 40px;"><i class="fal fa-file"></i></div>
					</div>
				</div>
			</div>
			<div class="col-6 col-lg-3 mb-3">
				<div class="d-flex justify-content-between align-items-center bg-white border rounded d-block p-3">
					<a routerLink="planholders" [queryParamsHandling]="'preserve'" class="custom-link">
						<div>
							<div>Plan Holders</div>
							<h4 class="text-dark text-decoration-none">
								@if(project()){
								@if(project()?.Permissions?.ShowPlanholdersList){
								{{ project()?.PlanholderCount }}
								}@else{
								NA
								}

								}@else{
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}

							</h4>
						</div>
					</a>
					<div>
						<div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
							style="height: 40px; width: 40px;"><i class="fal fa-list"></i></div>
					</div>
				</div>
			</div>
			<div class="col-6 col-lg-3 mb-3">
				<div class="d-flex justify-content-between align-items-center bg-white border rounded d-block p-3">
					<a routerLink="qa" class="custom-link">
						<div>
							<div>Questions</div>
							<h4 class="text-dark text-decoration-none">
								@if(project()){
								{{ project()?.QuestionCount }}
								}@else{
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}

							</h4>
						</div>
					</a>
					<div>
						<div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
							style="height: 40px; width: 40px;"><i class="fal fa-question"></i></div>
					</div>
				</div>
			</div>
			<div class="col-6 col-lg-3 mb-3">
				<a routerLink="../bid-opening" class="custom-link bg-white border rounded d-block p-3">
					<div></div>
					<h4 class="text-dark text-decoration-none"></h4>
				</a>
			</div>
		</div>
		<!-- add to favorites -->
		<div>
			@if(disableHyperlink()){
			Add to Favorites
			}@else{
			<a class="custom-link" style="cursor: pointer;"
				(click)="isFavorite ? removeFromFavorites() : addToFavorites()">
				<i class="far fa-star me-1 text-warning"></i>
				{{ isFavorite ? 'Remove from Favorites' : 'Add to Favorites' }}
			</a>
			}
		</div>
	</div>
</header>
<!-- project info -->
<div class="container">
	<section class="p-4">
		<router-outlet></router-outlet>
		@if(!project()){
		<div class="col-12 placeholder-glow">
			<div class="col-12">
				<span class="placeholder col-12"></span>
			</div>
		</div>
		}
	</section>
</div>