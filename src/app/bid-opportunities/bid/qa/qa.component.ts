import { Component, effect, inject } from '@angular/core';
import { QaInfoComponent } from './ui/qa-info/qa-info.component';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { BidOpsProjectService } from '../../shared/data-access/bid-ops-project.service';
import { CommonModule } from '@angular/common';
import { QaToolBarComponent } from './ui/qa-tool-bar/qa-tool-bar.component';
import { BidsOpsQAService } from '../../shared/data-access/bid-ops-project-qa.service';

@Component({
    selector: 'app-qa',
    imports: [CommonModule, QaInfoComponent, QaToolBarComponent],
    templateUrl: './qa.component.html',
    styleUrl: './qa.component.css'
})
export class QaComponent {

  bidOpsProjectService = inject(BidOpsProjectService);
  authService = inject(AuthService);
  bidOpsQAService = inject(BidsOpsQAService);
  project = this.bidOpsProjectService.project;
  projectId = this.bidOpsProjectService.projectId;
  isAuthorized = this.authService.isLoggedInSignal;

  projectIdEffect = effect(() => {
    if(this.projectId()){
      this.bidOpsQAService.projectId.set(this.projectId() as string);
    }
  });
}
