<!-- ask a question -->
<form [formGroup]="questionForm" (submit)="addQuestion()">
	<section class="rounded p-3 mb-3 bg-light border">
		<div>
			<!-- text area -->
			<div class="mb-3">
				<label  class="form-label" for="exampleFormControlTextarea1">Please type your question below. Be courteous, as your question will be visible to all CIVCAST users.</label>
				<textarea class="form-control" name="question" formControlName="question" rows="3"></textarea>
			</div>
			<!-- submit button -->
			<div class="d-flex justify-content-end">
				<button type="submit" class="btn btn-outline-dark" [disabled]="!questionForm.valid || isAskingQuestion()">
					@if(isAskingQuestion()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Submit
				</button>
			</div>
		</div>
	</section>
</form>