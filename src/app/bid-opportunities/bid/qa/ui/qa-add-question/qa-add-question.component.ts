import { CommonModule } from '@angular/common';
import { Component, effect, inject, input } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormGroup, FormControl, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { tap } from 'rxjs';
import { BidsOpsQAService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project-qa.service';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';

@Component({
    selector: 'app-qa-add-question',
    imports: [CommonModule, ReactiveFormsModule, FormsModule],
    templateUrl: './qa-add-question.component.html',
    styleUrl: './qa-add-question.component.css'
})
export class QaAddQuestionComponent {
  project = input.required<BidOpsProject>();
  qaService = inject(BidsOpsQAService);
  isAskingQuestion = this.qaService.isAskingQuestion;
  questionForm = this.qaService.questionForm;

  addQuestion() {    
    this.qaService.addQuestion();
  }
}
