import { CommonModule } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { BidsOpsQAService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project-qa.service';
import { QaAddQuestionComponent } from '../qa-add-question/qa-add-question.component';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';

@Component({
    selector: 'app-qa-tool-bar',
    imports: [CommonModule, QaAddQuestionComponent],
    templateUrl: './qa-tool-bar.component.html',
    styleUrl: './qa-tool-bar.component.css'
})
export class QaToolBarComponent {

  isAskQuestion = false;
  qaService = inject(BidsOpsQAService);
  bidOpsProjectService = inject(BidOpsProjectService);
  isLoading = this.qaService.isExcelLoading;
  expireQA = computed(() => this.bidOpsProjectService.project()?.Permissions.ExpireQA as boolean);
  isExcelLoading = this.qaService.isExcelLoading;
  askQuestion() {
    this.isAskQuestion = !this.isAskQuestion;
  }

  getExcel(){
    this.qaService.getQuestionsExcel(this.bidOpsProjectService.project()?.ProjectTitle as string);
  }
}
