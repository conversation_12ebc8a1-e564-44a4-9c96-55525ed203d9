<!-- checkbox: send-me-alerts -->
<section class="form-check mb-3" ng-if="project.Permissions.AllowQuestions && IsAuthenticated && !project.IsQAExpired">
	<input class="form-check-input" id="sendEmailQAAlerts" name="sendEmailQAAlerts" type="checkbox" />
	<label class="form-check-label" for="sendEmailQAAlerts">Send me email alerts for new questions and answers on
		this project. ❌</label>
</section>

<!-- links -->
<section class="mb-3">
	<button class="btn btn-outline-dark me-2" (click)="askQuestion()">Ask a Question</button>
	<button class="btn btn-outline-dark" (click)="getExcel()" [disabled]="isLoading()">
		@if(isLoading()){
			<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
		}
		Download Q&A (.xlsx)
	</button>
</section>

<!-- past deadline -->
@if(expireQA()){
<section class="alert alert-warning mb-3" role="alert">
	The deadline to ask questions is <span style="font-style: italic;">TODO</span>	
</section>
}

<!--ask-question-->
@if(isAskQuestion){
<app-qa-add-question></app-qa-add-question>
}