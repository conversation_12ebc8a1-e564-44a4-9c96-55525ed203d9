<!-- actual questions and answers -->
@if(!isLoading())
{
	<ul class="list-group">
	<!-- has questions -->
	@for(question of allQuestions(); track $index)
	{
		<!-- question + answers -->
		<li class="list-group-item py-4">
			<!-- question -->
			<div class="row">
				<div class="col-12 order-md-2 col-md-3 col-xl-2 col-xxl-1 d-md-flex justify-content-md-end">
					<div>
						<span class="badge text-bg-dark mb-2">{{ question.CreatedDate | date: 'M/dd/yyyy' }}
							<!-- ({{ question.numDays }} days ago) -->
						</span>
					</div>
				</div>
				<div class="col-12 order-md-1 col-md-9 col-xl-10 col-xxl-11">
					<h5 class="page-title d-inline-block mb-0 me-3">
						<span class="fw-bold">{{ $index + 1 }}. </span>
						<span class="fw-bold" [innerHTML]="question.Content"></span>
					</h5>
				</div>
			</div>
			<!-- answers -->
			@for (answer of question.Answers; track $index) 
			{
				<section class="mt-3 mb-3">
					@if(answer.IsDisabled){
					<div [innerHTML]="answer.Content" style="text-decoration:line-through">
					</div>
					}@else {
					<div [innerHTML]="answer.Content">
					</div>
					}
					<span class="badge text-bg-light">{{ answer.CreateDate | date: 'M/dd/yyyy' }}</span>
					<!-- <small class="text-muted">{{ answer.numDays }} days ago</small> -->
				</section>
			}
		</li>
	}
	<!-- no questions yet -->
	@empty 
	{
		<li class="list-group-item">
			<div class="alert alert-info mb-0" role="alert" ng-if="QA.length <= 0">No questions have been asked yet.</div>
		</li>
	}
</ul>
}
<!-- skeleton -->
@else 
{
	<app-qa-skeleton></app-qa-skeleton>
}

