import { CommonModule } from '@angular/common';
import { Component, effect, inject, input, On<PERSON><PERSON>roy } from '@angular/core';
import { BidsOpsQAService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project-qa.service';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { Question } from 'src/app/bid-opportunities/shared/interfaces/bid-ops-qa';
import { QaSkeletonComponent } from '../qa-skeleton/qa-skeleton.component';
import { HelperTools } from 'src/app/shared/utils/helper-tools';
import { v4 } from 'uuid';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs';

@Component({
    selector: 'app-qa-info',
    imports: [CommonModule, QaSkeletonComponent],
    templateUrl: './qa-info.component.html',
    styleUrl: './qa-info.component.css'
})
export class QaInfoComponent implements OnDestroy {
  ngOnDestroy(): void {
    
  }


  qaService = inject(BidsOpsQAService);
  questions = this.qaService.projectQuestions;
  allQuestions = this.qaService.questionsFormatted;

  isExcelLoading = this.qaService.isExcelLoading;
  isLoading = this.qaService.isLoading;


  
}