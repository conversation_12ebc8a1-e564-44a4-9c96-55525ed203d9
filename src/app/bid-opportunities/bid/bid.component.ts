import { Component, On<PERSON><PERSON>roy, OnInit, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { BidOpsProjectService } from '../shared/data-access/bid-ops-project.service';
import { BidMainComponent } from './ui/bid-main/bid-main.component';
import { Subscription } from 'rxjs';
import { BidBidderFolderService } from '../shared/data-access/bid-bidderfolder.service';
import { ClickStreamService } from 'src/app/account/shared/data-access/click-stream.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { RequiredDownloadsService } from 'src/app/ebid/data-access/required-downloads.service';
import { BidAdvService } from 'src/app/bid-adv/shared/data-access/bid.service';
import { EBidProjectService } from 'src/app/bid-adv/shared/data-access/bids-ebid-project.service';
import { BidsHistoryService } from 'src/app/bid-adv/shared/data-access/bids-history-service';
import { AcknowledgeService } from 'src/app/ebid/data-access/acknowledge.service';
import { AddendaAcknowledgeService } from 'src/app/ebid/data-access/addenda-acknowledge.service';
import { EBidFolderService } from 'src/app/ebid/data-access/bid-folder.service';
import { BidFormService } from 'src/app/ebid/data-access/bidform-service';
import { CompletionTimeService } from 'src/app/ebid/data-access/completion-time.service';
import { EBidHistoryService } from 'src/app/ebid/data-access/ebid-history.service';
import { EBidService } from 'src/app/ebid/data-access/ebid.service';
import { RequiredUploadService } from 'src/app/ebid/data-access/required-uploads.service';
import { WorkOrderService } from 'src/app/ebid/data-access/work-order-service';
import { BidOpsDocumentService } from '../shared/data-access/bid-ops-document.service';
import { BidOpsProjectsService } from '../shared/data-access/bid-ops-projects-service';
import { BidOverService } from '../shared/data-access/bid-over.service';
import { DownloadHistoryService } from '../shared/data-access/download-history.service';

@Component({
    selector: 'app-bid',
    imports: [CommonModule, BidMainComponent],
    templateUrl: './bid.component.html',
    providers: [EBidProjectService,
              EBidHistoryService,            
              EBidFolderService,
              BidOpsDocumentService,
              BidsHistoryService,
              RequiredDownloadsService,
              AddendaAcknowledgeService,
              RequiredUploadService,
              CompletionTimeService,
              WorkOrderService,
              BidFormService,
              EBidService,              
              AcknowledgeService,
              BidOpsProjectsService,					
              DownloadHistoryService,					
              BidOverService,
              BidBidderFolderService],
    styleUrl: './bid.component.css'
})
export class BidComponent implements OnInit, OnDestroy{

  bidOpsProjectService = inject(BidOpsProjectService);
  bidFolderService = inject(BidBidderFolderService);  
  eBidsService = inject(EBidProjectService);
  clickStreamService = inject(ClickStreamService);
  authService = inject(AuthService);
  aRoute = inject(ActivatedRoute);  
  project = this.bidOpsProjectService.project;  
  routeSubscription : Subscription | null = null;

  constructor(){
    //When logged in is signaled, we can start the click stream effect from this page
    effect(() => {
      if(this.authService.isLoggedInSignal()){
        this.routeSubscription = this.aRoute.params.subscribe(params => {
          const projectId = params['projectId'];
          if(projectId){     
            this.clickStreamService.addProjectClick(projectId);
            this.bidFolderService.projectId.set(projectId);       
            this.eBidsService.projectId.set(projectId);   
            this.bidFolderService.projectId.set(projectId);                     
          }
        });
      }
  });
  }

  ngOnInit(): void {
    this.bidFolderService.startEffects();

    this.routeSubscription = this.aRoute.params.subscribe(params => {
      const projectId = params['projectId'];
      if(projectId){        
        this.bidOpsProjectService.projectId.set(projectId);       

      }
    });
  }

  ngOnDestroy(): void {
    this.routeSubscription?.unsubscribe();
    this.bidFolderService.stopEffects();
  }
  
}