import { Component, OnDestroy, inject, signal } from '@angular/core';
import { BidOpsProjectService } from '../../shared/data-access/bid-ops-project.service';
import { BidOpsDocumentService } from '../../shared/data-access/bid-ops-document.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { DocumentInfoComponent } from './ui/document-info/document-info.component';
import { ProjectFileInfo } from '../../shared/interfaces/bid-ops-documents';

@Component({
    selector: 'app-documents',
    imports: [DocumentInfoComponent],
    templateUrl: './documents.component.html',
    styleUrl: './documents.component.css'
})
export class DocumentsComponent implements OnDestroy {

  bidOpsProjectService = inject(BidOpsProjectService);
  bidOpsDocumentService = inject(BidOpsDocumentService);
  authService = inject(AuthService);
  project = this.bidOpsProjectService.project;
  isAuthenticated = this.authService.isLoggedInSignal;
  filesLoaded = signal<boolean>(false);
  ngOnDestroy(): void {
    

  }

  download(event: ProjectFileInfo) {
  }

  filesLoadedEvent(event: boolean) {
    this.filesLoaded.set(event);
  }
}
