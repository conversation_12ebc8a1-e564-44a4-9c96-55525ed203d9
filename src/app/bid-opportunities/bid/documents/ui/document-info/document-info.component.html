<!-- skeleton -->
@if(isLoading){
	<app-document-skeleton></app-document-skeleton>
}@else{
	<!-- IF not official bidding docuements -->
	@if(!project()?.Permissions.IsPlansOfficial){
		<div class="alert alert-light">
			<div class="form-check">
				<label class="form-check-label text-success">
					<input type="checkbox" class="form-check-input" [(ngModel)]="verifyProjectPlans">
					I understand these are not official bidding documents.
				</label>
			</div>
		</div>
	}
	<!-- IF the project has plans -->
	@if(project()?.Permissions?.IsPlansOfficial){
		<!-- IF the files are too large -->
		@if(fileLimitCheck() && !fileLimitCheck()?.IsAllowed){
			<div class="alert alert-info">
				<i class="fas fa-info-circle me-2"></i>Due to their large size, files cannot be downloaded all at once or in groups.
				Please download each file individually.
			</div>
		<!-- zip progress bar and buttons --->
		}@else {
			<!-- buttons - download all and download selected -->
			@if(allFiles()?.length > 0 && fileLimitCheck()){
				<div class="d-flex mb-3">
					<button class="btn btn-primary ms-0 me-2" (click)="downloadAll()"
						[disabled]="isDownloadAll() || isSelectedFilesDownloadAll()">
						@if(isDownloadAll()){
							<i class="fas fa-circle-notch fa-spin fa-1x"></i>
						}
						Download All
					</button>
					<button class="btn btn-primary" (click)="downloadSelected()"
						[disabled]="isSelectedFilesDownloadAll() || isDownloadAll() || downloadSelectedDisabled()">
						@if(isSelectedFilesDownloadAll()){
							<i class="fas fa-circle-notch fa-spin fa-1x"></i>
						}
						Download Selected
					</button>
				</div>
			}
		}
		<!--all docs-->
		@for (group of (allFiles() | groupBy: 'File.Category.Group'); track $index) {
			<div class="card mb-3">
				<div class="card-body">
					<h2 class="card-title fs-6 page-title">
						{{ group.key }}</h2>
					@for (file of group.value; track $index) {
					<div class="row d-flex align-items-center">
						<!-- file no longer exists -->
						@if(file.isNotFound){
						<div class="col-6 col-md-8 order-1 order-md-1 text-warning">
							{{ file.File.Title}} - This document no longer exists.
						</div>
						}
						<!-- checkbox and file name -->
						@else {
						<div class="col-6 col-md-8 d-flex order-1 order-md-1">
							<!-- checkbox -->
							@if(!disableView() && isFilesAllowedToDownloadAll){
								<input class="form-check-label m-0 p-0 mr-2" type="checkbox" [(ngModel)]="file.isSelected"  (ngModelChange)="setSelectedDocuments(file)" />
							}
							<!-- file name -->
							@if(!file.isDownloading){
								<div>
									@if(!disableView()){
									<button type="button" (click)="download(file)"
										class="btn btn-link text-start text-decoration-none">{{ file.File.Title
										}}</button>
									}@else {
									<span class="text-secondary">{{ file.File.Title}}</span>
									}
								</div>
							<!-- progress bar -->
							}@else {
								<div class="w-100">
									<div class="progress" style="height:35px;">
										<div class="progress-bar progress-bar-striped progress-bar-animated bg-success"
											role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0"
											aria-valuemax="100"></div>
									</div>
								</div>
							}
						</div>
						}
						<!-- upload date -->
						<div class="col-12 col-md-3 order-3 order-md-2">
							<small class="text-muted">
								Uploaded on {{ file.File.DateStamp | date:'M/dd/yyyy' }}
							</small>
						</div>
						<!-- check mark or "x" -->
						@if(!disableView()){
						<div class="col-6 col-md-1 d-flex justify-content-end order-2 order-md-3">
							@if(isDownloadHistoryLoading()){
							<i class="fas fa-circle-notch fa-spin fa-2x"></i>
							}@else{
							@if(file.File.IsDownloaded === true){
							<i class="fas fa-check fa-lg text-success" aria-hidden="true"></i>
							}
							@else {
							<i class="fas fa-times fa-lg text-danger" aria-hidden="true"></i>
							}
							}
						</div>
						}
					</div>
					}
				</div>
			</div>
		}@empty {
			<div class="alert alert-info">
				<i class="fas fa-info-circle me-2"></i>No documents have been uploaded yet.
			</div>
		}
	}
}