import { CommonModule } from '@angular/common';
import { Component, effect, inject, OnDestroy, OnInit } from '@angular/core';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { BidOverService } from 'src/app/bid-opportunities/shared/data-access/bid-over.service';
import { EBidFolder } from 'src/app/ebid/ui/bid-folder/bid-folder.component';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';

@Component({
  selector: 'app-bid-over',
  imports: [CommonModule, EBidFolder],
  templateUrl: './bid-over.component.html',
  styleUrl: './bid-over.component.css'
})
export class BidOverComponent implements OnInit, OnDestroy {

  bidOverService = inject(BidOverService);
  bidOpsProjectService = inject(BidOpsProjectService);
  confirmService = inject(ConfirmService);
  localBidDate = this.bidOverService.localBidDate;
  eBid = this.bidOverService.eBid;
  bidderBidInfo = this.bidOverService.bidderBidInfo;
  projectId = this.bidOverService.projectId;
  view = this.bidOverService.view;
  isLoading = this.bidOverService.isLoading;
  ebidFormGroup = this.bidOverService.ebidFormGroup;
  isDownloadingReport = this.bidOverService.isDownloadingReport;
  isWithdrawingBid = this.bidOverService.isWithdrawingBid;

  constructor() {
    effect(() => {
      if(this.bidOpsProjectService.projectId()){
        this.bidOverService.projectId.set(this.bidOpsProjectService.projectId() as string);
      }
    });
  }	

  ngOnInit(): void {
    this.bidOverService.startEffects();    
  }
  ngOnDestroy(): void {
    this.bidOverService.stopEffects();
  }

  exportBidFolder(){
    this.bidOverService.exportBidFolder();
  }

  withdrawBid(){

    this.confirmService.open("Are you sure you want to withdraw this bid? This cannot be undone.", "Withdraw Bid").result.then((result) => {
      if(result == "yes"){
        this.bidOverService.withdrawBid();
      }
    });
  }
}