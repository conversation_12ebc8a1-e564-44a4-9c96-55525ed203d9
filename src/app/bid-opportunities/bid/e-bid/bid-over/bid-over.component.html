@if(isLoading()){
	<div class="placeholder-glow">
		<span class="placeholder col-12"></span>
	</div>
	
}@else {
	<div class="alert alert-info my-2">
		This bid is over. The bid date was {{  localBidDate() | date : 'M/dd/yyyy h:mm a' }}
	</div>
	
	@if(eBid() && bidderBidInfo()?.SubmittedAt){
	
		@if(bidderBidInfo()?.OpenedAt){
			<div class="alert alert-success mb-0" role="alert">
				Your bid has been opened on {{ bidderBidInfo()?.OpenedAt | date : 'M/dd/yyyy h:mm a' }}
			</div>
		}@else{
			<div class="alert alert-warning mb-0" role="alert">
				Your bid has been submitted and is awaiting opening.
			</div>		
		}

		@if(bidderBidInfo()?.RejectedAt){
			<div class="d-flex flex-column my-2 alert alert-danger">
				<span class="mb-4">Your bid has been returned by the Engineer.</span>
				<span class="mb-4">Do you want to delete this bid submission?</span>
				<div>
					<button type="button" class="btn btn-danger" (click)="withdrawBid();" [disabled]="isWithdrawingBid()">
						@if(isWithdrawingBid()){
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						Withdraw Bid
					</button>
				</div>
			</div>
		}
	
		<h4>Submitted Bid View</h4>
		<div class="btn-group mb-4" role="group" aria-label="Basic example">		
			<button type="button" class="btn btn-primary" (click)="exportBidFolder()" [disabled]="isDownloadingReport()">
				@if(isDownloadingReport()){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
				}
				
				Export Bid Folder
			</button>
		</div>
	
		<app-ebid-folder></app-ebid-folder>
	}@else {
		<div>
			<span>This bid is over. The bid date was You did not submit a bid.</span>
		</div>
	}
	
}
