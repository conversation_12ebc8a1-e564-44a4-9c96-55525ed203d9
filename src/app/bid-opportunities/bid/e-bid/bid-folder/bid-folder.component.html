
<!-- header -->
<header class="mb-3">
	<h5 class="page-title text-secondary">E-Bid</h5>
</header>

<div style="padding-bottom: 100px; float: left" #trackedDiv>
	&nbsp;
</div>

@if(isLoading()){
	<app-bid-folder-skeleton></app-bid-folder-skeleton>
}@else {
	@if(eBidLoadingError()){
		<div class="alert alert-danger my-2" role="alert">
			<h4>
				{{eBidLoadingError() }}
			</h4> 
			
			There was an issue loading the ebid. If this issue persists please contact support. Try refreshing the page.
		</div>
	}@else if(!eBid()){
		<div class="alert alert-info my-2" role="alert">
			<h4>
				This project is not running electronic bidding at this time.
			</h4> 
		</div>			
	}@else if (!isBidStarted()) {
		<div class="alert alert-info my-2" role="alert">		
			<h4>
				Bidding has not started yet.
			</h4> 
			<div>
				@if(startDate()){
					The bid is schedule to start at {{ startDate() | date : 'M/dd/yyyy h:mm a'  }}.
				}@else {
					<span style="font-style: italic; font-size: large;">No start date set.</span>
					
				}				
			</div>			
			
			<div>
				Please check back at that time.			
			</div>		
		</div>	
	
	}@else if(!eBid()?.IsEnabled){
		<div class="alert alert-info my-2" role="alert">
			<h4>
				Bidding is not available at this time
			</h4> 
		</div>		
	}
	@else{	
		<div class="btn-group my-4">
			@if(view() === BIDFORM_VIEWS.BID && bidderBidInfo()?.SubmittedAt === null){			

				<button type="button" class="btn btn-outline-dark" ngbTooltip="ctrl+s" placement="top" (click)="saveBidderBidInfo()"  [disabled]="isLoading()">
					@if(isSavingBidderInfo()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Save
				</button>
				<button type="button" class="btn btn-outline-dark"  [disabled]="isLoading() || !isFormValid || isSubmittingBid()" (click)="submitBid()">
					@if(isSubmittingBid()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Submit
				</button>			
			}@else if((view() === BIDFORM_VIEWS.BID || view() === BIDFORM_VIEWS.VIEW) && bidderBidInfo()?.SubmittedAt !== null){
		
				<button type="button" class="btn btn-outline-danger"  [disabled]="isUnSubmittingBid()" (click)="unSubmitBid()">
					@if(isUnSubmittingBid()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Remove Submission
				</button>		
				
			}
		</div>
		@if(!isFormValid){
			<div class="alert alert-danger">All sections must be complete before submitting your bid</div>
		}

		@if(bidderBidInfo()?.SubmittedAt !== null){
			<div class="alert alert-info">
				This bid was submitted on {{bidderBidInfo()?.SubmittedAt | date:'M/dd/yyyy hh:mm:ss a'}}
			</div>			
		}

		@if(isFormValid && view() === BIDFORM_VIEWS.BID && bidderBidInfo()?.SubmittedAt === null || isBidExpiringSoon()){
			<div class="text-center border border-dark" [ngClass]="{'fixed-bottom': !isInView(), 'rounded': isInView()}" 
			style="padding-top: 20px; padding-bottom: 20px; padding-left: 10px; padding-right: 10px; background-color: lightyellow;">
				@if(isFormValid){
					<div style="font-size: large;" class="text-center">
						Congratulations! All sections are complete.
						However, you have <span style="color: red; font-weight: 600;">NOT SUBMITTED</span> your bid. 
						<span style="color: red; font-weight: 600;">PLEASE SUBMIT YOUR BID</span> or it will not be considered.
					</div>
				}@else {
					<div style="font-size: large;" class="text-center">
						Not much time is left! All sections must be complete before submitting your bid.
					</div>
				}

				@if(showFinalWarning()){
					<div class="alert alert-danger my-4">
						Submit NOW, you will be redirected in a few seconds!
						<div class="mt-2">
							<button type="button" class="btn btn-outline-dark"  [disabled]="isLoading() || !isFormValid || isSubmittingBid()" (click)="submitBid()">
								@if(isSubmittingBid()){
									<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}
								Submit
							</button>
						</div>
					</div>
					
				}
			
				@if(!isInView()){
					<div class="mt-2">
						<button type="button" class="btn btn-outline-dark"  [disabled]="isLoading() || !isFormValid || isSubmittingBid()" (click)="submitBid()">
							@if(isSubmittingBid()){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
							Submit
						</button>
					</div>
					

					<app-bid-clock></app-bid-clock>
				}				
			</div>
			
		}

		@if(bidderBidInfo()?.SubmittedAt && view() === BIDFORM_VIEWS.BID){				
			<div class="text-muted small">Submitted at: {{bidderBidInfoData()?.SubmittedAt | date:'medium'}}</div>			
		}

		@if(bidderBidInfo()?.CurrentAction === "MAIN_BID_UPDATE"){
			<div class="alert alert-danger my-2" role="alert">
				The main bid was changed and may have effected your bid.  If you have submitted you must verify and re-submit..
			</div>
		}
			
		<div class="my-1">
			<span>Bid Date: </span>
			<!-- <span ng-if="Project.Permissions.IsTBA">TBA</span> -->
			<span>
				{{  localBidDate() | date : 'M/dd/yyyy h:mm a' }}			
			</span>
		</div>

		<section class="border mb-3 p-3">	
			<app-bid-clock [endDate]="localBidDate()"></app-bid-clock>
		</section>	

		<app-ebid-folder></app-ebid-folder>
		
	}
}

@if(!isInView()){
	<div style="margin-bottom: 200px;" #trackedDiv>

	</div>
}
