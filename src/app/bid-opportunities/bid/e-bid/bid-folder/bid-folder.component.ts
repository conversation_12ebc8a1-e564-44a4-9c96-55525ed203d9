import { trigger, state, style, transition, animate } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, computed, effect, ElementRef, inject, Injector, OnDestroy, OnInit, signal, ViewChild, viewChild } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { BidBidderFolderService } from 'src/app/bid-opportunities/shared/data-access/bid-bidderfolder.service';
import { BidOpsDocumentService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-document.service';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { BidClockService } from 'src/app/ebid/data-access/bid-clock.services';
import { EBidFolderService } from 'src/app/ebid/data-access/bid-folder.service';
import { EBidService } from 'src/app/ebid/data-access/ebid.service';
import { BidViews, DocumentInfo } from 'src/app/ebid/interfaces/ebid';
import { BidClockComponent } from 'src/app/ebid/ui/bid-clock/bid-clock.component';
import { BidFolderSkeletonComponent } from 'src/app/ebid/ui/bid-folder-skeleton/bid-folder-skeleton.component';
import { EBidFolder } from 'src/app/ebid/ui/bid-folder/bid-folder.component';
import { BaseEffectsService } from 'src/app/shared/interfaces/abstract-effects-service';

@Component({
  selector: 'app-bid-ops-bid-folder',
  imports: [CommonModule, EBidFolder, BidClockComponent, BidFolderSkeletonComponent],
  providers: [BidClockService],
  templateUrl: './bid-folder.component.html',
  styleUrl: './bid-folder.component.css'
})
export class BidFolderComponent implements OnInit, OnDestroy, AfterViewInit {

  //INJECTS
  eBidService = inject(EBidService);
  bidOpsProjectService = inject(BidOpsProjectService);
  bidOpsDocumentService = inject(BidOpsDocumentService);
  toastrService = inject(ToastrService);
  bidFolderService = inject(BidBidderFolderService);
  changeDetection = inject(ChangeDetectorRef);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  bidClockService = inject(BidClockService);

  //SIGNALS
  eBid = this.bidFolderService.eBid;
  bidderBidInfo = this.bidFolderService.bidderBidInfo;
  projectId = this.bidOpsProjectService.projectId;
  view = this.bidFolderService.view;
  documents = this.bidOpsDocumentService.files;
  project = this.bidOpsProjectService.project;
  localBidDate = this.bidOpsProjectService.localBidDate;
  startDate = this.bidFolderService.startDate;
  isBidStarted = this.bidFolderService.isBidStarted;
  ebidFormGroup = this.bidFolderService.ebidFormGroup;
  //LOCAL PROPERTIES
  isSavingBidderInfo = this.bidFolderService.isSavingBidderInfo;
  isLoading = this.bidFolderService.isLoading;
  isSubmittingBid = this.bidFolderService.isSubmittingBid;
  isUnSubmittingBid = this.bidFolderService.isUnSubmittingBid;
  eBidLoadingError = this.bidFolderService.ebidError;
  navigateSubscription: Subscription | null = null;
  isBidExpiringSoon = this.bidClockService.isBidExpiringSoon;

  @ViewChild('trackedDiv') trackedDiv: ElementRef = {} as ElementRef;
  isInView = signal(false);
  isValid = signal<boolean>(false)
  maxTimeToSubmitWhenBidOver = 10000;
  showFinalWarning = signal<boolean>(false);
  public readonly BIDFORM_VIEWS: typeof BidViews = BidViews;

  effectInjector = inject(Injector);

  constructor() { 

    this.navigateSubscription = this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (this.ebidFormGroup.touched && this.ebidFormGroup.dirty) {
          if (!confirm('You have not saved your form. Do you want to leave?')) {
            this.router.navigateByUrl(this.router.url, { skipLocationChange: true });
          }
        }
      } else if (event instanceof NavigationEnd) {
        this.ebidFormGroup.markAsPristine();
        this.ebidFormGroup.markAsUntouched();
        this.router.navigateByUrl(this.router.url, { skipLocationChange: true });
      }
    });

    effect(() => {
      if (this.bidClockService.clockEnd()) {
        console.log('clockEndEffect', this.bidClockService.clockEnd());

        const currentTime = new Date().getTime();
        const endTime = this.bidClockService.endDate()?.getTime();

        if(endTime){
          const timeDifference = currentTime - endTime;
          const isLessThan10Seconds = timeDifference < 10000;
          if (isLessThan10Seconds) {
            if(!this.bidderBidInfo()?.SubmittedAt && this.isFormValid){           
              this.showFinalWarning.set(true);
              setTimeout(() => {
                this.goToBidOver();  
              }, this.maxTimeToSubmitWhenBidOver);
              
              return;
            }
          }
        }          
        
        this.goToBidOver();       
      }
    });

    effect(() => {
      if (this.bidOpsProjectService.projectId()) {      
        this.bidFolderService.projectId.set(this.bidOpsProjectService.projectId() as string);
      }
    });

    effect(() => {
      if(this.localBidDate()){
        this.bidClockService.endDate.set(this.localBidDate());
      }
    });

    this.bidClockService.startEffects();
  }

  ngAfterViewInit() {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          this.isInView.set(entry.isIntersecting);
        });
      },
      { threshold: 0.1 } // Trigger when 10% of the element is visible
    );
    
    observer.observe(this.trackedDiv?.nativeElement);  
  }

  get isFormValid(): boolean {
    return this.ebidFormGroup?.valid ?? false;
  }

  get isFormDirty(): boolean {
    return this.ebidFormGroup?.dirty ?? false;
  }
  ngOnInit(): void {    
    this.bidFolderService.startEffects();    

  }
  ngOnDestroy(): void {
    this.navigateSubscription?.unsubscribe();        
    this.bidFolderService.stopEffects();
    this.bidClockService.stopEffects();
  }

  goToBidOver(){
    this.ebidFormGroup.markAsPristine();
    this.ebidFormGroup.markAllAsTouched();
    this.router.navigate(['../bid-over'], { relativeTo: this.aRoute, skipLocationChange: false, replaceUrl: true });  
  }
  saveBidderBidInfo() {
    this.bidFolderService.saveBidderBidInfo();
  }

  setView(view: BidViews) {
    this.view.set(view);
  }

  submitBid() {
    this.bidFolderService.submitBid();    
  }
  unSubmitBid() {
    this.bidFolderService.unSubmitBid();
  }

  downloadDocument(doc: DocumentInfo) {
    this.bidFolderService.downloadDocument(doc);
  }
}