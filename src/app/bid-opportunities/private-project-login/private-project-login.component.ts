import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BidOpsPrivateProjectService } from '../shared/data-access/bid-private-project.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'app-private-project-login',
    imports: [ReactiveFormsModule, FormsModule, CommonModule],
    templateUrl: './private-project-login.component.html',
    styleUrl: './private-project-login.component.css'
})
export class PrivateProjectLoginComponent {

  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  privateProjectService = inject(BidOpsPrivateProjectService);
  privateId = signal<string>('');
  privateForm = this.privateProjectService.privateForm;  
  isLoading = this.privateProjectService.projectLoading;
  errorMessage = this.privateProjectService.errorMessage;
  isAutoLogin = this.privateProjectService.isAutoLogin;

  constructor(){
    const privateId = this.aRoute.snapshot.queryParams['privateId'];
    this.privateId.set(privateId);

    if(privateId){
      this.privateForm.controls.privateId.setValue(privateId);
    }
  }

  goToPrivateProject(){
    this.privateProjectService.privateProjectId.set(this.privateForm.controls.privateId.value?.trim() ?? '');    
  }

  dismiss(){
    this.privateProjectService.dismissModal('Cross click');
    
    const snapshot = this.aRoute.snapshot;

    if(snapshot.queryParams['return-url']){
      this.router.navigate([snapshot.queryParams['return-url']],{ queryParams: this.aRoute.snapshot.queryParams, queryParamsHandling: 'merge', replaceUrl: true });
    }else{
      this.router.navigate(['/bid-opportunities'],{ queryParams: this.aRoute.snapshot.queryParams, queryParamsHandling: 'merge', replaceUrl: true });
    }

    
  }
}
