<form [formGroup]="privateForm" (submit)="goToPrivateProject()">
	<!-- modal: private bids login -->
	<div class="modal-content">
		<div class="modal-header">
			<h1 class="modal-title fs-5" id="exampleModalLabel">Private Bid Login</h1>
			<button type="button" class="btn-close" (click)="dismiss()" [disabled]="isAutoLogin()"></button>
		</div>
		<div class="modal-body">
			<p>Private bids are accessible by invitation only. You must receive an ID from the
				organization that listed the project.</p>

			<div class="mb-3">
				<label for="exampleInputEmail1" class="form-label">ID</label>
				<input class="form-control" type="text" formControlName="privateId" style="letter-spacing: 5px; font-size: larger; font-family: Consolas, sans-serif;"  />
				@if(errorMessage()){
					<div class="alert alert-danger mt-2" role="alert">
						{{ errorMessage() }}
					</div>
				}

				@if(isAutoLogin()){
					<div class="alert alert-info mt-2" role="alert">
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						Logging into Private Project...
					</div>
				}
			</div>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-secondary" (click)="dismiss()" [disabled]="isAutoLogin()">Close</button>
			<button type="submit" class="btn btn-primary" [disabled]="!privateForm.valid || isLoading() || isAutoLogin()">
				@if(isLoading() || isAutoLogin())
				{
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
				}
				Go
			</button>
		</div>
	</div>

</form>