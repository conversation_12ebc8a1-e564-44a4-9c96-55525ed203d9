export interface DownloadHistory{
	DateCreated: Date;
	FileTitle: string;
	FileType: string;	
	ProjectTitle: string;
	ProjectId:string;
	IsDownloading: boolean;
	FileId: string;
	FileExtension: string;
}

export interface DownloadHistoryInfo{
	History: DownloadHistory;
	ProjectStatus: ProjectStatus;
	
}

export enum ProjectStatus{
	Private = 'Private',
	Public = 'Public',
	Hidden = 'Hidden'
}

export interface DownloadHistoryGrid{
	DownloadHistory: DownloadHistory[];
	Total: number;
}

export interface DownloadHistoryResponse{
	DownloadHistory: DownloadHistory[];
	Total: number;
}

export class DownloadHistoryRequest{
	Page: number = 1;	
	Search: string | null = null;	
	SortBy: string = "DateCreated";		
	SortOrder: string = 'desc';	
	Limit: number = 25;
}