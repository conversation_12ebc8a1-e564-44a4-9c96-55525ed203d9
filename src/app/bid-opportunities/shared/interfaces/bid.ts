export interface BidOpsProjectsGrid{
	Projects: Array<BidOpsGridInfo>;
	TotalCount: number;
}

export interface BidOpsGridInfo{
	ProjectId: string;
	ProjectTitle: string;
	BidDate: Date;
	County: string;
	State: string;
	IsTBA: boolean;
	IsClicked: boolean;
}

export interface BidOpsProject {
	Id: string;
	ProjectTitle: string;
	CountyName: string;
	StateName: string;
	InternalId: string;
	ProjectUniqueId: string;
	DetailsEstimate: string;
	TypeOfWork: string;
	Owner:string;
	Password: string;
	Notes: string;
	MapCode:string;
	Scope:string;
	AdditionalNotes:string;
	DocumentCount: number;
	PlanholderCount: number;
	QuestionCount: number;
	CustomMapUrl: string | null;
	Location: Location;
	ReleaseDateTimeInfo: TimeInfo;
	BidDetails: BidDateTime;
	PreBidDetails: BidDateTime;
	Permissions: ProjectPermissions;
	ContactInfo: AccountProfile;
	PaymentDate: string | null;
	QAExpirationDateTimeInfo: TimeInfo;
	QuestionEmails: Array<EmailAddress>;
	Planholders: Array<Planholder>;
	// Questions: Array<Question>;
	IsTBA: boolean;
	IsBid: boolean;
	IsQAExpired: boolean;
	LowBidName: string;
	LowBidAmount: number;
	BidOpeningLink: string;
	PreBidMeetingLink: string;
  }

//   export interface Question{

//   }


  export interface Planholder{
	CreateDateTime: string;
	CompanyType: CompanyType;
	Actions: Array<string>;
	UserId: string;
	CognitoUserId: string;
	CompanyName: string;
	FirstName: string;
	LastName: string;
	Phone: string;
	Fax: string;
	Email: string;
	NotBidding: boolean;
	DBECerts: Array<string>;

}

export interface CompanyType{
	CompanyTypeId: number;
	Name: string;
	Order: number;
}

  export interface ProjectPermissions{
	  ShowPlanholdersList:boolean;
	  AllowQuestions:boolean;
	  IsPrivate: boolean;
	  IsVisible: boolean;
	  IsPlansOfficial: boolean;
	  IsTBA: boolean;
	  ExpireQA: boolean;
  }
  
  export interface BidDateTime {
	BidDateTimeInfo: TimeInfo;
	Location: string;
	Notes: string;
  }
  
  export interface TimeInfo {
	Date: string | null;
	Month: number | null;
	Day: number | null;
	Year: number | null;
	Hour: number | null;
	Minute: number | null;
	TimeZone: TimeZone | null;
  }
  
  export interface TimeZone {
	ZoneId: string;
	Name: string;
	Value: string;
	Offset: string;
  }
  
  export interface Location {
	LocationId: string | null;
	State: State | null;
	County: County | null;
	MapData: MapData | null;
  }
  
  export interface County {
	CountyId: number;
	State: string;
	StateId: number;
	Name: string;
	ADM2: string | null;
	Latitude: number;
	Longitude: number;
  }
  
  export interface MapData {
	Latitude: number;
	Longitude: number;
  }
  
  export interface State {
	StateId: number;
	Name: string;
	Abbreviation: string;
  }
  
  export interface EmailAddress{
	  DisplayName: string;
	  Email: string;
  }

  export interface AccountProfile{
    FirstName: string;
    LastName: string;
    Company: Company;
    Email: string;
    Username: string;
}

export interface Company{
    Name: string;
    Phone: string;
	Fax: string;
    Address: Address;
    CompanyType: CompanyType;
	Website:string;
}


export interface Address{
    Name: string;
    Address1: string;
    Address2: string;
    City: string;
    State: State;
    Zip: string;
}
