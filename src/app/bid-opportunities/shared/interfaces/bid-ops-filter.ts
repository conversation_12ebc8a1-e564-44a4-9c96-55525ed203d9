import { NgbDate } from "@ng-bootstrap/ng-bootstrap";
import { bool } from "aws-sdk/clients/signer";

export interface BidOpsFilter {
	selectedTime: number | null;
	search: string | null;
	hideTBA: boolean | null;
	projectTitle: string | null;
	state: string | null;
	county: string | null;
	internalId: string | null;
	publisher: string | null;
	projectworktype: string | null;
	orderBy: string;
	isReversed: boolean;
}