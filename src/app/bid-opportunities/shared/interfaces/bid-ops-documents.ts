import { ProjectFile } from "src/app/bid-adv/shared/interfaces/bids-docs";

export class ProjectFileInfo{
	constructor(file:ProjectFile)
	{
	  this.File = file;
	  
	}
	File: ProjectFile = {} as ProjectFile;
	isDownloading: boolean = false;
	isSelected: boolean = false;
	isNotFound: boolean = false;
  }

  export interface DocumentCheckLimitResponse {
	FileSizeInMB: number;
	RawFileSize: number
	IsAllowed: boolean;
}