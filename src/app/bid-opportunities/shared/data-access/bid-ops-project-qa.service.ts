import { HttpClient } from "@angular/common/http";
import { Injectable, computed, effect, inject, signal } from "@angular/core";
import { of, Subscription } from "rxjs";
import { environment } from "src/environments/environment";
import { Question } from "../interfaces/bid-ops-qa";
import { rxResource } from "@angular/core/rxjs-interop";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { FormControl, FormGroup, Validators } from "@angular/forms";

@Injectable({
	providedIn: 'root'
})
export class BidsOpsQAService {

	client = inject(HttpClient);
	projectId = signal<string | null>(null);
	projectQuestions = computed(() => this.projectQuestionsResource.value());
	questionsFormatted = signal<Array<Question>>([]);
	projectQuestionsSubscription: Subscription | null = null;
	excelDownloadSubscription: Subscription | null = null;
	isLoading = computed(() => this.projectQuestionsResource.isLoading());
	isExcelLoading = signal<boolean>(false);
	isAskingQuestion = signal<boolean>(false);

	questionForm = new FormGroup({
		question: new FormControl('', Validators.required)
	});

	destroy() {
		this.projectQuestionsSubscription?.unsubscribe();		
		this.excelDownloadSubscription?.unsubscribe();
	}

	projectQuestionsResource = rxResource({
		request: () => (
			this.projectId()),
		loader: (request) => {
			if (request.request) {
				return this.client.get<Array<Question>>(`${environment.services_root_endpoints.bid_ops_qa}/${request.request}`)
			}

			return of(null);
		}
	});


	questionsSignal = effect(() => {
		if (this.projectQuestions()) {
			let allQuestions = [];
			for (let question of this.projectQuestions() as Question[]) {
				for (let answer of question.Answers) {
					answer.Content = HelperTools.wrapImagesWithLinks(answer.Content);
				}

				allQuestions.push(question);
			}

			this.questionsFormatted.set(allQuestions);
		}
	});


	getQuestionsExcel(projectTitle: string) {
		this.excelDownloadSubscription?.unsubscribe();

		this.isExcelLoading.set(true);

		this.excelDownloadSubscription = this.client.get<QAExcelResponse>(`${environment.services_root_endpoints.bid_ops_qa}/${this.projectId()}/excel?project-title=${projectTitle}`).subscribe({
			next: (res) => {
				this.isExcelLoading.set(false);
				document.location.href = res.Url;
			},
			error: (err) => {
				this.isExcelLoading.set(false);
				console.error(err);
			}
		});

	}

	addQuestion() {
		this.isAskingQuestion.set(true);
		const question = this.questionForm.get('question')?.value as string

		if (!question) {
			this.isAskingQuestion.set(false);
			return;
		}

		this.client.post<Question>(`${environment.services_root_endpoints.bid_ops_qa}/${this.projectId()}`, question).subscribe({
			next: (res) => {
				this.projectQuestionsResource.update(questions => {
					let updatedQuestions = questions ? [...questions] : [];
					return [...updatedQuestions, res];
				});
							
				this.questionForm.get('question')?.setValue('');

				this.isAskingQuestion.set(false);
			},
			error: (err) => {
				this.isAskingQuestion.set(false);
				console.error(err);
			}
		});
	}


	private calculateQuestionsAndAnswersDays(questions: Array<Question>) {
		for (let question of questions) {
			question.numDays = Math.floor((new Date().getTime() - new Date(question.CreatedDate).getTime()) / (1000 * 60 * 60 * 24));

			for (let answer of question.Answers) {
				answer.numDays = Math.floor((new Date().getTime() - new Date(answer.CreateDate).getTime()) / (1000 * 60 * 60 * 24));
			}
		}
	}
}
export interface QAExcelResponse {
	Url: string;
}