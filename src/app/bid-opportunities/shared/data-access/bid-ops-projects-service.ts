import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { BidOpsProjectsGrid } from '../interfaces/bid';
import { rxResource } from '@angular/core/rxjs-interop';
import { BidOpsPrivateProjectService } from './bid-private-project.service';
import { BidOpsProjectService } from './bid-ops-project.service';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { ClickStreamService } from 'src/app/account/shared/data-access/click-stream.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Injectable()
export class BidOpsProjectsService {
  client = inject(HttpClient);
  privateProjectService = inject(BidOpsPrivateProjectService);
  bidOpsProjectService = inject(BidOpsProjectService);
  clickStreamService = inject(ClickStreamService);
  authService = inject(AuthService);
  router = inject(Router);
  projects = computed(() => this.bidOpsProjectsResource.value());
  isProjectsLoading = computed(() => this.bidOpsProjectsResource.isLoading());
  search = signal<string | null>(null);
  limit = signal<number>(50);
  currentPage = signal<number>(1);
  sortBy = signal<string>('BidDetails.BidDateTimeInfo.Date');
  sortOrder = signal<string>('desc');
  filterTitle = signal<string>("Currently");
  bidStart = signal<string | null>(null);
  bidEnd = signal<string | null>(null);
  hideTBA = signal<boolean>(false);
  releaseDateStart = signal<string | null>(null);
  releaseDateEnd = signal<string | null>(null);
  internalId = signal<string | null>(null);
  state = signal<string | null>(null);
  county = signal<string | null>(null);
  projectTitle = signal<string | null>(null);
  publisher = signal<string | null>(null);
  typeOfWork = signal<string | null>(null);
  initialize = signal<boolean>(false);

  privateProjectEffect = effect(() => {
    if (this.privateProjectService.privateProjectId() && this.privateProjectService.privateProject()) {
      this.bidOpsProjectService.projectId.set(this.privateProjectService.privateProject()?.Id as string);
      this.bidOpsProjectService.projectResource.set(this.privateProjectService.privateProject());
      this.privateProjectService.privateForm.patchValue({ privateId: "" });
      this.router.navigate(['bid-opportunities', 'projects', this.privateProjectService.privateProject()?.Id]);
    }
  });

  projectsEffect = effect(() => {
    if(this.projects() && this.authService.isLoggedInSignal()) {
        this.clickStreamService.projectIds.set(this.projects()?.Projects.map((project) => project.ProjectId) as string[]);
    }
  });

  clickStreamEffect = effect(() => {
    if(this.projects() && this.authService.isLoggedInSignal()){
      if(this.clickStreamService.foundProjectsFromClickStream()) {
          this.bidOpsProjectsResource.update((projects)=> {
            if (projects) {
              const foundProjects = this.clickStreamService.foundProjectsFromClickStream() as string[];
              projects.Projects.forEach((project) => {
                project.IsClicked = foundProjects.includes(project.ProjectId);
              });
            }
            return projects;
          })
      }
    }
  });

  bidOpsProjectsResource = rxResource({
    request: () => ({
      search: this.search(),
      page: this.currentPage(),
      limit: this.limit(),
      sortBy: this.sortBy(),
      sortOrder: this.sortOrder(),
      startDate: this.bidStart(),
      endDate: this.bidEnd(),
      hideTBA: this.hideTBA(),
      releaseDateStart: this.releaseDateStart(),
      releaseDateEnd: this.releaseDateEnd(),
      internalId: this.internalId(),
      state: this.state(),
      county: this.county(),
      projectTitle: this.projectTitle(),
      publisher: this.publisher(),
      typeOfWork: this.typeOfWork()

    }),
    loader: (request) => {

      if (this.initialize()) {
        let queryParams = new HttpParams(); // Initialize HttpParams

        if (request.request.search) {
          queryParams = queryParams.set('search', request.request.search || '');
        }
        if (request.request.page) {
          queryParams = queryParams.set('currentPage', request.request.page.toString());
        }
        if (request.request.sortBy) {
          queryParams = queryParams.set('sortBy', request.request.sortBy);
        }
        if (request.request.sortOrder) {
          queryParams = queryParams.set('sortOrder', request.request.sortOrder);
        }
        if (request.request.limit) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }

        if (request.request.startDate) {
          queryParams = queryParams.set('bidDateStart', request.request.startDate);
        }

        if (request.request.endDate) {
          queryParams = queryParams.set('bidDateEnd', request.request.endDate);
        }

        if (request.request.hideTBA) {
          queryParams = queryParams.set('hideTBA', request.request.hideTBA.toString());
        }

        if (request.request.releaseDateStart) {
          queryParams = queryParams.set('releaseDateStart', request.request.releaseDateStart);
        }

        if (request.request.releaseDateEnd) {
          queryParams = queryParams.set('releaseDateEnd', request.request.releaseDateEnd);
        }

        if (request.request.internalId) {
          queryParams = queryParams.set('internalId', request.request.internalId);
        }

        if (request.request.state) {
          queryParams = queryParams.set('state', request.request.state);
        }

        if (request.request.county) {
          queryParams = queryParams.set('county', request.request.county);
        }

        if (request.request.projectTitle) {
          queryParams = queryParams.set('projectTitle', request.request.projectTitle);
        }

        if (request.request.publisher) {
          queryParams = queryParams.set('publisher', request.request.publisher);
        }

        if (request.request.typeOfWork) {
          queryParams = queryParams.set('typeOfWork', request.request.typeOfWork);
        }


        return this.client.get<BidOpsProjectsGrid | null>(`${environment.services_root_endpoints.bid_ops_projects}/`, { params: queryParams });
      }

      return of(null);
    }
  });
}


export class BidOpsGridInfo {
  Grid: BidOpsProjectsGrid | null = null;
}