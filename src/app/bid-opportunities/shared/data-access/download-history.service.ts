import { HttpClient } from "@angular/common/http";
import { Injectable, computed, inject, signal } from "@angular/core";
import { DownloadHistory, DownloadHistoryGrid, DownloadHistoryInfo, DownloadHistoryRequest } from "../interfaces/download-history";
import { environment } from "src/environments/environment";
import { of, Subscription } from "rxjs";
import { rxResource } from "@angular/core/rxjs-interop";

@Injectable({
	providedIn: 'root'
  })
  export class DownloadHistoryService {
	httpClient = inject(HttpClient);

	isUserDownloadHistoryLoading = computed(() => this.downloadHistoryResource.isLoading());
	userDownloadHistory = computed(() => this.downloadHistoryResource.value());
	projectDocDowloadHistory = computed(() => this.projectDocDownloadHistoryResource.value());
	downloadHistoryRequest = signal<DownloadHistoryRequest | null>(null);	
	// projectDocHistoryRequest = signal<ProjectDocHistoryRequest | null>(null);
	projectId = signal<string | null>(null);
	search = signal<string | null>(null);
	isReversed = signal<boolean>(false);
	page = signal<number>(1);
	limit = signal<number>(50);
	sortBy = signal<string>('DateCreated');
	sortOrder = signal<string>('desc');

	destroy(){

	}

	downloadHistoryResource = rxResource({
		request: () => (this.downloadHistoryRequest()),
		loader: (requestInfo) => {
			if(requestInfo.request){
				// var request = {
				// 	Limit: requestInfo.request.Limit,
				// 	Page: requestInfo.request.Page,
				// 	SortBy: requestInfo.request.SortBy,
				// 	SortOrder: (this.isReversed()) ? 'asc' : 'desc',					
				// }as DownloadHistoryRequest;

				return this.getDownloadHistory(requestInfo.request);
			}

			return of(null);
		}		
	});

	projectDocDownloadHistoryResource = rxResource({
		request: () => this.projectId(),
		loader: (request) => {
			if(request.request){
				return this.httpClient.get<Array<string>>(`${environment.services_root_endpoints.bidops_user_download_history}/${request.request}`);
			}

			return of(null);
		}
	});
	

	getDownloadHistory(request: DownloadHistoryRequest) {
		let queryString = Object.entries(request)
		.filter(([key, value]) => value !== null && value !== undefined)
		.map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
		.join('&');

		return this.httpClient.get<DownloadHistoryGrid>(`${environment.services_root_endpoints.bidops_user_download_history}/?${queryString}`);

	}

	getProjectDocHistory(projectId: string, docIds: Array<string>) {		

		var request = {
			ProjectDownloadLookupInfos: docIds
		};

		return this.httpClient.post<ProjectDocDownloadHistoryResponse>(`${environment.services_root_endpoints.bidops_user_download_history}/${projectId}/user-doc-history-info`, request);	
	}
  }

  export interface ProjectDocDownloadHistoryResponse{
	ProjectDownloadHistoryInfos: Array<string>;
  }
  
//   export class ProjectDocHistoryRequest{

// 	constructor(projectId: string, docIds: Array<string>){
// 		this.projectId = projectId;
// 		this.docIds = docIds;
// 	}
// 	projectId: string;
// 	docIds: Array<string>;
//   }