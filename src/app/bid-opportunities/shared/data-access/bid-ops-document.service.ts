import { HttpClient } from "@angular/common/http";
import { Injectable, computed, effect, inject, signal } from "@angular/core";
import { Observable, of, Subscription } from "rxjs";
import { environment } from "src/environments/environment";
import { Router } from "@angular/router";
import { ProjectFile } from "src/app/bid-adv/shared/interfaces/bids-docs";
import { DocumentCheckLimitResponse, ProjectFileInfo } from "../interfaces/bid-ops-documents";
import { ToastrService } from "ngx-toastr";
import { rxResource } from "@angular/core/rxjs-interop";
import { DownloadHistoryService } from "./download-history.service";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { BidOpsProjectService } from "./bid-ops-project.service";
import { AuthService } from "src/app/shared/data-access/auth.service";

@Injectable()
export class BidOpsDocumentService {
	client = inject(HttpClient);
	router = inject(Router);
	toastrService = inject(ToastrService);
	downloadHistoryService = inject(DownloadHistoryService);
	bidOpsProjectService = inject(BidOpsProjectService);
	authService = inject(AuthService);
  	projectDownloadHistory = this.downloadHistoryService.projectDocDowloadHistory;
	files = computed(() => this.projectFilesResource.value());
	zipFileLimitCheck = computed(() => this.zipFileLimitCheckResource.value());
	zipDownloadComplete = signal<ProjectDownloadZipResponse | null>(null);
	projectId = signal<string | null>(null);
	allFiles = computed(() => {
		if (this.files()) {
			let files = new Array<ProjectFileInfo>();
			for (let file of this.files() as Array<ProjectFile>) {
				var nFile = new ProjectFileInfo(file);
				files.push(nFile);
			}

			return files;
		}
		return [];	
	})
	filesZipComplete = signal<ProjectDownloadZipResponse | null>(null);
	isDownloadAll = signal<boolean>(false);
	isSelectedFilesDownloadAll = signal<boolean>(false);
	isFilesAllowedToDownloadAll = signal<boolean>(false);
	projectTitle = computed(() => this.bidOpsProjectService.project()?.ProjectTitle ?? "");
	downloadFileSubscription: Subscription | null = null;
	getProjectFilesSubscription: Subscription | null = null;
	fileSizeLimitCheckSubscription: Subscription | null = null;
	downloadZipFileSubscription: Subscription | null = null;
	isDownloadHistoryLoading = signal<boolean>(false);
	filesZipCompleteEffect = effect(() => {
		if (this.filesZipComplete()) {
			HelperTools.downloadFile(this.filesZipComplete()?.PreSignedUrl as string);

			for (let file of this.filesZipComplete()?.FileInfos as Array<ProjectFile>) {
				var projectFile = this.allFiles()?.find((f) => f.File.FileId == file.FileId);
				if (projectFile) {
					projectFile.File.IsDownloaded = true;
					projectFile.isSelected = false;
				}
			}

			this.isDownloadAll.set(false);
			this.isSelectedFilesDownloadAll.set(false);
		}
	});


	projectIdEffect = effect(() => {
		if(this.projectId() && this.authService.isLoggedInSignal()){
			this.downloadHistoryService.projectId.set(this.projectId() as string);
		}
	});



	docHistoryEffect = effect(() => {
		if (this.projectDownloadHistory() && this.files()) {
			for (let doc of this.allFiles()) {

				var file = this.projectDownloadHistory()?.find((fileId) => doc.File.FileId == fileId);

				if (file) {
					doc.File.IsDownloaded = true;
				} else {
					doc.File.IsDownloaded = false;
				}
			}

			this.isDownloadHistoryLoading.set(false);
		}
	});

	projectFilesResource = rxResource({
		request: () => this.projectId(),
		loader: (projectId) => {
			if (projectId.request) {
				return this.client.get<Array<ProjectFile>>(`${environment.services_root_endpoints.bid_ops_documents}/noauth/${projectId.request}`);
			}

			return of(null);
		}
	});

	zipFileLimitCheckResource = rxResource({
		request: () => ({
			projectId: this.projectId(),
			isLoggedIn: this.authService.isLoggedInSignal()
		}),
		loader: (request) => {
			if (request.request.projectId && request.request.isLoggedIn) {
				return this.client.get<DocumentCheckLimitResponse>(`${environment.services_root_endpoints.bid_ops_documents}/auth/${request.request.projectId}/zip-limit-check`);
			}

			return of(null);
		}
	});
	destroy() {
		this.downloadFileSubscription?.unsubscribe();
		this.getProjectFilesSubscription?.unsubscribe();
		this.fileSizeLimitCheckSubscription?.unsubscribe();
		this.downloadZipFileSubscription?.unsubscribe();
	}

	downloadAll() {
		this.isDownloadAll.set(true);

		if (this.files()) {
			var projectId = this.projectId() as string;
			var fileInfos = Array<any>();
			for (let file of this.allFiles()) {
				var fileInfo = {
					FileName: file.File.Title,
					FileExtension: file.File.Storage?.FileExtension,
					FileType: file.File.Category?.Name,
					FileId: file.File.FileId,
					S3Key: `Projects/${projectId}/${file.File.FileId}.${file.File.Storage?.FileExtension}`
				};

				fileInfos.push(fileInfo);
			}

			this.downloadZipFile(projectId, this.projectTitle() as string, fileInfos);
		}
	}

	downloadZipFile(projectId: string, projectTitle: string, fileInfos: Array<any>) {
		if (fileInfos && fileInfos.length == 0) {
			this.toastrService.error('No files to download, you must select at least one file.');
			return;
		}	

		this.downloadZipFileSubscription = this.client.post<ProjectDownloadZipResponse>(`${environment.services_root_endpoints.bid_ops_documents_zip}`, { ProjectId: projectId, ProjectTitle: projectTitle, FileInfos: fileInfos }).subscribe({
			next: (response) => {
				this.isSelectedFilesDownloadAll.set(false);
				this.isDownloadAll.set(false);
				document.location.href = response.PreSignedUrl;				
			},
			error: (error) => {
				console.log(error);

				if (error.status === 404) {
					this.toastrService.info("There may be 1 or more files missing in order to zip. You must manually download each file.", "Download Issue");
				}
			}
		});
	}

	downloadFile(projectId: string, documentId: string, projectTitle: string): Observable<any> {
		return new Observable((observer) => {
			this.downloadFileSubscription = this.client.get<any>(`${environment.services_root_endpoints.bid_ops_documents}/auth/${projectId}/doc/${documentId}/download?projectTitle=${projectTitle}`).subscribe({
				next: (response) => {
					observer.next(response);
					observer.complete();
				},
				error: (error) => {
					observer.error(error);
					console.log(error);

				}
			});
		});
	}

	downloadSelected(downloadSelected: Array<ProjectFileInfo>) {
		if (downloadSelected) {
			this.isSelectedFilesDownloadAll.set(true);			
			var projectId = this.projectId() as string;
			var fileInfos = Array<any>();

			for (let file of downloadSelected) {
				var fileInfo = {
					FileName: file.File.Title,
					FileExtension: file.File.Storage?.FileExtension,
					FileType: file.File.Category?.Name,
					FileId: file.File.FileId,
					S3Key: `Projects/${projectId}/${file.File.FileId}.${file.File.Storage?.FileExtension}`
				};

				fileInfos.push(fileInfo);
			}

			this.downloadZipFile(projectId, this.projectTitle() as string, fileInfos);
		}
	}

	download(projectFile: ProjectFileInfo) {
		const fileName = `${projectFile.File?.Title}.${projectFile.File?.Storage?.FileExtension}`;
		projectFile.isDownloading = true;
		this.downloadFileSubscription = this.downloadFile(
			this.projectId() as string,
			projectFile.File?.FileId as string,
			this.projectTitle() as string
		).subscribe({
			next: (response) => {

				HelperTools.downloadFile(response.PreSignedUrl);
							

				projectFile.isDownloading = false;
				projectFile.File.IsDownloaded = true;
			},
			error: (error) => {
				console.log(error);
				if (error.status === 404) {
					projectFile.isNotFound = true;
					this.toastrService.info("File no longer exists", "Download Issue");
				}
				projectFile.isDownloading = false;
			}
		});
	}
}

export class ProjectDownloadZipResponse {
	constructor(preSignedUrl: string, fileInfos: Array<any>) {
		this.PreSignedUrl = preSignedUrl;
		this.FileInfos = fileInfos;
	}

	FileInfos: Array<any>;
	PreSignedUrl: string;
}

export class ProjectLimitResponseInfo {
	projectId: string;
	response: DocumentCheckLimitResponse;
	constructor(projectId: string, response: DocumentCheckLimitResponse) {
		this.projectId = projectId;
		this.response = response;
	}
}