import { HttpClient } from "@angular/common/http";
import { Injectable, ResourceStatus, computed, effect, inject, signal } from "@angular/core";
import { of, Subscription } from "rxjs";
import { environment } from "src/environments/environment";
import { BidOpsProject } from "../interfaces/bid";
import { ActivatedRoute, Router } from "@angular/router";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { rxResource } from "@angular/core/rxjs-interop";
import { BidOpsProjectService } from "./bid-ops-project.service";
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { PrivateProjectLoginComponent } from "../../private-project-login/private-project-login.component";
import { NgbActiveModal, NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";

@Injectable({
	providedIn: 'root'
})
export class BidOpsPrivateProjectService {
	client = inject(HttpClient);
	router = inject(Router);
	aRoute = inject(ActivatedRoute);
	modalService = inject(NgbModal);
	activeModal: NgbModalRef | null = null;
	projectLoading = computed(() => this.privateProjectResource.isLoading());
	privateProject = computed(() => this.privateProjectResource.value());
	privateProjectId = signal<string | null>(null);
	isAutoLogin = signal<boolean>(false);
	privateForm = new FormGroup({
		privateId: new FormControl('', [Validators.required])
	});


	errorMessage = signal<string | null>(null);

	openModal() {
		this.privateProjectResource.set(null);
		this.privateProjectId.set(null);
		this.errorMessage.set(null);
		this.isAutoLogin.set(false);

		this.clearForm();

		this.activeModal = this.modalService.open(PrivateProjectLoginComponent, { ariaLabelledBy: 'modal-basic-title' });

		const privateId = this.aRoute.snapshot.queryParams['privateId'];

		if(privateId){
			this.isAutoLogin.set(true);
			this.privateForm.controls.privateId.disable();
			this.privateForm.controls.privateId.setValue(privateId);
			setTimeout(() => {
				this.privateProjectId.set(privateId);
			}, 2000);
		}
	}

	closeMoadal(result: any) {
		this.errorMessage.set(null);
		this.activeModal?.close(result);

	}

	dismissModal(reason: any) {
		this.errorMessage.set(null);
		this.activeModal?.dismiss(reason);
		
	}

	clearForm() {
		this.privateForm.get('privateId')?.setValue('');
	}
	priveateProjectSuccess = effect(() => {

		if (this.privateProjectResource.error()) {
			const error = this.privateProjectResource.error() as any;
			if (error.status === 421 || error.status === 404) {				
				this.errorMessage.set("Private project does not exist with the code. Please check the code and try again.");
			} else{				
				this.errorMessage.set("An error occurred while fetching the project. Please try again. [" + error.error.message + "]");
			}

			this.isAutoLogin.set(false);
			this.privateProjectId.set(null);
			this.privateProjectResource.set(null);			


		}
	});


	privateProjectResource = rxResource({
		request: () => this.privateProjectId(),
		loader: (request) => {
			if (request.request) {
				return this.client.get<BidOpsProject>(`${environment.services_root_endpoints.bid_ops_projects}/${request.request}`);
			}

			return of(null);
		}
	});
}