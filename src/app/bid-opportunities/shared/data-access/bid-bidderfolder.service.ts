import { computed, effect, inject, Injectable, Injector, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { AcknowledgeAddendaSection, BidViews, DocumentInfo, EBid, EBidSectionTypes } from "src/app/ebid/interfaces/ebid";
import { BidOpsProjectService } from "./bid-ops-project.service";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { EBidService } from "src/app/ebid/data-access/ebid.service";
import { FormArray, FormGroup } from "@angular/forms";
import { EBidInfoGatherer } from "src/app/ebid/interfaces/ebid-bid-info-gather";
import { BidderBidInfo } from "src/app/ebid/interfaces/bidder-bid-info";
import { RequiredDownloadsService } from "src/app/ebid/data-access/required-downloads.service";
import { BidOpsDocumentService } from "./bid-ops-document.service";
import { ProjectFile } from "src/app/bid-adv/shared/interfaces/bids-docs";
import { DownloadHistoryService } from "./download-history.service";
import { EBidFolderService } from "src/app/ebid/data-access/bid-folder.service";
import { ToastrService } from "ngx-toastr";
import { EBidProjectService } from "src/app/bid-adv/shared/data-access/bids-ebid-project.service";
import { AddendaAcknowledgeService } from "src/app/ebid/data-access/addenda-acknowledge.service";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";

@Injectable()
export class BidBidderFolderService extends BaseEffectsService {
	eBidProjectService = inject(EBidProjectService);	
	eBidService = inject(EBidService);
	bidOpsDocumentService = inject(BidOpsDocumentService);
	requiredDownloadsService = inject(RequiredDownloadsService);
	eBidFolderService = inject(EBidFolderService);
	downloadHistoryService = inject(DownloadHistoryService);
	addendaAcknowledgeService = inject(AddendaAcknowledgeService);
	toastrService = inject(ToastrService);
	client = inject(HttpClient);
	startDate = this.eBidProjectService.startDate;
	isBidStarted = this.eBidProjectService.isBidStarted;	
	projectId = signal<string | null>(null);	
	eBid = computed(() => this.eBidProjectService.ebidResource.value());
	documents = this.bidOpsDocumentService.files;
	bidderBidInfo = computed(() => this.bidderInfoResource.value());
	view = signal<BidViews>(BidViews.BID);
	isSavingBidderInfo = signal<boolean>(false);
	ebidError = this.eBidProjectService.error;
	isLoading = computed(() => this.bidderInfoResource.isLoading() || this.eBidProjectService.ebidResource.isLoading());
	ebidFormGroup = this.eBidFolderService.ebidFormGroup;
	isSubmittingBid = signal<boolean>(false);
	isUnSubmittingBid = signal<boolean>(false);
	effectInjector = inject(Injector);
	destroy() {
		
	}

	startEffects(){
		this.registerEffect(effect(() => {
			this.requiredDownloadsService.projectDocumentDownloadHistory.set(this.downloadHistoryService.projectDocDowloadHistory() as Array<string>);	
		}, { injector: this.effectInjector as Injector }));
	

		this.registerEffect(effect(() => {
			if (this.eBid()) {
				this.eBidFolderService.eBid.set(this.eBid() as EBid);
			}
		}, { injector: this.effectInjector as Injector }));
	
		this.registerEffect(effect(() => {
			if (this.projectId()) {
				this.bidOpsDocumentService.projectId.set(this.projectId() as string);
				this.eBidProjectService.projectId.set(this.projectId() as string);
				this.eBidFolderService.projectId.set(this.projectId() as string)
			}
		}, { injector: this.effectInjector as Injector }));
	
		this.registerEffect(effect(() => {
			if(this.projectId() && this.eBid()){
				const requiredDownloadSection = this.eBid()?.Sections.find(s => s.SectionType === EBidSectionTypes.REQUIRED_DOWNLOADS);
	
				if(requiredDownloadSection){					
					this.requiredDownloadsService.isDownloadHistoryLoading.set(true);
					this.downloadHistoryService.projectId.set(this.projectId() as string);				
				}
			}
		}, { injector: this.effectInjector as Injector }));
	
		this.registerEffect(effect(() => {
			this.eBidFolderService.view.set(this.view());
		}, { injector: this.effectInjector as Injector }));

	
		this.registerEffect(effect(() => {
			if (this.documents()) {
				var infoDocs = new Array<DocumentInfo>();
				for (let doc of this.documents() as Array<ProjectFile>) {
					const docInfo = new DocumentInfo(doc.FileId as string, doc.Title as string, doc.Category?.Name as string, doc.Storage?.FileExtension as string);
					infoDocs.push(docInfo);
				}
	
				this.requiredDownloadsService.projectInfoDocuments.set(infoDocs);
				this.addendaAcknowledgeService.projectInfoDocuments.set(infoDocs);				
	
			}
		}, { injector: this.effectInjector as Injector }));
	
		this.registerEffect(effect(() => {
			if (this.bidderBidInfo()?.SubmittedAt) {
				this.view.set(BidViews.VIEW);
			} else {
				this.view.set(BidViews.BID);
			}		
		}, { injector: this.effectInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.eBidFolderService.sectionSetupComplete() && this.documents()) {
				this.checkAcknowledgeSection(this.getInfoDocs(this.documents() as Array<ProjectFile>));
			}
		}, { injector: this.effectInjector as Injector }));

		this.registerEffect(effect(() => {
			if (this.requiredDownloadsService.downloadDocumentRequest()) {
				this.downloadDocument(this.requiredDownloadsService.downloadDocumentRequest() as DocumentInfo);
			}
		}, { injector: this.effectInjector as Injector }));

		// this.registerEffect(effect(() => {
		// 	if(this.bidOpsProjectService.projectId()){
		// 		this.projectId.set(this.bidOpsProjectService.projectId() as string);
		// 	}
		// }, { injector: this.effectInjector as Injector }));

		this.registerEffect(effect(() => {
			this.eBidFolderService.bidderBidInfoData.set(this.bidderBidInfo() as BidderBidInfo);
		}, { injector: this.effectInjector as Injector }));
	}


	checkAcknowledgeSection(projectDocumentInfos: DocumentInfo[]) {	
		var sections = this.ebidFormGroup?.get('sections') as FormArray;

		if (!sections) {
			console.error('[ADD_ACK_SECTION] Sections form array is required');
			return;
		}

		const addendaAckSection = sections.controls?.find(s => s.value.section.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE) as FormGroup;

		if (addendaAckSection) {
			const addendaSection = this.eBid()?.Sections.find(s => s.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE) as AcknowledgeAddendaSection;

			if(addendaSection){
				const addendaIds = addendaSection.Acknowledgements?.map((a: any) => a.DocumentId);

				const addendaDocs = projectDocumentInfos.filter(d => addendaIds.includes(d.DocumentId));
	
				if(addendaDocs.length <= 0){
					sections.removeAt(sections.controls.indexOf(addendaAckSection));
				}
			}
							
		}
	}

	getInfoDocs(docs: Array<ProjectFile>): Array<DocumentInfo> {
		var infoDocs = new Array<DocumentInfo>();
		for (let doc of docs as Array<ProjectFile>) {
			const docInfo = new DocumentInfo(doc.FileId as string, doc.Title as string, doc.Category?.Name as string, doc.Storage?.FileExtension as string);
			infoDocs.push(docInfo);
		}

		return infoDocs;
	}


	bidderInfoResource = rxResource({
		request: () => ({
			projectId: this.projectId(),
			eBid: this.eBid()
		}),
		loader: (data) => {
			if (data.request.projectId && data.request.eBid) {
				return this.client.get<BidderBidInfo>(`${environment.services_root_endpoints.bid_ops_ebids}/${data.request.projectId}`);
			}

			return of(null);
		}
	});
	



	saveBidderBidInfo() {
		this.isSavingBidderInfo.set(true);
		this.eBidService.gatherBidInfo(this.ebidFormGroup, new EBidInfoGatherer()).subscribe({
			next: (bidderInfo: BidderBidInfo) => {
				if (this.bidderBidInfo()?.Id)
					bidderInfo.Id = this.bidderBidInfo()?.Id as string;

				this.eBidProjectService.saveBidderBidInfo(this.projectId() as string, bidderInfo).subscribe({
					next: (nBidderInfo) => {
						this.bidderInfoResource.update(() => { return nBidderInfo });
						this.isSavingBidderInfo.set(false);
						this.toastrService.success('Bidder information saved successfully');
						this.ebidFormGroup.markAsPristine();
						this.ebidFormGroup.markAsUntouched();
					},
					error: (error) => {
						console.log(error);
						this.isSavingBidderInfo.set(false);
					}
				});
			},
			error: (error) => {
				console.log(error);
				this.isSavingBidderInfo.set(false);
			}
		});

	}


	submitBid() {
		this.isSubmittingBid.set(true);
		this.eBidService.gatherBidInfo(this.ebidFormGroup, new EBidInfoGatherer()).subscribe({
			next: (gatheredBidderInfo: BidderBidInfo) => {
				this.eBidProjectService.submitBid(this.projectId() as string, this.eBid()?.Version as number, gatheredBidderInfo).subscribe({
					next: (nBidderInfo) => {
						this.bidderInfoResource.update(() => { return nBidderInfo });
						this.toastrService.success('You have submitted your bid successfully!');

						this.ebidFormGroup.markAsPristine();
						this.ebidFormGroup.markAsUntouched();
						this.isSubmittingBid.set(false);
					},
					error: (error) => {
						console.log(error);
						this.toastrService.error(`Error submitted bidder information. ${error.error.message}`);
						this.isSubmittingBid.set(false);
					}
				});
			},
			error: (error) => {
				console.log(error);
				this.isSubmittingBid.set(false);
			}

		});


	}


	unSubmitBid() {
		if (this.bidderBidInfo()) {
			this.isUnSubmittingBid.set(true);
			this.eBidService.gatherBidInfo(this.ebidFormGroup, new EBidInfoGatherer()).subscribe({
				next: (gatheredBidderInfo) => {
					this.eBidProjectService.unSubmitBid(this.projectId() as string, this.eBid()?.Version as number, gatheredBidderInfo).subscribe({
						next: (nBidderInfo) => {
							this.bidderInfoResource.update(() => { return nBidderInfo });
							this.toastrService.success('You have removed your bid submission');
							this.ebidFormGroup.markAsPristine();
							this.ebidFormGroup.markAsUntouched();
							this.isUnSubmittingBid.set(false);
						},
						error: (error) => {
							console.log(error);
							this.toastrService.error(`Error submitting bidder information. ${error.error.message}`);
							this.isUnSubmittingBid.set(false);
						}
					});
				},
				error: (error) => {
					console.log(error);
					this.isUnSubmittingBid.set(false);
				}
			});
		} else {
			this.toastrService.error('No bidder information to unsubmit');
		}

	}


	downloadDocument(doc: DocumentInfo) {
		this.bidOpsDocumentService.downloadFile(this.projectId() as string, doc.DocumentId, doc.DocumentName).subscribe({
			next: (response) => {
				this.requiredDownloadsService.markDownloadComplete(doc.DocumentId);

				HelperTools.downloadFile(response.PreSignedUrl, doc.DocumentName);
			},
			error: (error) => {
				console.log(error);
			}
		})
	}

}