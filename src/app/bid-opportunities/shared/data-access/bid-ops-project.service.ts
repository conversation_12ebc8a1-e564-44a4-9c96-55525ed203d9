import { HttpClient } from "@angular/common/http";
import { Injectable, computed, effect, inject, signal } from "@angular/core";
import { of, Subscription } from "rxjs";
import { environment } from "src/environments/environment";
import { BidOpsProject } from "../interfaces/bid";
import { Router } from "@angular/router";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { rxResource } from "@angular/core/rxjs-interop";
import { BidOpsPrivateProjectService } from "./bid-private-project.service";

@Injectable({
	providedIn: 'root'
})
export class BidOpsProjectService {
	client = inject(HttpClient);
	privateProjectService = inject(BidOpsPrivateProjectService);
	router = inject(Router);
	projectLoading = computed(() => this.projectResource.isLoading());
	project = computed(() => this.projectResource.value());
	projectId = signal<string | null>(null);	
	localBidDate = computed(() => {
		if (this.project()) {
			return HelperTools.convertToTimeZoneDate(
				this.project()?.BidDetails?.BidDateTimeInfo?.Year as number,
				this.project()?.BidDetails?.BidDateTimeInfo?.Month as number + 1,
				this.project()?.BidDetails?.BidDateTimeInfo?.Day as number,
				this.project()?.BidDetails?.BidDateTimeInfo?.Hour as number,
				this.project()?.BidDetails?.BidDateTimeInfo?.Minute as number,
				this.project()?.BidDetails?.BidDateTimeInfo?.TimeZone?.ZoneId as string);
		}

		return undefined;
	});

	privateProjectEffect = effect(() => {
		if (this.privateProjectService.privateProjectId() && this.privateProjectService.privateProject()) {
		  this.projectId.set(this.privateProjectService.privateProject()?.Id as string);
		  this.projectResource.set(this.privateProjectService.privateProject());
		  this.privateProjectService.privateForm.patchValue({ privateId: "" });
		  this.privateProjectService.dismissModal('From Project Page');
		}
	  });
	projectError = effect(() => {		
		const error = this.projectResource.error() as any;

		if(!error?.status)
			return;

		if(error.status === 404) {
			this.privateProjectService.privateProjectId.set(null);
			this.privateProjectService.privateProjectResource.set(null);
			this.router.navigate(['/bid-opportunities/project-not-found'], { queryParams: { projectId: this.projectId() }, skipLocationChange: true});
		}else if(error.status === 421){
			this.privateProjectService.privateProjectId.set(null);
			this.privateProjectService.privateProjectResource.set(null);
			if(error.error.uniqueId){
				this.privateProjectService.openModal();
			}
			//this.router.navigate(['/bid-opportunities'], { queryParams: { privateId: error.error.uniqueId }, replaceUrl: true, skipLocationChange: false});
		}else if(error.status === 422){
			this.privateProjectService.privateProjectId.set(null);
			this.privateProjectService.privateProjectResource.set(null);
			this.router.navigate(['/bid-opportunities/project-not-visible'], { queryParams: { projectId: this.projectId() }, skipLocationChange: true});
		}
		
	});
	

	projectResource = rxResource({
		request: () => this.projectId(),
		loader: (request) => {
			if (request.request) {
				return this.client.get<BidOpsProject>(`${environment.services_root_endpoints.bid_ops_projects}/${request.request}`);
			}

			return of(null);
		}
	});
}