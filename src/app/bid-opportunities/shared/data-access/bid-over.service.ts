import { effect, inject, Injectable, Injector, signal } from "@angular/core";
import { BidBidderFolderService } from "./bid-bidderfolder.service";
import { BidViews } from "src/app/ebid/interfaces/ebid";
import { BidOpsProjectService } from "./bid-ops-project.service";
import { BaseEffectsService } from "src/app/shared/interfaces/abstract-effects-service";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable()
export class BidOverService extends BaseEffectsService {

	bidFolderService = inject(BidBidderFolderService);
	bidOpsProjectService = inject(BidOpsProjectService);
	httpClient = inject(HttpClient);
	localBidDate = this.bidOpsProjectService.localBidDate;
	eBid = this.bidFolderService.eBid;
	bidderBidInfo = this.bidFolderService.bidderBidInfo;
	ebidFormGroup = this.bidFolderService.ebidFormGroup;
	isLoading = this.bidFolderService.isLoading;
	projectId = signal<string | undefined>(undefined);
	view = signal<BidViews>(BidViews.VIEW);
	overServiceInjector = inject(Injector);
	isDownloadingReport = signal(false);
	isWithdrawingBid = signal(false);
	startEffects() {
		this.bidFolderService.startEffects();
	}

	public override stopEffects(): void {
		this.bidFolderService.stopEffects();
		super.stopEffects();
	}

	exportBidFolder() {

		this.isDownloadingReport.set(true);
		this.httpClient.get(`${environment.services_root_endpoints.adverts_ebid_reports}/${this.projectId()}/user-bid-folder`).subscribe({
			next: (response: any) => {
				this.isDownloadingReport.set(false);
				document.location.href = response.PresignedUrl;
				//window.open(response.PresignedUrl, "_blank");
			},
			error: (err) => {
				this.isDownloadingReport.set(false);
				console.error(err);
			}
		});

	}

	withdrawBid() {
		this.isWithdrawingBid.set(true);
		this.httpClient.delete(`${environment.services_root_endpoints.bid_ops_ebids}/${this.projectId()}/bid/${this.bidderBidInfo()?.Id}`).subscribe({
			next: (response: any) => {
				this.isWithdrawingBid.set(false);
				this.bidFolderService.bidderInfoResource.reload();
			},
			error: (err) => {
				this.isDownloadingReport.set(false);
				console.error(err);
			}
		});
	}
}