import { Injectable, Injector, computed, effect, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { of, Subscription } from 'rxjs';
import { Planholder } from '../interfaces/bid';
import { ObfuscatePipe } from 'src/app/shared/utils/pipes/obfuscate.pipe';
import { rxResource } from '@angular/core/rxjs-interop';
import { DbeCertificationsService } from 'src/app/account/shared/data-access/dbe-certifications-service';
import { BaseEffectsService } from 'src/app/shared/interfaces/abstract-effects-service';
import { BidOpsProjectService } from './bid-ops-project.service';
@Injectable({
	providedIn: 'root',	
})
export class BidOpsPlanholdersService extends BaseEffectsService {
	client = inject(HttpClient);
	dbeCertificationService = inject(DbeCertificationsService);
	bidOpsProjectService = inject(BidOpsProjectService);
	obsucatePipe = new ObfuscatePipe();
	planholdersResponse = computed(() => this.planholdersResource.value());

	private planholdersRequestInfo = signal<PlanholderRequestInfo | null>(null);

	planholderSubscription: Subscription | null = null;
	excelDownloadResponse = signal<PlanholdExcelResponse | null>(null);
	excelDownloadSubscription: Subscription | null = null;
	isLoading = computed(() => this.planholdersResource.isLoading());
	projectTitle = computed(() => this.bidOpsProjectService.project()?.ProjectTitle ?? '');
	page = signal<number>(1);
	search = signal<string| null>(null);
	sortBy = signal<string>("CompanyType")
	sortOrder = signal<string>("desc");
	limit = signal<number>(25);
	projectId = signal<string | null>(null);
  	actionLoading = signal<boolean>(false);
  	userIsUpdating = signal<boolean>(false);
  	isDownloadingExcel = signal<boolean>(false);
	phSorts = [
		{ value: 'CompanyType', name: 'Company Type' },		
		{ value: 'CompanyName', name: 'Company Name' },
		{ value: 'ContactName', name: 'Contact Name' },
		{ value: 'Email', name: 'Email' },
		{ value: 'Phone', name: 'Phone' }
	];
	injector = inject(Injector);

	destroy(){
		this.planholderSubscription?.unsubscribe();
		this.planholdersRequestInfo.set(null);
		this.excelDownloadSubscription?.unsubscribe();
		this.excelDownloadResponse.set(null);
	}

	getExcel(){
		this.excelDownloadSubscription?.unsubscribe();
		this.isDownloadingExcel.set(true);		
		
		this.excelDownloadSubscription = this.client.get<PlanholdExcelResponse>(`${environment.services_root_endpoints.bid_ops_planholders}/${this.projectId()}/excel?project-title=${this.projectTitle()}`).subscribe({
			next:(response: PlanholdExcelResponse) => {				
				this.isDownloadingExcel.set(false);
				window.location.href = response.Url;
			},
			error:(err) => {
				this.isDownloadingExcel.set(false);
				console.log(err);				
			}			
		});
	
	}

	planholdersResource = rxResource({
		request: () => ({
			projectId: this.projectId(),
			limit: this.limit(),
			page: this.page(),
			search: this.search(),
			sortBy: this.sortBy(),
			sortOrder: this.sortOrder()			
		}),
		loader: (request) => {
			if(request.request.projectId){				
			
				let queryParams = new HttpParams(); // Initialize HttpParams

				if (request.request.search) {
					queryParams = queryParams.set('search', request.request.search || '');
				}
				if (request.request.page) {
					queryParams = queryParams.set('page', request.request.page.toString());
				}
				if (request.request.sortBy) {
					queryParams = queryParams.set('sortBy', request.request.sortBy);
				}
				if (request.request.sortOrder) {
					queryParams = queryParams.set('sortOrder', request.request.sortOrder);
				}
				if (request.request.limit) {
					queryParams = queryParams.set('limit', request.request.limit.toString());
				}
		
				return this.client.get<PlanholderResponse | null>(
					`${environment.services_root_endpoints.bid_ops_planholders}/${request.request.projectId}`,
					{ params: queryParams }
				);
			}

			return of(null);
		}
	});

	startEffects(){
		effect(() => {
			if(this.planholdersResponse()){
				const userIds = this.planholdersResponse()?.BiddingPlanholders?.filter((ph) => ph !== undefined)
				.map((ph) => ph.CognitoUserId);
		
			  if (userIds && userIds.length > 0) {
				this.dbeCertificationService.userIds.set(userIds);			
			  }
			}
		},{injector: this.injector});

		effect(() => {
			if(this.planholdersResponse() && this.dbeCertificationService.dbeByUserIds() && this.dbeCertificationService.staticCertifications()){
				for(let user of this.planholdersResponse()?.BiddingPlanholders as Planholder[]){
					user.DBECerts = [];
					var certs = this.dbeCertificationService.dbeByUserIds()?.filter(cert => cert.UserId === user.CognitoUserId);
					if (certs && certs.length > 0) {
						for(let cert of certs){
							var certAbbreviation = this.dbeCertificationService.staticCertifications()?.find(x => x.DBECertId === cert.DBECertId)?.Abbreviation.toUpperCase();
							if(certAbbreviation){							
								user.DBECerts = [...user.DBECerts, certAbbreviation];
							}							
						}
						
					}				
				}
			}
		},{injector: this.injector});
	}

	updatePlanholder(companyType:string){
		this.userIsUpdating.set(true);

		this.client.put<Planholder>(`${environment.services_root_endpoints.bid_ops_planholders}/${this.projectId()}`, {CompanyType: companyType}).subscribe({
			next:(response) => {			
				this.userIsUpdating.set(false);
				this.planholdersResource.update((planholderResponse) => {
					if(planholderResponse){
						planholderResponse.UserAsPlanholder.CompanyType = response.CompanyType;
						planholderResponse.UserAsPlanholder.Actions = response.Actions;
						planholderResponse.UserAsPlanholder.NotBidding = response.NotBidding;

						planholderResponse.BiddingPlanholders.map(planholder => {
							if (planholder.UserId === response.UserId) {
								planholder.CompanyType = response.CompanyType;
							}
							return planholder;
						});

						planholderResponse.NotBiddingPlanholders.map(planholder => {
							if (planholder.UserId === response.UserId) {
								planholder.CompanyType = response.CompanyType;
							}
							return planholder;
						});
					
						return {...planholderResponse};
					}
					return planholderResponse;
				});	


			},
			error:(err) => {
				console.log(err);
				this.userIsUpdating.set(false);
			}			
		});
	}

	addAction(action:string){
		this.actionLoading.set(true);

		this.client.put<Planholder>(`${environment.services_root_endpoints.bid_ops_planholders}/${this.projectId()}`, {Action: action}).subscribe({
			next:(response) => {			
				this.actionLoading.set(false);
				this.planholdersResource.reload();				
			},
			error:(err) => {
				console.log(err);
			}			
		
		});
	}
}

export interface PlanholdersGrid {
	Planholders: Array<Planholder>;
	Total: number;
}

export interface PlanholderResponse{
	BiddingPlanholders: Array<Planholder>;
	NotBiddingPlanholders: Array<Planholder>;
	BiddingTotal: number;
	_k: string;
	UserAsPlanholder: Planholder;
}

export class PlanholdersRequest {
	constructor(page: number  = 1, search: string | null = null, sortBy: string = "DateCreated", sortOrder: string = "desc", limit: number = 25){
		this.Page = page;
		this.Search = search;
		this.SortBy = sortBy;
		this.SortOrder = sortOrder;
		this.Limit = limit;
	}
	Page: number = 1;	
	Search: string | null = null;	
	SortBy: string = "DateCreated";		
	SortOrder: string = 'desc';	
	Limit: number = 25;		
}

export class PlanholderRequestInfo{
	projectId: string;
	request: PlanholdersRequest;
	constructor(projectId: string, request: PlanholdersRequest){
		this.projectId = projectId;
		this.request = request;
	}
}

export interface PlanholdExcelResponse{
	Url:string;
}