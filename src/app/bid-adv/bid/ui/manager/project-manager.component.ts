import { Component, OnInit, inject, signal, effect, AfterViewInit, viewChild, On<PERSON>estroy, HostListener } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router, RouterEvent } from '@angular/router';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { Subscription, tap } from 'rxjs';
import { CivCastAccountService } from 'src/app/shared/data-access/civcast-account-service';
import { FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MapConstants, ProjectConstants } from '../../../shared/interfaces/bids-manager';
import { GoogleMap, GoogleMapsModule } from '@angular/google-maps';
import { ProjectManagerService } from '../../../shared/data-access/bids-manager-service';
import { GlobalDataService } from 'src/app/shared/data-access/global-data-service';
import { CommonModule } from '@angular/common';
import { QuillModule } from 'ngx-quill';
import { ProjectManagerSkeletonComponent } from './ui/project-manager-skeleton/project-manager-skeleton.component';
import isEmail from 'validator/es/lib/isEmail';
import { County, EmailAddress } from '../../../shared/interfaces/advertisement';
import { FormErrorLabelDirective } from 'src/app/shared/utils/directives/form-error-label.directive';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { CalendarModule } from 'primeng/calendar';
import { PrimeCalendarCustomComponent } from 'src/app/shared/ui/prime-calendar-custom/prime-calendar-custom.component';
import { quillModules } from 'src/app/shared/utils/quill-modules';
import { CivCastDateTimePicker } from 'src/app/shared/ui/civcast-date-time-picker/civcast-date-time-picker.component';
import { environment } from 'src/environments/environment';
import { GoogleMapProjectService } from 'src/app/maps/services/google-map-project.service';
import { GoogleMapEmbedComponent } from 'src/app/maps/google-map/google-map-embed/google-map-embed.component';
import { GoogleMapComponent } from 'src/app/maps/google-map/google-map.component';
import { BidAdvService } from 'src/app/bid-adv/shared/data-access/bid.service';

@Component({
  selector: 'app-project-manager',
  templateUrl: './project-manager.component.html',
  styleUrls: ['./project-manager.component.css'],
  viewProviders: [NgForm],
  standalone: true,
  providers: [GoogleMapProjectService],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CalendarModule,
    QuillModule,
    GoogleMapsModule,
    FormErrorLabelDirective,
    ToastrModule,
    PrimeCalendarCustomComponent,
    ProjectManagerSkeletonComponent,
    CivCastDateTimePicker,
    GoogleMapComponent,
    GoogleMapEmbedComponent
  ]
})
export class ProjectManagerComponent implements OnInit, OnDestroy, AfterViewInit {

  @HostListener('window:beforeunload', ['$event'])
  unloadEvent(event: any) {
    if (this.projectFormGroup.touched && this.projectFormGroup.dirty) {
      event.preventDefault();
    }
  }

  ///////////////////SERVICES///////////////////////
  advertisementService = inject(BidAdvService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  globalDataService = inject(GlobalDataService);
  projectManagerService = inject(ProjectManagerService);
  accountService = inject(CivCastAccountService);
  toastr = inject(ToastrService);
  confirmService = inject(ConfirmService);
  googleMapProjectService = inject(GoogleMapProjectService);

  ///////////////////INPUTS///////////////////////

  selectedgoogleMap = viewChild.required<GoogleMap>("selectedgooglemap");
  googleMap = viewChild.required<GoogleMap>("googlemap");

  ////////////////SIGNALS//////////////////////////
  project = this.advertisementService.advertisement;
  timeZones = this.globalDataService.timeZones;
  projectFormGroup = this.projectManagerService.projectFormGroup;
  states = this.globalDataService.states;
  allCounties = this.globalDataService.counties;
  counties = signal<Array<County> | null>(null);
  userProfile = this.accountService.AccountProfile;
  isLoading = this.advertisementService.isAdvertisementLoading;
  isSaving = this.projectManagerService.isSaving;  
  customMapUrl = this.googleMapProjectService.url;

  ///////////////////LOCAL PROPERTIES///////////////////////

  selectedMarker: any = {};
  validationErrors: Array<string> = [];
  addOnlineBidding: boolean = false;
  notifyPlanHolder: boolean = false;
  projectConstants = ProjectConstants;
  qaAddEmailFormGroup: FormGroup = new FormGroup(
    {
      qaAddEmail: new FormControl('',
        {
          validators: [Validators.required, Validators.pattern(this.projectManagerService.emailPattern)]  //todo move this to constants patterns
        })
    });

  modules = quillModules;

  options: google.maps.MapOptions = { clickableIcons: false, gestureHandling: 'none', fullscreenControl: false, zoom: 15 };

  markerPosition: google.maps.LatLngLiteral | null = null
  selectedOptions: google.maps.MapOptions = { clickableIcons: false, zoom: 10, center: { lat: MapConstants.lat, lng: MapConstants.lng } };
  defaultCenter = { lat: MapConstants.lat, lng: MapConstants.lng };
  staticMapCenter = { lat: MapConstants.lat, lng: MapConstants.lng };

  apiKey: string = environment.google.maps.apiKey;

  //////////////Custom Subscriptions//////////////////////
  routerSubscription: Subscription | null = null;

  ///////////////////Signal to Observers///////////////////////

  countiesSignal = toSignal(toObservable(this.allCounties).pipe(
    tap((states => {
      if (this.project() && this.states() && this.allCounties()) {
        const stateAbbreviation = this.project()?.Location?.State?.Abbreviation;
        if (stateAbbreviation) {
          this.getCountiesByState(stateAbbreviation);
        }
      }
    }))));

  profileSignal = toSignal(toObservable(this.userProfile).pipe(
    tap((profile) => {
      if (profile) {
        const stateAbbreviation = profile?.Company?.Address?.State?.Abbreviation;
        if (stateAbbreviation) {
          if (!this.project()?.Location?.State?.Abbreviation) {
            this.projectFormGroup?.get('locationInfo')?.get('state')?.setValue(stateAbbreviation);
            this.getCountiesByState(stateAbbreviation);
          }
        }
      }
    })
  ));

  ///////////////////EFFECTS///////////////////////

  timeZoneChange = effect(() => {
    if (this.timeZones() && this.projectFormGroup) {
      this.projectFormGroup.markAsUntouched();
    }
  });

  ///////////////////METHODS///////////////////////
  constructor() {
    this.routerSubscription = this.router.events.subscribe((event) => {
      const ev = event as RouterEvent;
      if (ev.url && ev.url.indexOf('advertisements/new') === -1) {
        if (event instanceof NavigationStart) {
          if (this.projectFormGroup.touched && this.projectFormGroup.dirty) {
            if (!confirm('You have not saved your form. Do you want to leave?')) {
              this.router.navigateByUrl(this.router.url, { skipLocationChange: true });
            } else {
              this.projectFormGroup.markAsPristine();
              this.projectFormGroup.markAsUntouched();
            }
          }

        } else if (event instanceof NavigationEnd) {
          this.projectFormGroup.markAsPristine();
          this.projectFormGroup.markAsUntouched();
        }
      }

    });

    effect(() => {
      if (this.googleMapProjectService.latitude() && this.googleMapProjectService.longitude()) {
        this.projectFormGroup.get(['mapData', 'latitude'])?.setValue(this.googleMapProjectService.latitude());
        this.projectFormGroup.get(['mapData', 'longitude'])?.setValue(this.googleMapProjectService.longitude());

        this.markerPosition = {
          lat: this.googleMapProjectService.latitude() as number,
          lng: this.googleMapProjectService.longitude() as number
        };

        this.staticMapCenter = {
          lat: this.googleMapProjectService.latitude() as number,
          lng: this.googleMapProjectService.longitude() as number
        };
      } else {
        this.projectFormGroup.get(['mapData', 'latitude'])?.setValue(null);
        this.projectFormGroup.get(['mapData', 'longitude'])?.setValue(null);
        this.markerPosition = null;
        this.staticMapCenter = { lat: MapConstants.lat, lng: MapConstants.lng };
      }
    });

    effect(() => {
      if (this.project()?.Location?.MapData?.Latitude && this.project()?.Location?.MapData?.Longitude) {
        this.googleMapProjectService.latitude.set(this.project()?.Location?.MapData?.Latitude as number);
        this.googleMapProjectService.longitude.set(this.project()?.Location?.MapData?.Longitude as number);
      } else {
        this.googleMapProjectService.latitude.set(null);
        this.googleMapProjectService.longitude.set(null);
      }

      if (this.project()?.CustomMapUrl) {
        this.projectFormGroup.get('customMapUrl')?.setValue(this.project()?.CustomMapUrl);
        this.googleMapProjectService.url.set(this.project()?.CustomMapUrl as string);
      } else {
        this.projectFormGroup.get('customMapUrl')?.setValue(null);
        this.googleMapProjectService.url.set(null);
      }
    })

    effect(() => {
      if (this.googleMapProjectService.url()) {
        this.projectFormGroup.get('customMapUrl')?.setValue(this.googleMapProjectService.url(), { emitEvent: true });
      } else {
        this.projectFormGroup.get('customMapUrl')?.setValue(null);
        this.projectFormGroup.updateValueAndValidity();
      }
    });

    effect(() => {
      if(this.googleMapProjectService.touched()) {
        this.projectFormGroup.markAsTouched();
      }

      if(this.googleMapProjectService.isDirty()) {
        this.projectFormGroup.markAsDirty();
      }
    })

    this.googleMapProjectService.apiKey.set(this.apiKey);
  }

  ngAfterViewInit(): void {

  }
  ngOnDestroy(): void {
    this.routerSubscription?.unsubscribe();
    //this.projectManagerService.projectFormGroup.reset();
  }

  ngOnInit(): void {
    this.projectFormGroup.get('locationInfo.state')?.valueChanges.subscribe({
      next: (stateAbbreviation) => {
        if (stateAbbreviation) {
          this.getCountiesByState(stateAbbreviation);
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }
  getCountiesByState(stateAbbreviation: string) {
    this.counties.set(this.allCounties()?.filter(x => x.State === stateAbbreviation) as Array<County>);
  }

  importProfile() {
    this.projectManagerService.importProfile();
  }

  loadProjectGoogleMap() {

    if (this.projectFormGroup) {
      const latitude = this.projectFormGroup.get(['mapData', 'latitude'])?.value;
      const longitude = this.projectFormGroup.get(['mapData', 'longitude'])?.value;

      this.googleMapProjectService.latitude.set(latitude);
      this.googleMapProjectService.longitude.set(longitude);
    }
  }

  copyInput(value: string) {
    navigator.clipboard.writeText(value);
    this.toastr.success("Copied!");
  }

  deleteQAEmail(email: string) {
    this.projectManagerService.deleteQAEmail(email);
  }

  addQAEmail() {
    if (this.qaAddEmailFormGroup.get('qaAddEmail')?.valid) {
      const emailToAdd = this.qaAddEmailFormGroup.get('qaAddEmail')?.value;
      if (!isEmail(emailToAdd)) {
        console.log("Email is not an email");
        this.toastr.error("Email is not valid");
        return;
      }

      var emailAddress: EmailAddress = {
        DisplayName: emailToAdd,
        Email: emailToAdd
      };

      this.projectManagerService.addEmailToQAForm(emailAddress.DisplayName, emailAddress.Email);

    } else {
      console.log("Email is not valid");
      this.toastr.error("Email is not valid");
      return;
    }
  }
}