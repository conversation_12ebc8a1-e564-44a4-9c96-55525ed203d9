@if(isLoading()){
	<app-project-manager-skeleton></app-project-manager-skeleton>
}@else{
	<!-- form -->
	<div class="container">
		<form [formGroup]="projectFormGroup">
			<!-- title and advertisement date -->
			<div class="row">
				<div class="col-md-9">
					<div class="mb-4">
						<label for="projectTitle" class="form-label">Title*</label>
						<input type="text" id="projectTitle" name="projectTitle" class="form-control"
							formControlName="projectTitle" autocomplete="off" appFormErrorLabel
							[errors]="projectFormGroup?.get('projectTitle')?.errors" label="Project Title"
							[formControlInfo]="projectFormGroup?.get('projectTitle')" />
					</div>
				</div>
				<div class="col-md-3">
					<div class="mb-4">
						<label for="advertisementDate" class="form-label d-block">Advertisement Date*</label>
						<app-prime-calendar-custom #releaseDateCustom name="releaseDate"
							[formControl]="projectFormGroup?.get(['releaseDateInfo'])"></app-prime-calendar-custom>
					</div>
				</div>
			</div>
			<!-- award -->
			@if(project()?.Id){
			<div class="row">
				<div class="col-12 mb-4">
					<div class="card">
						<div class="card-header page-title fs-6">Award (Low Bidder)</div>
						<div class="card-body">
							<div class="row">
								<div class="col-12 col-md-6">
									<div class="mb-3">
										<label for="awardCompany" class="form-label">Company</label>
										<input class="form-control" id="awardCompany" type="text" name="lowBidName"
											formControlName="lowBidName" />
									</div>
								</div>
								<div class="col-12 col-md-6">
									<div class="mb-3">
										<label for="awardAmount" class="form-label">Amount</label>
										<input class="form-control" id="awardAmount" type="number" name="lowBidAmount"
											formControlName="lowBidAmount" />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			}
			<!-- general details and location -->
			<div class="row">
				<div class="col-lg-6 mb-4">
					<div class="card">
						<div class="card-header page-title fs-6">
							General Details
						</div>
						<div class="card-body">
							<div class="mb-3">
								<label for="internalId" class="form-label">Project No.*</label>
								<input type="text" class="form-control" name="internalId" id="internalId"
									formControlName="internalId" appFormErrorLabel
									[errors]="projectFormGroup?.get('internalId')?.errors" label="Internal Id"
									[formControlInfo]="projectFormGroup?.get('internalId')" autocomplete="off" />
							</div>
							<div class="mb-3">
								<label for="detailsEstimate" class="form-label">Estimate</label>
								<input type="text" class="form-control" name="detailsEstimate" id="detailsEstimate"
									formControlName="detailsEstimate" autocomplete="off"
									[maxlength]="projectConstants.DETAILS_ESTIMATE" />
							</div>
							<div class="mb-3">
								<label for="typeOfWork" class="form-label">Type of Work</label>
								<textarea type="text" class="form-control" name="typeOfWork" id="typeOfWork"
									formControlName="typeOfWork" autocomplete="off"
									[maxlength]="projectConstants.TYPE_OF_WORK"></textarea>
							</div>
							<div class="mb-3">
								<label for="owner" class="form-label">Owner</label>
								<textarea class="form-control" name="owner" id="owner" formControlName="owner"
									autocomplete="off" [maxlength]="projectConstants.OWNER"></textarea>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-6 mb-4">
					<div class="card">
						<div class="card-header page-title fs-6">
							Location
						</div>
						<div class="card-body">
							@if(isLocationLoading){
							<div class="bg-light">
								<div class="placeholder-glow">
									<div class="row">
										<div class="col-12 my-2">
											<span class="placeholder col-12" style="height:20px;"></span>
										</div>
									</div>
									<div class="row">
										<div class="col-4 my-2">
											<span class="placeholder col-12" style="height:20px;"></span>
										</div>
									</div>
									<div class="row">
										<div class="col-12 my-2">
											<span class="placeholder col-12" style="height:20px;"></span>
										</div>
									</div>
									<div class="row">
										<div class="col-8 my-2">
											<span class="placeholder col-12" style="height:20px;"></span>
										</div>
									</div>
								</div>
							</div>
							}@else {
							<div formGroupName="locationInfo">
								<div class="mb-3">
									<label for="state" class="form-label">State*</label>
									<div>
										@if(!states() && !projectFormGroup?.get(['locationInfo', 'state'])?.value){
										<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
										}@else if(states()) {
										<select name="state" id="state" class="form-control" formControlName="state"
											appFormErrorLabel
											[errors]="projectFormGroup?.get(['locationInfo', 'state'])?.errors"
											label="Release Date"
											[formControlInfo]="projectFormGroup?.get(['locationInfo', 'state'])">
											<option value="null" disabled>Select a state</option>
											@for (state of states(); track $index) {
											<option [value]="state.Abbreviation">
												{{ state.Name }}
											</option>
											}
										</select>
										}
									</div>
								</div>
								<div class="mb-3">
									<label for="county" class="form-label">County*</label>
									<div>
										@if(!counties()){
											@if(states()){
											<div class="alert alert-info">Select a State Above</div>
											}@else {
												<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
											}									
										}@else {
										<select name="county" id="county" formControlName="county" class="form-control"
											appFormErrorLabel
											[errors]="projectFormGroup?.get(['locationInfo', 'county'])?.errors"
											label="County"
											[formControlInfo]="projectFormGroup?.get(['locationInfo', 'county'])">
											<option value="null" disabled>Select a county</option>
											@for (county of counties(); track $index) {
											<option [value]="county.Name">
												{{ county.Name }}
											</option>
											}
										</select>
										}
									</div>
								</div>
								<div class="mb-3">
									<label for="mapCode" class="form-label">Map Code</label>
									<input class="form-control" name="mapCode" id="mapCode" formControlName="mapCode"
										[maxlength]="projectConstants.MAP_CODE" />
								</div>
							</div>
							<div formGroupName="mapData">
								<div class="mb-3">
									<a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#exampleModal">Add Map Location</a>
								</div>
								
								@if(projectFormGroup?.get(['mapData','latitude'])?.value &&
								projectFormGroup?.get(['mapData','longitude'])?.value){
									<google-map									
										#googlemap [options]="options" [(center)]="staticMapCenter" height="100px" width="100%">
										<map-marker [position]="markerPosition" [options]="markerOptions"></map-marker>
									</google-map>
							
								}

								@if(projectFormGroup?.get('customMapUrl')?.value && customMapUrl()){									
								<div class="mt-3">	
									<app-google-map-embed [url]="projectFormGroup?.get('customMapUrl')?.value" [apiKey]="apiKey"></app-google-map-embed>
								</div>
								}

								@if(!projectFormGroup?.get(['mapData','latitude'])?.value 
								&& !projectFormGroup?.get(['mapData','longitude'])?.value 
								&& !projectFormGroup?.get('customMapUrl')?.value)
								{
									<div class="text-center p-4 bg-warning-subtle w-100 rounded">
										<span class="falign-middle">
											No Location.
										</span>
									</div>
								}
							
							</div>
							}
						</div>
					</div>
				</div>
			</div>
			<!--prebid and bid-->
			<div class="row">
				<div class="col-lg-6 mb-4">
					<div class="card">
						<div class="card-header page-title fs-6">
							Pre-bid Details
						</div>
						<div class="card-body" formGroupName="preBidInfo">
							<div class="row">
								<div class="col-12">						
									<div class="mb-3">
										<app-civcast-date-time-picker name="preBidDateTime" formControlName="bidDateTimeInfo"></app-civcast-date-time-picker>
									</div>
								</div>				
							
								<div class="col-12">
									<div class="mb-3">
										<label for="biddetailslocation" class="form-label">Location</label>
										<textarea class="form-control" name="biddetailslocation" id="biddetailslocation"
											formControlName="location" [maxlength]="projectConstants.LOCATION"></textarea>
									</div>
								</div>
								<div class="col-12">
								<div class="mb-3">
									<label for="biddetailsnotes" class="form-label">Notes</label>
									<textarea class="form-control" name="biddetailsnotes" id="biddetailsnotes"
										formControlName="notes" [maxlength]="projectConstants.MAX_DEFAULT"></textarea>
								</div>
								</div>
								<div class="col-12">
									<div class="mb-3">
										<label for="preBidMeetingLink" class="form-label">Meeting Link (e.g., MS Teams,
											Zoom)</label>
										<textarea class="form-control" name="preBidMeetingLink" id="preBidMeetingLink"
											appFormErrorLabel [errors]="projectFormGroup?.get(['preBidInfo', 'link'])?.errors"
											label="Pre-Bid Meeting Link"
											[formControlInfo]="projectFormGroup?.get(['preBidInfo', 'link'])" formControlName="link"
											[maxlength]="projectConstants.MAX_DEFAULT"></textarea>
									</div>
								</div>
						
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-6 mb-4">
					<div class="card">
						<div class="card-header page-title fs-6">
							Bid Details
						</div>
						<div class="card-body">
							<div formGroupName="bidDateInfo">
								<div class="row">
									<div class="col-md-8">
										<div class="mb-3">
											<app-civcast-date-time-picker name="bidDateTime" formControlName="bidDateTimeInfo"></app-civcast-date-time-picker>
										</div>										
									</div>
									<div class="col-md-4">
										<div class="mb-3">
											<label class="form-label">Time Zone*</label>
											<select class="form-control" name="bidDateTimeZone" id="bidDateTimeZone"
												style="max-width:300px;" formControlName="timeZone" appFormErrorLabel
												[errors]="projectFormGroup?.get(['bidDateInfo', 'timeZone'])?.errors"
												label="Bid Time"
												[formControlInfo]="projectFormGroup?.get(['bidDateInfo', 'timeZone'])">
												@for (zone of timeZones(); track $index) {
												<option [value]="zone.ZoneId">
													{{zone.Name}}
												</option>
												}

											</select>
										</div>
									</div>
								</div>
								<div class="mb-3">
									<label for="bidLocation" class="form-label">Location</label>
									<textarea id="bidLocation" class="form-control" name="preBidDeitailsLocation"
										formControlName="location" [maxlength]="projectConstants.LOCATION"></textarea>
								</div>
								<div class="mb-3">
									<label for="bidNotes" class="form-label">Notes</label>
									<textarea id="bidNotes" class="form-control" name="prebidDetailsNotes"
										formControlName="notes" [maxlength]="projectConstants.MAX_DEFAULT"></textarea>
								</div>
								<div class="mb-3">
									<label for="bidOpeningLink" class="form-label">Meeting Link (e.g., MS Teams,
										Zoom)</label>
									<textarea class="form-control" name="bidOpeningLink" id="bidOpeningLink"
										appFormErrorLabel [errors]="projectFormGroup?.get(['bidDateInfo', 'link'])?.errors"
										label="Bid Opening Link"
										[formControlInfo]="projectFormGroup?.get(['bidDateInfo', 'link'])"
										formControlName="link" [maxlength]="projectConstants.MAX_DEFAULT"></textarea>
								</div>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="checkbox" name="isTBA" id="isTBA"
									formControlName="isTBA">
								<label class="form-check-label" for="isTBA">
									Display Bid Date as "TBA".
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--scope-->
			<div class="row mb-4">
				<div class="col-12">
					<div class="card">
						<div class="card-header page-title fs-6">
							Scope
						</div>
						<div class="card-body">
							<quill-editor name="scope" class="w-100" theme="snow" [modules]="modules"
								[styles]="{height: '300px'}" format="html" formControlName="scope"
								[maxlength]="projectConstants.SCOPE"></quill-editor>
						</div>
					</div>
				</div>
			</div>
			<!--contact address and info-->
			<div class="row" formGroupName="contactInfo">
				<div class="col-lg-6 mb-4">
					<div class="card">
						<div class="card-header d-flex justify-content-between align-items-center">
							<div class="page-title fs-6">Contact Address</div>
							<button type="button" ID="btnImportProfile" class="btn btn-success btn-sm"
								(click)="importProfile()">Import Profile</button>
						</div>
						<div class="card-body">						
							<div class="mb-3">
								<label class="form-label" for="contactCompany">Company</label>
								<input class="form-control" id="contactCompany" name="companyName" autocomplete="off"
									formControlName="companyName" [maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label class="form-label" for="contactAddress">Address</label>
								<input class="form-control" id="contactAddress" name="address1" autocomplete="off"
									formControlName="address1" [maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label class="form-label" for="contactAddress2">Address 2</label>
								<input class="form-control" id="contactAddress2" name="addresss2" autocomplete="off"
									formControlName="address2" [maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label for="contactCity" class="form-label">City</label>
								<input class="form-control" id="contactCity" name="city" autocomplete="off"
									formControlName="city" [maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label for="contactState" class="form-label">State</label>
								<input class="form-control" id="contactState" name="contactState" id="contactState"
									autocomplete="off" formControlName="state"
									[maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label for="contactZip" class="form-label">Zip</label>
								<input class="form-control" id="contactZip" name="zip" autocomplete="off"
									formControlName="zip" [maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label for="contactWebsite" class="form-label">Website</label>
								<textarea class="form-control" id="contactWebsite" name="website" autocomplete="off"
									appFormErrorLabel [errors]="projectFormGroup?.get(['contactInfo', 'webSite'])?.errors"
									label="Website Link"
									[formControlInfo]="projectFormGroup?.get(['contactInfo', 'webSite'])"
									formControlName="webSite"
									[maxlength]="projectConstants.DEFAULT_CONTACT_INFO"></textarea>
							</div>
							
						</div>
					</div>
				</div>
				<div class="col-lg-6 mb-4">
					<div class="card">
						<div class="card-header page-title fs-6">
							Contact Info
						</div>
						<div class="card-body">						
							<div class="mb-3">
								<label for="contactFirstName" class="form-label">First Name</label>
								<input class="form-control" id="contactFirstName" name="firstName"
									formControlName="firstName" [maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label for="contactLastName" class="form-label">Last Name</label>
								<input class="form-control" id="contactLastName" name="lastName" formControlName="lastName"
									[maxlength]="projectConstants.DEFAULT_CONTACT_INFO" />
							</div>
							<div class="mb-3">
								<label for="contactPhone" class="form-label">Phone</label>
								<input class="form-control" id="contactPhone" name="phone" formControlName="phone"
									[maxlength]="projectConstants.PHONE_MAX" />
							</div>
							<!-- <div class="mb-3">
								<label for="contactFax">Fax</label>
								<input class="form-control" id="contactFax" name="fax" formControlName="fax"
									[maxlength]="projectConstants.PHONE_MAX" />
							</div> -->
							<div class="mb-3">
								<label for="contactEmail" class="form-label">Email</label>
								<input class="form-control" id="contactEmail" name="email" formControlName="email"
									[maxlength]="projectConstants.EMAIL" />
							</div>
							
						</div>
					</div>
				</div>
			</div>
			<!--plans notes-->
			<div class="row mb-4">
				<div class="col-12">
					<div class="card">
						<div class="card-header page-title fs-6">
							<label for="plansNotes">Plans Notes</label>
						</div>
						<div class="card-body">
							<div class="mb-3">
								<textarea id="plansNotes" class="form-control" name="planNotes" style="height:100px;"
									formControlName="notes" [maxlength]="projectConstants.NOTES"></textarea>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--additional notes-->
			<div class="row mb-4">
				<div class="col-12">
					<div class="card">
						<div class="card-header page-title fs-6">
							<label for="addNotes">Additional Notes</label>
						</div>
						<div class="card-body">
							<div class="mb-3">
								<textarea id="addNotes" class="form-control" style="height:100px;" name="additionalNotes"
									formControlName="additionalNotes" [maxlength]="projectConstants.NOTES"></textarea>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--project settings and q/a-->
			<div class="row">
				<div class="col-lg-6 mb-4" formGroupName="projectSettings">
					<div class="card">
						<div class="card-header page-title fs-6">
							Settings
						</div>
						<div class="card-body">
							<!-- public or private -->
							<div class="mb-2">
								<div class="mb-2">Public or Private?</div>
								<div class="form-check">
									<input class="form-check-input" type="radio" name="isPrivate"
										formControlName="isPrivate" id="public" [value]="false">
									<label class="form-check-label" for="public">
										Public
									</label>
								</div>
								<div class="form-check">
									<input class="form-check-input" type="radio" name="isPrivate"
										formControlName="isPrivate" id="private" [value]="true">
									<label class="form-check-label" for="private">
										Private
									</label>
								</div>
							</div>
							@if(projectFormGroup?.get(['projectSettings', 'isPrivate'])?.value){
							@if(!this.projectFormGroup.get('projectSettings.uniqueId')?.value){
							<div class="border bg-light rounded p-3 mb-2">
								<div class="mb-2 lo_sensitive">
									<label class="form-label" for="private">
										Private Id
									</label>
									<div class="placeholder-glow">
										<div class="row">
											<div class="col-12 py-4">
												<span class="placeholder col-12" style="height:20px;"></span>
											</div>
										</div>
									</div>
								</div>	

							</div>
							}@else{
							<div class="border bg-light p-3 mb-2">
								<div class="mb-2 lo_sensitive">
									<label class="form-label" for="privateId">
										Private ID
									</label>
									<div class="input-group">
										<input class="form-control lo_sensitive" type="text" name="privateId" id="privateId"
											style="border: none; font-size: 1.5rem; letter-spacing: 3px; font-family: 'Consolas'"
											formControlName="uniqueId" appFormErrorLabel
											[errors]="projectFormGroup?.get(['projectSettings', 'uniqueId'])?.errors"
											label="Unique Id" [bindToNextParent]="true"
											[formControlInfo]="projectFormGroup?.get(['projectSettings', 'uniqueId'])"
											[value]="true">
										<button type="button" class="btn btn-primary"
											(click)="copyInput(projectFormGroup?.get(['projectSettings', 'uniqueId'])?.value)">Copy</button>
									</div>
								</div>
								<!-- <div class="mb-3">
									<label class="form-label">
										Password
									</label>
									<div class="input-group">
										<input type="text" name="projectPassword" class="form-control lo_sensitive"
											style="font-size: 1.5rem; letter-spacing: 3px; font-family: 'Consolas'"
											formControlName="password" appFormErrorLabel
											[errors]="projectFormGroup?.get(['projectSettings','password'])?.errors"
											label="Password" [bindToNextParent]="true"
											[formControlInfo]="projectFormGroup?.get(['projectSettings','password'])" />
										<button type="button" class="btn btn-primary"
											(click)="copyInput(projectFormGroup?.get(['projectSettings', 'password'])?.value)"
											[disabled]="projectFormGroup?.get(['projectSettings','password'])?.errors?.['required']">
											Copy
										</button>
									</div>
								</div> -->
							</div>
							}
							}
							<!-- public plan holders -->
							<div class="mb-3">
								<div class="form-check">
									<input class="form-check-input" type="checkbox" name="publicPHList" id="publicPHList"
										formControlName="showPlanholdersList">
									<label class="form-check-label" for="publicPHList">
										Public Plan Holders List
									</label>
								</div>
							</div>
							<!-- project visibility -->
							<div class="mb-3">
								<div class="form-check">
									<input class="form-check-input" type="checkbox" name="showProject" id="showProject"
										formControlName="isVisible">
									<label class="form-check-label" for="showProject">
										Show Project
									</label>
								</div>
							</div>
							<!-- official plans -->
							<div class="mb-3">
								<div class="form-check">
									<input class="form-check-input" type="checkbox" name="officialDocs" id="officialDocs"
										formControlName="isPlansOfficial">
									<label class="form-check-label" for="officialDocs">
										Plans are official bidding documents?
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-6 mb-4" formGroupName="qa">
					<div class="card">
						<div class="card-header page-title fs-6">
							Questions and Answers
						</div>
						<div class="card-body">
							<div>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" name="allowQA" id="allowQA"
										formControlName="allowQuestions">
									<label class="form-check-label" for="allowQA">
										Allow Questions
									</label>
								</div>
							</div>
							@if(projectFormGroup?.get('qa')?.get('allowQuestions')?.value){
							<!--add emails -->
							<div class="bg-light p-3 border rounded my-3">
								<div class="mb-2">Notification Email Addresses</div>
								<button type="button" class="btn btn-success mb-0" data-bs-toggle="modal"
									data-bs-target="#addQAEmailModal">Add Email(s)</button>
								<ul class="list-group mt-3" formArrayName="emails">
									@for (emailAddress of projectFormGroup?.get('qa.emails')?.controls; track $index) {
									<li class="list-group-item py-3" [formGroupName]="$index">
										<div class="mb-3">
											<label class="form-label">Display Name</label>
											<input class="form-control" name="displayNameinfo" formControlName="displayName"
												appFormErrorLabel
												[errors]="projectFormGroup?.get(['qa', 'emails', $index, 'displayName'])?.errors"
												label="Display Name"
												[formControlInfo]="projectFormGroup?.get(['qa', 'emails', $index, 'displayName'])" />
										</div>
										<div class="mb-3">
											<label class="form-label">Email</label>
											<input class="form-control" type="email" name="emailinfo"
												formControlName="email" appFormErrorLabel
												[errors]="projectFormGroup?.get(['qa', 'emails', $index, 'email'])?.errors"
												label="Email"
												[formControlInfo]="projectFormGroup?.get(['qa', 'emails', $index, 'email'])" />
										</div>
										<button class="btn btn-danger" type="button"
											(click)="deleteQAEmail(emailAddress.value.Email)">
											Remove
										</button>
									</li>
									}
								</ul>
							</div>
							<!-- add question cutoff date -->
							<div class="bg-light p-3 border rounded">
								<div class="form-check">
									<input class="form-check-input" type="checkbox" name="expireQA" id="expireQA"
										formControlName="expireQA">
									<label class="form-check-label" for="expireQA">
										Question Cutoff Date
									</label>
								</div>
								@if(projectFormGroup?.get('qa')?.get('expireQA')?.value){
								<div class="mt-3">
									<div class="row">
										<div class=" col-8 col-xl-8">
											<app-civcast-date-time-picker name="preBidDateTime" formControlName="qaExpirationDateInfo"></app-civcast-date-time-picker>
										</div>
										<div class=" col-4 col-xl-4">
											<div class="mb-3 mb-xl-0">
												<label for="qaExpirationTimeZone" class="form-label">Time Zone*</label>
												<select class="form-control" name="qaExpirationTimeZone"
													formControlName="qaExpirationTimeZone">
													<option *ngFor="let zone of timeZones()" [value]="zone.ZoneId">
														{{zone.Name}}
													</option>
												</select>
											</div>
										</div>
									</div>
								</div>
								}
							</div>
							}
						</div>
					</div>
				</div>
			</div>
			@if(!projectFormGroup?.valid){
			<div class="alert alert-danger">
				@if(projectFormGroup?.controls['releaseDate']?.errors?.['required']){
				<div>
					Advertisement Date is required.
				</div>
				}

				@if(projectFormGroup?.controls['projectTitle']?.errors?.['required']){
				<div>
					Project Title is required.
				</div>
				}

				@if(projectFormGroup?.controls['internalId']?.errors?.['required']){
				<div>
					Project No. is required.
				</div>
				}

				@if(projectFormGroup?.get(['bidDateInfo', 'bidDateTimeInfo'])?.errors?.['required']){

				<div>
					Bid Date is required.
				</div>
				}

				@if(projectFormGroup?.get(['bidDateInfo', 'bidDateTimeInfo'])?.errors?.['required']){

				<div>
					Bid Time is required.
				</div>
				}

				@if(projectFormGroup?.get(['bidDateInfo', 'timeZone'])?.errors?.['required']){

				<div>
					Bid Date Time Zone is required.
				</div>
				}

				@if(projectFormGroup?.get(['projectSettings', 'password'])?.errors?.['required'] &&
				projectFormGroup?.get(['projectSettings', 'isPrivate'])?.value){
				<div>
					Private Project Password is required.
				</div>
				}

				@for(item of projectFormGroup?.get(['qa', 'emails'])?.controls; track $index){
				@if(projectFormGroup?.get(['qa', 'emails', $index, 'displayName'])?.errors?.['required'] ){
				<div>
					Q/A Email Display Name is required.
				</div>
				}

				@if(projectFormGroup?.get(['qa', 'emails', $index, 'email'])?.errors?.['required'] ){
				<div>
					Q/A Email is required.
				</div>
				}


				@if(projectFormGroup?.get(['qa', 'emails', $index, 'email'])?.errors?.['pattern'] ){
				<div>
					Q/A Email is not propertly formated.
				</div>
				}

				@if(projectFormGroup?.get(['qa', 'emails', $index, 'email'])?.errors?.['maxlength'] ){
				Email is too long at index {{ $index + 1 }}.
				{{ projectFormGroup?.get(['qa', 'emails', $index, 'email'])?.errors?.maxlength.actualLength }}
				/ {{ projectFormGroup?.get(['qa', 'emails', $index, 'email'])?.errors?.maxlength.requiredLength }}.
				}
				}

				@if(projectFormGroup?.get(['projectSettings', 'uniqueId'])?.errors?.['required']){
				<div>
					Project Unique ID is required.
				</div>
				}

				@if(projectFormGroup?.get(['qa', 'expireQA'])?.value && projectFormGroup?.get(['qa',
				'allowQuestions'])?.value){
				@if(projectFormGroup?.get(['qa','qaExpirationDate'])?.errors?.['required']){

				<div>
					QA Cutoff Date is required.
				</div>
				}

				@if(projectFormGroup?.get(['qa','qaExpirationTime'])?.errors?.['required']){

				<div>
					QA Cutoff Time is required
				</div>
				}

				@if(projectFormGroup?.get(['qa','qaExpirationTimeZone'])?.errors?.['required']){

				<div>
					QA Cutoff Time Zone is required.
				</div>
				}

				}

				@if(projectFormGroup?.get(['locationInfo', 'state'])?.errors?.['required']){
				<div>
					State is required.
				</div>
				}

				@if(projectFormGroup?.get(['locationInfo', 'county'])?.errors?.['required']){
				<div>
					County is required.
				</div>
				}

				@if(validationErrors?.length > 0){
				<div aria-live="polite" toastContainer class="alert alert-danger">
					@for (error of validationErrors; track $index) {
					<div>
						{{ error }}
					</div>
					}
				</div>
				}
			</div>
			}
			<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-dialog-centered modal-xl">
					<div class="modal-content">
						<div class="modal-header">
							<h5 class="modal-title" id="exampleModalLabel">Add Map Location</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<app-google-map [defaultCenter]="defaultCenter" [apiKeyInput]="apiKey"></app-google-map>
						</div>
						<div class="modal-footer justify-content-right">						
							<div>
								<button type="button" class="btn btn-secondary mx-2" data-bs-dismiss="modal">Close</button>
<!-- 
								<button type="button" class="btn btn-primary" data-bs-dismiss="modal" (click)="saveMarker()"
									[disabled]="selectedMarkerPositions.length <= 0">Update</button> -->
							</div>

						</div>
					</div>
				</div>
			</div>
		</form>
	</div>
	<!--add qa email modal-->
	<div class="modal fade" id="addQAEmailModal" tabindex="-1" aria-labelledby="addQAEmailLabel" aria-hidden="true">
		<form [formGroup]="qaAddEmailFormGroup" (ngSubmit)="addQAEmail()">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="addQAEmailLabel">Add Email</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="row">
							<div class="mb-3">
								<label class="form-label">Email Address</label>
								<input name="qaAddEmail" type="email" class="form-control" formControlName="qaAddEmail"
									[maxlength]="projectConstants.EMAIL" />
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
						<button type="submit" class="btn btn-primary" data-bs-dismiss="modal"
							[disabled]="!qaAddEmailFormGroup.valid">Add Email</button>
					</div>
				</div>
			</div>
		</form>
	</div>
}