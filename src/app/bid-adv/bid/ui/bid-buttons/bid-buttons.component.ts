import { CommonModule } from '@angular/common';
import { Component, effect, inject, input } from '@angular/core';
import { ActivatedRoute, Router, UrlSerializer } from '@angular/router';
import { ProjectManagerService } from 'src/app/bid-adv/shared/data-access/bids-manager-service';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { Advertisement } from 'src/app/bid-adv/shared/interfaces/advertisement';

@Component({
    selector: 'app-bid-buttons',
    imports: [CommonModule],
    templateUrl: './bid-buttons.component.html',
    styleUrl: './bid-buttons.component.css'
})
export class BidButtonsComponent {  
  projectManagerService = inject(ProjectManagerService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  urlSerializer = inject(UrlSerializer);
  project = input<Advertisement>({} as Advertisement);  
  isSaving = this.projectManagerService.isSaving;
  projectFormGroup = this.projectManagerService.projectFormGroup;
  state = input<string>('');  
  validity = input<boolean>(false);

  validEffect = effect(() => {
    if(this.validity()) {
      console.log('valid');
    }else{
      console.log('invalid');
    }
  });

  cancelProject() {
      this.router.navigate(['../'], { relativeTo: this.aRoute });

  }

  saveAndPay(){
    this.save();
  }

  saveAndPayLater(){
    this.save();

  }

  saveAndAddToCart(){
    this.save();
  }

  update(){
    this.save();
  }

  private save(redirectUrl: Array<string> | null = null){
    if(this.project()?.Id){
      this.projectManagerService.updateAdvertisement(this.project()?.Id as string, this.projectManagerService.projectFormGroup.getRawValue());
    }else{
      this.projectManagerService.createAdvertisement(this.projectManagerService.projectFormGroup.getRawValue());
    }
    
  }

  createUrl(routeCommands: any[]): string {
    const urlTree = this.router.createUrlTree(routeCommands);
    return this.urlSerializer.serialize(urlTree);
  }
}
