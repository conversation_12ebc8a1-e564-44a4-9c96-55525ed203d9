import { Component, effect, inject, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EditorChangeContent, EditorChangeSelection, QuillModule } from 'ngx-quill';
import { environment } from 'src/environments/environment';
import { MessageCenterEmailLog } from '../../shared/interfaces/bids-email-planholder';
import { BidsAdvEmailPlanholdersService } from '../../shared/data-access/bids-email-planholders.service';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { BidAdvService } from '../../shared/data-access/bid.service';

@Component({
    selector: 'app-email-planholders',
    templateUrl: './email-planholders.component.html',
    styleUrls: ['./email-planholders.component.css'],
    standalone: true,
    encapsulation: ViewEncapsulation.None,
    imports: [CommonModule, FormsModule, QuillModule],
    viewProviders: [NgForm]
})
export class EmailPlanholdersComponent implements OnInit {

  bidAdvSerivce = inject(BidAdvService);
  project = this.bidAdvSerivce.advertisement;
   modules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],        // toggled buttons      
      [{ 'header': 1 }, { 'header': 2 }],               // custom button values
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],      
      [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent       
      [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],  
      [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme      
      [{ 'align': [] }],  
      ['clean'],                                         // remove formatting button  
      ['link']                         // link and image, video
    ]
  }

  content = "";
  emailLogs: any = [];
  messageLog: MessageCenterEmailLog = {} as MessageCenterEmailLog;
  currentEmailLog: MessageCenterEmailLog = {} as MessageCenterEmailLog;
  isSending: boolean = false;

  projectEffect = effect(() =>{
      if(this.project()){
          
        this.messageLog = {
          Body: `CIVCAST PROJECT INFO:<br />Title: ${this.project()?.ProjectTitle} <br />`,
          CreatedDate: null,
          GatheredEmails: [],
          Id: null,
          SentDate: null,
          Subject: null
          
        };


        if(this.project()?.Permissions?.IsPrivate){
          this.messageLog.Body += `Id: ${this.project()?.ProjectUniqueId}<br />Password: ${this.project()?.Password}`;
        }

        this.getSentEmails();
      }
  });

  constructor(private aRoute: ActivatedRoute, private emailPlanholderService: BidsAdvEmailPlanholdersService) {
    

   }

  ngOnInit(): void {
  
  }

  getSentEmails(){
    this.emailPlanholderService.getSentEmails(this.project()?.Id as string).subscribe(result => {
      this.emailLogs = result;
    });
  }

  sendEmail(){
    this.isSending = true;
    const pLink = `${environment.BaseSiteUrl}/bidops/projects/${this.project()?.Id}`;

    if(this.messageLog){
      this.emailPlanholderService.sendEmail(this.project()?.Id as string, pLink, this.messageLog).subscribe({
        next: (value) => {
          this.isSending = false;
        }, error: (err) => {
          this.isSending = false;
        }      
      });
    }


  }

  setEmailLog(email: MessageCenterEmailLog){
    this.currentEmailLog = email;
  }

  changedEditor(event: EditorChangeContent | EditorChangeSelection) {
    // tslint:disable-next-line:no-console
    console.log('editor-change', event)
  }

  focus($event: any) {
    // tslint:disable-next-line:no-console
    console.log('focus', $event)
    // this.focused = true
    // this.blurred = false
  }

  blur($event: any) {
    // tslint:disable-next-line:no-console
    console.log('blur', $event)
    // this.focused = false
    // this.blurred = true
  }

}
