<!-- header -->
<header class="mb-3">
	<h5 class="page-title text-secondary">Email Plan Holders</h5>
	<p>Email your plan holders. Keep them informed with customized communications for seamless
		project coordination.</p>
</header>
<!-- tabs -->
<section>
	<ul class="nav nav-tabs mb-3" id="myTab" role="tablist">
		<li class="nav-item" role="presentation">
			<button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane"
				type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">New Email</button>
		</li>
		<li class="nav-item" role="presentation">
			<button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
				type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">Sent Emails</button>
		</li>
	</ul>
</section>
<!-- email -->
<section>
	<div class="tab-content" id="myTabContent">
		<!-- new email -->
		<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab"
			tabindex="0">
			<form #sendEmailForm="ngForm" (ngSubmit)="sendEmail()">
				<!-- subject -->
				<div class="mb-3">
					<label class="form-label fw-bold">Subject</label>
					<input type="text" #subject name="subject" class="form-control" [(ngModel)]="messageLog.Subject"
						required />
				</div>
				<!-- body -->
				<div>
					<label class="form-label fw-bold">Body</label>
					<div>
						<quill-editor name="emailBody" [modules]="modules" format="html" [(ngModel)]="messageLog.Body"
							[styles]="{height: '300px'}" (onFocus)="focus($event)"
							(onEditorChanged)="changedEditor($event)" (onBlur)="blur($event)" [required]="true">
						</quill-editor>
					</div>
					<div id="emailHelp" class="form-text">A link to the project will automatically be inserted in
						the email.</div>
				</div>
				<!-- send email button -->
				<div class="d-flex justify-content-end">
					<button type="submit" class="btn btn-outline-dark"
						[disabled]="!sendEmailForm.form.valid || isSending">
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isSending"></i>
						Send Email
					</button>
				</div>
			</form>
		</div>
		<!-- sent emails -->
		<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
			<p class="text-secondary">Select an email below to view it.</p>
			<div class="row mb-5">
				<div class="col-12 col-md-6">
					<h1 class="page-title fs-5 pb-2 mb-3 border border-top-0 border-start-0 border-end-0">Sent Emails</h1>
					<ul class="list-group mb-3">
						<li class="list-group-item" *ngFor="let email of emailLogs">
							<div>
								<span class="badge text-bg-info text-white">{{ email.SentDate | date: 'M/dd/yyyy h:mm
									a'}}</span>
							</div>
							<div>
								<a class="text-decoration-none" href="javascript:void(0)"
									(click)="setEmailLog(email)">{{email.Subject || 'No Subject' }}</a>
							</div>
						</li>
						<li class="list-group-item" *ngIf="emailLogs?.length <= 0">
							No emails sent yet.
						</li>
					</ul>
				</div>
				<div class="col-12 col-md-6">
					<h1 class="page-title fs-5 pb-2 mb-3 border border-top-0 border-start-0 border-end-0">Selected Email</h1>
					<div class="card" *ngIf="currentEmailLog">
						<div class="card-body">
							<!-- to and subject -->
							<div class="mb-3">
								<div>
									<span class="fw-bold">To: </span>
									<span *ngFor="let emailInfo of currentEmailLog?.GatheredEmails; let isLast=last">
										{{emailInfo.Address}}{{isLast ? '' : ', '}}
									</span>
									<span *ngIf="currentEmailLog?.GatheredEmails?.length <= 0">
										<i>None</i>
									</span>
								</div>
								<div>
									<span class="fw-bold">Subject: </span> {{ currentEmailLog.Subject}}
								</div>
							</div>
							<!-- body -->
							<div>
								<div [innerHtml]="currentEmailLog.Body">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>