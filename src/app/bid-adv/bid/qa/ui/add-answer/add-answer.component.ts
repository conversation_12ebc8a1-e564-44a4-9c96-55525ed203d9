import { Component, computed, effect, inject, input, OnDestroy } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { BidsAdvQAService } from 'src/app/bid-adv/shared/data-access/bids-qa.service';
import { EditorModule,  } from 'primeng/editor';
import { CustomQuillComponent } from 'src/app/shared/ui/custom-quill/custom-quill.component';
import { v4 } from 'uuid';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-add-answer',
    imports: [CommonModule, FormsModule, ReactiveFormsModule, EditorModule, CustomQuillComponent],
    templateUrl: './add-answer.component.html',
    styleUrl: './add-answer.component.css'
})
export class AddAnswerComponent implements OnDestroy {
  isShowAnswer: boolean = false;  
  isAddingAnswer = computed(() => this.qaQuestionsService.isAddingAnswer(this.questionId()));
  answerId:string = v4();
  formId:string = v4();
  questionId = input.required<string>();
  projectId = input.required<string>();
  qaQuestionsService = inject(BidsAdvQAService);  
  s3Prefix = computed(() => {
    return `public/project/${this.projectId()}/qa/${this.questionId()}`;
  });


  answerForm = new FormGroup({
    answer: new FormControl('', Validators.required)
  });

  constructor() {
    effect(() => {
      if(this.questionId()){
        this.qaQuestionsService.addAnswerForm(this.questionId(), this.answerForm);
      }
    });

  }
  ngOnDestroy() {
    this.qaQuestionsService.removeAnswerForm(this.questionId());
  }

  showAnswer(){
    this.isShowAnswer = !this.isShowAnswer;
  }

  
  addAnswer(){    
    this.qaQuestionsService.addAnswer(this.questionId(), this.answerForm.get('answer')?.value as string);
  }

  cancelReply(){
    this.isShowAnswer = false;
  } 
}
