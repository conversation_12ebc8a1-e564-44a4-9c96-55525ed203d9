import { CommonModule } from '@angular/common';
import { Component, effect, inject, signal } from '@angular/core';
import {  BidsAdvQAService } from 'src/app/bid-adv/shared/data-access/bids-qa.service';
import { Answer, Question } from 'src/app/bid-adv/shared/interfaces/questions';
import { AddAnswerComponent } from '../add-answer/add-answer.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { QaSkeletonComponent } from '../qa-skeleton/qa-skeleton.component';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { AllowedProfile } from 'src/app/shared/interfaces/account-profile';

@Component({
    selector: 'app-qa-info',
    imports: [CommonModule, AddAnswerComponent, FormsModule, ReactiveFormsModule, QaSkeletonComponent, NgbAccordionModule],
    templateUrl: './qa-info.component.html',
    styleUrl: './qa-info.component.css'
})
export class QaInfoComponent {

  qaQuestionsService = inject(BidsAdvQAService);  
  questions = this.qaQuestionsService.projectQuestions;
  allQuestions = this.qaQuestionsService.questionsFormatted;
  currentFilter = 'all';
  actionLoading = signal<boolean>(false);
  isAskingQuestion = this.qaQuestionsService.isAskingQuestion;
  projectId = this.qaQuestionsService.projectId;
  isLoading = this.qaQuestionsService.isLoading;
  questionForm = this.qaQuestionsService.questionForm;
  isDownloadingExcel = this.qaQuestionsService.isDownloadingExcel;
  filter = signal<string>('all');

  constructor() {
    effect(() => {
      if(this.filter() === 'all'){
        //this.allQuestions.set(this.questions().map(question => new QuestionInfo(question)));
      }
    });
  }

  showUserProfile(userId: string){
    
  }

  downloadExcel(){    
    this.qaQuestionsService.exportQuestionsExcel();
  }

  askQuestion(){    
    this.qaQuestionsService.addQuestion(this.questionForm.get('question')?.value as string);
  }

  disableAnswer(questionId:string, answer: Answer, isDisabled: boolean){ 
    this.qaQuestionsService.updateAnswer(questionId, answer, {Disabled: !isDisabled});
  }

  filterQuestions(filter: string){
    // this.currentFilter = filter;
    // if(filter === 'all'){
    //   this.allQuestions = [];
    //   for(let question of this.questions() as Question[]){
    //     this.allQuestions?.push(new QuestionInfo(question));
    //   }
    // }
    // else if(filter === 'answered'){
    //   this.allQuestions = [];
    //   const filteredQuestions = this.questions()?.filter(question => question.Answers?.length > 0) ?? [];
    //   for(let question of filteredQuestions){
    //     this.allQuestions?.push(new QuestionInfo(question));
    //   }
    // }
    // else if(filter === 'unanswered'){
    //   this.allQuestions = [];
    //   const filteredQuestions = this.questions()?.filter(question => question.Answers?.length === 0) ?? [];
    //   for(let question of filteredQuestions){
    //     this.allQuestions?.push(new QuestionInfo(question));
    //   }

    // }
  }

}


