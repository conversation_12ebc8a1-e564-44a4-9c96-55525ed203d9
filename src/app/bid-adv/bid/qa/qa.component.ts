import { Component, ElementRef, EventEmitter, OnDestroy, Output, ViewChild, effect, inject } from '@angular/core';
import { QaInfoComponent } from './ui/qa-info/qa-info.component';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { BidsAdvService } from '../../shared/data-access/bids.service';
import { CustomQuillComponent } from 'src/app/shared/ui/custom-quill/custom-quill.component';
import { DndDirective } from 'src/app/shared/utils/directives/dnd.directive';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { QaSkeletonComponent } from './ui/qa-skeleton/qa-skeleton.component';
import { BidsAdvQAService } from '../../shared/data-access/bids-qa.service';
import { BidAdvService } from '../../shared/data-access/bid.service';

@Component({
    selector: 'app-qa',
    imports: [QaInfoComponent],
    templateUrl: './qa.component.html',
    standalone: true,
    styleUrl: './qa.component.css'
})
export class QaComponent implements OnDestroy {

  authService = inject(AuthService);
  bidsAdvQAService = inject(BidsAdvQAService);
  bidsAdvService = inject(BidAdvService);  
  isAllowedAccess = this.authService.isLoggedInSignal;
  
  ngOnDestroy(): void {
    
  }

  projectIdEffect = effect(() => {
    if(this.bidsAdvService.projectId()){
      this.bidsAdvQAService.projectId.set(this.bidsAdvService.projectId() as string);
    }
  });

  constructor() { }

}
