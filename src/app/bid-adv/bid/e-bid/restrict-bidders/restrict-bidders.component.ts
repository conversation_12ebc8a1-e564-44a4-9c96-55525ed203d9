import { Component, computed, effect, inject, OnD<PERSON>roy, OnInit, signal } from '@angular/core';
import { RestrictedBiddersService } from '../../../shared/data-access/bids-ebid-restricted-bidder.service';
import { BidsAdvService } from '../../../shared/data-access/bids.service';
import { CommonModule } from '@angular/common';
import { BidAdvertsPlanholdersService } from '../../../shared/data-access/bids-planholders-service';
import { FormsModule } from '@angular/forms';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { BidAdvService } from 'src/app/bid-adv/shared/data-access/bid.service';

@Component({
    selector: 'app-restrict-bidders',
    imports: [CommonModule, FormsModule, ToggleSwitchModule, NgbPagination],
    standalone: true,
    templateUrl: './restrict-bidders.component.html',
    styleUrl: './restrict-bidders.component.css'
})
export class RestrictBiddersComponent implements OnInit, OnDestroy {

    restrictedBiddersService = inject(RestrictedBiddersService);
    bidAdvService = inject(BidAdvService);
    restrictedBidders = this.restrictedBiddersService.restrictedBidders;    
    isLoading = this.restrictedBiddersService.isLoading;
    isSaving = this.restrictedBiddersService.isSaving;
    total = this.restrictedBiddersService.totalPlanholders;
    limit = this.restrictedBiddersService.planholderLimit;
    currentPage = this.restrictedBiddersService.planholderPage;
    isBidExpired = this.bidAdvService.bidDateExpired;

    ngOnInit(): void {
        this.restrictedBiddersService.startEffects();
    }
    ngOnDestroy(): void {
        this.restrictedBiddersService.stopEffects();
    }

    saveRestrictedBidders() {
        this.restrictedBiddersService.saveRestrictedBidders(true);
    }

    changePage(page: number) {
        this.restrictedBiddersService.planholderPage.set(page);
    }

}
