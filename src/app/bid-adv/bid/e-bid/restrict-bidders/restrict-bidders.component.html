<!-- header -->
<header>
	<h6 class="page-title mb-3">Blocked Bidders</h6>
	<p class="mb-3">The list below displays your plan holders. Only selected plan holders are eligible to submit a bid.</p>
</header>
<!-- plan holders -->
<section class="col-12 mb-3">
	@if(isLoading()){
		<ul class="list-group placeholder-glow">
			@for (item of [1,2,3,4]; track $index) {
				<li class="list-group-item">
					<div class="placeholder col-12"></div>										
				</li>	
			}
		
		</ul>
	}@else {
		@if(isBidExpired()){
			<div class="alert alert-info mb-3" role="alert">
				Bidding has expired.
			</div>

		}@else {
			<ul class="list-group">
				@for (rb of restrictedBidders(); track $index) {
					<li class="list-group-item">
						<div class="row d-flex align-items-center">		
							<div class="col-12 mb-1 mb-md-0 col-md-6">
								<div>{{ rb.PlanholderInfo?.CompanyName }}</div>
								<div class="text-secondary">{{rb.PlanholderInfo?.FirstName}} {{ rb.PlanholderInfo?.LastName }}</div>
							</div>
							<div class="col-12 col-md-6 d-flex justify-content-md-end">
								<div class="form-check">
									<input type="checkbox" class="form-check-input" [id]="$index" [(ngModel)]="rb.IsChecked">
									<label class="form-check-label" [for]="$index">Block</label>
								</div>
							</div>
						</div>
					</li>
				}@empty {						
					<tr>
						<td colspan="3">
							<div class="alert alert-info mb-0" role="alert">
								No plan holders yet.
							</div>
						</td>
					</tr>
				}
			</ul>

						<!-- footer -->
			<footer class="col-12 d-flex justify-content-end mb-3">
				@if(total() > limit()){
					<ngb-pagination [collectionSize]="total()" [pageSize]="limit()" [page]="currentPage()" [rotate]="true" [maxSize]="5"
					[boundaryLinks]="true" (pageChange)="changePage($event)">
					<ng-template ngbPaginationFirst>First</ng-template>
					<ng-template ngbPaginationPrevious>Previous</ng-template>
					<ng-template ngbPaginationNext>Next</ng-template>
					<ng-template ngbPaginationLast>Last</ng-template>
				</ngb-pagination>
				}


				<button type="button" class="btn btn-outline-dark" (click)="saveRestrictedBidders()" [disabled]="isSaving()">
					@if(isSaving()){
						<i class="spinner-border spinner-border-sm"></i>
					}
					
					Save
				</button>
			</footer>
		}
	
	}

</section>
