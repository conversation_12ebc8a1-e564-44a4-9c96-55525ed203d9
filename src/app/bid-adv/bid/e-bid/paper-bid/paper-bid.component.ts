import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, input, model, OnDestroy, signal } from '@angular/core';
import { FormArray, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { PaperBidService } from 'src/app/bid-adv/shared/data-access/bids-ebid-paper-bid.service';
import { EBidFolderService } from 'src/app/ebid/data-access/bid-folder.service';
import { EBidService } from 'src/app/ebid/data-access/ebid.service';
import { BidViews, EBidSectionTypes } from 'src/app/ebid/interfaces/ebid';
import { BidCompletionTimeComponent } from 'src/app/ebid/ui/bid-completion-time/bid-completion-time.component';
import { BidFormComponent } from 'src/app/ebid/ui/bid-form/bid-form.component';
import { BidWorkOrderComponent } from 'src/app/ebid/ui/bid-work-order/bid-work-order.component';

@Component({
  selector: 'app-paper-bid',
  imports: [CommonModule, MultiSelectModule, FormsModule, ReactiveFormsModule, BidFormComponent,
    BidCompletionTimeComponent, BidWorkOrderComponent],  
  standalone: true,
  templateUrl: './paper-bid.component.html',
  styleUrl: './paper-bid.component.css'
})
export class PaperBidComponent implements OnDestroy {
  paperBidService = inject(PaperBidService);
  eBidService = inject(EBidService);
  eBidFolderService = inject(EBidFolderService);
  eBid = this.paperBidService.eBid;  
  bidderBidInfo = this.paperBidService.bidderBidInfo;
  view  = this.paperBidService.view;
  isLoading = computed(() => this.paperBidService.paperBidResource.isLoading());
  projectId = model<string | null>(null);  
	ebidFormGroup = this.paperBidService.ebidFormGroup;
  companyInfoGroup = this.paperBidService.companyInfoGroup;
  public readonly EBID_SECTIONS_TYPES: typeof EBidSectionTypes = EBidSectionTypes;
  public readonly BIDFORM_VIEWS: typeof BidViews = BidViews;
  

  constructor() {
    this.eBidFolderService.startEffects();
  }
  ngOnDestroy(): void {   
    this.eBidFolderService.stopEffects();
  
  }

  projectIdEffect = effect(() => {
    if (this.projectId()) {
      this.paperBidService.projectId.set(this.projectId() as string);
    }
  });  
 
}
