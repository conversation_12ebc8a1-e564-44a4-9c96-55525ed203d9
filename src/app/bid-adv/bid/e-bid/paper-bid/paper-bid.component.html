<!-- @if(isLoading()){
	Loading...
}@else{ -->

	<div class="mb-2">
		<div class="my-4">
			<section class="border mb-3 p-3">
						<!-- title & description -->
			<section class="d-flex justify-content-between align-items-end mb-3">	
				<h1 class="page-title fs-5 m-0">User Info</h1>
			</section>
				<div [formGroup]="companyInfoGroup">
					<div class="row">
						<div class="col-12 col-md-6">
							<div>						
								@if(view() === BIDFORM_VIEWS.BID){
									<label for="companyName" class="form-label">Company Name</label>
									<input type="text" class="form-control" id="companyName" name="companyName"
									formControlName="CompanyName" />
								}@else {
									<span style="font-weight:600">Company Name:</span>
									{{companyInfoGroup?.get('CompanyName')?.value}}
								}								
								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="firstName" class="form-label">First Name</label>
										<input type="text" class="form-control" id="firstName" name="firstName"
											formControlName="FirstName" />
									}@else {
										<span style="font-weight:600">First Name:</span>
										{{companyInfoGroup?.get('FirstName')?.value}}
									}
								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="lastName" class="form-label">Last Name</label>
										<input type="text" class="form-control" id="lastName" name="lastName"
											formControlName="LastName" />
									}@else {
										<span style="font-weight:600">Last Name:</span>
										{{companyInfoGroup?.get('LastName')?.value}}
									}					
								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="email" class="form-label">Email</label>
										<input type="text" class="form-control" id="email" name="email"
											formControlName="Email" />
									}@else {
										<span style="font-weight:600">Email:</span>
										{{companyInfoGroup?.get('Email')?.value}}
									}							
								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="companyDescription" class="form-label">Company Description</label>
										<textarea class="form-control" id="companyDescription" name="companyDescription"
											formControlName="CompanyDescription"></textarea>
									}@else {
										<span style="font-weight:600">Company Description:</span>
										{{companyInfoGroup?.get('CompanyDescription')?.value}}
									}		

								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="companyName" class="form-label">Phone</label>
										<input type="text" class="form-control" id="phone" name="phone"
											formControlName="Phone" />
									}@else {
										<span style="font-weight:600">Company Description:</span>
										{{companyInfoGroup?.get('CompanyDescription')?.value}}
									}

								</div>
								</div>
								<div class="col-12 col-md-6">
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="address1" class="form-label">Address 1</label>
										<input type="text" class="form-control" id="address1" name="address1"
											formControlName="Address1" />
									}@else {
										<span style="font-weight:600">Address 1:</span>
										{{companyInfoGroup?.get('Address1')?.value}}
									}

								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="address2" class="form-label">Address 2</label>
										<input type="text" class="form-control" id="address2" name="address2"
											formControlName="Address2" />
									}@else {
										<span style="font-weight:600">Address 2:</span>
										{{companyInfoGroup?.get('Address2')?.value}}
									}

								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="city" class="form-label">City</label>
									<input type="text" class="form-control" id="city" name="city"
										formControlName="City" />
									}@else {
										<span style="font-weight:600">City:</span>
										{{companyInfoGroup?.get('City')?.value}}
									}

								</div>
								<div>
									@if(view() === BIDFORM_VIEWS.BID){
										<label for="state" class="form-label">State</label>
										<input type="text" class="form-control" id="state" name="state"
											formControlName="State" />
									}@else {
										<span style="font-weight:600">State:</span>
										{{companyInfoGroup?.get('State')?.value}}
									}

								</div>
							<div>
								@if(view() === BIDFORM_VIEWS.BID){
									<label for="zip" class="form-label">Zip</label>
									<input type="text" class="form-control" id="zip" name="zip"
										formControlName="Zip" />
								}@else {
									<span style="font-weight:600">Zip:</span>
									{{companyInfoGroup?.get('Zip')?.value}}
								}
						
					
							</div>
					
						</div>
	
					</div>
	
				</div>
			</section>
		</div>
	</div>
	

		<div class="mb-2">	
			<form [formGroup]="ebidFormGroup">		
				@let sections = ebidFormGroup?.get('sections')?.controls;

				@for (section of sections; track $index) {	
					@let sectionId = section.value.section.SectionId;
					@let sectionType = section.value.section.SectionType;	
												
					@if(sectionType === EBID_SECTIONS_TYPES.BID_FORM){
						<div class="my-4">
							<section class="border mb-3 p-3">
								<app-bid-form [bidFormSection]="section">
								</app-bid-form>
							</section>
						</div>
					}@else if(sectionType === EBID_SECTIONS_TYPES.COMPLETION_TIME){
						<div class="my-4">
							<section class="border mb-3 p-3">
								<app-bid-completion-time [completionTimeSection]="section">
								</app-bid-completion-time>
							</section>
						</div>
					}@else if(sectionType === EBID_SECTIONS_TYPES.WORK_ORDER){
						<div class="my-4">
							<section class="border mb-3 p-3">
								<app-bid-work-order [workOrderSection]="section">
								</app-bid-work-order>
							</section>
						</div>
					}								
				
				}
			</form>		
		</div>	
<!-- } -->
	


