<!-- header -->
<h5 class="page-title fs-6 mb-3">E-Bid Setup</h5>
@if(isEBidLoading()){
	<app-bid-folder-skeleton></app-bid-folder-skeleton>
}@else {
	@let isUsersWorkingOnBid = usersWorkingOnBid()?.length > 0;
	@if(eBidLoadingError()){
		<div class="alert alert-info" role="alert">
			<h4>
				{{eBidLoadingError() }}
			</h4> 
			Something went wrong while loading the e-Bid. Try refreshing the page. If the problem continues, please contact support.
		</div>
	}@else{
		@if(bidDateExpired()){
			<div class="alert alert-info">
				<span class="d-block mb-3">
					This bid ended on {{ localBidDate() | date: 'M/dd/yyyy hh:mm a z'}}. Setup is no longer available.
				</span>
				@if(!eBid()?.OpenedAt){
				<span class="d-block">
					Want to open bids? Click <a routerLink="../open">here</a>.
				</span>
				}@else if(eBid()?.OpenedAt){
				<span class="d-block">
					Looking for bid results? Click <a routerLink="../results">here</a>.
				</span>
				}@else {
				<span class="d-block">
					To continue setting up the bid, please ensure the bid date is set in the future.
				</span>
				}
			</div>
		}@else {
			@if(eBid()?.OpenedAt){
				<div class="alert alert-info">
					<span class="d-block mb-3">
						The bid has been opened. Setup is no longer available.
					</span>
					<span class="d-block">
						To view the bid results, click <a routerLink="../results">here</a>.</span>
				</div>
			}@else{
				
				@if(usersWorkingOnBid()?.length > 0){
					<div class="alert alert-info">
						<span class="d-block mb-3">
							There are currently {{usersWorkingOnBid()?.length}} user(s) working on this bid. Making changes to the bid can effect users working on the bid.					
							 <button class="btn btn-link p-0 m-0 align-baseline" (click)="showWorkingOnBidModal()">Click here</button> to view companies.
						</span>								 
					</div>
				}
				<div class="btn-group me-2">
					<button type="button" class="btn btn-outline-dark" (click)="viewChange(BIDFORM_VIEWS.EDIT)" [ngClass]="{'active': view() === BIDFORM_VIEWS.EDIT}"
						[disabled]="isLoading()">Bid Setup</button>						
					<button type="button" class="btn btn-outline-dark" (click)="viewChange(BIDFORM_VIEWS.BID)" [ngClass]="{'active': view() === BIDFORM_VIEWS.BID}"
						[disabled]="isLoading()">Bid Preview</button>
				</div>
				
				@if(view() === BIDFORM_VIEWS.EDIT){		
					<p-multiSelect 
					[options]="sections()"
					[(ngModel)]="selectedSections" 	
					optionLabel="name"	
					[filter]="false"
					[showToggleAll]="false"
					[showHeader]="false"
					[displaySelectedLabel]="false"	
					(onChange)="sectionsSelected($event)"
					placeholder="Select Bid Sections" [disabled]="isLoading()" />
					<div class="btn-group me-2">
						<button type="button" class="btn" ngbTooltip="ctrl+z"  [disabled]="isLoading()" [ngClass]="{'btn-outline-primary': undoHistoryCount() <= 0, 'btn-primary': undoHistoryCount() > 0}" (click)="undoTool()" placement="top" [disabled]="undoHistoryCount() <= 0">			
							@if(isUndoDisabled){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
							Undo
						</button>
						<button type="button" class="btn" ngbTooltip="ctrl+y"  [disabled]="isLoading()" [ngClass]="{'btn-outline-primary': redoHistoryCount() <= 0, 'btn-primary': redoHistoryCount() > 0}" (click)="redoTool()" placement="top" [disabled]="redoHistoryCount() <= 0">			
							@if(isRedoDisabled){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
							Redo
						</button>
						<button type="button" class="btn" [disabled]="isLoading() || !ebidFormGroup?.valid || !ebidFormGroup?.dirty" [ngClass]="{'btn-outline-danger': !ebidFormGroup?.valid, 'btn-outline-dark': ebidFormGroup?.valid}"  (click)="saveBid()">
							@if(isSavingBid()){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
			
							@if(!ebidFormGroup?.valid){
								<i class="fas fa-exclamation-triangle fa-1x mx-2"></i>
							}				
							Save
						</button>	
					</div>					
				}
				<form [formGroup]="ebidFormGroup">
					@if(view() === BIDFORM_VIEWS.EDIT){
					<!--Bid Setup-->
					<section>
						<div class="border p-3 my-3">
							<div class="row gx-2">
								<div class="col-6 col-xl-3">		
									<label class="form-check-label" for="enableEBid">
											Turn e-bidding off for now
										</label>							
									<div class="mt-2">
										<input type="checkbox" class="form-check-input" name="enableEBid" id="enableEBid"
											[checked]="!ebidFormGroup?.get('IsEnabled')?.value"
											(change)="setIsEnabled($event)" />
																	
									</div>
								</div>
									<label for="BeginDate" class="form-label">When do you want to allow users to start submitting bids?</label>
								<div class="col-6 col-xl-3">
									<div class="form-group">
										<label for="BeginDate" class="form-label">Date*</label>
										<div>
											<app-prime-calendar-custom name="BeginDate" (dateChange)="dateChanged($event)"
												formControlName="BeginDate"></app-prime-calendar-custom>
										</div>									
									</div>
								</div>
								<div class=" col-6 col-xl-3">
									<div class="mb-3 mb-xl-0">
										<label for="BeginDateTimeInfo" class="form-label">Time*</label>
										<app-time-picker-alt #bidBeginDateTimePicker name="BeginDateTimeInfo"
											formControlName="BeginDate"></app-time-picker-alt>
									</div>
								</div>
								<div class=" col-6 col-xl-3">
									<div class="mb-3 mb-xl-0">
										<label for="BeginTimeZone" class="form-label">Time Zone*</label>
										<select class="form-control" name="BeginTimeZone" formControlName="BeginTimeZone">
											<option *ngFor="let zone of timeZones()" [value]="zone.ZoneId">
												{{zone.Name}}
											</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</section>
					}
				
			
					<app-ebid-folder [showBidFormImport]="showBidFormImport()" [useDisabledOnBidFormItem]="isUsersWorkingOnBid" [allowFullBidFormDelete]="isUsersWorkingOnBid"></app-ebid-folder>
				</form>
			}			
		}
	}
}

<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" #workingOnBidModal>
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Working on Bid</h5>
				<!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button> -->
			</div>
			<div class="modal-body">		
				<div class="alert alert-info">
					The following companies are working on bids or have submitted their bids. If you update the bid it will effect their bids they are working on.
				</div>				
				<ul class="list-group">
					@for(workingOn of usersWorkingOnBid(); track $index){						
						<li class="list-group-item">
							<div class="row">
								<div class="col-4">
									{{workingOn.UserInfo?.Company?.Name}}
									<div style="font-style: italic;" class="small">
										{{workingOn.UserInfo?.FirstName}} {{workingOn.UserInfo?.LastName}}
									</div>
								</div>
								<div class="col-5 text-right">
									<div>
										<a href="tel:{{workingOn.UserInfo?.Company?.Phone | phoneFormatter}}">p: {{workingOn.UserInfo?.Company?.Phone | phoneFormatter}} </a>
									</div>
									<div>
										<a href="mailto:{{workingOn.UserInfo?.Email}}">email: {{workingOn.UserInfo?.Email}}</a>										
									</div>
									
								</div>
								<div class="col-3">
									<div class="text-right">
										@if(!workingOn.SubmittedAt){
											<span class="badge bg-warning text-dark">Working on Bid</span>
										}@else{
											<span class="badge bg-success">Submitted Bid</span>
										}
									</div>
								</div>
							</div>				
						</li>
					}	
				</ul>				
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" (click)="hideWorkingOnBidModal()">Close</button>						
			</div>
		</div>
	</div>
</div>
