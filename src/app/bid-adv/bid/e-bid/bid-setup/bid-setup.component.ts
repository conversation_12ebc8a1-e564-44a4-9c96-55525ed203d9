import { Component, computed, effect, EffectRef, HostListener, inject, Injector, On<PERSON><PERSON>roy, OnInit, viewChild } from '@angular/core';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { BidViews, EBidSectionNames, EBidSectionTypes } from 'src/app/ebid/interfaces/ebid';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { EBidFolder } from 'src/app/ebid/ui/bid-folder/bid-folder.component';
import { BidFolderSkeletonComponent } from 'src/app/ebid/ui/bid-folder-skeleton/bid-folder-skeleton.component';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router, RouterEvent, RouterLink } from '@angular/router';
import { Advertisement } from 'src/app/bid-adv/shared/interfaces/advertisement';
import { GlobalDataService } from 'src/app/shared/data-access/global-data-service';
import { TimePickerAltComponent } from 'src/app/shared/ui/time-picker-alt/time-picker.component';
import { PrimeCalendarCustomComponent } from 'src/app/shared/ui/prime-calendar-custom/prime-calendar-custom.component';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { BidSetupService } from 'src/app/bid-adv/shared/data-access/bids-ebid-setup.service';
import { EBidProjectService } from 'src/app/bid-adv/shared/data-access/bids-ebid-project.service';
import { EBidHistoryService } from 'src/app/ebid/data-access/ebid-history.service';
import { PhonesPipe } from 'src/app/shared/utils/pipes/phone.pipe';
import { BaseEffectsService } from 'src/app/shared/interfaces/abstract-effects-service';
import { BidFormService } from 'src/app/ebid/data-access/bidform-service';
import { EBidFolderService } from 'src/app/ebid/data-access/bid-folder.service';
import { BidAdvService } from 'src/app/bid-adv/shared/data-access/bid.service';

@Component({
  selector: 'app-bid-setup',
  imports: [CommonModule, RouterLink, EBidFolder, FormsModule, TimePickerAltComponent, PrimeCalendarCustomComponent,
    ReactiveFormsModule, MultiSelectModule, BidFolderSkeletonComponent, ToggleSwitchModule, PhonesPipe],
  providers: [NgForm],
  standalone: true,
  templateUrl: './bid-setup.component.html',
  styleUrl: './bid-setup.component.css'  
})
export class BidSetupComponent extends BaseEffectsService implements OnDestroy, OnInit {
  @HostListener('window:beforeunload', ['$event'])
  unloadEvent(event: any) {
    if (this.ebidFormGroup.touched && this.ebidFormGroup.dirty) {
      event.preventDefault();
    }
  }


  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
      event.preventDefault();
      this.undoTool();
    } else if (event.ctrlKey && event.key === 'y') {
      event.preventDefault();
      this.redoTool();
    } else if (event.ctrlKey && event.key === 's') {
      if (this.view() === BidViews.BID) {
        this.toastrService.error("Saving not supported for bid from this page");
      } else if (this.view() === BidViews.EDIT) {
        event.preventDefault();
        this.bidSetupService.saveBid()
      }
    } else if (event.ctrlKey && event.key.toLocaleLowerCase() === 'z' && event.shiftKey) {
      event.preventDefault();
      this.redoTool();
    }
  }

  bidsAdvService = inject(BidAdvService);
  toastrService = inject(ToastrService);
  eBidHistoryService = inject(EBidHistoryService);
  eBidProjectService = inject(EBidProjectService);
  eBidFolderService = inject(EBidFolderService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  bidSetupService = inject(BidSetupService);
  globalDataService = inject(GlobalDataService);  
  bidFormService = inject(BidFormService);    
  workingOnBidModal = viewChild<any>('workingOnBidModal');
  bidBeginDateTimePicker = viewChild<any>('bidBeginDateTimePicker');
  workingOnBidModalViewer: any;
  selectedSections = this.bidSetupService.selectedSections;
  undoHistoryCount = computed(() => this.eBidHistoryService.undoStack().length);
  redoHistoryCount = computed(() => this.eBidHistoryService.redoStack().length);
  eBid = this.bidSetupService.eBid;
  project = this.bidsAdvService.advertisement;
  view = this.bidSetupService.view;
  usersWorkingOnBid = this.bidSetupService.usersWorkingOnBid;

  ////////////////////Signals/////////////////////

  
  documents = this.bidSetupService.documents;
  isLoading = this.bidSetupService.isLoading;
  isSavingBid = this.bidSetupService.isSavingBid;
  isEBidLoading = this.bidSetupService.isEBidLoading;
  localBidDate = computed(() => this.bidsAdvService.getLocalBrowserBidDateInfo(this.project() as Advertisement));
  bidDateExpired = computed(() => this.bidsAdvService.isBidDateExpired(this.project() as Advertisement));
	ebidFormGroup: FormGroup = this.bidSetupService.ebidFormGroup;
  sections = computed(() => this.bidSetupService.sections());
  routerSubscription: Subscription | null = null;
  navigateSubscription: Subscription | null = null;
  timeZones = this.globalDataService.timeZones;
  eBidLoadingError = this.eBidProjectService.error;
  showBidFormImport = computed(() => ((this.usersWorkingOnBid().length <= 0 && this.view() === BidViews.EDIT)));

  public readonly BIDFORM_VIEWS: typeof BidViews = BidViews;
  public readonly BID_SECTION_TYPES: typeof EBidSectionTypes = EBidSectionTypes;
  public readonly BID_SECTION_NAMES: typeof EBidSectionNames = EBidSectionNames;  

  constructor() {
    super();

    this.bidSetupService.sections.set([
      {
        name: EBidSectionNames.ACKNOWLEDGE,
        type: EBidSectionTypes.ACKNOWLEDGE
      },
      {
        name: EBidSectionNames.BID_FORM,
        type: EBidSectionTypes.BID_FORM
      },
      {
        name: EBidSectionNames.COMPLETION_TIME,
        type: EBidSectionTypes.COMPLETION_TIME
      },
      {
        name: EBidSectionNames.REQUIRED_DOWNLOADS,
        type: EBidSectionTypes.REQUIRED_DOWNLOADS
      },
      {
        name: EBidSectionNames.REQUIRED_UPLOADS,
        type: EBidSectionTypes.REQUIRED_UPLOADS
      },
      {
        name: EBidSectionNames.WORK_ORDER,
        type: EBidSectionTypes.WORK_ORDER
      }
    ]);

    this.routerSubscription = this.aRoute.queryParamMap.subscribe({
      next: (params) => {
        this.bidFormService.showList.set(false);
        if (params.has('view')) {
          this.view.set(params.get('view') as BidViews);
        } else {
          this.view.set(BidViews.EDIT);
        }
      }
    });


    this.navigateSubscription = this.router.events.subscribe((event) => {
      const ev = event as RouterEvent;
      if (ev.url && ev.url.indexOf('e-bid/setup') === -1) {
        if (event instanceof NavigationStart) {

          if (this.ebidFormGroup.touched && this.ebidFormGroup.dirty) {
            if (!confirm('You have not saved your form. Do you want to leave?')) {

              this.router.navigateByUrl(this.router.url, { skipLocationChange: true });
            } else {
              this.ebidFormGroup.markAsPristine();
              this.ebidFormGroup.markAsUntouched();
            }
          }

        } else if (event instanceof NavigationEnd) {
          this.ebidFormGroup.markAsPristine();
          this.ebidFormGroup.markAsUntouched();
          this.router.navigateByUrl(this.router.url, { skipLocationChange: true });
        }
      }
    });
  }

  ngOnInit(): void {    
    this.bidSetupService.startEffects();
  }

  
  ngOnDestroy(): void {
    this.routerSubscription?.unsubscribe();
    this.navigateSubscription?.unsubscribe();        
    this.bidSetupService.stopEffects();
    
  }

  setIsEnabled(event: any) {
    	this.ebidFormGroup?.get('IsEnabled')?.setValue(!event.target.checked);
			this.ebidFormGroup?.get('IsEnabled')?.markAsTouched();
			this.ebidFormGroup?.get('IsEnabled')?.markAsDirty();
  }

  dateChanged(date: Date) {
    this.bidBeginDateTimePicker()?.setDisplayTime(date);
  }


  sectionsSelected(sectionsInfo: any) {
    this.bidSetupService.sectionsSelected(sectionsInfo);
  }

  getInvalidControls(formGroup: FormGroup = this.ebidFormGroup): any[] {
    const invalidControls = Array<any>([{ key: "TODO", value: "TO DO: Implement getInvalidControls" }]);
    // this.runInvalidControls(invalidControls, formGroup);
    return invalidControls;
  }

  undoTool() {
    this.eBidHistoryService.undo();
  }

  redoTool() {
    this.eBidHistoryService.redo();    
  }

  viewChange(view: BidViews) {
    this.router.navigate([], {
      queryParams: { view: view }, queryParamsHandling: 'merge', relativeTo: this.aRoute
    });
  }  

  saveBid() {
    this.bidSetupService.saveBid();
  }

  // saveBidFinal() {
  //   this.getSaveBidModal()?.hide();
  //   this.latestSubmissions.set([]);
  //   this.bidSetupService.finalSaveBid();
  // }

  // hideModal() {
  //   this.latestSubmissions.set([]);
  //   this.getSaveBidModal()?.hide();
  // }

  // showSaveBidModal() {
  //   this.getSaveBidModal()?.show();
  // }

  // getSaveBidModal() {
  //   if (this.saveModalViewer) {
  //     return this.saveModalViewer;
  //   }

  //   const modalElement = this.saveBidModal()?.nativeElement;
  //   this.saveModalViewer = new (window as any).bootstrap.Modal(modalElement, { backdrop: 'static', keyboard: false });
  //   return this.saveModalViewer;

  // }

  showWorkingOnBidModal() {
    this.getWorkingOnBidModal()?.show();
  }

  hideWorkingOnBidModal() {
    this.getWorkingOnBidModal()?.hide();
  }
  getWorkingOnBidModal() {
    if (this.workingOnBidModalViewer) {
      return this.workingOnBidModalViewer;
    }

    const modalElement = this.workingOnBidModal()?.nativeElement;
    this.workingOnBidModalViewer = new (window as any).bootstrap.Modal(modalElement, { backdrop: true, keyboard: true });
    return this.workingOnBidModalViewer;
  }
}



export class EBidSelectedData {
  name: string = '';
  type: string = '';
}