import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, computed, effect, inject, OnInit, signal, viewChild } from '@angular/core';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { Advertisement } from 'src/app/bid-adv/shared/interfaces/advertisement';
import { PaperBidComponent } from '../paper-bid/paper-bid.component';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { EBidFolder } from 'src/app/ebid/ui/bid-folder/bid-folder.component';
import { BidViews, DocumentInfo } from 'src/app/ebid/interfaces/ebid';
import { EBidProjectService } from 'src/app/bid-adv/shared/data-access/bids-ebid-project.service';
import { BidResultsService } from 'src/app/bid-adv/shared/data-access/bids-ebid-results.service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { PaperBidService, PaperBidSaveType } from 'src/app/bid-adv/shared/data-access/bids-ebid-paper-bid.service';
import { BidsAdvDocsService } from 'src/app/bid-adv/shared/data-access/bids-docs.service';
import { RequiredDownloadsService } from 'src/app/ebid/data-access/required-downloads.service';
import { AddendaAcknowledgeService } from 'src/app/ebid/data-access/addenda-acknowledge.service';
import { ProjectFile } from 'src/app/bid-adv/shared/interfaces/bids-docs';
import { EBidOpeningService } from 'src/app/bid-adv/shared/data-access/bids-ebid-opening.service';
import { CompanyResult } from 'src/app/bid-adv/shared/interfaces/bid-results';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { EBidFolderService } from 'src/app/ebid/data-access/bid-folder.service';
import { BidAdvService } from 'src/app/bid-adv/shared/data-access/bid.service';


@Component({
    selector: 'app-bid-results',
    imports: [CommonModule, PaperBidComponent, RouterLink, EBidFolder, ReactiveFormsModule],   
    standalone: true, 
    providers: [],
    templateUrl: './bid-results.component.html',
    styleUrl: './bid-results.component.css'
})
export class BidResultsComponent implements AfterViewInit { 
    bidResultsService = inject(BidResultsService);    
    advertisementService = inject(BidAdvService)
    eBidProjectService = inject(EBidProjectService);
    paperBidService = inject(PaperBidService);    
    confirmService = inject(ConfirmService);
    projectDocsService = inject(BidsAdvDocsService);
    requiredDownloadsService = inject(RequiredDownloadsService);
	addendaAckService = inject(AddendaAcknowledgeService);    
    openBidsService = inject(EBidOpeningService);    
    eBidFolderService = inject(EBidFolderService);
    documents = this.projectDocsService.currentProjectBidDocs;
    paperBidModal = viewChild<any>('paperBidModal');
    submissionView = viewChild<any>('submissionModal');
    project = this.advertisementService.advertisement;
    projectId = this.advertisementService.projectId;
    isLoading = computed(() => this.bidResultsService.bidResults.isLoading() || this.eBidProjectService.ebidResource.isLoading() || this.paperBidService.isPaperBidLoading());
    bidDateExpired = this.advertisementService.bidDateExpired;
    localBidDate = computed(() => this.advertisementService.getLocalBrowserBidDateInfo(this.project() as Advertisement));
    hasWorkOrder = computed(() => this.results() ? this.results()?.CompanyResults.filter(x => x.WorkOrderUserValue !== null).length : false);
    postedDocumentResults = computed(() => {
        if(this.results()?.EBid?.PostedDocuments){
            return {
                HasBidResults : this.results()?.EBid?.PostedDocuments?.BidResultsProjectFileId ? true : false,
                HasBidTabs: this.results()?.EBid?.PostedDocuments?.BidTabsFileId ? true : false
            }
        }else{
            return {
                HasBidResults : false,
                HasBidTabs: false
            }
        }
    });
    postedDocuments = computed(() => this.results()?.EBid?.PostedDocuments);
    results = this.bidResultsService.results;
    paperBidView = this.paperBidService.view;
    paperBidModalViewer: any;
    submissionModalViewer: any;
    saveType: PaperBidSaveType = PaperBidSaveType.None;
    router = inject(Router);
    activatedRoute = inject(ActivatedRoute);    
    currentBidderBidInfo = this.bidResultsService.currentBidderBidInfo;    
    ebidFormGroup = this.paperBidService.ebidFormGroup;
    bidderBidInfoId = this.bidResultsService.bidderBidInfoId;
    bidsOpening = this.openBidsService.bidsOpening; 
    bidsRejecting = this.bidResultsService.bidsRejecting;
    isDownloadingReport = this.bidResultsService.isDownloadingReport;
    isRebuildingReport = this.bidResultsService.isRebuildingReport;
    bidResultsReportInfo = this.bidResultsService.bidResultsReportInfo;
    isPostingDocuments = this.bidResultsService.isPostingDocuments;
    public readonly BIDFORM_VIEWS: typeof BidViews = BidViews;
    eBidPostedDocumentsFormGroup = new FormGroup({
        HasBidResults: new FormControl(false),
        HasBidTabs: new FormControl(false)
    });
    constructor() {
        
        effect(() => {
            if(this.results()){
                const hasBidResults = this.results()?.EBid?.PostedDocuments?.BidResultsProjectFileId ? true : false;
                const hasBidTabs = this.results()?.EBid?.PostedDocuments?.BidTabsFileId ? true : false;
    
                this.eBidPostedDocumentsFormGroup.get('HasBidResults')?.setValue(hasBidResults);
                this.eBidPostedDocumentsFormGroup.get('HasBidTabs')?.setValue(hasBidTabs);
                this.eBidPostedDocumentsFormGroup.get('HasBidResults')?.enable();
                this.eBidPostedDocumentsFormGroup.get('HasBidTabs')?.enable();
            }else{
                this.eBidPostedDocumentsFormGroup.get('HasBidResults')?.disable();
                this.eBidPostedDocumentsFormGroup.get('HasBidTabs')?.disable();
            }
        });
    
        effect(() => {
            if(this.bidsOpening() && this.results()){
                for(let compResult of this.results()?.CompanyResults as Array<CompanyResult>){
                   var hasOpen = this.bidsOpening().find(x => x === compResult.SubmissionId);
                   if(hasOpen){
                    compResult.IsOpening = true;
                   }else{
                    compResult.IsOpening = false;
                   }
    
                }
            }
        });
    
        effect(() => {
            if(this.bidsRejecting() && this.results()){
                for(let compResult of this.results()?.CompanyResults as Array<CompanyResult>){
                   var hasOpen = this.bidsRejecting().find(x => x === compResult.SubmissionId);
                   if(hasOpen){
                    compResult.IsRejecting = true;
                   }else{
                    compResult.IsRejecting = false;
                   }
    
                }
            }
        });
    
        effect(() => {
            if(this.paperBidService.saveResponse()){
                this.hideModal();
                this.bidResultsService.bidResults.reload();
            }
        });
    
        effect(() => {   
            if(this.paperBidService.deleteResponse()){            
                this.bidResultsService.bidResults.reload()
            }
        });
    
        effect(() => {
            if(this.openBidsService.openBidResponse() && this.openBidsService.bidsOpening().length <= 0){
                this.bidResultsService.bidResults.reload();
            }
        });
    
        effect(() => {
            if(this.bidResultsService.bidRejected() && this.bidResultsService.bidsRejecting().length <= 0){
                this.bidResultsService.bidResults.reload();
            }
        });    

        
        effect(() => {
		    if (this.documents()) {
			    this.requiredDownloadsService.projectInfoDocuments.set(this.getInfoDocs(this.documents() as Array<ProjectFile>));
			    this.addendaAckService.projectInfoDocuments.set(this.getInfoDocs(this.documents() as Array<ProjectFile>));
		    }
	    });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.activatedRoute.queryParams.subscribe(params => {

                if (params['show-paper-bid-modal']) {
                    const paperBidId = params['show-paper-bid-modal'];
                    this.editPaperBid(paperBidId);
                }else if(params['show-submission']){
                    const submissionid = params['show-submission'];
                    this.viewBidInfo(submissionid);
                }else if(params['show-paper-bid-view']){
                    const paperBidId = params['show-paper-bid-view'];
                    this.showPaperBid(paperBidId);
                }else{
                    this.hideModal();
                }
            });
        }, 1000);
    }


    saveBid() {
        this.paperBidService.save(this.saveType);
    }



    addPaperBid() {        
        this.paperBidService.paperBidId.set(null);
        this.paperBidService.clearForm();
        this.paperBidView.set(BidViews.BID);        
        this.saveType = PaperBidSaveType.Create;
        this.showPaperBidModal();
    }

    viewBidInfo(bidderBidInfoId:string){
        this.bidderBidInfoId.set(bidderBidInfoId);
        this.paperBidView.set(BidViews.VIEW);
        this.showSubmissionModal();
    }

    deletePaperBid(paperBidId: string) {
       this.confirmService.open('Delete Paper Bid', 'Are you sure you want to delete this paper bid?').result.then((result) => {
        if(result === 'yes'){
            this.paperBidService.deletePaperBid(paperBidId);
        }
        
       });           
        
    }


    getInfoDocs(docs: Array<ProjectFile>): Array<DocumentInfo> {
		var infoDocs = new Array<DocumentInfo>();
		for (let doc of docs as Array<ProjectFile>) {
			const docInfo = new DocumentInfo(doc.FileId as string, doc.Title as string, doc.Category?.Name as string, doc.Storage?.FileExtension as string);
			infoDocs.push(docInfo);
		}

		return infoDocs;
	}


    editPaperBid(paperBidId: string) {
        this.saveType = PaperBidSaveType.Save;
        this.paperBidService.projectId.set(this.projectId() as string);
        this.paperBidService.paperBidId.set(paperBidId);        
        this.paperBidService.view.set(BidViews.BID);
        this.showPaperBidModal();
    }
    
    showPaperBid(paperBidId: string) {
        this.saveType = PaperBidSaveType.None;
        this.paperBidService.projectId.set(this.projectId() as string);
        this.paperBidService.paperBidId.set(paperBidId);        
        this.paperBidService.view.set(BidViews.VIEW);
        this.showPaperBidModal();
    }

    showPaperBidModal() {
        this.getPaperBidModal()?.show();
    }

    hideModal() {
        this.router.navigate([], { queryParams: { "show-submission": null, 'show-paper-bid-view': null, 'show-paper-bid-modal': null }, queryParamsHandling: 'merge' });
        this.getPaperBidModal()?.hide();
        this.getSubmissionModal()?.hide();
        this.paperBidService.paperBidId.set(null);
        this.bidResultsService.bidderBidInfoId.set(null);
        // this.paperBidService.destroy();
    }

    getPaperBidModal() {
        if (this.paperBidModalViewer) {
            return this.paperBidModalViewer;
        }

        const modalElement = this.paperBidModal()?.nativeElement;
        this.paperBidModalViewer = new (window as any).bootstrap.Modal(modalElement, { backdrop: 'static', keyboard: false });
        return this.paperBidModalViewer;

    }

    showSubmissionModal(){
        this.getSubmissionModal()?.show();
    }

    getSubmissionModal(){
        if(this.submissionModalViewer){
            return this.submissionModalViewer;
        }

        const modalElement = this.submissionView()?.nativeElement;
        this.submissionModalViewer = new (window as any).bootstrap.Modal(modalElement, { backdrop: 'static', keyboard: false });
        return this.submissionModalViewer;
    }

    openBid(submissionId: string){
        this.openBidsService.openBid(submissionId, this.projectId() as string);
    }
    rejectBid(submissionId: string){
        this.confirmService.open('Reject Bid', 'Are you sure you want to reject this bid?').result.then((result) => {
            if(result === "yes"){
                this.bidResultsService.rejectBid(submissionId, this.projectId() as string);
            }
            
        });        
    }

    unRejectBid(submissionId: string){
        this.confirmService.open('Remove Rejection', 'Are you sure you want to remove the rejection of this bid?').result.then((result) => {
            if(result === "yes"){
                this.bidResultsService.unRejectBid(submissionId, this.projectId() as string);
            }            
        });
    }

    buildReport(){    
        this.bidResultsService.buildBidReport();
    }

    downloadReport(){
        if(this.bidResultsReportInfo()){
            this.bidResultsService.downloadReportFile();
        }else{
            this.bidResultsService.buildBidReport();
        }
    }

    postBidDocuments(){
        this.bidResultsService.postBidDocuments(this.eBidPostedDocumentsFormGroup.value);
    }
}