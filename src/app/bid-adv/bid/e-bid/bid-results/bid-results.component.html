
	@if(!bidDateExpired() && results()?.EBid?.OpenedAt == null && !isLoading()) {
		<div class="alert alert-info">You cannot see results because the bid is not expired. ({{localBidDate() | date: 'M/dd/yyyy h:mm a'}})</div>
	}@else {
		@if(!bidDateExpired() && !isLoading()){
			<div class="alert alert-danger">The Bid Date is in the future ({{localBidDate() | date: 'M/dd/yyyy h:mm a'}}). </div>
	
			@if(results()?.EBid?.OpenedAt){
				<div class="alert alert-info">
					The bids where opened at ({{results()?.EBid?.OpenedAt | date: 'M/dd/yyyy h:mm a'}})		
				</div>
			}
			
		}
		
			<!-- post bid results -->
			<section class="mb-3">

				<h1 class="page-title fs-5 mb-3">Post Bid Results</h1>
				<form [formGroup]="eBidPostedDocumentsFormGroup" (submit)="postBidDocuments()">
					<div class="border p-3 d-flex align-items-center">
						<div class="me-4">
							@if(isLoading()){
								<div class="placeholder-glow row mx-2" style="width:300px;">
									<div class="placeholder col-12 my-2">
										
									</div>
									<div class="placeholder col-12">
								
									</div>
								</div>
								
							}@else{									
							
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="postBidResults" name="postBidResults" formControlName="HasBidResults">
									<label class="form-check-label" for="postBidResults">
										Post Bid Results
									</label>
								</div>
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="postBidTabs" name="postBidTabs" formControlName="HasBidTabs">
									<label class="form-check-label" for="postBidTabs">
										Post Bid Tabluations
									</label>
								</div>
							}
							
							
						</div>
						<div>
							<button type="submit" class="btn btn-outline-dark"  [disabled]="isLoading() || isPostingDocuments() || results()?.CompanyResults?.length <= 0">
								@if(isPostingDocuments()){
									<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}
								Save
							</button>
						</div>
					</div>
				</form>
			</section>
		
	
		<!-- title & description -->
		<section class="d-flex justify-content-between align-items-end mb-3">
			<h1 class="page-title fs-5 m-0">Bid Results</h1>
			<div class="d-flex justify-content-end">		
				<button type="button" class="btn btn-outline-dark me-2" (click)="addPaperBid()" [disabled]="isLoading()">Add Paper Bid</button>

				@if(results()?.CompanyResults?.length > 0){
					<div class="btn-group">
						<button type="button" class="btn btn-outline-dark" [disabled]="isLoading() || isDownloadingReport() || isRebuildingReport()" (click)="downloadReport()">
							@if(isDownloadingReport() || isRebuildingReport()){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
							
							Download Bids
						</button>
						@if(bidResultsReportInfo()){
							<button type="button" class="btn btn-outline-dark dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
								<span class="visually-hidden">Toggle Dropdown</span>
							  </button>
							  <ul class="dropdown-menu">
								<li><a class="dropdown-item" href=" javascript:void(0)" (click)="buildReport()">Rebuild Report</a></li>
							  </ul>
						}
				
					  </div>
				}				
			</div>
		</section>
		<!-- bid results -->
		<section class="table-responsive mb-3">
			<table class="table table-bordered">
				<!-- col group for column styles -->
				<colgroup>
					<col />
					<col />
					<col />
					<col />
					<col />
					<col />
					<col />
					<col />
				</colgroup>
				<!-- col headers -->
				<thead>
					<!-- <tr>
						<td colspan="8" class="bg-light fw-bold">Apparent Low Bidders</td>
					</tr> -->
					<tr class="placeholder-glow">
						<th scope="col">#</th>
						<th scope="col">Company</th>
						@if(isLoading()){
							@for (info of [1,2,3]; track $index) {
								<th>									
									<div class="placeholder col-12" style="height: 30px; width: 200px"></div>
								</th>
							}
						}@else {
							

							@if(results()?.CompanyResults?.length > 0){
								@for (info of results()?.CompanyResults[0].BidInfos; track $index) {
									<th scope="col" class="text-end">{{info.FormName}}</th>
								}
			
								@for (info of results()?.CompanyResults[0].CompletionInfos; track $index) {
									<th scope="col" class="text-end">{{info.CompletionName}}</th>
								}
							}					

							@if(hasWorkOrder()){
								<th scope="col" class="text-end">Work Order</th>
							}
						}
					
						<th scope="col"></th>
					</tr>
				</thead>
				@if(isLoading()){
					<tbody class="placeholder-glow">
						@for(item of [1,2,3,4]; track $index){
							<tr>
								<th scope="row">
									<div class="placeholder col-12" style="height: 30px;"></div>
								</th>
								<td>
									<div class="placeholder col-12" style="height: 30px;"></div>
								</td>
								@for (info of [1,2,3]; track $index) {
									<td>
										<div class="placeholder col-12" style="height: 30px;"></div>
									</td>
								}
		
							
								<td>								
							
								</td>
							</tr>
						}
					
					</tbody>
				}@else {					
					<!-- bidders -->
					@for (result of results()?.CompanyResults; track $index) {
						<tbody>
							<tr>						
								<td scope="row">{{$index + 1}}</td>
								<td>
									<div>{{result.CompanyName }}</div>
									<div class="text-secondary small">{{ result.SubmittedAt | date: 'M/dd/yyyy h:mm a' }}</div>
								</td>
								@for (info of result.BidInfos; track $index) {
									<td class="text-end">{{ info.Total | currency }}</td>
								}
		
								@for (completionInfo of result.CompletionInfos; track $index) {
									<td class="text-end">{{ completionInfo.UserValue }} {{ completionInfo.CompletionTimeOption }}</td>
								}

								@if(hasWorkOrder()){
									<td class="text-end">
										@if(result.WorkOrderUserValue){
											<div>
												{{ result.WorkOrderUserValue | number}}
											</div>
											<div>
												{{ result.WorkOrderTotal | currency }}
											</div>										
										}
									
										
									</td>
								}
							
								<td>								
								
									@if(result.PaperBidId){
										<a class="btn btn-light btn-sm" role="button" aria-disabled="true" routerLink="./" [queryParams]="{ 'show-paper-bid-view': result.PaperBidId }" [skipLocationChange]="true">View</a>
										<a class="btn btn-light btn-sm" role="button" aria-disabled="true" routerLink="./" [queryParams]="{ 'show-paper-bid-modal': result.PaperBidId }" [skipLocationChange]="true">Edit Paper Bid</a>
										<button class="btn btn-outline-danger btn-sm mx-2" role="button" (click)="deletePaperBid(result.PaperBidId)">
											<i class="fas fa-trash"></i>
										</button>
									}@else{
										@if(result.RejectedAt){											
											<button class="btn btn-light btn-sm" (click)="unRejectBid(result.SubmissionId)" [disabled]="result.IsRejecting">
												@if(result.IsRejecting){
													<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
												}

												Remove Rejection
											</button>		
											<span class="text-danger small">Rejected on {{ result.RejectedAt | date:'M/dd/yyyy h:mm:ss a' }}</span>							
										}@else {
											@if(result.OpenedAt){
												<a class="btn btn-light btn-sm" role="button" aria-disabled="true" routerLink="./" [queryParams]="{ 'show-submission': result.SubmissionId }" [skipLocationChange]="true">View</a>
											}@else {											
												<button class="btn btn-light btn-sm" (click)="openBid(result.SubmissionId)" [disabled]="result.IsOpening">												
													@if(result.IsOpening){
														<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
													}
													
													Open
												</button>
											}
											<button class="btn btn-light btn-sm" (click)="rejectBid(result.SubmissionId)" [disabled]="result.IsRejecting">
												@if(result.IsRejecting){
													<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
												}
												Reject Bid
											</button>
										}
								
									}
								</td>
							</tr>
						</tbody>
					}@empty {			
						<tbody style="list-style-type: none;">
							<tr class="table-info">					
								<td colspan="6">
									No Bids Received - <span style="font-style: italic;" class="small">You can add paper bids with the button above.</span>
								</td>							
							</tr>
						</tbody>
					}
			}
	
			</table>
		</section>
		@if(!isLoading()){
			<!-- bid anaylsis -->
			<section class="mb-3">
				<h1 class="page-title fs-5 mb-3">Bid Analysis</h1>
				<div class="border rounded p-3">
					<div class="mb-1">
						<span class="fw-bold me-1">Estimate:</span>
						<span>{{results()?.Analysis?.Estimate }}</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold me-1">Bidders:</span>
						<span>{{ results()?.Analysis?.TotalBidders }}</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold me-1">AMLT ($):</span>
						<span>{{ results()?.Analysis?.AMLT | currency }}</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold me-1">AMLT (%):</span>
						<span>{{ results()?.Analysis?.AMLTPercentage }}%</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold me-1">Avg. Bid:</span>
						<span>{{ results()?.Analysis?.AverageBid  | currency }}</span>
					</div>
				</div>
			</section>
		}

	}
	
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" #paperBidModal>
		<div class="modal-dialog  modal-fullscreen" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">
						@if(paperBidView() === BIDFORM_VIEWS.BID){
							Add Paper Bid
						}@else {
							View Paper Bid
						}
						
					</h5>
					<!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button> -->
				</div>
				<div class="modal-body">
					<app-paper-bid [projectId]="projectId()"></app-paper-bid>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" (click)="hideModal()">Cancel</button>
					@if(paperBidView() === BIDFORM_VIEWS.BID){
						<button type="button" class="btn btn-primary" (click)="saveBid()">Save</button>
					}
				</div>
			</div>
		</div>
	</div>


	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" #submissionModal>
		<div class="modal-dialog  modal-fullscreen" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="exampleModalLabel">Submission</h5>
					<!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button> -->
				</div>
				<div class="modal-body">						
					<app-ebid-folder></app-ebid-folder>	
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" (click)="hideModal()">Close</button>									
				</div>
			</div>
		</div>
	</div>

