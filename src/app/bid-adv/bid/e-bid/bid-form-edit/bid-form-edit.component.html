<!-- title & description -->
<section class="d-flex justify-content-between align-items-end mb-3">
	<h1 class="page-title fs-5 m-0">Edit Bid Form</h1>
	<div class="d-flex justify-content-end">
		<button type="button" class="btn btn-outline-danger me-2">Delete</button>
		<button type="button" class="btn btn-outline-dark me-2">+ Add Bid Form</button>
		<button type="button" class="btn btn-outline-dark">Save</button>
		
	</div>
</section>
<!-- bid forms (hide/show) -->
<section class="mb-4">
	<div class="">
		<table class="table table-bordered">
			<!-- col group for column styles -->
			<colgroup>
				<col style="width: 50px;" />
				<col style="width: 75px;" />
				<col style="width: 75px;" />
				<col />
				<col style="width: 60px;" />
				<col style="width: 150px;" />
				<col style="width: 150px;" />
				<col style="width: 80px;" />
			</colgroup>
			<!-- base bid -->
			<thead>
				<tr>
					<th colspan="8" class="bg-dark">
						<div class="row">
							<div class="col-10 col-md-6 col-lg-4">
								<input type="email" class="form-control" id="exampleInputEmail1"
									aria-describedby="emailHelp" placeholder="Bid Form Name (Base Bid, Alt 1, etc.)">
							</div>
							<div class="col-2 col-md-6 col-lg-8 d-flex justify-content-end">
								<button type="button" class="btn btn-outline-light btn-sm me-1">+ Add Section</button>
								<button type="button" class="btn btn-outline-light btn-sm">Delete Section</button>
							</div>
						</div>
					</th>
				</tr>
			</thead>
			<thead>
				<tr>
					<th scope="col"></th>
					<th scope="col">ID 1</th>
					<th scope="col">ID 2</th>
					<th scope="col">Desc.</th>
					<th scope="col">Unit</th>
					<th scope="col">Qty</th>
					<th scope="col">Unit Price</th>
					<th scope="col"></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td colspan="8" class="bg-light fw-bold">
						<div class="col-12 col-md-6 col-lg-4">
							<input type="email" class="form-control" id="exampleInputEmail1"
								aria-describedby="emailHelp" placeholder="Section Name">
						</div>
					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">
										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">More</h5>
								</li>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td colspan="8" class="text-end">Subtotal: $5,000.00</td>
				</tr>
				<tr>
					<td colspan="8" class="bg-light fw-bold">
						<div class="col-12 col-md-6 col-lg-4">
							<input type="email" class="form-control" id="exampleInputEmail1"
								aria-describedby="emailHelp" placeholder="Section Name">
						</div>
					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td colspan="8" class="text-end">Subtotal: $5,000.00</td>
				</tr>
			</tbody>
			<!-- alt 1 -->
			<thead>
				<tr>
					<th colspan="8" class="bg-dark">
						<div class="row">
							<div class="col-10 col-md-6 col-lg-4">
								<input type="email" class="form-control" id="exampleInputEmail1"
									aria-describedby="emailHelp" placeholder="Bid Form Name (Base Bid, Alt 1, etc.)">
							</div>
							<div class="col-2 col-md-6 col-lg-8 d-flex justify-content-end">
								<button type="button" class="btn btn-outline-light btn-sm me-1">+ Add Section</button>
								<button type="button" class="btn btn-outline-light btn-sm">Delete Section</button>
							</div>
						</div>
					</th>
				</tr>
			</thead>
			<thead>
				<tr>
					<th scope="col"></th>
					<th scope="col">ID 1</th>
					<th scope="col">ID 2</th>
					<th scope="col">Desc.</th>
					<th scope="col">Unit</th>
					<th scope="col">Qty</th>
					<th scope="col">Unit Price</th>
					<th scope="col"></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td colspan="8" class="bg-light fw-bold">
						<div class="col-12 col-md-6 col-lg-4">
							<input type="email" class="form-control" id="exampleInputEmail1"
								aria-describedby="emailHelp" placeholder="Section Name">
						</div>
					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td colspan="8" class="text-end">Subtotal: $5,000.00</td>
				</tr>
				<tr>
					<td colspan="8" class="bg-light fw-bold">
						<div class="col-12 col-md-6 col-lg-4">
							<input type="email" class="form-control" id="exampleInputEmail1"
								aria-describedby="emailHelp" placeholder="Section Name">
						</div>
					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td class="grip">
						<i class="fas fa-grip-vertical fa-lg text-secondary"></i>
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="text" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<input type="number" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
					</td>
					<td>
						<div class="input-group">
							<span class="input-group-text" id="basic-addon1">$</span>
							<input type="text" class="form-control bg-warning-subtle" placeholder="" aria-label=""
								aria-describedby="basic-addon1">
						</div>
					</td>
					<td class="grip">
						<div class="dropdown">
							<button class="btn btn-outline-secondary dropdown-toggle" type="button"
								data-bs-toggle="dropdown" aria-expanded="false">
								<i class="fas fa-cog"></i>
							</button>
							<ul class="dropdown-menu dropdown-menu-end">
								<li>
									<h5 class="dropdown-header">Unit Price Settings</h5>
								</li>
								<form class="px-3">
									<div class="mb-3">

										<select class="form-select mb-1" aria-label="Default select example">
											<option value="1">Normal</option>
											<option value="1">Fixed</option>
											<option value="1">Max</option>
											<option value="1">Min</option>
											<option value="1">% of Total Bid</option>
										</select>
									</div>
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											Not Required
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<li>
									<h5 class="dropdown-header">Quantity Settings</h5>
								</li>
								<form class="px-3">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="dropdownCheck">
										<label class="form-check-label" for="dropdownCheck">
											 Allow Bidder to Enter Quantity
										</label>
									</div>
								</form>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item" href="#">Add Bid Item Above</a>
								<a class="dropdown-item" href="#">Add Bid Item Below</a>
								<a class="dropdown-item" href="#">Add Replacement Alternate</a>
								<a class="dropdown-item text-danger" href="#">Delete Bid Item</a>
							</ul>
						</div>

					</td>
				</tr>
				<tr>
					<td colspan="8" class="text-end">Subtotal: $5,000.00</td>
				</tr>
			</tbody>
		</table>
	</div>
</section>