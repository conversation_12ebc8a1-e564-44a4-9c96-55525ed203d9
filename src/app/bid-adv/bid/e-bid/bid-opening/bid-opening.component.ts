import { CommonModule } from '@angular/common';
import { Component, effect, inject, OnDestroy } from '@angular/core';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { BidAdvService } from 'src/app/bid-adv/shared/data-access/bid.service';
import { EBidOpeningService } from 'src/app/bid-adv/shared/data-access/bids-ebid-opening.service';
import { EBidProjectService } from 'src/app/bid-adv/shared/data-access/bids-ebid-project.service';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { TimeInfo } from 'src/app/bid-adv/shared/interfaces/advertisement';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';



@Component({
    selector: 'app-bid-opening',
    imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterLink],    
    templateUrl: './bid-opening.component.html',
    standalone: true,
    styleUrl: './bid-opening.component.css'
})
export class BidOpeningComponent implements OnDestroy {
    bidOpeningService = inject(EBidOpeningService);    
    advertisementService = inject(BidAdvService);
    eBidProjectService = inject(EBidProjectService);
    confirmService = inject(ConfirmService);
    aRoute = inject(ActivatedRoute);
    router = inject(Router);
    project = this.bidOpeningService.project;
    submissions = this.bidOpeningService.submissions;
    isLoading = this.bidOpeningService.isLoading;
    ebid = this.bidOpeningService.ebid;
    localBidDate = this.bidOpeningService.localBidDate;
    bidDateExpired = this.bidOpeningService.bidDateExpired;    
    openBidsForm = this.bidOpeningService.openBidsForm; 


    ebidEffect = effect(() => {
        if(this.ebid() && this.project()){
            if(this.advertisementService.isDateExpired(this.project()?.BidDetails.BidDateTimeInfo as TimeInfo) && this.ebid()?.OpenedAt){
                this.goToResults();
            }
        }
    });


    openBidsResponseEffect = effect(() => {
        if(this.bidOpeningService.openBidsResponse()){
            this.goToResults();
        }
    });

	goToResults() {
		this.router.navigate(['../results'], {relativeTo: this.aRoute, replaceUrl: true});
	}

    constructor() {

    }

    ngOnDestroy(): void {
  
    }

    openBids(){

        this.confirmService.open('Are you sure you want to open the selected bids? (Once opened, this bid cannot be resealed)').result.then((result: string) => {
            if(result.toLocaleLowerCase() === "yes"){
                let bidderInfoIds: Array<string> = [];
                this.openBidsForm.get('submissions')?.value.forEach((submission: any) => {
                    if(submission.open){
                        bidderInfoIds.push(submission.id);
                    }
                });
                
                this.bidOpeningService.openBids(bidderInfoIds);
            }
        });
    }    
}