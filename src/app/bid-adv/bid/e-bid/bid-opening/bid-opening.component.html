<!-- title & description -->
<h2 class="page-title fs-6 mb-3">Bid Opening</h2>
@if(isLoading()){
	<!-- received bids -->
	<ul class="list-group mb-3">
		@for (item of [0,1,2,3]; track $index) {
			<li class="list-group-item">
				<div class="row d-flex align-items-center">
					<div class="col-12 mb-1 mb-md-0 d-flex align-items-center">
						<i class="fas fa-folder fa-lg me-2" style="color: #F4DBA3"></i>
						<span class="placeholder col-11"></span>
					</div>
				</div>
			</li>
		}
	</ul>
}@else {
	@if(!bidDateExpired()){	

		@if(ebid()?.OpenedAt){
			<div class="alert alert-danger">
				Your Bid is in the future ({{localBidDate() | date: 'M/dd/yyyy h:mm a'}}), but the bids are open. 
				If you are looking for results click <a routerLink="../results">here</a>.		
			</div>		
		}@else {
			<div class="alert alert-info">
				You can't open bids until the bid date has passed. ({{ localBidDate() | date: 'M/dd/yyyy h:mm a'}})
				If you want to go to bid setup click <a routerLink="../setup">here</a>.
			</div>
		}
	}@else {
		<form [formGroup]="openBidsForm" (submit)="openBids()">
			<ul class="list-group mb-3">
				@for(submission of openBidsForm.get('submissions')?.controls; track $index){
					<form [formGroup]="submission">
						<li class="list-group-item">
							<div class="row d-flex align-items-center">
								<div class="col-12 mb-1 mb-md-0 col-md-6 d-flex align-items-center">
									<i class="fas fa-folder fa-lg me-2" style="color: #F4DBA3"></i>
									{{ submission.value.companyName}}
								</div>
								<div class="col-12 col-md-6 d-flex justify-content-md-end">
									<div class="form-check">
										<input type="checkbox" class="form-check-input" id="exampleCheck1" formControlName="open">
										<label class="form-check-label" for="exampleCheck1">Open</label>
									</div>
								</div>
							</div>
						</li>
					</form>				
				}@empty {
					<li style="list-style-type: none;">
						<div class="col-12 alert alert-info">					
							No bids received. You may open the bid and add any paper bids.
						</div>
					</li>
				}
			</ul>
					
			<!-- I think we need to let them open bids in case they want to add manual bids even if they don't receive a digital bid-->
			<div class="d-flex justify-content-end">
				<button type="submit" class="btn btn-outline-dark">Open Bids</button>
			</div>
		</form>
	}

}