import { Component, effect, inject, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { EBidActions, EBidSocketService } from 'src/app/ebid/data-access/ebid-socket.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { BidsAdvService } from '../../shared/data-access/bids.service';
import { ToastrService } from 'ngx-toastr';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BidsAdvDocsService } from '../../shared/data-access/bids-docs.service';
import { BidAdvService } from '../../shared/data-access/bid.service';


@Component({
  selector: 'app-e-bid',
  imports: [RouterOutlet, FormsModule],
  standalone: true,
  templateUrl: './e-bid.component.html',
  styleUrl: './e-bid.component.css',
  providers: []
})
export class EBidComponent implements OnInit, OnDestroy {

  bidAdvService = inject(BidAdvService);
  bidsAdvDocsService = inject(BidsAdvDocsService);
  // ebSocketService = inject(EBidSocketService);
  authService = inject(AuthService);
  toastrService = inject(ToastrService);
  router = inject(Router);
  routerSubscription: Subscription | null = null;
  testSendMessage: string = '';

  constructor() {
    effect(() => {
      this.bidsAdvDocsService.projectId.set(this.bidAdvService.projectId());
    });

  }

  // messageEffect = effect(() => {
  //   if (this.ebSocketService.eBidIncomingMessage()) {
  //     this.toastrService.success('New message received');
  //   }
  // });

  // projectEffect = effect(() => {
  //   if (this.bidsAdvService.advertisement()) {
  //     this.ebSocketService.connectToWebSocket(this.bidsAdvService.advertisement()?.Id as string,"engineer");
  //   }
  // });

  ngOnInit(): void {
    // this.routerSubscription = this.router.events.subscribe(event => {
    //   if (event instanceof NavigationEnd) {
    //     // console.log('Location changed to:', event.urlAfterRedirects);
    //     // Add your logic here
    //     //this.ebSocketService.sendDataMessage(EBidActions.CHANGE_PAGE, {url: location.href});
    //   }
    // });
  }
  ngOnDestroy(): void {
    this.routerSubscription?.unsubscribe();
    // this.ebSocketService.destroy();
  }

  // sendButtonMessage(message:string): void {
  //   this.ebSocketService.sendDataMessage(EBidActions.BUTTON_CLICK, {message: message});
  // }
}