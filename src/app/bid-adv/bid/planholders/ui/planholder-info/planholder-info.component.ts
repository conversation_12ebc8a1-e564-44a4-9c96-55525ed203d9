import { CommonModule } from '@angular/common';
import { Component, OnDestroy, inject, input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
import { BidAdvertsPlanholdersService } from 'src/app/bid-adv/shared/data-access/bids-planholders-service';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { Planholder } from 'src/app/bid-adv/shared/interfaces/bids-planholder';
import { PhonesPipe } from 'src/app/shared/utils/pipes/phone.pipe';

@Component({
    selector: 'app-planholder-info',
    imports: [CommonModule, FormsModule, NgbPaginationModule, PhonesPipe],
    templateUrl: './planholder-info.component.html',
    styleUrl: './planholder-info.component.css'
})
export class PlanholderInfoComponent implements OnDestroy {
  advertisementService = inject(BidsAdvService);
  planholdersService = inject(BidAdvertsPlanholdersService);
  aRoute = inject(ActivatedRoute);  
  planholdersGrid = this.planholdersService.planholdersGrid;
  isLoading =  this.planholdersService.isLoading;
  currentPage = this.planholdersService.page;
  limit = this.planholdersService.limit;
  search = this.planholdersService.search;
  orderBy = this.planholdersService.sortBy;
  isReversed = this.planholdersService.isReversed;
  searchInputChange = new Subject<string>();    
  
  constructor( ) {                

      this.searchInputChange
      .pipe(debounceTime(400), distinctUntilChanged())
      .subscribe(value => {
        if(value === ''){
          this.planholdersService.search.set(null);          
        }else{
          this.planholdersService.search.set(value);
        }
        
      });

  }
  ngOnDestroy(): void {
    
  }
  
  trackByMethod(index:number, ph:Planholder): string {
    return ph.UserId;
  }

  changePage($event: any){    
    this.planholdersService.page.set($event);    
  }

  setOrder(orderBy:string)  {    
    const isReversed = !this.isReversed();        
    this.planholdersService.sortBy.set(orderBy);
    this.planholdersService.isReversed.set(isReversed);
  }
}
