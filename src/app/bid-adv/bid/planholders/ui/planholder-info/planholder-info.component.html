<!-- header -->
<header class="mb-3">
	<h2 class="page-title fs-6 mb-3">Plan Holders</h2>
</header>
<!-- search -->
<section class="col-12 col-md-6 col-lg-5 col-xl-4 mb-3">
	<div class="input-group">
		<span class="input-group-text" id="basic-addon1">
			<i class="fas fa-search" aria-hidden="true"></i>
		</span>
		<input type="text" class="form-control" placeholder="Search" [ngModel]="search()"
			(ngModelChange)="this.searchInputChange.next($event)" />
	</div>
</section>
<!-- plan holders -->
<section>
	<table class="table align-middle">
		<thead>
			<tr class="d-none d-lg-table-row">
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('CompanyName')">Company</a>
					@if(orderBy() === 'CompanyName'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }">
					</i>
					}
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('LastName')">Name</a>
					@if(orderBy() === 'LastName'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }">
					</i>
					}
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('Email')">Contact
						Info</a>
					@if(orderBy() === 'Email'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }">
					</i>
					}
				</th>
				<th scope="col"></th>
			</tr>
		</thead>
		<tbody>
			@if(isLoading()){
			@for (item of [0,1,2,3,4,5,6,7,8]; track $index) {
			<tr class="placeholder-glow">
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
			</tr>
			}
			<!-- <app-planholders-skeleton></app-planholders-skeleton> -->
			}@else {
			@for (ph of planholdersGrid()?.Planholders; track $index) {
			<tr>
				<td class="d-none d-lg-table-cell">
					<div>{{ ph.CompanyName }}</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<div class="text-nowrap">{{ ph.FirstName }} {{ ph.LastName}}</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<div>{{ ph.Phone | phoneFormatter }}</div>
					<div>
						<a class="text-decoration-none text-primary text-opacity-75" href='mailto:{{ ph.Email }}'>{{
							ph.Email }}</a>
					</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<span class="badge bg-primary bg-opacity-10 text-primary fw-normal">{{ ph.CompanyType?.Name
						}}</span>
				</td>
				<td class="d-lg-none">
					<div class="fw-bold">{{ ph.CompanyName }}</div>
					<div>{{ ph.FirstName }} {{ ph.LastName}}</div>
					<div>{{ ph.Phone }}</div>
					<div>
						<a class="text-decoration-none text-primary text-opacity-75"
							href='mailto:{{ ph.Email }}'>{{ph.Email }}</a>
					</div>
					<div>
						<span class="badge bg-primary bg-opacity-10 text-primary fw-normal">{{ ph.CompanyType?.Name
							}}</span>
					</div>
				</td>
			</tr>
			}@empty {
			<tr>
				<td colspan="4">
					<div class="alert alert-info mb-0" role="alert">
						No plan holders yet.
					</div>
				</td>
			</tr>
			}
			}
		</tbody>
	</table>
</section>
<!-- footer -->
<footer class="col-12 d-flex justify-content-end mb-3">
	<ngb-pagination [collectionSize]="planholdersGrid()?.Total" [pageSize]="limit()" [page]="currentPage()"
		[rotate]="true" [maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)">
		<ng-template ngbPaginationFirst>First</ng-template>
		<ng-template ngbPaginationPrevious>Previous</ng-template>
		<ng-template ngbPaginationNext>Next</ng-template>
		<ng-template ngbPaginationLast>Last</ng-template>
	</ngb-pagination>
</footer>