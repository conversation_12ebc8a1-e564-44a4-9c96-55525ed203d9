import { Component, Input, OnDestroy, OnInit, effect, inject } from '@angular/core';

import { toSignal } from '@angular/core/rxjs-interop';
import { BidsAdvService } from '../../shared/data-access/bids.service';
import { PlanholderInfoComponent } from './ui/planholder-info/planholder-info.component';
import { BidAdvertsPlanholdersService } from '../../shared/data-access/bids-planholders-service';
import { BidAdvService } from '../../shared/data-access/bid.service';



@Component({
    selector: 'app-adv-planholders',
    templateUrl: './adv-planholders.component.html',
    styleUrls: ['./adv-planholders.component.css'],
    standalone: true,
    imports: [PlanholderInfoComponent]
})
export class AdvPlanholdersComponent {
  bidsAdvPlanholdersService = inject(BidAdvertsPlanholdersService);
  bidsAdvService = inject(BidAdvService);

  projectIdEffect = effect(() => {
    if (this.bidsAdvService.projectId()) {
      this.bidsAdvPlanholdersService.projectId.set(this.bidsAdvService.projectId() as string);
    }
  });
}