import { Component, inject, input, signal } from '@angular/core';
import { ProjectManagerComponent } from '../ui/manager/project-manager.component';
import { BidButtonsComponent } from '../ui/bid-buttons/bid-buttons.component';
import { BaseAdvComponent } from '../../shared/interfaces/base-adv';
import { AddOnlineBidComponent } from '../ui/add-online-bid/add-online-bid.component';
import { BidAdvService } from '../../shared/data-access/bid.service';

@Component({
    selector: 'app-edit-bid',
    imports: [ProjectManagerComponent, BidButtonsComponent, AddOnlineBidComponent],
    standalone: true,
    templateUrl: './edit-bid.component.html',
    styleUrl: './edit-bid.component.css'
})
export class EditBidComponent extends BaseAdvComponent {
	advertisementService = inject(BidAdvService);
  project = this.advertisementService.advertisement;


  cancelProject() {

  }
}
