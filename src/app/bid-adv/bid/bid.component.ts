import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, OnDestroy, OnInit, inject } from '@angular/core';
import { ActivatedRoute, RouterLink, RouterOutlet } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { EBidProjectService } from '../shared/data-access/bids-ebid-project.service';
import { BidAdvService } from '../shared/data-access/bid.service';
import { BidBidderFolderService } from 'src/app/bid-opportunities/shared/data-access/bid-bidderfolder.service';
import { BidOpsDocumentService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-document.service';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { AcknowledgeService } from 'src/app/ebid/data-access/acknowledge.service';
import { AddendaAcknowledgeService } from 'src/app/ebid/data-access/addenda-acknowledge.service';
import { EBidFolderService } from 'src/app/ebid/data-access/bid-folder.service';
import { BidFormService } from 'src/app/ebid/data-access/bidform-service';
import { CompletionTimeService } from 'src/app/ebid/data-access/completion-time.service';
import { EBidHistoryService } from 'src/app/ebid/data-access/ebid-history.service';
import { EBidService } from 'src/app/ebid/data-access/ebid.service';
import { RequiredDownloadsService } from 'src/app/ebid/data-access/required-downloads.service';
import { RequiredUploadService } from 'src/app/ebid/data-access/required-uploads.service';
import { WorkOrderService } from 'src/app/ebid/data-access/work-order-service';
import { BidsAdvDocsService } from '../shared/data-access/bids-docs.service';
import { BidDocumentsHistoryService } from '../shared/data-access/bids-document-history.service';
import { EBidOpeningService } from '../shared/data-access/bids-ebid-opening.service';
import { PaperBidService } from '../shared/data-access/bids-ebid-paper-bid.service';
import { RestrictedBiddersService } from '../shared/data-access/bids-ebid-restricted-bidder.service';
import { BidResultsService } from '../shared/data-access/bids-ebid-results.service';
import { BidSetupService } from '../shared/data-access/bids-ebid-setup.service';
import { BidsAdvEmailLogsService } from '../shared/data-access/bids-email-logs-service';
import { BidsAdvEmailPlanholdersService } from '../shared/data-access/bids-email-planholders.service';
import { BidsHistoryService } from '../shared/data-access/bids-history-service';
import { ProjectManagerService } from '../shared/data-access/bids-manager-service';
import { BidAdvertsPlanholdersService } from '../shared/data-access/bids-planholders-service';
import { BidsAdvQAService } from '../shared/data-access/bids-qa.service';
import { AdvReportsService } from '../shared/data-access/bids-reports.service';

@Component({
  selector: 'app-bid-project',
  templateUrl: './bid.component.html',
  styleUrls: ['./bid.component.css'],
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, FormsModule],
  providers: [
    BidAdvService,
    BidBidderFolderService,
    BidsAdvDocsService,
    BidDocumentsHistoryService,
    BidAdvertsPlanholdersService,
    AdvReportsService,
    BidsAdvQAService,
    BidsAdvEmailLogsService,
    BidsAdvEmailPlanholdersService,
    BidsHistoryService,
    ProjectManagerService,
    EBidProjectService,
    EBidOpeningService,
    BidResultsService,
    EBidHistoryService,
    EBidFolderService,
    BidSetupService,
    EBidService,
    RequiredDownloadsService,
    AddendaAcknowledgeService,
    RequiredUploadService,
    CompletionTimeService,
    WorkOrderService,
    BidFormService,
    RestrictedBiddersService,
    AcknowledgeService,
    PaperBidService
  ]
})
export class BidProjectComponent implements OnInit, OnDestroy, AfterViewInit {

  advertisementService = inject(BidAdvService);
  eBidProjectService = inject(EBidProjectService);
  eBid = this.eBidProjectService.eBid;
  project = this.advertisementService.advertisement;
  isLoading = this.advertisementService.isAdvertisementLoading;

  constructor(private aRoute: ActivatedRoute) {
       this.aRoute.params.subscribe(params => {
      const projectId = params['id'];
      if (projectId) {
        // Set the project ID in the service
        this.advertisementService.projectId.set(projectId);
        this.eBidProjectService.projectId.set(projectId);
      } else {
        console.warn('No project ID found in route parameters', this.aRoute.snapshot);
      }
    });
  }
  ngAfterViewInit(): void {    

  }
  ngOnInit(): void {
   
  }
  ngOnDestroy(): void {

  }
}