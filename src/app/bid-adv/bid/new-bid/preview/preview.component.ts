import { Component, effect, inject, signal } from '@angular/core';
import { BidButtonsComponent } from '../../ui/bid-buttons/bid-buttons.component';
import { CommonModule } from '@angular/common';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { BidOpsProject, County, State } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { ToastrService } from 'ngx-toastr';
import { Router, RouterLink } from '@angular/router';
import { ProjectManagerService } from 'src/app/bid-adv/shared/data-access/bids-manager-service';
import { HelperTools } from 'src/app/shared/utils/helper-tools';
import { BidMainComponent } from 'src/app/bid-opportunities/bid/ui/bid-main/bid-main.component';
import { BidBidderFolderService } from 'src/app/bid-opportunities/shared/data-access/bid-bidderfolder.service';
import { EBidProjectService } from 'src/app/bid-adv/shared/data-access/bids-ebid-project.service';
import { EBidService } from 'src/app/ebid/data-access/ebid.service';
import { BidOpsDocumentService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-document.service';

@Component({
    selector: 'app-preview',
    imports: [CommonModule, BidButtonsComponent, BidMainComponent, RouterLink],
    providers: [BidBidderFolderService, EBidProjectService, EBidService, BidOpsDocumentService],
    standalone: true,
    templateUrl: './preview.component.html',
    styleUrl: './preview.component.css'
})
export class PreviewComponent {
  toastr = inject(ToastrService);
  router = inject(Router);
  bidOpsProjectService = inject(BidOpsProjectService);
  advertisementService = inject(BidsAdvService);  
  projectManagerService = inject(ProjectManagerService);
  project = signal<BidOpsProject>({} as BidOpsProject);

  previewDataEffect = effect(() => {
    if (this.projectManagerService.previewAdvertisement()) {
      var formData = this.projectManagerService.previewAdvertisement()?.getRawValue();
      var bidOpsProjectPreview = {
        ProjectTitle: formData.projectTitle
      } as BidOpsProject;
      this.bidOpsProjectService.projectResource.set(bidOpsProjectPreview);
    }
  });
  // newRequest = effect(() => {
  //   if (this.advertisementService.updateRequestSignal() && this.advertisementService.previewData()) {
  //     this.advertisementService.createAdvertisement(this.advertisementService.previewData()).subscribe({
  //       next: (data) => {
  //         console.log("New Data", data);

  //         this.advertisementService.advertisementResource.set(data);
  //         this.advertisementService.previewBidOps.set(null);
  //         this.advertisementService.previewData.set(null);
  //         this.advertisementService.updateRequestSignal.set(null);
  //         this.toastr.success("Project Updated");

  //         this.router.navigate(['/advertisements', 'projects', data.Id]);


  //       },
  //       error: (err) => {
  //         console.log(err);
  //         this.advertisementService.updateRequestSignal.set(null);
  //         this.toastr.error("Project could not be saved");
  //       }
  //     });
  //   }
  // });
  
  // eff = effect(() => {
  //   if(this.advertisementService.previewData()){
  //     var data = this.advertisementService.previewData();

  //     var bidOpsProject: BidOpsProject = {
  //       Id: '',
  //       Scope: data.scope,
  //       Notes: data.notes,
  //       LowBidAmount: data.lowBidAmount,
  //       LowBidName: data.lowBidName,
  //       AdditionalNotes: data.additionalNotes,
  //       QuestionEmails: data.qa?.emails,
  //       BidOpeningLink: data.bidDateInfo.link,
  //       PreBidMeetingLink: data.preBidInfo.link,
  //       Permissions: {
  //         AllowQuestions: data.qa?.allowQuestions,
  //         ExpireQA: data.qa?.expireQA,
  //         IsPrivate: data.projectSettings?.isPrivate,
  //         ShowPlanholdersList: data.projectSettings?.showPlanholdersList,
  //         IsVisible: data.projectSettings?.isVisible,
  //         IsPlansOfficial: data.projectSettings?.isPlansOfficial,
  //         IsTBA: data.isTBA
  //       },
  //       IsTBA: data.isTBA,
  //       StateName: data.locationInfo?.state,        
  //       CountyName: data.locationInfo?.county,
  //       IsBid: false,
  //       MapCode: data.locationInfo?.mapCode,
  //       Password: data.projectSettings?.password,
  //       PaymentDate: null,
  //       Planholders: [],
  //       ProjectUniqueId: data.projectSettings?.uniqueId,
  //       QAExpirationDateTimeInfo: {
  //         Date: data.qa?.qaExpirationDate ? new Date(data.qa?.qaExpirationDate?.year, data.qa?.qaExpirationDate?.month - 1, data.qa?.qaExpirationDate?.day).toISOString() : null,
  //         Year: data.qa?.qaExpirationDate?.year,
  //         Month: data.qa?.qaExpirationDate?.month,
  //         Day: data.qa?.qaExpirationDate?.day,
  //         Hour: data.qa?.qaExpirationTime?.hour,
  //         Minute: data.qa?.qaExpirationTime?.minute,
  //         TimeZone: null 
  //       },
  //       ContactInfo: {
  //         Username: '',
  //         Company: {
  //           Name: data.contactInfo?.companyName,
  //           CompanyType: {
  //             CompanyTypeId: 0,
  //             Name: '',
  //             Order: 0
  //           },
  //           Address: {
  //             Name: data.contactInfo?.companyName,
  //             Address1: data.contactInfo?.address1,
  //             Address2: data.contactInfo?.address2,
  //             City: data.contactInfo?.city,
  //             State: {} as State,
  //             Zip: data.contactInfo?.zip
  //           },
  //           Website: data.contactInfo?.webSite,
  //           Phone: data.contactInfo?.phone,
  //           Fax: data.contactInfo?.fax
  //         },
  //         FirstName: data.contactInfo?.firstName,
  //         LastName: data.contactInfo?.lastName,
  //         Email: data.contactInfo?.email
  //       },
  //       ProjectTitle: data.projectTitle,
  //       ReleaseDateTimeInfo: {
  //         Date: data.releaseDate?.date ? new Date(data.releaseDate?.date.year, data.releaseDate?.date.month - 1, data.releaseDate?.date.day).toISOString() : null,
  //         Year: data.releaseDate?.year,
  //         Month: data.releaseDate?.month,
  //         Day: data.releaseDate?.day,          
	//         Hour: 0,
	//         Minute: 0,
  //         TimeZone: null
  //       },
  //       BidDetails: {
  //         Location: data.bidDateInfo?.location,
  //         Notes: data.bidDateInfo?.notes,
  //         BidDateTimeInfo: {
  //           Date: data.bidDateInfo?.bidDateTimeInfo, // ? new Date(data.bidDateInfo?.date.year, data.bidDateInfo?.date.month - 1, data.bidDateInfo?.date.day).toISOString() : null,
  //           Year: data.bidDateInfo?.date?.year,
  //           Month: data.bidDateInfo?.date?.month,
  //           Day: data.bidDateInfo?.date?.day,
  //           Hour: data.bidDateInfo?.time?.hour,
  //           Minute: data.bidDateInfo?.time?.minute,
  //           TimeZone: null
  //         }
  //       },
  //       PreBidDetails: {
  //         Location: data.preBidInfo?.location,
  //         Notes: data.preBidInfo?.notes,
  //         BidDateTimeInfo: {
  //           Date:  data.preBidInfo?.bidDateTimeInfo, //data.preBidInfo?.date ? new Date(data.preBidInfo?.date.year, data.preBidInfo?.date.month - 1, data.preBidInfo?.date.day).toISOString() : null,
  //           Year: data.preBidInfo?.year,
  //           Month: data.preBidInfo?.month,
  //           Day: data.preBidInfo?.day,
  //           Hour: data.preBidInfo?.time?.hour,
  //           Minute: data.preBidInfo?.time?.minute,
  //           TimeZone: null
  //         }
  //       },
  //       InternalId: data.internalId,
  //       DetailsEstimate: data.detailsEstimate,
  //       TypeOfWork: data.typeOfWork,
  //       Owner: data.owner,
  //       Location: {
  //         LocationId: '',
  //         MapData: {
  //           Latitude: data.mapData?.latitude,
  //           Longitude: data.mapData?.longitude
  //         },
  //         State: {} as State,
  //         County: {} as County
  //       },
  //       PlanholderCount: 0,
  //       DocumentCount: 0,
  //       QuestionCount: 0,
  //       IsQAExpired: false
  //     }

  //     this.bidOpsProjectService.projectResource.set(bidOpsProject);
  //     this.project.set(bidOpsProject);
  //     this.advertisementService.previewBidOps.set(bidOpsProject);
  //   }
  // },{allowSignalWrites: true})

}
