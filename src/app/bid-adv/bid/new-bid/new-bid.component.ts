import { Component, effect, inject, input, OnD<PERSON>roy, OnInit, signal } from '@angular/core';
import { ProjectManagerComponent } from '../ui/manager/project-manager.component';
import { BidsAdvService } from '../../shared/data-access/bids.service';
import { Advertisement } from '../../shared/interfaces/advertisement';
import { BaseAdvComponent } from '../../shared/interfaces/base-adv';
import { PreviewRequestSignalInfo } from '../../shared/interfaces/advetisement-requests';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { ToastrService } from 'ngx-toastr';
import { AddOnlineBidComponent } from '../ui/add-online-bid/add-online-bid.component';
import { ProjectManagerService } from '../../shared/data-access/bids-manager-service';
import { HelperTools } from 'src/app/shared/utils/helper-tools';
import { FormGroup } from '@angular/forms';
import { PreviewComponent } from './preview/preview.component';
import { BidAdvService } from '../../shared/data-access/bid.service';

@Component({
    selector: 'app-new-bid',
    imports: [RouterOutlet],
    providers: [BidAdvService, ProjectManagerService],
    standalone: true,
    templateUrl: './new-bid.component.html',
    styleUrl: './new-bid.component.css'
})
export class NewBidComponent extends BaseAdvComponent implements OnInit {
  advertisementService = inject(BidAdvService);
  projectManagerService = inject(ProjectManagerService);
  toastr = inject(ToastrService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  project = input<Advertisement>({} as Advertisement);
  bidOpsProject = signal<BidOpsProject | null>(null);

  projectFormGroup = this.projectManagerService.projectFormGroup;

  constructor() {
    super();
  }

  ngOnInit(): void {    
    this.advertisementService.advertisementResource.set({} as Advertisement);
    this.advertisementService.projectId.set(null);    
  }


}
