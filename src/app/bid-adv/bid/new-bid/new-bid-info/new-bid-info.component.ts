import { Component, inject, input, OnInit, signal } from "@angular/core";
import { FormGroup } from "@angular/forms";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { ProjectManagerService } from "src/app/bid-adv/shared/data-access/bids-manager-service";
import { BidsAdvService } from "src/app/bid-adv/shared/data-access/bids.service";
import { Advertisement } from "src/app/bid-adv/shared/interfaces/advertisement";
import { BidOpsProject } from "src/app/bid-opportunities/shared/interfaces/bid";
import { AddOnlineBidComponent } from "../../ui/add-online-bid/add-online-bid.component";
import { ProjectManagerComponent } from "../../ui/manager/project-manager.component";
import { BidAdvService } from "src/app/bid-adv/shared/data-access/bid.service";



@Component({
  selector: 'app-new-bid-info',
  imports: [ProjectManagerComponent, AddOnlineBidComponent],
  templateUrl: './new-bid-info.component.html',
  styleUrl: './new-bid-info.component.css'
})
export class NewBidInfoComponent implements OnInit {


  advertisementService = inject(BidAdvService);
  projectManagerService = inject(ProjectManagerService);
  toastr = inject(ToastrService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  project = input<Advertisement>({} as Advertisement);
  bidOpsProject = signal<BidOpsProject | null>(null);

  projectFormGroup = this.projectManagerService.projectFormGroup;

  constructor() {

  }

  ngOnInit(): void {    
    if(this.projectManagerService.previewAdvertisement()){
      this.projectManagerService.projectFormGroup = this.projectManagerService.previewAdvertisement() as FormGroup;
    }else{
      this.advertisementService.advertisementResource.set({} as Advertisement); 
      this.advertisementService.projectId.set(null);    
    }         
    
  }
  continue() {    
    this.projectManagerService.previewAdvertisement.set(this.projectManagerService.projectFormGroup);    
    this.router.navigate(['preview'], { relativeTo: this.aRoute });
  }
}