import { Component, computed, model, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { of, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { DocumentDownloadHistory } from '../../shared/interfaces/bids-download-history';
import { BidDocumentsHistoryService } from '../../shared/data-access/bids-document-history.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { rxResource } from '@angular/core/rxjs-interop';

@Component({
    selector: 'app-downloads',
    templateUrl: './downloads.component.html',
    styleUrls: ['./downloads.component.css'],
    standalone: true,
    imports: [CommonModule, FormsModule, NgbPaginationModule]
})
export class AdvDownloadsComponent {

  // @Input() project: BidOpsProject | null = null;
  projectId = model.required<string>();

  isLoading = computed(() => this.downloadHistoryResource.isLoading());
  currentPage = signal(1);
  max = signal(25);
  searchSTR = "";
  search = signal<string | null>(null);
  total: number = 0;
  orderBy = signal<string>("DateTime");
  isReversed = signal<boolean>(false);
  searchInputChange = new Subject<string>();  
  downloadHistory = computed(() => this.downloadHistoryResource.value());
  //downloadHistory: Array<DocumentDownloadHistory> = []
  constructor(
    private documentHistoryService: BidDocumentsHistoryService,
    private aRoute: ActivatedRoute) { 
          
      this.aRoute.parent?.paramMap.subscribe({
        next: (params) => {
          const projectId =  params.get('id') || '';
          if(projectId){
           this.projectId.set(projectId);
          }
          
        }
      });

      this.searchInputChange
      .pipe(debounceTime(600), distinctUntilChanged())
      .subscribe(value => {
        if(value === ''){
          this.search.set(null);
        } else{
          this.search.set(value);
        }
      });

  }

  downloadHistoryResource = rxResource({
    request: () => ({ 
      projectId: this.projectId(),
      search: this.search(),
      max: this.max(),
      currentPage: this.currentPage(),
      orderBy: this.orderBy(),
      isReversed: this.isReversed()
    }),
    loader: (requestInfo) => {
      
      if(requestInfo.request.projectId){
        return this.documentHistoryService.getDocumentHistory(requestInfo.request.projectId, requestInfo.request.max, requestInfo.request.currentPage, requestInfo.request.orderBy, requestInfo.request.isReversed, requestInfo.request.search)
      }

      return of(null);
    }
  });

  trackByMethod(index:number, download:DocumentDownloadHistory): string {
    return download.Id;
  }

  changePage($event: any){
    this.currentPage.set($event);    
  }

  setOrder(orderBy:string)
  {
    this.orderBy.set(orderBy);
    this.isReversed.set(!this.isReversed());    
  }
}