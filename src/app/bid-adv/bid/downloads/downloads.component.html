<!-- header -->
<header>
	<h2 class="page-title fs-6 mb-3">Download History</h2>
	<p>This history shows every time one of your documents was downloaded by a contractor or
		anyone else. Sort by document name to see all the users who have downloaded
		a certain document or sort by user to see all the documents a certain user has downloaded.</p>
</header>
<!-- search -->
<section class="col-12 col-md-6 col-lg-5 col-xl-4 mb-3">
	<div class="input-group">
		<span class="input-group-text" id="basic-addon1">
			<i class="fas fa-search" aria-hidden="true"></i>
		</span>
		<input type="text" class="form-control" placeholder="Search" [(ngModel)]="searchSTR"
			(ngModelChange)="this.searchInputChange.next($event)" />
	</div>
</section>
<!-- download history -->
<section class="mb-3">
	<table class="table align-middle">
		<thead>
			<tr class="d-none d-lg-table-row">
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('DateTime')">Date</a>
					<i class="fas text-body-secondary"
						[ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"
						*ngIf="orderBy() === 'DateTime'"></i>
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('LastName')">Company</a>
					<i class="fas text-body-secondary"
						[ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"
						*ngIf="orderBy() === 'LastName'"></i>
				</th>
				<th>
					Name
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('Email')">Contact</a>
					<i class="fas text-body-secondary"
						[ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"
						*ngIf="orderBy() === 'Email'"></i>
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('FileTitle')">File Type/Name</a>
					<i class="fas text-body-secondary"
						[ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"
						*ngIf="orderBy() === 'FileTitle'"></i>
				</th>
			</tr>
		</thead>
		@if(isLoading()){
		<tbody>
			@for (item of [0,1,2,3,4,5,6,7,8]; track $index) {
			<tr class="placeholder-glow">
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td>
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
			</tr>
			}
		</tbody>
		}@else {
		<tbody>
			@for (download of downloadHistory()?.DocumentDownloadHistorys; track trackByMethod) {
			<tr>
				<td class="d-none d-lg-table-cell">
					{{ download.DateCreated | date: 'M/dd/yyyy'}}
				</td>
				<td class="d-none d-lg-table-cell">
					<div>{{ download.CompanyName }}</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<div>{{ download.FirstName }} {{download.LastName}}</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<div>{{ download.Phone }}</div>
					<!-- {{ download.Fax }} -->
					<a class="text-decoration-none text-primary text-opacity-75 d-block"
						href='mailto:{{ download.Email }}'>{{ download.Email
						}}</a>
				</td>
				<td class="d-none d-lg-table-cell small">
					<div class="fw-bold">{{ download.FileType }}</div>
					<div>{{ download.FileTitle }}</div>
				</td>
				<td class="d-lg-none">
					<div class="fw-bold">{{ download.DateCreated | date: 'M/dd/yyyy'}}</div>
					<div>{{ download.CompanyName }}</div>
					<div>{{ download.FirstName }} {{download.LastName}}</div>
					<div><a class="text-decoration-none text-primary text-opacity-75 d-block"
							href='tel:{{ download.Phone }}'>{{ download.Phone
							}}</a></div>
					<div><a class="text-decoration-none text-primary text-opacity-75 d-block"
							href='mailto:{{ download.Email }}'>{{
							download.Email}}</a></div>
					<div>{{ download.FileType }}</div>
					<div>{{ download.FileTitle }}</div>
				</td>
			</tr>
			}@empty {
			<tr>
				<td colspan="4">
					<div class="alert alert-info mb-0" role="alert">
						No downloads yet.
					</div>
				</td>
			</tr>
			}
		</tbody>
		}
	</table>
</section>
<!-- footer -->
<footer class="d-flex justify-content-end mb-3">
	<ngb-pagination [collectionSize]="downloadHistory()?.TotalCount" [pageSize]="max()" [page]="currentPage()"
		[rotate]="true" [maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)" [disabled]="isLoading()">
		<ng-template ngbPaginationFirst>First</ng-template>
		<ng-template ngbPaginationPrevious>Previous</ng-template>
		<ng-template ngbPaginationNext>Next</ng-template>
		<ng-template ngbPaginationLast>Last</ng-template>
	</ngb-pagination>
</footer>