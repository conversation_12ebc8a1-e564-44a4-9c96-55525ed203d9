<!-- header -->
<header>
	<h2 class="page-title fs-6 mb-3">Reports</h2>
	<p>Export your project-related data for offline viewing and record-keeping.</p>
</header>
<section class="row">
	<!-- Step 1 - Select the items you want in your report. -->
	<div class="col-12 col-md-6 mb-3">
		<div class="border p-3">
			<h3 class="page-title fs-6">Select Data for Export</h3>
			<div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="generalInfo" id="generalInfo"
						name="generalInfo" [(ngModel)]="reportFilterBuilder.General">
					<label class="form-check-label" for="generalInfo">General Info</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="projectDetails" id="details"
						[(ngModel)]="reportFilterBuilder.Details">
					<label class="form-check-label" for="details">Project Details</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="listOfFiles" id="files"
						[(ngModel)]="reportFilterBuilder.Files">
					<label class="form-check-label" for="files">List of Files</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="qa" id="qa"
						[(ngModel)]="reportFilterBuilder.QA">
					<label class="form-check-label" for="qa">Q & A</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="planHolders" id="planholders"
						[(ngModel)]="reportFilterBuilder.Planholders">
					<label class="form-check-label" for="planholders">Plan Holders</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="downloadHistory" id="downloadhistory"
						[(ngModel)]="reportFilterBuilder.DownloadHistory">
					<label class="form-check-label" for="downloadhistory">Download History</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="emailHistory" id="emailhistory"
						[(ngModel)]="reportFilterBuilder.EmailHistory">
					<label class="form-check-label" for="emailhistory">Email History</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="sentMessages" id="sentmessages"
						[(ngModel)]="reportFilterBuilder.SentMessages">
					<label class="form-check-label" for="sentmessages">Sent Messages</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="userHistory" id="activityhistory"
						[(ngModel)]="reportFilterBuilder.ActivityHistory">
					<label class="form-check-label" for="activityhistory">User History</label>
				</div>
				<div class="mb-2 form-check">
					<input type="checkbox" class="form-check-input" name="addendaAcknowledgement" id="addenda"
						[(ngModel)]="reportFilterBuilder.Addenda">
					<label class="form-check-label" for="addenda">Addenda Acknowledgement</label>
				</div>
			</div>
		</div>
	</div>
	<!-- Step 2 - Format your report. -->
	<div class="col-12 col-md-6 mb-3">
		<div class="border p-3 mb-3">
			<h3 class="page-title fs-6">Format Report</h3>
			<div>
				<div class="mb-3 form-check">
					<input type="checkbox" class="form-check-input" name="includeHeader" id="includeHeader"
						[(ngModel)]="reportFilterBuilder.UseHeader">
					<label class="form-check-label" for="includeHeader">Include Header</label>
				</div>
				<div class="mb-3">
					<input type="email" class="form-control" name="headerText" id="headerText"
						[(ngModel)]="reportFilterBuilder.HeaderText" aria-describedby="emailHelp"
						[disabled]="!reportFilterBuilder.UseHeader" placeholder="Type header here.">
				</div>
				<div class="mb-3 form-check">
					<input type="checkbox" class="form-check-input" name="includeFooter" id="includeFooter"
						[(ngModel)]="reportFilterBuilder.UseFooter">
					<label class="form-check-label" for="includeFooter">Include Footer</label>
				</div>
				<div class="mb-3">
					<input type="email" class="form-control" name="emailHelp" id="emailHelp"
						[(ngModel)]="reportFilterBuilder.FooterText" aria-describedby="emailHelp"
						[disabled]="!reportFilterBuilder.UseFooter" placeholder="Type footer here.">
				</div>
			</div>
		</div>
	</div>
</section>
<div class="d-flex justify-content-end">
	<button type="button" class="btn btn-outline-dark" (click)="runReport()" [disabled]="isReportRunning">
		<i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isReportRunning"></i>
		Create Report
	</button>
</div>