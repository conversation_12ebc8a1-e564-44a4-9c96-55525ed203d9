import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { interval } from 'rxjs';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { ReportBuilderRequest } from '../../shared/interfaces/bids-reports';
import { AdvReportsService } from '../../shared/data-access/bids-reports.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';

@Component({
    selector: 'app-report-builder',
    templateUrl: './report-builder.component.html',
    styleUrls: ['./report-builder.component.css'],
    standalone: true,
    imports: [CommonModule, FormsModule, NgbPaginationModule]
})
export class ReportBuilderComponent implements OnInit {

  reportFilterBuilder: ReportBuilderRequest;
  project: BidOpsProject;

  isReportRunning: boolean = false;

  constructor(
    private advReportService: AdvReportsService, 
    private lambdaAWSService: LambdaAWSService,
    private toastr: ToastrService,
    private aRoute:ActivatedRoute) { 

    this.project = this.aRoute.snapshot.parent?.parent?.data['projectData'];

    if(this.project){
      this.reportFilterBuilder = new ReportBuilderRequest(this.project.Id);      
    }else{
      this.reportFilterBuilder = new ReportBuilderRequest("");
    }
  }

  ngOnInit(): void {
  }

  //TODO: Move to Lambda URL
  async runReport(){

    // this.advReportService.RunDailyLogReport(this.reportFilterBuilder).subscribe({
    //   next: (result) => {
    //     console.log(result);
    //   }
    // })

    this.isReportRunning = true;

    try {
      this.advReportService.RunReportBuilderReport(this.reportFilterBuilder).subscribe({
        next: (store) => {
          var check = interval(5000);
          var reportSub = check.subscribe(async () => {
            if(store.Status !== "completed" && store.Status !== "error"){
              store = await this.advReportService.GetReportInfo(store.Id).toPromise();
            }else{
              try {
                reportSub.unsubscribe();      
      
                if(store.Status === "completed"){            
                  var key = store.Key;
                  console.log(key);
      
                  this.lambdaAWSService.getDownloadPresignedUrl(key, "diary-report.pdf").subscribe({
                    next: (value) => {
                      window.location.href = value.PresignedUrl;    
                    },
                    error: (err) => {
                      console.log(err);
                      this.toastr.error("Could not retrieve download");
                    }
                  });
    
                }else{
                  this.toastr.error("There was an issue creating this report");
                }
                
              } catch (error) {
                console.log(error);
                this.isReportRunning = false;
              }finally{
                this.isReportRunning = false;
              }
            }
          });
      
          setTimeout(() => {
            reportSub.unsubscribe();
            this.isReportRunning = false;
          }, 900000);
        },
        error: (err) => {
          this.isReportRunning = false;
        }
      });       

    } catch (error) {
      this.isReportRunning = false;
    }
  }
}