<!-- page header -->
<header class="bg-light p-3 p-lg-4">
	<div class="container">	
		<!-- page title -->
		<div class="col-12 d-flex justify-content-between align-items-center mb-3">
			@if(isLoading()){
			<div class="col-12 placeholder-glow">
				<span class="placeholder col-12"></span>
			</div>
			}@else {
			<h1 class="page-title fs-5 lh-base m-0 me-2 me-xl-0">{{ project()?.ProjectTitle }}</h1>
			}
			<div class="dropdown d-xl-none">
				<a class="dropdown-toggle text-dark text-decoration-none" href="#" role="button"
					data-bs-toggle="dropdown" aria-expanded="false">
					More
				</a>
				<ul class="dropdown-menu dropdown-menu-end">
					<li>
						<h6 class="dropdown-header">E-Bidding</h6>
					</li>
					<li><a class="dropdown-item" href="#">Bid Setup</a></li>
					<li><a class="dropdown-item" href="#">Bid Opening</a></li>
					<li><a class="dropdown-item" href="#">Block Bidders</a></li>
					<li><a class="dropdown-item" href="#">Award</a></li>
					<li>
						<hr class="dropdown-divider">
					</li>
					<li>
						<h6 class="dropdown-header">History</h6>
					</li>
					<li><a class="dropdown-item" href="#">Email Plan Holders</a></li>
					<li><a class="dropdown-item" href="#">Email History</a></li>
					<li><a class="dropdown-item" href="#">Download History</a></li>
					<li><a class="dropdown-item" href="#">User History</a></li>
					<li>
						<hr class="dropdown-divider">
					</li>
					<li>
						<h6 class="dropdown-header">Post-Bid</h6>
					<li><a class="dropdown-item" href="#">Report Builder</a></li>
					<li><a class="dropdown-item" href="#">Delete Project</a></li>
				</ul>
			</div>
			<!-- <nav>
				<app-bid-project-navigation></app-bid-project-navigation>
			</nav> -->
		</div>
		<!-- links -->
		<div class="row">
			<div class="col-6 col-lg-3 mb-3">
				<a routerLink="docs" class="custom-link bg-white border rounded d-block p-3">
					<div>Bid Documents</div>
					<h4 class="text-dark text-decoration-none">						
						@if(isLoading()){
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}@else{
							{{ project()?.DocumentCount}}
							
						}
					</h4>
				</a>
			</div>
			<div class="col-6 col-lg-3 mb-3">
				<a routerLink="plan-holders" class="custom-link bg-white border rounded d-block p-3">
					<div>Plan Holders</div>
					<h4 class="text-dark text-decoration-none">						
						@if(isLoading()){
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}@else{
							{{ project()?.PlanholderCount }}							
						}
					</h4>
				</a>
			</div>
			<div class="col-6 col-lg-3 mb-3">
				<a routerLink="qa" class="custom-link bg-white border rounded d-block p-3">
					<div>Answers/Questions</div>
					<h4 class="text-dark text-decoration-none">
						@if(isLoading()){
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}@else{
							{{ project()?.AnswerCount}}/{{ project()?.QuestionCount}}							
						}						
					</h4>
				</a>
			</div>
			<div class="col-6 col-lg-3 mb-3">			
				<a routerLink="e-bid/open" class="custom-link bg-white border rounded d-block p-3">
					<div>Received Bids</div>	
					@if(isLoading()){
						<h4 class="text-dark text-decoration-none">
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>

						</h4>								
					}@else{				
						@if(eBid()?.OpenedAt){
							<h4 class="text-dark text-decoration-none">{{ project()?.ReceivedBidsCount + (project()?.PaperBidsCount ?? 0) }}</h4>
						}@else {
							<h4 class="text-dark text-decoration-none">NA</h4>
						}
					}
				</a>				
			</div>
		</div>
	</div>
</header>
<!--  -->
<section class="p-3 p-lg-4">
	<div class="container">
		<router-outlet></router-outlet>
	</div>
</section>