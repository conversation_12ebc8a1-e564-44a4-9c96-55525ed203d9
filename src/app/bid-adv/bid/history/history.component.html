<!-- header -->
<header>
	<h2 class="page-title fs-6 mb-3">User History</h2>
	<p>This is a history of edits, changes, and other activity for this project. Set up delegates
		to track the activity of individual users.</p>
</header>
<!-- search -->
<form [formGroup]="inputForm">
	<section class="col-12 col-md-6 col-lg-5 col-xl-4 mb-3">
		<div class="input-group">
			<span class="input-group-text" id="basic-addon1">
				<i class="fas fa-search" aria-hidden="true"></i>
			</span>
			<input type="text" class="form-control" name="search" placeholder="Search" formControlName="search" />
		</div>
	</section>
</form>
<!-- user history -->
<section class="col-12 mb-3">
	<table class="table align-middle">
		<thead>
			<tr class="d-none d-lg-table-row">
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1" style="cursor: pointer;"
						(click)="setOrder('DateTime')">Date</a>
					@if(orderBy() === 'DateTime'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed, 'fa-sort-down': !isReversed }"></i>
					}
				</th>
				<th scope="col">
					Name
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1" style="cursor: pointer;"
						(click)="setOrder('Description')">Description</a>
					@if(orderBy() === 'Description'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed, 'fa-sort-down': !isReversed }"></i>
					}

				</th>
			</tr>
		</thead>
		<tbody>
			@if(isLoading()){
			@for (item of [0,1,2,3,4,5,6,7,8]; track $index) {
			<tr class="placeholder-glow">
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-50">
					<div class="placeholder w-100"></div>
				</td>
			</tr>
			}
			}@else {
			@for(history of bidOpsHistory()?.History; track index){
			<tr>
				<td class="d-none d-lg-table-cell text-nowrap">
					{{ history.DateCreated | date: 'M/dd/yyyy h:mma'}}
				</td>
				<td class="d-none d-lg-table-cell text-nowrap px-3">
					<div>{{ history.FirstName }} {{ history.LastName}}</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<div [innerHtml]="history.Description"></div>
					<ng-container *ngFor="let item of history.ComparisonInfo">
						<div [innerHtml]="item"></div>
					</ng-container>
				</td>
				<td class="d-lg-none">
					<div class="fw-bold">{{ history.DateCreated | date: 'M/dd/yyyy h:mma'}}</div>
					<div>{{ history.FirstName }} {{ history.LastName}}</div>
					<div [innerHtml]="history.Description"></div>
					<ng-container *ngFor="let item of history.ComparisonInfo">
						<div [innerHtml]="item"></div>
					</ng-container>
				</td>
			</tr>
			}@empty {
			<tr>
				<td colspan="2">
					<div class="alert alert-info mb-0" role="alert">
						No user history yet.
					</div>
				</td>
			</tr>
			}
			}
		</tbody>
	</table>
</section>
<!-- pagination -->
<footer class="col-12 mb-3">
	<div class="d-flex justify-content-end">
		<ngb-pagination [collectionSize]="bidOpsHistory()?.TotalCount" [pageSize]="max()" [page]="currentPage()"
			[rotate]="true" [maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)"
			[disabled]="isLoading()">
			<ng-template ngbPaginationFirst>First</ng-template>
			<ng-template ngbPaginationPrevious>Previous</ng-template>
			<ng-template ngbPaginationNext>Next</ng-template>
			<ng-template ngbPaginationLast>Last</ng-template>
		</ngb-pagination>
	</div>
</footer>