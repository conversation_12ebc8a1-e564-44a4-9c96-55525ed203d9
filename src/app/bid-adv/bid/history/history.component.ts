import { Component, computed, model, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { BidsHistoryService } from '../../shared/data-access/bids-history-service';
import { rxResource } from '@angular/core/rxjs-interop';

@Component({
    selector: 'app-bid-adv-history',
    templateUrl: './history.component.html',
    styleUrls: ['./history.component.css'],
    standalone: true,
    imports: [CommonModule, FormsModule, NgbPaginationModule, ReactiveFormsModule]
})
export class BidAdvComponentHistoryComponent {

  isLoading = computed(() => this.bidOpsHistoryResource.isLoading());
  currentPage = signal(1);
  max = signal(25);
  search = signal<string | null>(null);
  total: number = 0;
  orderBy = signal<string>("DateTime");
  isReversed = signal<boolean>(false);
  searchInputChange = new Subject<string>();  
  bidOpsHistory = computed(() => this.bidOpsHistoryResource.value() || []);
  projectId = model.required<string>();

  inputForm = new FormGroup({
    search: new FormControl(null)
  })

  constructor(
    private bidOpsHistoryService: BidsHistoryService,
    private aRoute: ActivatedRoute) { 

      this.aRoute.parent?.paramMap.subscribe({
        next: (params) => {
          const projectId =  params.get('id') || '';
          if(projectId){
           this.projectId.set(projectId);
          }          
        }
      });         
      

      this.inputForm.get('search')?.valueChanges.pipe(debounceTime(600), distinctUntilChanged()).subscribe({
        next: (value) => {
          if(value){
            this.search.set(value);
          }else{
            this.search.set(null);          
          }  
        }
      });

  }

  bidOpsHistoryResource = rxResource({
    request: () => ({
      projectId: this.projectId(),
      search: this.search(),
      orderBy: this.orderBy(),
      isReversed: this.isReversed(),
      max: this.max(),
      currentPage: this.currentPage()
    }),
    loader: (requestInfo) => {
      return this.bidOpsHistoryService.getHistory(requestInfo.request.projectId, 
        requestInfo.request.max, 
        requestInfo.request.currentPage,
         requestInfo.request.orderBy, 
         requestInfo.request.isReversed, 
         requestInfo.request.search);
    }
  });

  // getHistory(){
  //   this.isLoading = true;
  //   this.bidOpsHistoryService.getHistory(this.projectId(), this.max, this.currentPage, this.orderBy, this.isReversed, this.search).subscribe(
  //     {
  //       next: async (result) => {          
  //         this.bidOpsHistory = result.History;
  //         this.total = result.TotalCount;
  //         this.isLoading = false;
  //       },
  //       error: (err) => {
  //         this.isLoading = false;
  //         console.log(err);
  //       }
  //     }
  //   )
  // }

  changePage($event: any){
    this.currentPage.set($event);
    
  }

  setOrder(orderBy:string)
  {
    this.orderBy.set(orderBy);
    this.isReversed.set(!this.isReversed());    
  }

}
