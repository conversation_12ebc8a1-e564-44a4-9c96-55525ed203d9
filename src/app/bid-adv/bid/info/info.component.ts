import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, inject } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { BidsAdvService } from '../../shared/data-access/bids.service';
import { BidAdvService } from '../../shared/data-access/bid.service';

@Component({
  selector: 'app-info',
  templateUrl: './info.component.html',
  styleUrls: ['./info.component.css'],
  standalone: true,
  imports: [CommonModule, RouterLink]
})
export class BidAdvInfoComponent implements OnInit {

  advertisementService = inject(BidAdvService);
  project = this.advertisementService.advertisement;
  isLoading = this.advertisementService.isAdvertisementLoading;
  constructor(private aRoute: ActivatedRoute) {
      
  }

  ngOnInit(): void {
  }

  deleteProject(){
    
  }
}