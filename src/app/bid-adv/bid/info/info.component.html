@if(isLoading()){
<div class="col-12 mb-3 placeholder-glow">
	<div class="border rounded p-3">
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>
		<div class="my-4">
			<span class="placeholder col-12" style="height:20px;"></span>
		</div>

	</div>
</div>
}@else {
<div class="row mb-3">
	<div class="col-12 col-xl-9 mb-3">
		<div class="card mb-3">
			<div class="card-body">
				<div class="d-flex justify-content-between align-items-center mb-3">
					<h2 class="card-title fs-6 page-title mb-0">Bid Advertisement</h2>
					<a routerLink="../edit" class="btn btn-outline-dark">Edit</a>
				</div>
				<!-- details ----------------------->
				<div class="border rounded p-3 mb-3">
					<h3 class="page-title fs-6">Details</h3>
					<div class="mb-1">
						<span class="fw-bold">ID: </span>
						<span>SAWS Job Number 22-7001</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Estimate:: </span>
						<span>$32,000,000.00</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Type: </span>
						<span>Construction</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Owner: </span>
						<span>Grand Prairie Development, LLC</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Location: </span>
						<span>Bexar County, TX</span>
					</div>
				</div>
				<!-- pre-bid ----------------------->
				<div class="border rounded p-3 mb-3">
					<h3 class="page-title fs-6">Pre-Bid Meeting</h3>
					<div class="mb-1">
						<span>9/15/2024 &#64; 2:00 PM</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Location: </span>
						<span>Virtual</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Link: </span>
						<a href="#" class="custom-link">Meeting</a>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Notes: </span>
						<span></span>
					</div>
				</div>
				<!-- bid opening ------------------->
				<div class="border rounded p-3 mb-3">
					<h6 class="page-title fs-6">Bid Opening</h6>
					<div class="mb-1">
						<span>10/1/2024 &#64; 5:00 PM</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Location: </span>
						<span>Virtual</span>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Link: </span>
						<a href="#" class="custom-link">Meeting</a>
					</div>
					<div class="mb-1">
						<span class="fw-bold">Notes: </span>
						<span></span>
					</div>
				</div>
				<!-- scope ------------------------->
				<div class="border rounded p-3 mb-3">
					<h6 class="page-title mb-3">Scope</h6>
					<div class="mb-1">
					</div>
				</div>
				<!-- contact ---------------->
				<div class="border rounded p-3 mb-3">
					<h6 class="page-title fs-6">Contact</h6>
					<div class="mb-1">
						San Antonio Water System
					</div>
					<div class="mb-1">
						2800 US HWY 281 N
					</div>
					<div class="mb-1">
						San Antonio, 78212
					</div>
					<div class="mb-1">
						<a href="#" class="text-decoration-none">Company Website</a>
					</div>
					<div class="mb-1">
						Lindsay Esquivel
					</div>
					<div class="mb-1">
						210-233-3409
					</div>
					<div class="mb-1">
						lindsay.esquivel&#64;saws.org
					</div>
				</div>
				<!-- bid package notes ------------->
				<div class="border rounded p-3 mb-3">
					<h6 class="page-title fs-6">Bid Package Notes</h6>
					<div class="mb-1">
						Any Details regarding this RFCSP can be found at:
						https://apps.saws.org/business_center/contractsol/SNO_Drill.cfm?id=4321&View=Yes.
					</div>
				</div>
				<!-- additional notes -------------->
				<div class="border rounded p-3 mb-3">
					<h6 class="page-title fs-6">Additional Notes</h6>
					<div class="mb-1">
						Any Details regarding this RFCSP can be found at:
						https://apps.saws.org/business_center/contractsol/SNO_Drill.cfm?id=4321&View=Yes.
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-12 col-xl-3">
		<!--private ---------------------->
		<div class="card mb-3" *ngIf="project()?.Permissions?.IsPrivate">
			<div class="card-body">
				<h2 class="card-title fs-6 page-title">Private</h2>
				<div>
					<span class="fw-bold">ID: </span>
					<a class="text-decoration-none" style="letter-spacing:2px; font-family:'Consolas', monospace;"
						[routerLink]="['/bid-opportunities/projects', project()?.Id]" [queryParams]="{ privateId: project()?.ProjectUniqueId, 'return-url': '/advertisements/projects/' + project()?.Id }">{{project()?.ProjectUniqueId}}</a>
				</div>
				<!-- <div>
					<span class="fw-bold">Password: </span>
					<span class="lo_sensitive "
						style="letter-spacing:2px; font-family:'Consolas', monospace;">{{project()?.Password}}</span>
				</div> -->
			</div>
		</div>
		<!-- e-bidding ------------------->
		<div class="card mb-3">
			<div class="card-body">
				<h2 class="card-title fs-6 page-title">E-Bidding</h2>
				<div class="d-flex flex-column">
					<a routerLink="../e-bid/setup" class="custom-link mb-1">Bid Set Up</a>
					<a routerLink="../e-bid/open" class="custom-link mb-1">Bid Opening</a>
					<a routerLink="../e-bid/restrict-bidders" class="custom-link mb-1">Block Bidders</a>
					<a routerLink="../e-bid/results" class="custom-linke">Award</a>
				</div>
			</div>
		</div>
		<!-- history --------------------->
		<div class="card mb-3">
			<div class="card-body">
				<h2 class="card-title fs-6 page-title">History</h2>
				<div class="d-flex flex-column">
					<a routerLink="../email-log" class="custom-link mb-1">Email History</a>
					<a routerLink="../history" class="custom-link mb-1">User History</a>
					<a routerLink="../downloads" class="custom-link">Download History</a>
				</div>
			</div>
		</div>
		<!-- post bid -------------------->
		<div class="card mb-3">
			<div class="card-body">
				<h2 class="card-title fs-6 page-title">Post-Bid</h2>
				<div class="d-flex flex-column">
					<a routerLink="../reports" class="custom-link mb-1">Reports</a>
					<a href="#" class="custom-link">Delete Project </a>
				</div>
			</div>
		</div>
		<!-- delete button --------------->
		<div class="d-grid">
			<button type="button" class="btn btn-outline-danger" (click)="deleteProject()">Delete Project</button>
		</div>
	</div>
</div>
}