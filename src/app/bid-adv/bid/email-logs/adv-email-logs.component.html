<!-- header -->
<header>
	<h2 class="page-title fs-6 mb-3">Email History</h2>
	<p>This history shows the emails sent out for this project, such as bid alerts, addendum
		alerts, etc.</p>
</header>
<!-- search -->
<section class="col-12 col-md-6 col-lg-5 col-xl-4 mb-3">
	<form [formGroup]="inputForm">
		<div class="input-group">
			<span class="input-group-text" id="basic-addon1">
				<i class="fas fa-search" aria-hidden="true"></i>
			</span>
			<input type="text" class="form-control" name="search" placeholder="Search" formControlName="search" />
		</div>
	</form>
</section>
<!-- email history -->
<section class="mb-3">
	<table class="table align-middle">
		<thead>
			<tr class="d-none d-lg-table-row">
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('DateTime')">Date</a>
					@if(orderBy() === 'DateTime'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"></i>
					}
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('LastName')">Recipient</a>
					@if(orderBy() === 'LastName'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"></i>
					}
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('Email')">Email</a>
					@if(orderBy() === 'Email'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"></i>
					}
				</th>
				<th scope="col">
					<a class="text-decoration-none text-dark text-nowrap clickable me-1"
						(click)="setOrder('Comment')">Description</a>
					@if(orderBy() === 'FileTitle'){
					<i class="fas" [ngClass]="{ 'fa-sort-up': isReversed(), 'fa-sort-down': !isReversed() }"></i>
					}
				</th>
			</tr>
		</thead>
		<tbody>
			@if(isLoading()){
			@for (item of [0,1,2,3,4,5,6,7,8]; track $index) {
			<tr class="placeholder-glow">
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
				<td class="w-25">
					<div class="placeholder w-100"></div>
				</td>
			</tr>
			}
			}@else {
			@for (log of emailLogs()?.EmailLogs; track $index) {
			<tr *ngFor="let log of emailLogs; let i = index;">
				<td class="d-none d-lg-table-cell fw-bold">
					{{ log.CreateDate | date: 'M/dd/yyyy h:mm:ssa'}}
				</td>
				<td class="d-none d-lg-table-cell">
					<div class="text-nowrap">{{ log.FirstName }} {{ log.LastName}}</div>
				</td>
				<td class="d-none d-lg-table-cell">
					<a class="text-decoration-none text-primary text-opacity-75" href='mailto:{{ log.Email }}'>{{
						log.Email }}</a>
				</td>
				<td class="d-none d-lg-table-cell">
					{{ log.NotificationType }}
				</td>
				<td class="d-lg-none">
					<div class="fw-bold">
						{{ log.CreateDate | date: 'M/dd/yyyy h:mm:ssa'}}
					</div>
					<div>
						{{ log.FirstName }} {{ log.LastName}}
					</div>
					<div>
						<a class="text-decoration-none text-primary text-opacity-75" href='mailto:{{ log.Email }}'>{{
							log.Email }}</a>
					</div>
					<div>
						{{ log.NotificationType }}
					</div>
				</td>
			</tr>
			}@empty {

			<tr>
				<td colspan="4">
					<div class="alert alert-info mb-0" role="alert">
						No sent emails yet.
					</div>
				</td>
			</tr>

			}
			}
			<tr *ngIf="!isLoading && emailLogs.length <= 0">
				<td colspan="4">
					<div class="alert alert-info mb-0" role="alert">
						No sent emails yet.
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</section>
<!-- footer -->
<footer class="d-flex justify-content-end mb-3">
	<ngb-pagination [collectionSize]="emailLogs()?.TotalCount" [pageSize]="max()" [page]="currentPage()" [rotate]="true"
		[maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)" [disabled]="isLoading()">
		<ng-template ngbPaginationFirst>First</ng-template>
		<ng-template ngbPaginationPrevious>Previous</ng-template>
		<ng-template ngbPaginationNext>Next</ng-template>
		<ng-template ngbPaginationLast>Last</ng-template>
	</ngb-pagination>
</footer>