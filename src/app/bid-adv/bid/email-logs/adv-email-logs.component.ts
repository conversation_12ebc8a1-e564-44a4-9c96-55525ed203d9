import { Component, computed, Input, model, OnInit, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { EmailLog } from '../../shared/interfaces/bids-email-log';
import { BidsAdvEmailLogsService } from '../../shared/data-access/bids-email-logs-service';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { rxResource } from '@angular/core/rxjs-interop';

@Component({
    selector: 'app-adv-email-logs',
    templateUrl: './adv-email-logs.component.html',
    styleUrls: ['./adv-email-logs.component.css'],
    standalone: true,
    imports: [CommonModule, FormsModule, ReactiveFormsModule, NgbPaginationModule]
})
export class BidAdvEmailLogsComponent implements OnInit {

  projectId = model.required<string>();

  isLoading = computed(() => this.emailLogsResource.isLoading());
  currentPage = signal(1);
  max = signal(25);
  search = signal<string | null>(null);
  total: number = 0;
  orderBy = signal<string>("DateTime");
  isReversed = signal<boolean>(false);  
  emailLogs  = computed(() => this.emailLogsResource.value()?.EmailLogs || []);
  
  inputForm = new FormGroup({
    search: new FormControl(null)
  })

  constructor(
    private emailLogsService: BidsAdvEmailLogsService,
    private aRoute: ActivatedRoute) { 
          
      this.aRoute.parent?.paramMap.subscribe({
        next: (params) => {
          const projectId = params.get('id') || '';
          if(projectId){
            this.projectId.set(projectId);
          }          
        }
      });
      // this.aRoute.snapshot.parent.params
      // this.project = this.aRoute.snapshot.parent?.data['projectData'];

      this.inputForm.get('search')?.valueChanges.pipe(debounceTime(600), distinctUntilChanged()).subscribe({
        next: (value) => {
          if(value){
            this.search.set(value);
          }else{
            this.search.set(null);          
          }  
        }
      });

      // this.searchInputChange
      // .pipe(debounceTime(600), distinctUntilChanged())
      // .subscribe(value => {
      //   if(value === ''){
      //     this.search = null;
      //   }
      //   this.getEmailLogs();
      // });

  }

  emailLogsResource = rxResource({
    request: () => ({
      projectId: this.projectId(),
      search: this.search(),
      orderBy: this.orderBy(),
      isReversed: this.isReversed(),
      max: this.max(),
      currentPage: this.currentPage()
    }),
    loader: (requestInfo) => {
      return this.emailLogsService.getEmailLogs(requestInfo.request.projectId, 
        requestInfo.request.max, 
        requestInfo.request.currentPage,
        requestInfo.request.orderBy,
        requestInfo.request.isReversed,
        requestInfo.request.search);
    }
  });

  ngOnInit(): void {
    //this.getEmailLogs();
  }

  // getEmailLogs(){
  //   this.isLoading = true;
  //   this.emailLogsService.getEmailLogs(this.projectId, this.max, this.currentPage, this.orderBy, this.isReversed, this.search).subscribe(
  //     {
  //       next: async (result) => {          
  //         this.emailLogs = result.EmailLogs;
  //         this.total = result.TotalCount;
  //         this.isLoading = false;
  //       },
  //       error: (err) => {
  //         this.isLoading = false;
  //         console.log(err);
  //       }
  //     }
  //   )
  // }

  changePage($event:any){
    this.currentPage.set($event);
    // this.getEmailLogs();
  }

  setOrder(orderBy:string)
  {
    this.orderBy.set(orderBy);
    this.isReversed.set(!this.isReversed());
    //this.getEmailLogs();
  }

}
