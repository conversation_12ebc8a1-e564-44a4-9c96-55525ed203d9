
<!-- place holder -->
@if(isDocumentLoading()){
	<div class="row placeholder-glow">
		<div class="col-12 my-4">
			<span class="placeholder col-12"  style="height: 50px;"></span>
		  </div>
	</div>
<!-- edit doc -->
}@else {
	<form [formGroup]="documentEditGroup" (submit)="saveEdit($event)">
		<div class="row bg-light p-3">
			<!-- type -->
			<div class="col-12 col-md-3">
				<div class="mb-3">
					<label class="form-label" for="docCategory">Type*</label>
					<select class="form-control" name="docCategory" formControlName="docCategory">
						@for(category of documentCategories(); track $index) {
							<option [value]="category.Name">
								{{category.Name}}
							</option>
						}
					
					</select>
				</div>
			</div>
			<!-- type -->
			<div class="col-12 col-md-3">
				<div class="mb-3">
					<label class="form-label" for="docTitle">Name*</label>
					<input type="text" formControlName="title" class="form-control"
						name="docTitle" />
				</div>
			</div>
			@if(document()?.Doc?.Storage?.FileExtension !== 'pdf') {
				<div class="alert alert-warning my-3">
					This document has the file extension of '{{ document()?.Doc?.Storage?.FileExtension }}'
					and is not a 'pdf' document. Page numbers and page dimensions not required
				</div>
			}@else {
				<!-- no. of pages -->
				<div class="col-12 col-md-3">
					<div class="mb-3">
						<label class="form-label" for="numberOfPages">No. of Pages</label>
						<input type="number" class="form-control" name="numberOfPages" formControlName="numberOfPages"/>
					</div>
				</div>
				<!-- page dimensions -->
				<div class="col-12 col-md-3">
					<div class="mb-3">
						<label class="form-label" for="pageDimensions">Page Dimensions</label>
						<select class="form-control" name="pageDimensions" formControlName="pageDimensions">
							@for (dimension of pageDimensions(); track $index) {
								<option [value]="dimension.Display">
									{{dimension.Display}}
								</option>
							}					
						</select>
					</div>
				</div>
			}
			<div class="col-12 d-flex justify-content-md-end">
				<button class="btn btn-secondary me-2" (click)="cancel()">Cancel</button>
				<button class="btn btn-primary" type="submit"
					[disabled]="!documentEditGroup.valid">
					@if(isDocumentUpdating){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Update
				</button>
			</div>	
		</div>
	</form>
}
