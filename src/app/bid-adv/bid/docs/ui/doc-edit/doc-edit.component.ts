import { CommonModule } from '@angular/common';
import { Component, effect, inject, input, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { tap } from 'rxjs';
import { BidsAdvDocsService } from 'src/app/bid-adv/shared/data-access/bids-docs.service';
import {  Category, Dimension, DocInfo, ProjectFile } from 'src/app/bid-adv/shared/interfaces/bids-docs';

@Component({
    selector: 'app-doc-edit',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './doc-edit.component.html',
    styleUrl: './doc-edit.component.css'
})
export class DocEditComponent {
  projectId = input.required<string>();
  document = input.required<DocInfo>();
  toastrService = inject(ToastrService);
  projectDocsService = inject(BidsAdvDocsService);
  documentCategories = this.projectDocsService.documentCategories;
  pageDimensions = this.projectDocsService.pageDimensions;
  isDocumentUpdating = false;
  isDocumentLoading = signal<boolean>(true);

  documentEditGroup = new FormGroup({
    title: new FormControl<string | null>(null, Validators.required),
    pageDimensions: new FormControl<string | null>(null),
    numberOfPages: new FormControl<number | null>(null),
    docCategory: new FormControl<string | null>(null)
  });

  documentUpdateEffect = effect(() => {
    if(this.projectDocsService.updatedDocument()){
      if(this.projectDocsService.updatedDocument()?.FileId === this.document()?.Doc?.FileId){
        this.isDocumentUpdating = false;      
        this.documentEditGroup.get('title')?.enable();
        this.documentEditGroup.get('pageDimensions')?.enable();
        this.documentEditGroup.get('numberOfPages')?.enable();
        this.documentEditGroup.get('docCategory')?.enable();
      }
    }
  });

  signalDoc = toSignal(toObservable(this.document).pipe(
    tap(document => {
    if(document){
      this.documentEditGroup.patchValue({
        title:document.Doc?.Title,
        pageDimensions: document.Doc?.Dimensions?.Display,
        numberOfPages: document.Doc?.NumberOfPages,
        docCategory: document.Doc?.Category?.Name
      });

      this.isDocumentLoading.set(false);
    }
    })
  ));


  saveEdit(evt:any){
    this.isDocumentUpdating = true;
    this.documentEditGroup.get('title')?.disable();
    this.documentEditGroup.get('pageDimensions')?.disable();
    this.documentEditGroup.get('numberOfPages')?.disable();
    this.documentEditGroup.get('docCategory')?.disable();
    var fileId =  this.document()?.Doc?.FileId as string;
    if(fileId){
      const updateInfo =this.documentEditGroup.getRawValue();
      this.projectDocsService.updateDocument(this.projectId(), fileId, updateInfo);
    }else{
      this.isDocumentUpdating = false;
      console.log("FileId is missing");
      
    }
  }

  cancel(){
    this.document().IsEditing = false;
  }
}
