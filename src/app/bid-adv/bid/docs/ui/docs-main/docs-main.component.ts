import { CommonModule } from '@angular/common';
import { Component, OnD<PERSON>roy, OnInit, effect, inject, input, viewChild } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import ObjectID from 'bson-objectid';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subscription, takeUntil, tap } from 'rxjs';
import { BidsAdvDocsService } from 'src/app/bid-adv/shared/data-access/bids-docs.service';
import { Advertisement } from 'src/app/bid-adv/shared/interfaces/advertisement';
import { DocumentCategory, FileInfo, ProjectFile, DocInfo, PdfDocumentMetaData, GetDocumentInfoResponse, Dimension } from 'src/app/bid-adv/shared/interfaces/bids-docs';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { GlobalDataService, TimeZone } from 'src/app/shared/data-access/global-data-service';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { PrimeCalendarCustomComponent } from 'src/app/shared/ui/prime-calendar-custom/prime-calendar-custom.component';
import { TimePickerAltComponent } from 'src/app/shared/ui/time-picker-alt/time-picker.component';
import { FileSize } from 'src/app/shared/utils/pipes/files-pipe';
import { DocSkeletonComponent } from '../doc-skeleton/doc-skeleton.component';
import { DocEditComponent } from '../doc-edit/doc-edit.component';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';
import { EBidProjectService } from 'src/app/bid-adv/shared/data-access/bids-ebid-project.service';

@Component({
    selector: 'app-docs-main',
    imports: [CommonModule, ReactiveFormsModule, PrimeCalendarCustomComponent, TimePickerAltComponent, FileSize, DocSkeletonComponent, DocEditComponent],
    providers: [NgForm],
    templateUrl: './docs-main.component.html',
    styleUrl: './docs-main.component.css'
})
export class DocsMainComponent implements OnDestroy, OnInit{

  project = input<Advertisement | null>(null);
  bidDateTimePicker = viewChild.required<TimePickerAltComponent>("bidDateTimePicker");

  ////////////////////Services/////////////////////
  aRoute = inject(ActivatedRoute);
  router = inject(Router);  
  toastrService = inject(ToastrService);
  projectDocsService = inject(BidsAdvDocsService);
  globalDataService = inject(GlobalDataService);
  confirmService = inject(ConfirmService);
  awsService = inject(LambdaAWSService);
  advertisementService = inject(BidsAdvService);  

  ////////////////////ToObservables/ToSignals/////////////////////


  ////////////////////Signals/////////////////////
  documentCategories = this.projectDocsService.documentCategories;
  pageDimensions = this.projectDocsService.pageDimensions;
  isDocumentsLoading = this.projectDocsService.isProjectDocumentsLoading;
  timeZones = this.globalDataService.timeZones;

  ////////////////////Effects/////////////////////


  ////////////////////Local Variables/////////////////////

  selectedCategory: DocumentCategory | null = null;
  needMoreInfo: boolean = false;
  onlineBidStatus: any;
  isSaving: boolean = false;
  isGatheringDocumentInfo: boolean = false;
  isSavingDocToProject: boolean = false;
  allowChangeBidDate: boolean = false;
  gatheredDocumentInfo: string | null = null;
  file: ProjectFile | null = null;
  selectedFile: File | null = null;
  documents: Array<DocInfo> = [];

  docMetaData: PdfDocumentMetaData = {} as PdfDocumentMetaData;
  WarningMessage: string = ""
  documentFormGroup: FormGroup = new FormGroup({
    name: new FormControl('', Validators.required),
    documentUploader: new FormControl(null, Validators.required),
    documentUploaderFileInfo: new FormControl(null, Validators.required),
    category: new FormControl('', Validators.required),
    notifyUsers: new FormControl(true),
    confirmAddenda: new FormControl(false),
    changeBidDate: new FormControl(false),
    bidDateTime: new FormControl(null),
    bidTimeZone: new FormControl(null),
    s3Key: new FormControl(null),
    s3Bucket: new FormControl(null),
    numberOfPages: new FormControl(null),
    pageSize: new FormControl(null),
    bidDateTimeInfo: new FormControl(null),
  });  
  needMoreInfoGroup: FormGroup = new FormGroup({
    pageSize: new FormControl(null, Validators.required),
    numberOfPages: new FormControl(null, Validators.required),
  });
  uploadFileSubscription: Subscription | null = null;
  uploadFileProgressSubscription: Subscription | null = null;
  downloadFileSubscription: Subscription | null = null;
  metaDocumentSubscription: Subscription | null = null;
  routerSubscription: Subscription | null = null;

  ///////////////////METHODS///////////////////////

  setupFormEvents() {
    this.documentFormGroup.get('category')?.valueChanges.subscribe({
      next: (value) => {
        if (value) {
          this.documentCategoryChanged(value);
        }
      },
      error: (err) => {
        console.log(err);
      }
    });

    this.documentFormGroup.get('confirmAddenda')?.valueChanges.subscribe({
      next: (value) => {
  
      },
      error: (err) => {
        console.log(err);
      }
    });

    this.documentFormGroup.controls['changeBidDate'].valueChanges.subscribe({
      next: (value) => {
        if (value) {
          this.setupNewBidDate().subscribe({
            next: () => {
              if (value) {
                this.documentFormGroup.get('bidDateTime')?.addValidators([Validators.required]);
                this.documentFormGroup.get('bidTimeZone')?.addValidators([Validators.required]);               
      
              }else{
                this.documentFormGroup.get('bidDateTime')?.clearValidators();
                this.documentFormGroup.get('bidTimeZone')?.clearValidators();
              }
            },
            error: (err) => {
              console.log(err);
            }
          });
        }
      },
      error: (err) => {
        console.log(err);
      }
    });

    this.documentFormGroup.get('bidDateTime')?.valueChanges.subscribe({
      next: (value) => {
        if(value){                    
          this.bidDateTimePicker()?.showTimeDisplay(value);  
          
          var d = new Date(value);
          this.documentFormGroup?.get('bidDateTimeInfo')?.setValue({ year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate(), hour: d.getHours(), minute: d.getMinutes(), second: 0 });
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  constructor() { 

    this.routerSubscription = this.router.events.subscribe(event => {
        if (event instanceof NavigationStart) {
          if((this.uploadFileSubscription != null && this.uploadFileSubscription.closed == false) 
            || (this.downloadFileSubscription != null && this.downloadFileSubscription.closed == false)
          || (this.metaDocumentSubscription != null && this.metaDocumentSubscription.closed == false)){           
            if (!confirm('You have not finished uploading. Do you want to leave?')) {
              this.router.navigateByUrl(this.router.url);
            }              
          }
        }
      });


    effect(() => {
      if (this.project()) {
        this.setupFormEvents();
      }
    });

    effect(() => {
      const documents = this.projectDocsService.currentProjectBidDocs();
      if(documents){
         this.isSavingDocToProject = false;
        this.isGatheringDocumentInfo = false;      
        this.documents = [];
        if(documents){     
          for(let doc of documents){
            const document: DocInfo = {
              Doc: doc,
              IsDeleting: false,
              IsEditing: false,
              IsPurchasable: (doc.Category) ? this.getIsPurchasable(doc.Category.Name) : false,
              SelectedCategory: null,
              SelectedDimension: null,
              IsUpdating: false,
              IsDownloading: false,
              DocEditing: null
            };

            
            this.documents.push(document);
          }
        }
      }
    });

      
    this.projectDocsService.getPageDimensions();
    this.projectDocsService.getDocumentCategories();
  }
  ngOnInit(): void {
    
  }
  ngOnDestroy(): void {
    this.cancelUpload();
    this.routerSubscription?.unsubscribe();
  }

  cancelUpload() {
    this.metaDocumentSubscription?.unsubscribe();
    this.uploadFileSubscription?.unsubscribe();
    this.downloadFileSubscription?.unsubscribe();
    this.uploadFileSubscription = null;
    this.metaDocumentSubscription = null;
    this.downloadFileSubscription = null;
    this.uploadFileProgressSubscription?.unsubscribe();    
  }


  editFile(doc: DocInfo) {
    doc.IsEditing = true;
  }

  closeUploadInfo() {
    //this.setupNewFile();    
    this.documentFormGroup.reset();
    this.documentFormGroup.get('notifyUsers')?.setValue(true);
    this.isSaving = false;
    this.gatheredDocumentInfo = null;
    this.isGatheringDocumentInfo = false;
    this.needMoreInfo = false;
  }



  deleteDoc(doc: DocInfo) {
    this.confirmService.open("Are you sure you want to delete this file?").result.then(result => {
      if(result === "yes"){
        doc.IsDeleting = true;
        if (this.project()) {
          this.projectDocsService.deleteDocument(this.project()?.Id as string, doc.Doc?.FileId as string);
        } else {
          this.toastrService.error("project not found");
        }
      }
 
    });
  }

  setupNewBidDate(): Observable<any> {
    return new Observable<any>(obs => {          
          if (this.project()) {
            if (this.project()?.BidDetails) {
              if (this.project()?.BidDetails.BidDateTimeInfo) {
                if (this.project()?.BidDetails?.BidDateTimeInfo.Day && this.project()?.BidDetails?.BidDateTimeInfo?.Month && this.project()?.BidDetails?.BidDateTimeInfo?.Year) {

                  var bidDate = new Date(this.project()?.BidDetails.BidDateTimeInfo.Year as number, this.project()?.BidDetails.BidDateTimeInfo.Month as number, this.project()?.BidDetails.BidDateTimeInfo.Day as number, 0, 0, 0, 0);

                  if (this.project()?.BidDetails.BidDateTimeInfo.Hour != null && this.project()?.BidDetails.BidDateTimeInfo.Minute != null) {
                    bidDate.setHours(this.project()?.BidDetails.BidDateTimeInfo.Hour as number);
                    bidDate.setMinutes(this.project()?.BidDetails.BidDateTimeInfo.Minute as number);
                  }

                  this.documentFormGroup.controls['bidDateTime'].setValue(bidDate);

                }

                let bidTimeZone = this.timeZones()?.find(x => x.Value === this.project()?.BidDetails?.BidDateTimeInfo?.TimeZone?.Value);

                if (bidTimeZone) {
                  this.documentFormGroup.controls['bidTimeZone'].setValue(bidTimeZone.ZoneId);
                }

              }
            }

            obs.next();
            obs.complete();
          } else {
            console.log("setupNewBidDate|project not found");
            obs.error("project not found");
          }


        });
    
  }

  getIsPurchasable(docType: string): boolean {
    const category = this.documentCategories()?.find(x => x.Name === docType);
    if (category) {
      return category.IsPurchasable;
    }     
    
    return false;
    
  }

  downloadFile(file: DocInfo) {
    file.IsDownloading = true;
    const key = `Projects/${this.project()?.Id}/${file.Doc?.FileId}.${file.Doc?.Storage?.FileExtension}`;
    const fileName = `${file.Doc?.Title}.${file.Doc?.Storage?.FileExtension}`;

    this.downloadFileSubscription = this.projectDocsService.getDocumentDownloadUrl(this.project()?.Id as string, key, fileName).subscribe({
      next: (response) => {
        file.IsDownloading = false;
        document.location.href = response.PreSignedUrl;
      },
      error: (err) => {
        file.IsDownloading = false;
        console.log(err);
      }
    })
  }

  onFileChange(event: any) {
    if (event?.target?.files && event?.target?.files?.length) {
      const file = event.target.files[0];

      const ext = file.name.split('.').pop();
      const fileId = ObjectID().toString();

      let fileInfo: FileInfo = {
        File: file,
        Name: file.name,
        Size: file.size,
        isComplete: false,
        isProgressing: false,
        uploadProgress: 0,
        FileId: fileId,
        Extension: ext,
        Key: null
      };


      this.documentFormGroup.get('documentUploaderFileInfo')?.setValue(fileInfo);
    }
  }

  uploadFile(event: any) {
    this.isSaving = true;
    var file = this.documentFormGroup.get('documentUploaderFileInfo')?.value as FileInfo;

    if (!file) {
      return;
    }

    const key = `Projects/${this.project()?.Id}/${file.FileId}.${file.Extension}`;

    this.uploadFileSubscription = this.projectDocsService.getDocumentUploadUrl(this.project()?.Id as string, key, file.File?.type).subscribe(result => {

      this.uploadFileProgressSubscription = this.awsService.uploadFileWithSignedURLWithProgress(result.PreSignedUrl, file.File).subscribe({
        next: (event) => {
          if (event && typeof event === 'number') {
            file.isProgressing = true;
            file.uploadProgress = +event;
          } else if (file.uploadProgress === 100 && !event) {
            file.isProgressing = false;
            file.isComplete = true;
  
            this.documentFormGroup.get("s3Key")?.setValue(key);
            this.documentFormGroup.get("s3Bucket")?.setValue(result.Bucket);
  
            this.finalizeUpload(key);
          }
        }, error: (err) => {
          file.isProgressing = false;
          file.isComplete = false;
        }
      }); 
      
    });

  }

  finalizeUpload(s3Key: string) {
    try {
      var file = this.documentFormGroup.get('documentUploaderFileInfo')?.value as FileInfo;
      this.gatheredDocumentInfo = null;


      if (file) {
        if (file.Extension === "pdf") {
          this.gatheredDocumentInfo = "loading";
          this.metaDocumentSubscription = this.getDocumentMetaData(s3Key).subscribe({
            next: (metaData: PdfDocumentMetaData | null) => {
              if (metaData) {
                this.gatheredDocumentInfo = "success";

                if (!metaData.NeedsReview) {
                  this.needMoreInfo = false;
                  this.documentFormGroup.get('pageSize')?.setValue(metaData.PageSize?.Display);
                  this.documentFormGroup.get('numberOfPages')?.setValue(metaData.NumberOfPages);

                  this.saveDoc();                  
                } else {
                  this.needMoreInfo = metaData.NeedsReview;
                }
              }
            },
            error: (err) => {
              this.gatheredDocumentInfo = "error";
              console.log(err);
              this.needMoreInfo = true;
            }
          });
        } else {
          this.gatheredDocumentInfo = "not_needed";
          this.needMoreInfo = false;
          this.saveDoc();
        }
      }
    } catch (error) {      
      this.gatheredDocumentInfo = "error";
      this.needMoreInfo = false;
      console.log(error);
    }

  }

  getDocumentMetaData(s3Key: string): Observable<PdfDocumentMetaData | null> {
    return new Observable<PdfDocumentMetaData>(obs => {
      if (s3Key) {
        this.metaDocumentSubscription = this.projectDocsService.getDocumentMetaData(this.project()?.Id as string, s3Key).subscribe({
          next: (response: GetDocumentInfoResponse) => {
            obs.next(response.MetaData);
            obs.complete();
          },
          error: (err) => {
            this.gatheredDocumentInfo = "error";
            console.log(err);
            obs.error(err);
          }
        });
      } else {
        obs.error("no key found");
      }
    });

  }


  saveDocNeedMoreInfo(){
    var pageSize = this.needMoreInfoGroup.get("pageSize")?.value;
    var numberOfPages = this.needMoreInfoGroup.get("numberOfPages")?.value;

    if(pageSize && numberOfPages){
      this.documentFormGroup.get('pageSize')?.setValue(pageSize);
      this.documentFormGroup.get('numberOfPages')?.setValue(numberOfPages);
      this.saveDoc();
    }
  }
  saveDoc() {
    this.needMoreInfo = false;    
    this.isSavingDocToProject = true;
    this.saveFile();
  }

  saveFile(){
    var file = this.documentFormGroup.get('documentUploaderFileInfo')?.value as FileInfo;
    if (!file) {
      throw Error("file is not found");
    }

    var data = this.documentFormGroup.getRawValue();   
    
    this.projectDocsService.addDocument(this.project()?.Id as string, data);

  }

  documentCategoryChanged(category: string) {
    if (category === "Addenda" || category === "Plan Revisions") {
      this.allowChangeBidDate = true;
      this.documentFormGroup.get('notifyUsers')?.setValue(true);
      this.documentFormGroup.get('notifyUsers')?.disable();
    } else {
      this.allowChangeBidDate = false;
      this.documentFormGroup.get('notifyUsers')?.enable();
    }
  }
}
