<form [formGroup]="documentFormGroup" (submit)="uploadFile($event)">
	<!-- upload documents (yes, paid)-->
	@if(!isSaving){
		<section class="border p-3 mb-3">
			<h3 class="page-title fs-6">Upload Document</h3>
			<div class="row">
				<!-- doc type -->
				<div class="col-12 col-md-6 col-xl-4">
					<div class="mb-3 mb-md-3">
						<label for="category" class="form-label">Type*</label>
						<select class="form-control" name="category" id="category" formControlName="category">
							@for (category of documentCategories(); track $index) {
							<option [value]="category.Name">
								{{category.Name}}
							</option>
							}
						</select>
					</div>
				</div>
				<!-- doc name -->
				<div class="col-12 col-md-6 col-xl-4">
					<div class="mb-3 mb-md-3">
						<label for="exampleInputEmail1" class="form-label">Name*</label>
						<input type="text" formControlName="name" class="form-control" name="title"
							[ngClass]="{'input-is-invalid': documentFormGroup.controls['name']?.errors?.['required'] && documentFormGroup.controls['name']?.dirty}" />
					</div>
				</div>
				<!-- choose file -->
				<div class="col-12 col-md-6 col-xl-4">
					<div class="mb-3 mb-md-3">
						<label for="exampleInputEmail1" class="form-label">Choose File*</label>
						<input #docsUploader class="form-control" type="file" id="docsUploader" (change)="onFileChange($event)"
							formControlName="documentUploader" name="documentUploader" />
					</div>
				</div>
				<!-- notify users -->
				<div class="col-12">
					<div class="mb-3 mb-md-0">
						<div class="form-check">
							<input class="form-check-input" type="checkbox" name="notifyUsers" id="notifyUsers"
								formControlName="notifyUsers" />
							<label class="form-check-label" for="notifyUsers">
								Notify plan holders of document upload.
							</label>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- options -->
		@if(allowChangeBidDate){
		<section class="border p-3 mb-3">
			<h3 class="page-title fs-6">Addendum Options</h3>
			<!-- Ask bidders to confirm they received notification? -->
			<div class="mb-2">
				<div class="form-check">
					<input class="form-check-input" type="checkbox" name="confirmAddenda" id="confirmAddenda"
						formControlName="confirmAddenda" />
					<label class="form-check-label" for="confirmAddenda">Ask bidders to confirm they received
						notification?</label>
				</div>
			</div>
			<!-- Change bid date? -->
			<div>
				<div class="form-check">
					<input class="form-check-input" type="checkbox" name="changeBidDate" id="changeBidDate"
						formControlName="changeBidDate">
					<label class="form-check-label" for="changeBidDate">Change bid date?</label>
				</div>
			</div>
		</section>
		}
		<!-- new bid date -->
		@if(documentFormGroup.get('changeBidDate')?.value){
		<section class="border p-3 mb-3">
			<h3 class="page-title fs-6">New Bid Date</h3>
			<div class="row">
				<!-- date -->
				<div class="col-12 col-md-4">
					<div class="mb-3 mb-md-0">
						<label class="form-label" for="bidDate">Date</label>
						<div>
							<app-prime-calendar-custom id="bidDate" name="bidDate"
								[formControl]="documentFormGroup.get('bidDateTime')"></app-prime-calendar-custom>
						</div>
					</div>
				</div>
				<!-- time -->
				<div class="col-12 col-md-4">
					<div class="mb-3 mb-md-0">
						<label class="form-label" for="bidTime">Time</label>
						<div>
							<app-time-picker-alt #bidDateTimePicker id="bidTime" name="bidTime"
								[formControl]="documentFormGroup.get('bidDateTime')"></app-time-picker-alt>
						</div>
					</div>
				</div>
				<!-- time zone -->
				<div class="col-12 col-md-4">
					<div class="mb-3 mb-md-0">
						<label class="form-label">Time Zone*</label>
						<select class="form-control" name="bidDateTimeZone" id="bidDateTimeZone" style="max-width:300px;"
							formControlName="bidTimeZone">
							<option *ngFor="let zone of timeZones()" [value]="zone.ZoneId">
								{{zone.Name}}
							</option>
						</select>
					</div>
				</div>
			</div>
		</section>
		}
		<!-- online bid warning -->
		@if(onlineBidStatus?.IsEnabled){
		<section class="bg-warning border rounded p-3 mb-3" *ngIf="onlineBidStatus?.IsEnabled">
			<span>
				This project is an online bid. If you upload new bid docs, plans, addenda,
				or
				plan revisions, bidders will need to acknowledge the new file and re-sumbit
				their bid.
				All users who are bidding this project will be notified.
			</span>
		</section>
		}	
		<!-- upload button -->
		<section class="d-flex justify-content-end mb-3">
			<button type="submit" class="btn btn-outline-dark" [disabled]="(isSaving || !documentFormGroup.valid)">Upload
			</button>
		</section>
	}
	<!-- file upload box: needs to be styled -->
	 @else {
		<div class="row mb-3">
			<div class="col-12">
				<div class="card mb-4">
					<div class="card-header d-flex justify-content-between">
						<div>
							<span style="font-weight:bold">File Upload Information</span>
						</div>
						<div class="btn-group">
							<button class="btn btn-outline-secondary justify-content-end"
							(click)="closeUploadInfo()">Close</button>
						
							<!-- <button class="btn btn-outline-primary mx-2" (click)="cancelUpload()">
								Cancel
							</button> -->
						</div>
					</div>
					<div class="card-body">
					
						<div class="row align-items-center">
							<div class="col-9 mb-1 mb-md-0 col-md-5">
								{{documentFormGroup.get('documentUploaderFileInfo')?.value?.Name}}
							</div>
							<div class="col-3 mb-1 mb-md-0 col-md-2 text-end">
								{{documentFormGroup.get('documentUploaderFileInfo')?.value?.Size | filesize }}
							</div>
							<div class="col-9 mb-1 mb-md-0 col-md-4">
								<div class="progress" style="height:30px;">
									<div class="progress-bar bg-success" role="progressbar"
										[style.width]="documentFormGroup.get('documentUploaderFileInfo')?.value?.uploadProgress + '%'" aria-valuemin="0"
										aria-valuemax="100"></div>
								</div>
							</div>
							<div class="col-3 mb-1 mb-md-0 col-md-1 text-end">
								@if(documentFormGroup.get('documentUploaderFileInfo')?.value?.uploadProgress < 100 && documentFormGroup.get('documentUploaderFileInfo')?.value?.isProgressing){								
									{{ documentFormGroup.get('documentUploaderFileInfo')?.value?.uploadProgress }}%							
								}@else if(documentFormGroup.get('documentUploaderFileInfo')?.value?.uploadProgress === 100 && documentFormGroup.get('documentUploaderFileInfo')?.value?.isComplete){
										<i class="fas fa-check text-success"></i>
								}		
								
							</div>
						</div>
		
						@if(documentFormGroup.get('documentUploaderFileInfo')?.value?.isComplete){
								<div class="row my-2">
									@if(gatheredDocumentInfo === 'loading' || gatheredDocumentInfo === 'error' || gatheredDocumentInfo === 'success'){
										<div class="col-12 align-items-center">
											@if(gatheredDocumentInfo === 'loading'){
												<i class="fas fa-circle-notch fa-spin fa-2x text-success"></i>
											}
											
											@if(gatheredDocumentInfo === 'success'){
												<i class="fas fa-check text-success"></i>
											}
											
											@if(gatheredDocumentInfo === 'error'){
												<i class="fas fa-exclamation-triangle text-danger fa-2x"></i>
											}
											
											<span class="fs-5">
												Gathering Document Information
											</span>
										</div>
									}
									
								
									@if(!needMoreInfo && gatheredDocumentInfo === 'success'){
										<div class="col-12">
											<div class="alert alert-info">
												<div>
													Page Numbers: <span style="font-weight: bold;">
														{{ this.documentFormGroup.get('pageSize')?.value }}</span>
												</div>
												<div>
													Page Size: 
													<span style="font-weight: bold;">{{ this.documentFormGroup.get('numberOfPages')?.value }}</span>
												</div>
				
											</div>
										</div>
									}@else {
										<div class="col-12">
											<div class="alert alert-info">
												<div>
													<span style="font-weight: bold;">Not a PDF no document info needed</span>
												</div>
											</div>
										</div>
									}
					
								</div>
								<!--need pages and dimennsions-->
		
								@if(needMoreInfo && gatheredDocumentInfo === 'success'){
									<form [formGroup]="needMoreInfoGroup"  (submit)="saveDocNeedMoreInfo()">							
										<div class="row mb-4">
											<div class="col-12">
												<div class="bg-light border rounded p-3">
													<div class="text-danger mb-1">
														{{ WarningMessage }}
													</div>
													<div class="mb-3">
														<label for="pageNumberGather">Number of Pages</label>
														<input type="number" class="form-control" name="pageNumberGather" formControlName="numberOfPages" />
													</div>
													<div class="mb-3">										
														<label for="pageDimensions">Page Dimensions</label>
														<select class="form-control" name="pageDimensions" formControlName="pageSize">
															@for(dimension of pageDimensions(); track $index) {
																<option [value]="dimension.Display">
																	{{ dimension.Display }}
																</option>
															}													
														</select>
													</div>
													<div class="d-flex justify-content-end my-2">
														<button [disabled]="!needMoreInfoGroup.valid || isSavingDocToProject" type="submit" class="btn btn-success">
															@if(isSavingDocToProject){
																<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
															}

															Save Document
														</button>
													</div>
												</div>
											</div>
										</div>
									</form>
								}
				
								@if(isSavingDoc){
									<div class="row">
										<div class="col-12 align-items-center">
											@if(!gatheredSaveDocument){
												<i class="fas fa-circle-notch fa-spin fa-2x text-success"></i>
											}@else if(gatheredSaveDocument === 'success'){
												<i class="fas fa-check text-success"></i>
											}@else if(gatheredSaveDocument === 'error'){
												<i class="fas fa-exclamation-triangle text-danger fa-2x"></i>
											}							
											<span class="fs-5">
												Saving Document
											</span>
										</div>
									</div>
								}							
						
						}

		
						@if(gatheredDocumentInfo === 'success' || gatheredDocumentInfo === 'not_needed'){
							<div class="row">
								<div class="col-12 align-items-center">			
									@if(isSavingDocToProject){					
										<i class="fas fa-circle-notch fa-spin text-succeess fa-2x"></i>
										Saving Document to Project
									}@else {
										<i class="fas fa-check text-success"></i>
										File Saved to project!
									}
								</div>
							</div>
						}
						
					</div>
				</div>
			</div>
		</div>
	}
</form>
<!-- project documents  -->
 <section class="border p-3 mb-3">
	<h3 class="page-title fs-6">Project Documents</h3>
	@if(isDocumentsLoading()){
	<app-doc-skeleton></app-doc-skeleton>
	}@else {
	<ul class="list-group list-group-flush border border-start-0 border-end-0">
		<li class="list-group-item fw-bold">
			<div class="row">
				<!-- doc name col header -->
				<div class="col-6 col-md-4 col-xl-3 col-xxl-3">
					<span class="fw-bold">Name</span>
				</div>
				<!-- no. of pages col header -->
				<div class="col-12 d-none d-md-block col-md-2 col-xl-2 xxl-2">
					<span class="fw-bold">Pages</span>
				</div>
				<!-- dimensions col header -->
				<div class="col-12 d-none d-xxl-block col-xxl-2">
					<span class="fw-bold">Page Dimensions</span>
				</div>
				<!-- upload date col header -->
				<div class="col-12 d-none d-xl-block col-xl-3 col-xxl-2">
					<span class="fw-bold">Date Added</span>
				</div>
				<!-- doc type col header -->
				<div class="col-12 d-none d-md-block col-md-3 col-xl-3 col-xxl-2">
					<span class="fw-bold">Type</span>
				</div>
				<!-- additional menu options -->
				<div class="col-6 col-md-1 col-xl-1 col-xxl-1 d-flex justify-content-end">

				</div>
			</div>
		</li>
		@for(doc of documents; track $index){
		<li class="list-group-item">
			@if(doc.IsEditing) {
			<app-doc-edit [document]="doc" [projectId]="project()?.Id"></app-doc-edit>
			}@else {
			<div class="row align-items-center">
				<!-- doc name -->
				<div class="col-6 col-md-4 col-xl-3 col-xxl-3">
					@if(doc.IsDownloading){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}@else {
					<a href="javascript:void(0)" (click)="downloadFile(doc)">{{ doc.Doc?.Title}}</a>
					}
				</div>
				<!-- no. of pages -->
				<div class="col-12 d-none d-md-block col-md-2 col-xl-2 col-xxl-2">
					@if(doc.Doc?.Dimensions){
					<span>{{ doc.Doc?.NumberOfPages }}</span>
					}
				</div>
				<!-- dimensions -->
				<div class="col-12 d-none d-xxl-block col-xxl-2">
					@if(doc.Doc?.Dimensions){
					<span>{{ doc.Doc?.Dimensions?.Display }}</span>
					}
				</div>
				<!-- upload date -->
				<div class="col-12 d-none d-xl-block col-xl-3 col-xxl-2">
					<span>{{ doc.Doc?.DateStamp | date:'MM/dd/yyyy' }}</span>
				</div>
				<!-- doc type -->
				<div class="col-12 d-none d-md-block col-md-3 col-xl-3 col-xxl-2">
					<span>{{ doc.Doc?.Category?.Name}}</span>
				</div>
				<!-- additional menu options -->
				<div class="col-6 col-md-3 col-xl-1 col-xxl-1 d-flex justify-content-end">
					<div class="dropdown">
						<a class="text-dark" href="#" data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-ellipsis-v px-3 py-2"></i>
						</a>
						<ul class="dropdown-menu dropdown-menu-end">
							<li (click)="editFile(doc)">
								<a class="dropdown-item" href="javascript:void(0)">Edit</a>
							</li>
							<li (click)="deleteDoc(doc)">
								@if(doc.IsDeleting){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}@else {
								<a class="dropdown-item" href="javascript:void(0)">
									Delete
								</a>
								}
							</li>
							<li>
								@if(doc.IsDownloading){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}@else {
								<a class="dropdown-item" href="javascript:void(0)" (click)="downloadFile(doc)">
									Download
								</a>
								}
							</li>
							@if(doc.Doc?.ConfirmRequired){
							<li>
								<a class="dropdown-item" href="javascript:void(0)" (click)="viewFileAcks(doc)">Addendum
									Response Log</a>
							</li>
							}
						</ul>
					</div>
				</div>
			</div>
			}
		</li>
		}@empty {
		<li class="list-group-item" *ngIf="documents.length <= 0">
			You haven't uploaded any docuemnts yets.
		</li>
		}
		<!-- <li class="list-group-item" ng-if="!IsAllowedDeleteFile">
																<span class="text-danger">You do not have access to this feature.</span>
															</li> -->
	</ul>
	}
</section>
