import { Component, ElementRef, OnInit, ViewChild, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BidsAdvService } from '../../shared/data-access/bids.service';
import { DocsMainComponent } from './ui/docs-main/docs-main.component';
import { DocSkeletonComponent } from './ui/doc-skeleton/doc-skeleton.component';
import { BidAdvService } from '../../shared/data-access/bid.service';
import { BidsAdvDocsService } from '../../shared/data-access/bids-docs.service';

@Component({
    selector: 'app-project-docs',
    templateUrl: './project-docs.component.html',
    styleUrls: ['./project-docs.component.css'],
    standalone: true,
    imports: [CommonModule, FormsModule, ReactiveFormsModule, DocsMainComponent, DocSkeletonComponent]
})
export class ProjectDocsComponent implements OnInit {

  ////////////////////Inputs/ViewChilds/Outputs/////////////////////
  @ViewChild("docsUploader") filesComponentFileUploader: ElementRef | null = null;  

  ////////////////////Services/////////////////////
  bidsAdvService = inject(BidAdvService);  
  bidsAdvDocsService = inject(BidsAdvDocsService);
 

  ////////////////////Signals/////////////////////

  project = this.bidsAdvService.advertisement;
  isProjectLoading = this.bidsAdvService.isAdvertisementLoading



  constructor() {
 
    effect(() => {
     this.bidsAdvDocsService.projectId.set(this.bidsAdvService.projectId());
    })

      //this.setupNewFile();
  }

  ngOnInit(): void {

  }


}