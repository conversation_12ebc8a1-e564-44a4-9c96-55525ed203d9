import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DevBarComponent } from '../shared/ui/dev-bar/dev-bar.component';

import { BidsAdvService } from './shared/data-access/bids.service';

@Component({
    selector: 'app-bid-adv',
    templateUrl: './bid-adv.component.html',
    styleUrls: ['./bid-adv.component.css'],
    standalone: true,
    imports: [CommonModule, RouterModule, DevBarComponent],
    providers: [BidsAdvService]
})
export class BidAdvComponent {		
	constructor() {

	}

}
