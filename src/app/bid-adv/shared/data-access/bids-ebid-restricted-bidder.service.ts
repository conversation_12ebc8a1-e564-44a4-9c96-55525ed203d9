import { HttpClient } from "@angular/common/http";
import { computed, effect, Inject, inject, Injectable, Injector, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { RestrictedBidder, RestrictedBidders } from "../interfaces/restricted-bidders";
import { environment } from "src/environments/environment";
import { Planholder } from "../interfaces/bids-planholder";
import { of } from "rxjs";
import { BaseFormService } from "src/app/ebid/data-access/base-form-service";
import { BidOpsPlanholdersService } from "src/app/bid-opportunities/shared/data-access/bid-ops-planholders.service";
import { AuthService } from "src/app/shared/data-access/auth.service";
import { BidAdvService } from "./bid.service";


Injectable();
export class RestrictedBiddersService extends BaseFormService{

	client = inject(HttpClient);	
    bidsAdvService = inject(BidAdvService); 
	authService = inject(AuthService);
	projectId = signal<string | null>(null);
	isEnabled = computed(() => this.restrictedBiddersData()?.IsEnabled);
	restrictedBiddersData = computed(() => this.restrictedBiddersResource.value());
	isLoading = computed(() => this.restrictedBiddersResource.isLoading() || this.planholdersService.isLoading());
    planholdersService = inject(BidOpsPlanholdersService);
	planholders = computed(() => {
		return [...this.planholdersService.planholdersResponse()?.BiddingPlanholders as Array<Planholder>, ...this.planholdersService.planholdersResponse()?.NotBiddingPlanholders as Array<Planholder>];
	});    
	restrictedBidders = signal<Array<RestrictedBidder> | null>(null);    
	isSaving = signal<boolean>(false);
	planholderPage = signal<number>(1);
	planholderSearch = signal<string | null>(null);
	planholderSortBy = signal<string>("CompanyName");
	planholderLimit = signal<number>(50);
	totalPlanholders = computed(() => this.planholdersService.planholdersResponse()?.BiddingTotal as number);	
	constructor(){
		super(inject(Injector));
	}

	restrictedBiddersResource = rxResource({
		request: () => (this.projectId()),
		loader: (request) => {
			if(request.request){
				return this.client.get<RestrictedBidders>(`${environment.services_root_endpoints.adverts_ebid}/${this.projectId()}/restrict-bidders`)
			}

			return of(null);
		}
	});

	destroy() {
		this.projectId.set(null);
		this.restrictedBidders.set(null);
	}

	startEffects(){

		this.registerEffect(effect(() => {
			if(this.planholderLimit() || this.planholderPage() || this.planholderSearch() || this.planholderSortBy()){
				this.planholdersService.limit.set(this.planholderLimit());
				this.planholdersService.page.set(this.planholderPage());
				this.planholdersService.search.set(this.planholderSearch());
				this.planholdersService.sortBy.set(this.planholderSortBy());
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if(this.bidsAdvService.projectId()){
				this.planholdersService.projectId.set(this.bidsAdvService.projectId() as string);
				this.projectId.set(this.bidsAdvService.projectId() as string);
			}
		}, { injector: this.$effectsInjector as Injector }));

		this.registerEffect(effect(() => {
			if(this.planholders()){
				let restrictedBidders = new Array<RestrictedBidder>();
	
				this.authService.getSubId().then((subId) => {					
					for(let ph of this.planholders() as Array<Planholder>){
						if(subId === ph.CognitoUserId){	
							continue;
						}

						const rb = this.restrictedBiddersData()?.Restricted.find(userId => userId === ph.CognitoUserId);
		
						var nRestrictedBidder = new RestrictedBidder(ph.CognitoUserId, ph);
						
						if(rb){
							nRestrictedBidder.IsChecked = true;
						}else{
							nRestrictedBidder.IsChecked = false;
						}
		
						restrictedBidders.push(nRestrictedBidder);
					}
		
					this.restrictedBidders.set(restrictedBidders);
				});

				
			}
		}, { injector: this.$effectsInjector as Injector }));
	}
	saveRestrictedBidders(isEnabled: boolean){
		this.isSaving.set(true);
		const checkedUserIds = this.restrictedBidders()?.filter(rb => rb.IsChecked).map(rb => rb.UserId) as Array<string>;
		const request = {
			IsEnabled: isEnabled,
			UserIds: checkedUserIds
		};
		
		this.client.post<RestrictedBidders>(`${environment.services_root_endpoints.adverts_ebid}/${this.projectId()}/restricted-bidders`, request).subscribe({
			next: (data) => {
				this.restrictedBiddersResource.set(data);
				this.isSaving.set(false);
			},
			error: (err) => {
				console.log(err);
				this.isSaving.set(false);
			}
		});
	}

}