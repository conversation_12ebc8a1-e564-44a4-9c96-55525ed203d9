import { computed, effect, EffectRef, inject, Injectable, linkedSignal, signal } from "@angular/core";
import { EBid, EBidSection, EBidSectionTypes } from "../../../ebid/interfaces/ebid";
import { HttpClient } from "@angular/common/http";
import { BidderBidInfo, BidSectionInfo, GatheredBidderBidInfo } from "src/app/ebid/interfaces/bidder-bid-info";
import { environment } from "src/environments/environment";
import { ToastrService } from "ngx-toastr";
import { Observable, of } from "rxjs";
import { ProjectFile } from "../interfaces/bids-docs";
import { rxResource } from "@angular/core/rxjs-interop";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { FormGroup, FormControl, Validators, FormArray } from "@angular/forms";
import { BidAdvService } from "./bid.service";


@Injectable()
export class EBidProjectService {
	client = inject(HttpClient);
	toastrService = inject(ToastrService);		
	projectId = signal<string | null>(null);
	eBid = computed(() => this.ebidResource.value());
	isLoading = computed(() => this.ebidResource.isLoading());	


	startDate = computed(() => {
		if(!this.eBid()?.BeginDate)
			return null;
		
		var year = new Date(this.eBid()?.BeginDate as Date).getFullYear() as number;
		var month = new Date(this.eBid()?.BeginDate as Date).getMonth() as number + 1;
		var day = new Date(this.eBid()?.BeginDate as Date).getDate() as number;
		var hour = new Date(this.eBid()?.BeginDate as Date).getHours() as number;
		var minute = new Date(this.eBid()?.BeginDate as Date).getMinutes() as number;
		var timeZone = this.eBid()?.BeginTimeZone as string;


		return HelperTools.convertToTimeZoneDate(
			year,
			month,
			day,
			hour,
			minute,
			timeZone);
	});

	isBidStarted = computed(() => {
		const startDate = this.startDate();

		if(!startDate)
			return false;

		const now = new Date();
		return startDate && startDate < now;
	});
	error = computed(() => {
		const err = this.ebidResource.error() as any;
		return err ? err.error.message : null;
	  });;

	ebidResourceErrorEffect: EffectRef | null = null;

	constructor() {
	
	}

	startEffects(){
		this.ebidResourceErrorEffect = effect(() => {
			if(this.ebidResource.error()){
				this.toastrService.error("There was an issue loading the eBid", "Error");
			}
		});
	}

	endEffects(){
		this.ebidResourceErrorEffect?.destroy();
	}

	ebidResource = rxResource({
		request: () => this.projectId(),
		loader: (request) => {
			if(request){
				return this.getEBid(request.request as string);
			}
			return of(null);
		}
	});

	getEBid(projectId:string)  {
		if(projectId){
			return this.client.get<EBid>(`${environment.services_root_endpoints.adverts_ebid}/${projectId}`);
		}

		return of(null);
	  }

	  destroy(){		
		this.ebidResource.set(null);					
	  }


	saveBidderBidInfo(projectId: string, bidderInfo: GatheredBidderBidInfo): Observable<BidderBidInfo>{		
		if(bidderInfo.Id){						
			return this.updateBidderBidInfo(projectId, bidderInfo.Id, bidderInfo);
		}else{
			return this.addNewBidderBidInfo(projectId, bidderInfo);	
		}
	}

	submitBid(projectId: string, ebidVersion: number, bidderInfo: GatheredBidderBidInfo): Observable<BidderBidInfo>{
		if(!projectId)
			throw new Error("ProjectId is required");		
			
		const request = new SubmitRequest(bidderInfo.SectionsData, ebidVersion);						

		return this.client.post<BidderBidInfo>(`${environment.services_root_endpoints.bid_ops_ebids}/${projectId}/submit`, request);
	}

	unSubmitBid(projectId: string,ebidVersion: number, bidderInfo: GatheredBidderBidInfo): Observable<BidderBidInfo>{
		if(!projectId)
			throw new Error("ProjectId is required");		
			
		const request = new SubmitRequest(bidderInfo.SectionsData, ebidVersion);						

		return this.client.post<BidderBidInfo>(`${environment.services_root_endpoints.bid_ops_ebids}/${projectId}/unsubmit`, request);
	}

	updateBidderBidInfo(projectId:string, bidInfoId:string, bidInfo: GatheredBidderBidInfo) : Observable<BidderBidInfo>{
		if(!projectId)
			throw new Error("ProjectId is required");
		if(!bidInfoId)
			throw new Error("BidInfoId is required");

		const bidderInfoRequest = new BidderBidInfoUpdateRequest(bidInfo.SectionsData);

		return this.client.patch<BidderBidInfo>(`${environment.services_root_endpoints.bid_ops_ebids}/${projectId}/bid/${bidInfoId}`,bidderInfoRequest);		
	}

	addNewBidderBidInfo(projectId:string, bidInfo:GatheredBidderBidInfo): Observable<BidderBidInfo>{
		if(!projectId)
			throw new Error("ProjectId is required");

		const bidderInfoRequest = new BidderBidInfoUpdateRequest(bidInfo.SectionsData);
		return this.client.post<BidderBidInfo>(`${environment.services_root_endpoints.bid_ops_ebids}/${projectId}`, bidderInfoRequest);
	}

	bindPolymorphicProperties(sections: Array<EBidSection>){
		for(let section of sections){
			if(section.SectionType === EBidSectionTypes.BID_FORM){
				section._t  = ["BidFormSection"];
			}else if(section.SectionType === EBidSectionTypes.WORK_ORDER){
				section._t  = ["WorkOrderSection"];
			}else if(section.SectionType === EBidSectionTypes.COMPLETION_TIME){
				section._t  = ["CompletionTimeSection"];
			}else if(section.SectionType === EBidSectionTypes.ACKNOWLEDGE){
				section._t  = ["AcknowledgeSection"];
			}else if(section.SectionType === EBidSectionTypes.REQUIRED_UPLOADS){
				section._t  = ["RequiredUploadsSection"];
			}else if(section.SectionType === EBidSectionTypes.REQUIRED_DOWNLOADS){
				section._t  = ["RequiredDownloadsSection"];
			}else if(section.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE){
				section._t  = ["AddendaAcknowledgeSection"];
			}	
		}
	}

	saveBid(projectId:string, bid:EBid){
		
		this.bindPolymorphicProperties(bid.Sections);

		if(bid.Id){
			const request = {
				IsEnabled: bid.IsEnabled,
				BeginDate: bid.BeginDate,
				BeginTimeZone: bid.BeginTimeZone,
				Sections: bid.Sections
			};

			return this.client.patch<EBid>(`${environment.services_root_endpoints.adverts_ebid}/${projectId}/bid/${bid.Id}`, request);
		}else{
			var request ={
				EBid: bid
			};

			return this.client.post<EBid>(`${environment.services_root_endpoints.adverts_ebid}/${projectId}`, request);
		}
	}
}

export class SaveBidderInfoRequest{

	constructor(projectId:string, oldBidInfo: BidderBidInfo | null | undefined, newBidInfo:BidderBidInfo){
		this.ProjectId = projectId;
		this.OldBidInfo = oldBidInfo;
		this.NewBidInfo = newBidInfo;
	}

	ProjectId:string;
	OldBidInfo: BidderBidInfo | null | undefined;
	NewBidInfo:BidderBidInfo;
}

export class BidderBidInfoUpdateRequest{
	constructor(sections: Array<BidSectionInfo>){
		this.Sections = sections;
	}
	Sections: any[];
}

export class SubmitRequest{
	constructor(sections: Array<BidSectionInfo>, version: number){	
		this.ClientLastMainBidVersion = version;
		this.Sections = sections;
	}
	Sections: Array<BidSectionInfo>;
	ClientLastMainBidVersion:number;
}