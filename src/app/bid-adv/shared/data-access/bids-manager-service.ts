import { effect, inject, Injectable, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { FormGroup, FormControl, Validators, FormArray } from '@angular/forms';
import { ProjectConstants } from '../interfaces/bids-manager';
import { BidsAdvService } from './bids.service';
import { Advertisement, EmailAddress } from '../interfaces/advertisement';
import { NgbDate, NgbDateStruct, NgbTimeStruct } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment';
import { CivCastAccountService } from 'src/app/shared/data-access/civcast-account-service';
import { Router, ActivatedRoute } from '@angular/router';
import { BidAdvService } from './bid.service';



@Injectable()
export class ProjectManagerService {  
  client = inject(HttpClient);
  accountService = inject(CivCastAccountService);
  advertisementService = inject(BidAdvService);
  router = inject(Router);
  activatedRoute = inject(ActivatedRoute);
  isSaving = signal<boolean>(false);
  previewAdvertisement = signal<FormGroup | null>(null);
  emailPattern = "^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$";
  linkPattern = /(http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;  
  advertisement = this.advertisementService.advertisement;
  projectConstants = ProjectConstants;
  projectFormGroup: FormGroup = new FormGroup({
    lowBidName: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.LOW_BID_NAME)]}),
    lowBidAmount: new FormControl(null),    
    projectTitle: new FormControl(null, { validators: [Validators.required, Validators.maxLength(ProjectConstants.PROJECT_TITLE_LENGTH)]}),
    releaseDateInfo: new FormControl(null, Validators.required),
    releaseDate: new FormControl<NgbDateStruct>({ year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate()}, Validators.required),
    bidDateInfo: new FormGroup({
      bidDateTimeInfo: new FormControl(null, Validators.required),
      date: new FormControl(null, Validators.required),
      time: new FormControl(null, Validators.required),
      location: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.LOCATION)]}),
      notes: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.MAX_DEFAULT)]}),
      timeZone: new FormControl(null, Validators.required),
      link: new FormControl(null, {validators: [Validators.pattern(this.linkPattern)]})
    }),
    isTBA: new FormControl(false),
    preBidInfo: new FormGroup({
      bidDateTimeInfo: new FormControl(null),
      location: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.LOCATION)]}),
      notes: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.MAX_DEFAULT)]}),
      date: new FormControl(null),
      time: new FormControl(null),
      link: new FormControl(null, {validators: [Validators.pattern(this.linkPattern)]})
    }),
    internalId: new FormControl(null, {validators: [Validators.required, Validators.maxLength(ProjectConstants.INTERNALID)]}),
    detailsEstimate: new FormControl(null, Validators.maxLength(ProjectConstants.DETAILS_ESTIMATE)),
    typeOfWork: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.TYPE_OF_WORK)]}),
    owner: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.OWNER)]}),  
    customMapUrl: new FormControl(null),  
    locationInfo: new FormGroup({
      mapCode: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.MAP_CODE)]}),
      state: new FormControl(null, Validators.required),
      county: new FormControl(null, Validators.required)     
    }),
    mapData: new FormGroup({
      latitude: new FormControl(null),
      longitude: new FormControl(null)
    }),     
    scope: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.SCOPE)]}),
    notes: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.NOTES)]}),
    additionalNotes: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.NOTES)]}),  
    contactInfo: new FormGroup({
      companyName: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      firstName: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      lastName: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      email: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.EMAIL)]}),      
      address1: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      address2: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      city: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      state: new FormControl(null),
      zip: new FormControl(null),
      webSite: new FormControl(null, {validators: [Validators.pattern(this.linkPattern), Validators.maxLength(ProjectConstants.DEFAULT_CONTACT_INFO)]}),
      phone: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.PHONE_MAX)]}),
      fax: new FormControl(null, {validators: [Validators.maxLength(ProjectConstants.PHONE_MAX)]}),
    }),
    projectSettings: new FormGroup({
      isPrivate: new FormControl(false),
      uniqueId: new FormControl(null),
      password: new FormControl(null),
      showPlanholdersList: new FormControl(true),
      isVisible: new FormControl(true),
      isPlansOfficial: new FormControl(true),
    }),
    qa: new FormGroup({
      allowQuestions: new FormControl(false),
      expireQA: new FormControl(false),
      qaExpirationDateInfo: new FormControl(null),
      qaExpirationTimeInfo: new FormControl(null),
      qaExpirationDate: new FormControl(null),
      qaExpirationTime: new FormControl(null),
      qaExpirationTimeZone: new FormControl(null),        
      emails: new FormArray([])
    }) 
  });  

  projectEffect = effect(() => {
    if(this.advertisement()){
      this.setupFormGroup(this.advertisement() as Advertisement);
      this.setupFormChanges();
    }
  });

  setupIsPrivateValidators() {
    // this.projectFormGroup?.get('projectSettings.password')?.addValidators([Validators.required, Validators.maxLength(100)]);
    this.projectFormGroup?.get('projectSettings.uniqueId')?.addValidators([Validators.required, Validators.maxLength(100)]);

    // this.projectFormGroup?.get('projectSettings.password')?.updateValueAndValidity();
    this.projectFormGroup?.get('projectSettings.uniqueId')?.updateValueAndValidity();
  }

  setupExpireQAValidators() {
    this.projectFormGroup?.get('qa.qaExpirationDate')?.addValidators(Validators.required);
    this.projectFormGroup?.get('qa.qaExpirationTime')?.addValidators(Validators.required);
    this.projectFormGroup?.get('qa.qaExpirationTimeZone')?.addValidators(Validators.required);
  }

  setupFormChanges() {
    if (this.projectFormGroup) {
      this.projectFormGroup.get('qa.expireQA')?.valueChanges.subscribe({
        next: (value) => {
          if (this.projectFormGroup) {
            if (value) {
              this.setupExpireQAValidators();
            } else {
              this.projectFormGroup.get('qa.qaExpirationDate')?.clearValidators();
              this.projectFormGroup.get('qa.qaExpirationTime')?.clearValidators();
              this.projectFormGroup.get('qa.qaExpirationTimeZone')?.clearValidators();
            }

            this.projectFormGroup.get('qa.qaExpirationDate')?.updateValueAndValidity();
            this.projectFormGroup.get('qa.qaExpirationTime')?.updateValueAndValidity();
            this.projectFormGroup.get('qa.qaExpirationTimeZone')?.updateValueAndValidity();
          }

        },
        error: (err) => {
          console.log(err);
        }
      });

      this.projectFormGroup.get('projectSettings.isPrivate')?.valueChanges.subscribe({
        next: (value) => {
          if (this.projectFormGroup) {
            if (value) {
              this.setupIsPrivateValidators();
              this.gatherPrivateId();
            } else {
              // this.projectFormGroup.get('projectSettings.password')?.clearValidators();
              this.projectFormGroup.get('projectSettings.uniqueId')?.clearValidators();
            }
          }
        },
        error: (err) => {
          console.log(err);
        }
      });

      this.projectFormGroup.get('locationInfo.state')?.valueChanges.subscribe({
        next: (stateAbbreviation) => {
          if (stateAbbreviation) {            
            this.projectFormGroup?.get('locationInfo')?.get('county')?.setValue(null);
          } else {
            this.projectFormGroup?.get('locationInfo')?.get('county')?.setValue(null);
          }
        },
        error: (err) => {
          console.log(err);
        }
      });


      this.projectFormGroup.get('bidDateInfo.bidDateTimeInfo')?.valueChanges.subscribe({
        next: (value) => {
          if (value) {
            var cd = new Date(value);
            var t = this.projectFormGroup?.get('bidDateInfo.bidDateTimeInfo')?.status;
            var d = new Date(value);
            this.projectFormGroup?.get('bidDateInfo.date')?.setValue({ year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate() });
            this.projectFormGroup?.get('bidDateInfo.time')?.setValue({ hour: d.getHours(), minute: d.getMinutes(), second: 0 });
          } else {
            this.projectFormGroup?.get('bidDateInfo.date')?.setValue({ year: null, month: null, day: null });
            this.projectFormGroup?.get('bidDateInfo.time')?.setValue({ hour: null, minute: null, second: null });
          }
        },
        error: (err) => {
          console.log(err);
        }
      });

      this.projectFormGroup.get('preBidInfo.bidDateTimeInfo')?.valueChanges.subscribe({
        next: (value) => {
          if (value) {
            var d = new Date(value);
            this.projectFormGroup?.get('preBidInfo.date')?.setValue({ year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate() });
            this.projectFormGroup?.get('preBidInfo.time')?.setValue({ hour: d.getHours(), minute: d.getMinutes(), second: 0 });
          } else {
            this.projectFormGroup?.get('preBidInfo.date')?.setValue({ year: null, month: null, day: null });
            this.projectFormGroup?.get('preBidInfo.time')?.setValue({ hour: null, minute: null, second: null });
          }
        },
        error: (err) => {
          console.log(err);
        }
      });

      this.projectFormGroup.get('qa.qaExpirationDateInfo')?.valueChanges.subscribe({
        next: (value) => {
          if (value) {
            ;
            var d = new Date(value);
            this.projectFormGroup?.get('qa.qaExpirationDate')?.setValue({ year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate() });
          }
        },
        error: (err) => {
          console.log(err);
        }
      });


      this.projectFormGroup.get('qa.qaExpirationTimeInfo')?.valueChanges.subscribe({
        next: (value) => {
          if (value) {
            ;
            var d = new Date(value);
            this.projectFormGroup?.get('qa.qaExpirationTime')?.setValue({ hour: d.getHours(), minute: d.getMinutes(), second: 0 });
          }
        },
        error: (err) => {
          console.log(err);
        }
      });

      this.projectFormGroup.get('releaseDateInfo')?.valueChanges.subscribe({
        next: (value) => {
          if (value) {
            var d = new Date(value);
            this.projectFormGroup?.get('releaseDate')?.setValue({ year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate() });
          }
        },
        error: (err) => {
          console.log(err);
        }
      });
    }
  }



  setupFormGroup(project:Advertisement){
    const convertedDates = this.convertProjectDates(project);

    this.projectFormGroup.patchValue({
      lowBidName: project.LowBidName,
      lowBidAmount: project.LowBidAmount,
      projectTitle: project.ProjectTitle,
      releaseDateInfo: convertedDates?.releaseDateInfo,
      releaseDate: { year: convertedDates?.releaseDate?.year, month: convertedDates?.releaseDate?.month, day: convertedDates?.releaseDate?.day},
      bidDateInfo: {
        bidDateTimeInfo: convertedDates?.bidDateAndTimeInfo,
        date: { year: convertedDates?.bidDate?.year, month: convertedDates?.bidDate?.month, day: convertedDates?.bidDate?.day},
        time: convertedDates?.bidTime,
        location: project.BidDetails?.Location,
        notes: project.BidDetails?.Notes,
        timeZone: project.BidDetails?.BidDateTimeInfo?.TimeZone?.ZoneId ?? null,
        link: project.BidOpeningLink
      },
      isTBA: project.Permissions?.IsTBA,
      preBidInfo: {
        bidDateTimeInfo: convertedDates?.preBidDateAndTimeInfo,
        location: project.PreBidDetails?.Location,
        notes: project.PreBidDetails?.Notes,
        date: { year: convertedDates?.preBidDate?.year, month: convertedDates.preBidDate?.month, day: convertedDates.preBidDate?.day},
        time: convertedDates?.preBidTime,
        link: project.PreBidMeetingLink
      },
      internalId: project.InternalId,
      detailsEstimate: project.DetailsEstimate,
      typeOfWork: project.TypeOfWork,
      owner: project.Owner,
      locationInfo: {
        mapCode: project.MapCode,
        state: project.Location?.State?.Abbreviation ?? null,
        county: project.Location?.County?.Name ?? null
      },
      mapData: project.Location ? {
        latitude: project.Location?.MapData?.Latitude ?? null,
        longitude: project.Location?.MapData?.Longitude ?? null
      } : null,
      scope: project.Scope,
      notes: project.Notes,
      additionalNotes: project.AdditionalNotes,
      contactInfo: {
        companyName: project.ContactInfo?.Company?.Name,
        firstName: project.ContactInfo?.FirstName,
        lastName: project.ContactInfo?.LastName,
        email: project.ContactInfo?.Email,      
        address1: project.ContactInfo?.Company?.Address?.Address1,
        address2: project.ContactInfo?.Company?.Address?.Address2,
        city: project.ContactInfo?.Company?.Address?.City,
        state: project.ContactInfo?.Company.Address?.State?.Abbreviation,
        zip: project.ContactInfo?.Company?.Address?.Zip,
        webSite: project.ContactInfo?.Company?.Website,
        phone: project.ContactInfo?.Company?.Phone,
        fax: project.ContactInfo?.Company?.Fax
      },
      projectSettings: {
        isPrivate: project.Permissions?.IsPrivate || false,
        uniqueId: project.ProjectUniqueId,
        password: project.Password,
        showPlanholdersList: project.Permissions?.ShowPlanholdersList || true,
        isVisible: project.Permissions?.IsVisible || true,
        isPlansOfficial: project.Permissions?.IsPlansOfficial || true,
      },
      qa: {
        allowQuestions: project.Permissions?.AllowQuestions,
        expireQA: project.Permissions?.ExpireQA,
        qaExpirationDateInfo: convertedDates?.qaExpirationDateInfo,
        qaExpirationTimeInfo: convertedDates?.qaExpirationTimeInfo,
        qaExpirationDate: { year: convertedDates.qaExpirationDate?.year, month: convertedDates.qaExpirationDate?.month, day: convertedDates.qaExpirationDate?.day},
        qaExpirationTime: { hour: convertedDates.qaExpirationTime?.hour, minute: convertedDates.qaExpirationTime?.minute, second: convertedDates.qaExpirationTime?.second},
        qaExpirationTimeZone: project.QAExpirationDateTimeInfo?.TimeZone?.ZoneId
      }

    });

    this.projectFormGroup.get('projectSettings.uniqueId')?.disable();
    if(project.QuestionEmails){
 
      for(let emailAddress of project.QuestionEmails){
        this.addEmailToQAForm(emailAddress.DisplayName, emailAddress.Email);      
      }
    }

    this.projectFormGroup.markAsUntouched();
    this.projectFormGroup.markAsPristine();
  }

  convertProjectDates(project: Advertisement): any{

    var convertedDates = new ConvertedAdvertisementDates();

    if(project){
      if(project.ReleaseDateTimeInfo && project.ReleaseDateTimeInfo?.Date){
        if(project.ReleaseDateTimeInfo?.Day
          && (project.ReleaseDateMonthNatural || project.ReleaseDateTimeInfo?.Month)
          && project.ReleaseDateTimeInfo?.Year){
            convertedDates.releaseDate = new NgbDate(project.ReleaseDateTimeInfo.Year, project.ReleaseDateMonthNatural ?? project.ReleaseDateTimeInfo?.Month, project.ReleaseDateTimeInfo.Day);

            var month = (project.ReleaseDateMonthNatural) ? project.ReleaseDateMonthNatural  - 1 : project.ReleaseDateTimeInfo.Month;
            if(month !== undefined && month !== null){
              convertedDates.releaseDateInfo = new Date(project.ReleaseDateTimeInfo.Year, month, project.ReleaseDateTimeInfo.Day);
            }
        }else{
          convertedDates.releaseDate = this.parseStringDate(project.ReleaseDateTimeInfo.Date);
          convertedDates.releaseDateInfo = project.ReleaseDateTimeInfo.Date;
        }      
      }else{
        if(!project.ReleaseDateTimeInfo){
          convertedDates.releaseDate = new NgbDate(new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate());
          convertedDates.releaseDateInfo = new Date();
        }      
      }
  
      if(project.PreBidDetails 
        && project.PreBidDetails.BidDateTimeInfo 
        && project.PreBidDetails.BidDateTimeInfo.Year 
        && (project.PreBidDateMonthNatural || project.PreBidDetails.BidDateTimeInfo.Month)
        && project.PreBidDetails.BidDateTimeInfo.Day){
          convertedDates.preBidDate = new NgbDate(project.PreBidDetails.BidDateTimeInfo.Year, project.PreBidDateMonthNatural ?? project.PreBidDetails.BidDateTimeInfo.Month, project.PreBidDetails.BidDateTimeInfo.Day);
          convertedDates.preBidTime = { hour: project.PreBidDetails?.BidDateTimeInfo?.Hour, minute: project.PreBidDetails?.BidDateTimeInfo?.Minute, second: 0 } as NgbTimeStruct;

          var month = (project.PreBidDateMonthNatural) ? project.PreBidDateMonthNatural  - 1 : project.PreBidDetails.BidDateTimeInfo.Month;

          if(month !== undefined && month !== null){
            convertedDates.preBidDateAndTimeInfo = new Date(
              project.PreBidDetails.BidDateTimeInfo.Year, 
              month, 
              project.PreBidDetails.BidDateTimeInfo.Day,
              project.PreBidDetails.BidDateTimeInfo.Hour ?? 0, 
              project.PreBidDetails.BidDateTimeInfo.Minute ?? 0, 0);
          }

      }
    
      if(project.BidDetails 
        && project.BidDetails.BidDateTimeInfo      
        && project.BidDetails.BidDateTimeInfo.Year 
        && (project.BidDateMonthNatural || project.BidDetails.BidDateTimeInfo.Month)
        && project.BidDetails.BidDateTimeInfo.Day){
          convertedDates.bidDate = new NgbDate(project.BidDetails.BidDateTimeInfo.Year, project.BidDateMonthNatural ??  project.BidDetails.BidDateTimeInfo.Month, project.BidDetails.BidDateTimeInfo.Day);
          convertedDates.bidTime = { hour: project.BidDetails?.BidDateTimeInfo?.Hour, minute: project.BidDetails?.BidDateTimeInfo?.Minute, second: 0 } as NgbTimeStruct;

          var month = project.BidDateMonthNatural ? project.BidDateMonthNatural - 1 : project.BidDetails.BidDateTimeInfo.Month;

          if(month !== undefined && month !== null){
            convertedDates.bidDateAndTimeInfo = new Date(project.BidDetails.BidDateTimeInfo.Year, 
              month, 
              project.BidDetails.BidDateTimeInfo.Day,
              project.BidDetails.BidDateTimeInfo.Hour ?? 0, 
              project.BidDetails.BidDateTimeInfo.Minute ?? 0, 0);
          }  
      }
  
      if(project.QAExpirationDateTimeInfo
        && project.QAExpirationDateTimeInfo.Year
        && (project.QAExpirationDateMonthNatural || project.QAExpirationDateTimeInfo.Month)
        && project.QAExpirationDateTimeInfo.Day){
          convertedDates.qaExpirationDate = new NgbDate(project.QAExpirationDateTimeInfo.Year, project.QAExpirationDateMonthNatural ?? project.QAExpirationDateTimeInfo.Month, project.QAExpirationDateTimeInfo.Day);

          var month = project.QAExpirationDateMonthNatural ? project.QAExpirationDateMonthNatural - 1 : project.QAExpirationDateTimeInfo.Month;

          if(month !== undefined && month !== null){
            convertedDates.qaExpirationDateInfo = new Date(project.QAExpirationDateTimeInfo.Year, month, project.QAExpirationDateTimeInfo.Day);
          }          
      }
  
      if(project.QAExpirationDateTimeInfo){
        convertedDates.qaExpirationTime = { hour: project.QAExpirationDateTimeInfo.Hour, minute: project.QAExpirationDateTimeInfo.Minute, second: 0 } as NgbTimeStruct;
        convertedDates.qaExpirationTimeInfo = new Date(0, 1,1, (project.QAExpirationDateTimeInfo.Hour) ? project.QAExpirationDateTimeInfo.Hour: 0,
        (project.QAExpirationDateTimeInfo.Minute)? project.QAExpirationDateTimeInfo.Minute : 0, 0);
      }

 
  
    }
    return convertedDates;
  }
  

  addMarker(latitude: number, longitude: number){
    this.projectFormGroup?.get(['mapData','latitude'])?.setValue(latitude);
    this.projectFormGroup?.get(['mapData','longitude'])?.setValue(longitude);
  }



  deleteQAEmail(email: string) {
    if(this.projectFormGroup){
      let emailsFormArray = this.projectFormGroup.get(['qa','emails']) as FormArray;
      let idx = emailsFormArray.controls.findIndex(control => control.value.Email === email);
      if (idx !== -1) {
        emailsFormArray.removeAt(idx);
      }
    }
  }

  importProfile() {

    this.projectFormGroup.get('contactInfo')?.get('companyName')?.setValue(this.accountService.AccountProfile()?.Company?.Name);
    this.projectFormGroup.get('contactInfo')?.get('firstName')?.setValue(this.accountService.AccountProfile()?.FirstName);
    this.projectFormGroup.get('contactInfo')?.get('lastName')?.setValue(this.accountService.AccountProfile()?.LastName);
    this.projectFormGroup.get('contactInfo')?.get('email')?.setValue(this.accountService.AccountProfile()?.Email);
    this.projectFormGroup.get('contactInfo')?.get('address1')?.setValue(this.accountService.AccountProfile()?.Company?.Address?.Address1);
    this.projectFormGroup.get('contactInfo')?.get('address2')?.setValue(this.accountService.AccountProfile()?.Company?.Address?.Address2);
    this.projectFormGroup.get('contactInfo')?.get('city')?.setValue(this.accountService.AccountProfile()?.Company?.Address?.City);
    this.projectFormGroup.get('contactInfo')?.get('state')?.setValue(this.accountService.AccountProfile()?.Company?.Address?.State?.Abbreviation);
    this.projectFormGroup.get('contactInfo')?.get('zip')?.setValue(this.accountService.AccountProfile()?.Company?.Address?.Zip);
    this.projectFormGroup.get('contactInfo')?.get('webSite')?.setValue(this.accountService.AccountProfile()?.Company?.Website);
    this.projectFormGroup.get('contactInfo')?.get('phone')?.setValue(this.accountService.AccountProfile()?.Company?.Phone);
    this.projectFormGroup.get('contactInfo')?.get('fax')?.setValue(this.accountService.AccountProfile()?.Company?.Fax);



  }

  addEmailToQAForm(displayName:string, email: string){
    if(this.projectFormGroup){
      let emailsFormArray = this.projectFormGroup.get(['qa','emails']) as FormArray;
      emailsFormArray.push(new FormGroup({
        email: new FormControl(email, {validators: [Validators.pattern(this.emailPattern), Validators.required, Validators.maxLength(this.projectConstants.EMAIL)]}),
        displayName: new FormControl(displayName, {validators: [Validators.required, Validators.max(this.projectConstants.EMAIL)]})
      }));
    }
  }

  gatherPrivateId(){    
    if (!this.projectFormGroup.get('projectSettings.uniqueId')?.value) {
      this.setPrivateId();
    }    
  }

  setPrivateId(){
    this.client.get<PrivateIdResponse>(`${environment.services_root_endpoints.advertisements}/${this.advertisement()?.Id}/private-id`).subscribe({
      next: (result) => {
        if (result) {
          this.projectFormGroup?.get('projectSettings.uniqueId')?.setValue(result.PrivateId);
        } else {
          this.projectFormGroup?.get('projectSettings.uniqueId')?.setValue(null);
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  parseStringDate(date: string | null): NgbDate | null {
    if (!date) {
      return null;
    }

    var splDate = date.split("T")[0];
    const time = moment(splDate, "YYYY-MM-DD");

    return new NgbDate(time.year(), time.month() + 1, time.day());
  }

  buildEmailsFormArray(emails: Array<EmailAddress>): FormArray {
    const formArray = new FormArray<any>([]);

    if(emails){
      emails.forEach(email => {
        formArray.push(new FormGroup({
          email: new FormControl(email.Email, [Validators.required, Validators.pattern(this.emailPattern)]),
          displayName: new FormControl(email.DisplayName, [Validators.required])
        }));
      });
    }
 

    return formArray;
  }

  
  createAdvertisement(advertisement: any){
    this.isSaving.set(true);

    this.client.post<Advertisement>(`${environment.services_root_endpoints.advertisements}/`, advertisement).subscribe({
      next: (data) => {
        this.advertisementService.advertisementResource.set(data);
        this.advertisementService.projectId.set(data.Id);       
        this.isSaving.set(false);
        this.projectFormGroup.markAsUntouched();
        this.projectFormGroup.markAsPristine();

        this.router.navigate(['advertisements/projects', data.Id]);
      },
      error: (err) => {
        this.isSaving.set(false);
        console.error(err);
      }
    });
  }

  updateAdvertisement(projectId:string, advertisement: any){
    if(!projectId){
      throw new Error("projectId is required to update advertisement");
    }
    
    this.isSaving.set(true);

    this.client.put<Advertisement>(`${environment.services_root_endpoints.advertisements}/${projectId}`, advertisement).subscribe({
      next: (data) => {
        data.AnswerCount = this.advertisement()?.AnswerCount as number;
        data.QuestionCount = this.advertisement()?.QuestionCount as number;
        data.DocumentCount = this.advertisement()?.DocumentCount as number;
        data.PlanholderCount = this.advertisement()?.PlanholderCount as number;
        data.ReceivedBidsCount = this.advertisement()?.ReceivedBidsCount as number;
        
        this.advertisementService.advertisementResource.set(data);        
        this.isSaving.set(false);        
        this.projectFormGroup.markAsUntouched();
        this.projectFormGroup.markAsPristine();

        this.router.navigate(['advertisements', 'projects', projectId, 'info']);
      },
      error: (err) => {
        this.isSaving.set(false);
        console.error(err);
      }
    });
  }

}

export interface PrivateIdResponse{
  PrivateId: string;
}

export class ConvertedAdvertisementDates{
  bidDate: NgbDate | null = null;
  bidTime: NgbTimeStruct | null = null;
  bidDateAndTimeInfo: Date | null = null;
  preBidDateAndTimeInfo: Date | null = null;
  preBidDate: NgbDate | null = null;
  preBidTime: NgbTimeStruct | null = null;
  releaseDate: NgbDate | null = null;
  releaseDateInfo: Date | string | null = null;
  qaExpirationDate: NgbDate | null = null;
  qaExpirationDateInfo: Date | null = null
  qaExpirationTime: NgbTimeStruct | null = null;
  qaExpirationTimeInfo: Date | null = null;
}