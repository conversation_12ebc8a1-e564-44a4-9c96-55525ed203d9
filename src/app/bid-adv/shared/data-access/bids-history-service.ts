import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { BidOpsHistory } from '../interfaces/bid-ops-history';

@Injectable()
export class BidsHistoryService {

  constructor(private client: HttpClient) {}

  getHistory(projectId:string, max:number = 25, currentPage: number = 0, orderBy: string, isReversed: boolean, search: string | null = null): Observable<BidOpsHistoryGrid>{
    var url = `${environment.services_root_endpoints.bid_ops_history}/history/${projectId}`;
    

    url += `?max=${max}&current-page=${currentPage}`;

    if(search){
      url += `&search=${search}`;
    }

    if(orderBy){
      url +=`&order-by=${orderBy}`;
    }

    url +=`&is-reversed=${isReversed}`;

	  return this.client.get<BidOpsHistoryGrid>(url);    
  }


}

export interface BidOpsHistoryGrid{
  History: Array<BidOpsHistory>;
  TotalCount: number;
}