import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { MessageCenterEmailLog } from '../interfaces/bids-email-planholder';


@Injectable()
export class BidsAdvEmailPlanholdersService {

	constructor(private client: HttpClient) { }

	sendEmail(projectId: string, projectLink:string, log: MessageCenterEmailLog): Observable<any> {
		var request = {
			ProjectLink: projectLink,
			Log: log
		};

		return this.client.patch<any>(`${environment.services_root_endpoints.adverts_email_planholders}/emails/${projectId}/send-email`, request);
	}

	getSentEmails(projectId:string): Observable<any>{
		return this.client.get<any>(`${environment.services_root_endpoints.adverts_email_planholders}/emails/${projectId}`);
	}
}