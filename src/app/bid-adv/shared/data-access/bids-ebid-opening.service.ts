import { HttpClient } from "@angular/common/http";
import { computed, effect, inject, Injectable, signal, } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { BidderBidInfo } from "src/app/ebid/interfaces/bidder-bid-info";
import { environment } from "src/environments/environment";
import { Submission } from "../interfaces/ebid";
import { EBid } from "src/app/ebid/interfaces/ebid";
import { ActivatedRoute, Router } from "@angular/router";
import { FormTools } from "src/app/shared/utils/form-tools";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { EBidProjectService } from "./bids-ebid-project.service";
import { TimeInfo } from "../interfaces/advertisement";
import { BidAdvService } from "./bid.service";

@Injectable()
export class EBidOpeningService {
    advertisementService = inject(BidAdvService);
    project = this.advertisementService.advertisement;
    submissions = computed(() => this.submissionsResource.value());    
    aRoute = inject(ActivatedRoute);
    router = inject(Router);
    eBidProjectService = inject(EBidProjectService); 
    isLoading = computed(() => this.submissionsResource.isLoading() || this.eBidProjectService.ebidResource.isLoading());    
    ebid = computed(() => this.eBidProjectService.ebidResource.value());
    localBidDate = computed(() => this.advertisementService.getLocalBrowserBidDateInfoByTimeInfo(this.project()?.BidDetails.BidDateTimeInfo as TimeInfo));
    bidDateExpired = computed(() => this.advertisementService.isDateExpired(this.project()?.BidDetails.BidDateTimeInfo as TimeInfo));    
    openBidsForm = new FormGroup({
        submissions: new FormArray([], FormTools.atLeastOneRequiredValidator('open'))
    });
	bidsOpening = signal<string[]>([]);
	httpClient = inject(HttpClient);
	projectId = computed(() => this.advertisementService.projectId());
	openBidsResponse = signal<OpenBidsResponse | null>(null);
	openBidResponse = signal<BidderBidInfo | null>(null);
	submissionsResource = rxResource({
		request: () => (this.projectId()),
		loader: (request) => {
			if (request.request) {
				return this.httpClient.get<Array<Submission> | null>(`${environment.services_root_endpoints.adverts_ebid}/${request.request}/submissions`);
			}

			return of(undefined);
		}
	});

	eff = effect(() => {
        if(this.submissions()){
            this.setupSubmissionFormGroup();
        }
    });

	setupSubmissionFormGroup(){
        for(let submission of this.submissions() as Array<Submission>){
            const submissionFormsArray = this.openBidsForm.get('submissions') as FormArray;
            submissionFormsArray.push(new FormGroup({
                id: new FormControl(submission.BidderInfo.Id),
                companyName: new FormControl(submission.UserInfo?.Company?.Name),
                openDate: new FormControl(submission.BidderInfo.OpenedAt),
                open: new FormControl(true)               
            }));
        }
    }

	destroy() {		
		this.submissionsResource.set(null);		
		this.openBidsResponse.set(null);	
		this.openBidsForm = new FormGroup({
			submissions: new FormArray([], FormTools.atLeastOneRequiredValidator('open'))
		});
	}

	openBids(bidInfoIds: Array<string>, projectId: string | null = null){
		const request = new OpenBidsRequest(bidInfoIds);
		this.httpClient.patch<OpenBidsResponse>(`${environment.services_root_endpoints.adverts_ebid}/${projectId ?? this.projectId()}/open-bids`, request).subscribe({
			next: (response) => {
				this.openBidsResponse.set(response);
			},
			error: (err) => {
				console.error(err);
			},
		});
	}

	openBid(submissionId: string, projectId: string | null = null){					
		this.bidsOpening.update(x => [...x, submissionId]);
		this.httpClient.patch<OpenBidsResponse>(`${environment.services_root_endpoints.adverts_ebid}/${projectId ?? this.projectId()}/open-bids/${submissionId}`, {}).subscribe({
			next: (response) => {
				const openedInfo = response.OpenedBidderBidInfo.find(x => x.Id == submissionId) as BidderBidInfo;
				this.openBidResponse.set(openedInfo);								
				this.bidsOpening.update(x => x.filter(y => y != submissionId));
			},
			error: (err) => {
				console.error(err);
			}
		});
	}



}

export interface OpenBidsResponse{
	OpenedBidderBidInfo : Array<BidderBidInfo>;
	LatestEBid: EBid;
	OpenedAt: Date;	
}

export class OpenBidsRequest{
	constructor(bidInfoIds: Array<string>){
		this.BidderInfoIds = bidInfoIds;
	}
	BidderInfoIds: Array<string>;
}