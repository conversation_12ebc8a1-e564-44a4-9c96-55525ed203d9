export class BidsFilter
{
	page: number = 1;
	search: string | null = null;
	startBidDate: string | null = null;
	endBidDate: string | null = null;
	sortBy: string = "BidDate";
	sortOrder: string = "asc";
	orderByName: string = "BidDate";
}

// export interface BidsGrid{
// 	Advertisements: Array<BidOpsProject>;
// 	Total: number;
// }

// export class BidOpsProject implements IBidOpsProject{	
// 	Id: string = "";
// 	ProjectTitle: string = "";
// 	CountyName: string = "";
// 	StateName: string = "";
// 	InternalId: string = "";
// 	ProjectUniqueId: string = "";
// 	DetailsEstimate: string = "";
// 	TypeOfWork: string = "";
// 	Owner: string = "";
// 	Password: string = "";
// 	Notes: string = "";
// 	MapCode: string = "";
// 	Scope: string = "";
// 	AdditionalNotes: string = "";
// 	Location: Location = {} as Location;
// 	ReleaseDateTimeInfo: TimeInfo = {} as TimeInfo;
// 	BidDetails: BidDateTime | null = null;
// 	PreBidDetails: BidDateTime = {}  as BidDateTime;
// 	Permissions: ProjectPermissions = {} as ProjectPermissions;
// 	ContactInfo: AccountProfile = {} as AccountProfile;
// 	PaymentDate: string | null;
// 	QAExpirationDateTimeInfo: TimeInfo;
// 	QuestionEmails: EmailAddress[];
// 	Planholders: Planholder[];
// 	Questions: Question[];
// 	IsTBA: boolean;

// }



// export interface BidOpsProject {
// 	Id: string;
// 	ProjectTitle: string;
// 	CountyName: string;
// 	StateName: string;
// 	InternalId: string;
// 	ProjectUniqueId: string;
// 	DetailsEstimate: string;
// 	TypeOfWork: string;
// 	Owner:string;
// 	Password: string;
// 	Notes: string;
// 	MapCode:string;
// 	Scope:string;
// 	AdditionalNotes:string;
// 	Location: Location;
// 	ReleaseDateTimeInfo: TimeInfo;
// 	BidDetails: BidDateTime;
// 	PreBidDetails: BidDateTime;
// 	Permissions: ProjectPermissions;
// 	ContactInfo: AccountProfile;
// 	PaymentDate: string | null;
// 	QAExpirationDateTimeInfo: TimeInfo;
// 	QuestionEmails: Array<EmailAddress>;
// 	Planholders: Array<Planholder>;
// 	Questions: Array<Question>;
// 	IsTBA: boolean;
// 	IsBid: boolean;
//   }

//   export interface Question{

//   }


//   export interface Planholder{
// 	CreateDateTime: string;
// 	CompanyType: CompanyType;
// 	Actions: Array<string>;
// 	UserId: string;
// 	CompanyName: string;
// 	FirstName: string;
// 	LastName: string;
// 	Phone: string;
// 	Fax: string;
// 	Email: string;
// 	NotBidding: boolean;

// }

// export interface CompanyType{
// 	CompanyTypeId: number;
// 	Name: string;
// 	Order: number;
// }

//   export interface ProjectPermissions{
// 	  ShowPlanholdersList:boolean;
// 	  AllowQuestions:boolean;
// 	  IsPrivate: boolean;
// 	  IsVisible: boolean;
// 	  IsPlansOfficial: boolean;
// 	  IsTBA: boolean;
// 	  ExpireQA: boolean;
//   }
  
//   export interface BidDateTime {
// 	BidDateTimeInfo: TimeInfo;
// 	Location: string;
// 	Notes: string;
//   }
  
//   export interface TimeInfo {
// 	Date: string | null;
// 	Month: number | null;
// 	Day: number | null;
// 	Year: number | null;
// 	Hour: number | null;
// 	Minute: number | null;
// 	TimeZone: TimeZone | null;
//   }
  
//   export interface TimeZone {
// 	ZoneId: string;
// 	Name: string;
// 	Value: string;
//   }
  
//   export interface Location {
// 	LocationId: string | null;
// 	State: State | null;
// 	County: County | null;
// 	MapData: MapData | null;
//   }
  
//   export interface County {
// 	CountyId: number;
// 	State: string;
// 	StateId: number;
// 	Name: string;
// 	ADM2: string | null;
// 	Latitude: number;
// 	Longitude: number;
//   }
  
//   export interface MapData {
// 	Latitude: number;
// 	Longitude: number;
//   }
  
//   export interface State {
// 	StateId: number;
// 	Name: string;
// 	Abbreviation: string;
//   }
  
//   export interface EmailAddress{
// 	  DisplayName: string;
// 	  Email: string;
//   }

//   export interface AccountProfile{
//     FirstName: string;
//     LastName: string;
//     Company: Company;
//     Email: string;
//     Username: string;
// }

// export interface Company{
//     Name: string;
//     Phone: string;
// 	Fax: string;
//     Address: Address;
//     CompanyType: CompanyType;
// 	Website:string;
// }


// export interface Address{
//     Name: string;
//     Address1: string;
//     Address2: string;
//     City: string;
//     State: State;
//     Zip: string;
// }

  
export const timeFilters: Array<TimeOptions> = [
    {
      title: 'Current',
      value: 0,
      name: 'CurrentBids'      
    },
    {
      title: 'New Today',
      value: 1,
      name: 'Today'      
    },
    {
      title: 'New Yesterday',
      value: 2,
      name: 'Yesterday'
    },
    {
      title: 'New This Week',
      value: 3,
      name: 'Week'
    },
    {
      title: 'New This Month',
      value: 4,
      name: 'Month'
    },
    {
      title: 'Just Bid (In the last 30 days.)',
      value: 5,
      name: 'CurrentBids'
    },
    {
      title: 'All (Archives)',
      value: 6,
      name: 'All'
    }
  ];

export interface TimeOptions{
    title:string;
    value: number;
    name: string;
}