export interface BidAnalysis {
	Estimate: string | undefined;
	TotalBidders: number;
	AMLT: number;
	AMLTPercent: number;
	AverageBid: number;
}

export interface CompanyResult{
	SubmissionId: string;
	CompanyName: string;
	BidInfos: Array<BidderBidInfo>;
	CompletionInfos: Array<CompletionInfo>;
	WorkOrderType: string;
	WorkOrderUserValue: number | null;
	TotalBid: number | null;
	PaperBidId:string;
	IsOpening: boolean;
	IsRejecting: boolean;
	RejectedAt: Date;
	OpenedAt: Date;
	SubmittedAt: Date;
}

export interface BidderBidInfo{
	FormName: string;
	Total: number | undefined;
}

export interface CompletionInfo{
	CompletionName: string;
	CompletionTimeOption: string;
	UserValue: number | undefined;
}