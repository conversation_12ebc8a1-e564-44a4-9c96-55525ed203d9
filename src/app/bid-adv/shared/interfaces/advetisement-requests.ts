import { v4 } from "uuid";

export class UpdateRequestSignalInfo{
	RequestDate: Date = new Date();
	RequestId: string = v4();
	ProjectId: string | null = null;
	redirectUrl: Array<string> | null = null;
}

export class PreviewRequestSignalInfo{
	
	RequestDate: Date = new Date();
	RequestId: string = v4();
}

// export class SaveNewProjectSignalInfo{
	
// 	RequestDate: Date = new Date();
// 	RequestId: string = v4();
// }