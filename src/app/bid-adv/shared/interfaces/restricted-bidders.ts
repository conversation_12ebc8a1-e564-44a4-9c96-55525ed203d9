import { Planholder } from "./bids-planholder";

export interface RestrictedBidders {
	Id:string;
	IsEnabled:boolean;
	ProjectId:string;	
	CreatedAt: Date;
	LastUpdatedBy: string;
	Restricted: Array<string>;
}

export class RestricedBiddersRequest {
	constructor(userIds: Array<string>) {
		this.UserIds = userIds;
	}

	UserIds: Array<string>;

}

export class RestrictedBidder{
	constructor(userId: string, planholderInfo: Planholder) {
		this.UserId = userId;
		this.PlanholderInfo = planholderInfo;
	}

	UserId: string;
	IsChecked: boolean = false;
	PlanholderInfo: Planholder;
}