<!-- search -->
<div class="row">
	<!-- search by id or name -->
	<div class="col-12 col-md mb-2 mb-md-0">
		<div class="input-group">
			<span class="input-group-text" id="basic-addon1">
				<i class="fas fa-search" aria-hidden="true"></i>
			</span>
			<input type="text" name="search" id="search" class="form-control" name="UserSearch"
				placeholder="Search by ID or name" [ngModel]="search()"
				(ngModelChange)="this.searchInputChange.next($event)" />
		</div>
	</div>
	<!-- bid date -->
	<div class="col-12 col-md mb-2 mb-md-0">
		<app-date-time-picker placeholderTitle="Filter by Bid Date" (on-date-change)="onDateSelect($event)"
			[(date)]="selectedDate"></app-date-time-picker>
	</div>
	<!-- current/archives -->
	<div class="col-12 col-md">
		<select class="form-select" name="tfSelect" id="tfSelect" [(ngModel)]="selectedTimeOption"
			(ngModelChange)="selectTime($event)">
			@for (option of timeOptions; track $index) {
			<option [ngValue]="option">
				{{ option.title}}
			</option>
			}
		</select>
	</div>
</div>