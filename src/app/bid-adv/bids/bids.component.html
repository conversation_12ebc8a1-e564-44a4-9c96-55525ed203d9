<!-- header -->
<header class="bg-light p-3 py-lg-4 mb-3">
  <div class="container">
    <!-- page title -->
    <div class="d-flex align-items-center justify-content-between mb-3">
      <h1 class="page-title fs-5 mb-0">Bid Management</h1>
      <button type="button" class="btn btn-primary" routerLink="../new">+ New Project</button>
    </div>
    <!-- filters -->
    <app-bids-filter></app-bids-filter>
  </div>
</header>
<!-- project list (bid management) -->
<section class="px-3">
  <div class="container">
    <table class="table">
      <thead>
        <tr class="d-none d-lg-table-row">
          <th scope="col" style="width: 120px">
            <a class="fw-bold text-dark text-nowrap me-1" style="cursor: pointer;" (click)="setOrder('biddate')">Bid
              Date
            </a>
            <app-sort-icon [CurrentSortBy]="sortBy()" SortBy="BidDate" [CurrentSortOrder]="sortOrder()"></app-sort-icon>
          </th>
          <th scope="col" style="width: 120px">
            <a class="fw-bold text-dark text-nowrap me-1" style="cursor: pointer;"
              (click)="setOrder('internalId')">ID</a>
            <app-sort-icon [CurrentSortBy]="sortBy()" SortBy="InternalId"
              [CurrentSortOrder]="sortOrder()"></app-sort-icon>
          </th>
          <th scope="col">
            <a class="fw-bold text-dark text-nowrap me-1" style="cursor: pointer;"
              (click)="setOrder('projecttitle')">Name</a>
            <app-sort-icon [CurrentSortBy]="sortBy()" SortBy="ProjectTile"
              [CurrentSortOrder]="sortOrder()"></app-sort-icon>
          </th>
          <th scope="col">
          </th>
        </tr>
      </thead>
      <tbody>
        @if(isLoading())
        {
        <tr class="placeholder-glow"
          *ngFor="let item of [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25]">
          <td><span class="placeholder w-100"></span></td>
          <td><span class="placeholder w-100"></span></td>
          <td><span class="placeholder w-100"></span></td>
          <td><span class="placeholder w-100"></span></td>
        </tr>
        }
        @else {
        @for (bid of advertisementsGrid()?.Advertisements; track $index) {
        <tr>
          <td class="d-flex flex-column d-lg-none">
            <div class="mb-1">
            @if(bid.IsTBA){
            <span class="fw-bold me-2">TBA</span>
            }@else {
            <span class="fw-bold me-2">{{ bid.BidDate | date: 'M/dd/yyyy'}}</span>
            }
            @if(bid.IsPrivate){
                <span class="badge text-bg-info">Private</span>
            }
          </div>
            <div>{{ bid?.InternalId }}</div>
            <a class="text-decoration-none d-block mb-1" [routerLink]="[bid?.Id]"> {{ bid?.ProjectTitle }}</a>
          </td>
          <td class="d-none d-lg-table-cell">
            @if(bid.IsTBA){
            TBA
            }@else {
            {{ bid.BidDate | date: 'M/dd/yyyy'}}
            }
          </td>
          <td class="d-none d-lg-table-cell">
            <span>{{ bid?.InternalId }}</span>
          </td>
          <td class="d-none d-lg-table-cell">
            <a class="custom-link" [routerLink]="[bid?.Id]"> {{ bid?.ProjectTitle }}</a>
          </td>
          <td class="d-none d-lg-table-cell text-end">
            @if(bid.IsPrivate){
            <span class="badge text-bg-info">Private</span>
            }
          </td>
        </tr>
        }
        <!-- no projects -->
        @empty {
        <tr>
          <td colspan="4">
            <div class="alert alert-info m-0" role="alert">
              There are no projects that match your search criteria.
            </div>
          </td>
        </tr>
        }
        }
      </tbody>
    </table>
  </div>
</section>
<!-- footer -->
<footer class="px-3 mb-4">
  @if(advertisementsGrid()?.Total > 0){
  <div class="container d-flex justify-content-end">
    <ngb-pagination [collectionSize]="advertisementsGrid()?.Total" [pageSize]="limit()" [(page)]="currentPage"
      [rotate]="true" [maxSize]="5" [boundaryLinks]="true" (pageChange)="changePage($event)" [disabled]="isLoading()">
      <ng-template ngbPaginationFirst>First</ng-template>
      <ng-template ngbPaginationPrevious>Previous</ng-template>
      <ng-template ngbPaginationNext>Next</ng-template>
      <ng-template ngbPaginationLast>Last</ng-template>
    </ngb-pagination>
  </div>
  }
</footer>