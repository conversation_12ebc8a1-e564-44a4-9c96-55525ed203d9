import { Component, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NgbDate, NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { BidsAdvService } from '../shared/data-access/bids.service';
import { CommonModule } from '@angular/common';
import { BidsFilterComponent } from './ui/bids-filter/bids-filter.component';
import { FormsModule, NgForm, ReactiveFormsModule } from '@angular/forms';
import { SortIconComponent } from 'src/app/shared/ui/sort-icon/sort-icon.component';
import { Advertisement, AdvertisementInfo } from '../shared/interfaces/advertisement';

@Component({
    selector: 'app-bids',
    templateUrl: './bids.component.html',
    styleUrls: ['./bids.component.css'],
    imports: [CommonModule, BidsFilterComponent, FormsModule, ReactiveFormsModule, RouterModule, NgbPaginationModule, SortIconComponent],
    viewProviders: [NgForm]
})
export class BidsComponent implements OnInit, OnDestroy {

  router = inject(Router);
  advertisementService = inject(BidsAdvService);
  aRoute = inject(ActivatedRoute);
  bidDate:NgbDate | null = null;
  advertisementsGrid = this.advertisementService.advertisementGrid;  
  isLoading = this.advertisementService.isAdvertisementsLoading;
  currentPage: number = 1;
  max: number = 25;
  sortBy = this.advertisementService.sortBy;
  sortOrder = this.advertisementService.sortOrder;
  search = this.advertisementService.search;
  limit = this.advertisementService.limit;
  

  constructor() { }
  ngOnDestroy(): void {
    this.advertisementService.destroy();
  }

  trackById(index: number, item: any): string {
    return `${item.Id}-${index}`;
  }

  ngOnInit(): void {
    this.advertisementService.initialized.set(true);
  }


  changePage(pageInfo: number) {

    this.router.navigate([], {
      queryParams: {currentPage: pageInfo},
      queryParamsHandling: "merge"
    });
  }

  setOrder(sortBy: string) {

    this.router.navigate([], {
      queryParams: {sortBy: sortBy, sortOrder: this.advertisementService.sortOrder() === 'desc' ? 'asc' : 'desc'},
      queryParamsHandling: "merge"
    });

  }


}
