import { Route } from '@angular/router';
import { BidAdvComponent } from './bid-adv.component';

export const BIDS_ADV_ROUTES: Route[] = [
	{path : '' , redirectTo : 'projects' , pathMatch : 'full'},
	{
		path: '',		
		component: BidAdvComponent,
		children: [
			{
				path: 'new',
				providers: [],
				data: { projectType: 'new' },
				loadComponent: () => import('./bid/new-bid/new-bid.component').then(m => m.NewBidComponent),
				children: [
					{
						path: '',
						loadComponent: () => import('./bid/new-bid/new-bid-info/new-bid-info.component').then(m => m.NewBidInfoComponent),
					},
					{
						path: 'preview',
						data: { projectType: 'new' },
						loadComponent: () => import('./bid/new-bid/preview/preview.component').then(m => m.PreviewComponent),
						children: [	
							{
								path: '',
								pathMatch: 'full',
								redirectTo: 'summary'
							},
							{
								path: 'summary',
								loadComponent: () => import('../bid-opportunities/bid/summary/summary.component').then(m => m.SummaryComponent)
							}
						]
					}
				]
			},			
			{
				path: 'projects',
				loadComponent: () => import('./bids/bids.component').then(m => m.BidsComponent)
			},						
			{
				path: 'projects/:id',
				loadComponent: () => import('./bid/bid.component').then(m => m.BidProjectComponent),
				children: [
					{
						path: '',
						pathMatch: 'full',
						redirectTo: 'info'
					},
					{
					  path: "info", 
					  loadComponent: () => import('./bid/info/info.component').then(m => m.BidAdvInfoComponent)
					},
					{
					  path: "plan-holders", 
					  providers: [],
					  loadComponent: () => import('./bid/planholders/adv-planholders.component').then(m => m.AdvPlanholdersComponent)
					},
					{
					  path: "downloads", 
					  loadComponent: () => import('./bid/downloads/downloads.component').then(m => m.AdvDownloadsComponent)
					},
					{
					  path: "history", 
					  loadComponent: () => import('./bid/history/history.component').then(m => m.BidAdvComponentHistoryComponent)
					},
					{
					  path: "email-log", 
					  loadComponent: () => import('./bid/email-logs/adv-email-logs.component').then(m => m.BidAdvEmailLogsComponent)
					},
					{
					  path: "docs", 
					  providers: [],
					  loadComponent: () => import('./bid/docs/project-docs.component').then(m => m.ProjectDocsComponent)
					},
					{
					  path: "email-plan-holders", 
					  loadComponent: () => import('./bid/email-planholders/email-planholders.component').then(m => m.EmailPlanholdersComponent)
					},
					{
					  path: "reports", 
					  loadComponent: () => import('./bid/reports/report-builder.component').then(m => m.ReportBuilderComponent)
					},
					{
					  path: "edit", 
					  data: { projectType: 'edit' },
					  loadComponent: () => import('./bid/edit-bid/edit-bid.component').then(m => m.EditBidComponent)
					},
					{
						path: 'qa',
						loadComponent: () => import('./bid/qa/qa.component').then(m => m.QaComponent)
					},					
					{
						path: 'e-bid',						
						loadComponent: () => import('./bid/e-bid/e-bid.component').then(m => m.EBidComponent),
						children: [
							{
								path: 'open',
								loadComponent: () => import('./bid/e-bid/bid-opening/bid-opening.component').then(m => m.BidOpeningComponent)
							},
							{
								path: 'setup',
								loadComponent: () => import('./bid/e-bid/bid-setup/bid-setup.component').then(m => m.BidSetupComponent)
							},
							{
								path: 'results',
								loadComponent: () => import('./bid/e-bid/bid-results/bid-results.component').then(m => m.BidResultsComponent)

							},
							{
								path: 'setup/edit',
								loadComponent: () => import('./bid/e-bid/bid-form-edit/bid-form-edit.component').then(m => m.BidFormEditComponent)
							},
							{
								path: 'restrict-bidders',
								providers: [],
								loadComponent: () => import('./bid/e-bid/restrict-bidders/restrict-bidders.component').then(m => m.RestrictBiddersComponent)
							}
						]
					}
				  ]
			}	
		]
	}	
];
