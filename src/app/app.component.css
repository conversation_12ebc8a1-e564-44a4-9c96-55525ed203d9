/* dashboard */

.header {
  padding: 1.2rem 1.7rem !important;
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
}

.logo {
  width: 130px;
}

.main {
  flex: 1;
  overflow-y: auto; /* Allows scrolling only if content exceeds */
}

.wrapper {
  display: flex;
  height: calc(100vh - 50px); /* Adjusts to the header height */
}

.sidebar {
  width: 235px;
  color: white;
  padding: 1rem !important;
}

.border, .border-bottom, .border-top, .border-start, .border-end, .card  {
  border-color: rgba(231, 234, 243, .7) !important;
  border-width: 1px !important;
}


.nav-link {
  color: #525b75 !important;
}

.nav-link:hover {
  background-color: #EFF2F6;
  text-decoration: none !important;
}

civcast-growl {
  position: absolute;
  right: 8px;
  top: 8px;
  z-index: 10;
}

.iframe {
  display: none;
}

.fal {
  width: 30px;
}

.ng-progress-bar{
  --ng-progress-color: red;
  --ng-progress-thickness: 4;
}