import { <PERSON>mpo<PERSON>, ElementRef, NgZone, OnInit, Renderer2, viewChild, ViewChild } from '@angular/core';
import { environment } from 'src/environments/environment';
import { AuthService } from './shared/data-access/auth.service';
import { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError, RouterOutlet } from '@angular/router';
// import { NgProgressbar, NgProgressRef } from 'ngx-progressbar';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { DelegateLocalStorageService } from './shared/data-access/delegate-localstorage-service';
import { DefaultSyncService } from './shared/data-access/default-sync-service';
import { CivCastTokenService } from './shared/interfaces/civcast-token/civcast-token-service';
import { forkJoin } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Hub } from 'aws-amplify/utils';
import { fetchAuthSession, getCurrentUser } from "aws-amplify/auth";
import { ScriptLoaderService } from './shared/data-access/script-loader-service';
import 'aws-amplify/auth/enable-oauth-listener';
import { RouterLink } from '@angular/router';
import { AccountsSettingsService } from './account/user/shared/data-access/accounts-settings-service';
declare const handleUrlChange: () => void;
@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.css'],
    imports: [RouterOutlet, RouterLink]
})
export class AppComponent implements OnInit {
  isLoggedIn = false;
  showNav = false;
  // progressBar = viewChild(NgProgressRef);
  // marketingApp: SafeResourceUrl;
  loadIframe: boolean = false;
  @ViewChild('marketingFrame') marketingFrame: ElementRef<HTMLIFrameElement> | undefined;
  constructor(private authService: AuthService,
    private delegateService: DelegateLocalStorageService,
    private civcastTokenService: CivCastTokenService,
    private router: Router,
    private sanitizer: DomSanitizer,
    private accountsSettingsService: AccountsSettingsService,
    private defaultSyncService: DefaultSyncService,
    private zone: NgZone,
    private scriptLoaderService: ScriptLoaderService,
    private rederer2:Renderer2,
     httpClient:HttpClient) {


    console.log("Auth 1");


    this.scriptLoaderService.loadKnownScripts(this.rederer2);

    this.router.events.subscribe(event => {
      switch (true) {
        case event instanceof NavigationStart:  
        case event instanceof NavigationCancel:
        case event instanceof NavigationError:
        case event instanceof NavigationEnd:{
          //handleUrlChange(); 
          // if(event.url === "/"){ 
          //   this.runDefaultHub();
          // }
          // console.log(event);
          break; 
        }
        default: {
          break;
        }
      }
    });

    // this.marketingApp = this.sanitizer.bypassSecurityTrustResourceUrl(`${environment.marketing_url}${'?frame=app'}`);

    if (this.getParameterByName("from") == "marketing") {
      this.checkLoginMarketing();
    }

    
    Hub.listen('auth', async (data) => {
      switch (data.payload.event) {
        case 'signedIn':
          console.log("Auth 4");

          forkJoin([this.civcastTokenService.getToken(), this.delegateService.CreateAndGetDelegatedId()]).subscribe(
          {
            next: () => {
              this.defaultSyncService.verifyAndSync().then(result => {
                console.log("Sync Done");
              });
              var invitationId = sessionStorage.getItem("invitationId");
              if (invitationId) {
                sessionStorage.removeItem("invitationId");
              } else {                            
                this.runDefaultHub();
              }      

              this.authService.isLoggedIn.next(true);
              this.authService.isLoggedInSignal.set(true);
            }       
          });
          break;
        case 'signedOut':
          localStorage.clear();
          this.authService.isLoggedIn.next(false);
          break;
        case 'signInWithRedirect_failure':
          console.log("Auth 5");
          console.log(data);
          this.authService.isLoggedIn.next(false);          
          break;
        case 'tokenRefresh':
           
          console.log("Token refreshed")
          break;
        case 'tokenRefresh_failure':
          localStorage.clear();
          this.router.navigate(['/login']);
          break;
        default:
          break;
      }
    });

 

  }
  getParameterByName(name: string) {
    var match = RegExp('[?&]' + name + '=([^&]*)').exec(window.location.search);
    return match && decodeURIComponent(match[1].replace(/\+/g, ' '));
  }

  Initialize() {
    try {
      console.log("Auth 6");

      getCurrentUser().then(result => {
        if (result) {
          fetchAuthSession().then(userinfo => {
            console.log("Auth 7");
            if (userinfo) {
            
              console.log("Auth 8");
              var currentId = this.delegateService.GetDelegationIdFromClientStore();
              forkJoin([this.delegateService.CreateAndGetDelegatedId()]).subscribe({
                next: (services) => {

                  if (currentId != services[0]) {
                    window.location.replace(window.location.href);
                  } else {
                    if (document.location.href === `${environment.BaseSiteUrl}/`) {
                      this.runDefaultHub();
                    }else{
                      this.authService.isLoggedIn.next(true);
                      this.authService.isLoggedInSignal.set(true);
                    }
                  }
                },error: (err) => {
                  console.log(err);      
                  var mainHub = this.accountsSettingsService.getMainDefaultHub();
                  this.zone.run(() => {
                    this.router.navigate([mainHub.Route]);                      
                  });
                }
              });
            }
            else {
              console.log("Auth 9");
              this.authService.isLoggedIn.next(false);
              this.authService.isLoggedInSignal.set(false);
            }

          }, err => {
            this.authService.isLoggedIn.next(false);
            this.authService.isLoggedInSignal.set(false);
            console.log(err);
          });

        } else {
          this.authService.isLoggedIn.next(false);
          var invitationId = sessionStorage.getItem("invitationId");
          if (invitationId) {
            sessionStorage.removeItem("invitationId");
          }
        }
      }, err => {
        this.authService.isLoggedIn.next(false);
        console.log(err);
      });

    } catch (error) {
      console.log(error);
      this.authService.isLoggedIn.next(false);
    } finally {

    }
  }

  async checkLoginMarketing() {
    var isAuth = false;
    try {
      var user = await getCurrentUser();

      if (user) {
        var currentAuth = await fetchAuthSession();

        if (currentAuth) {
          await this.delegateService.CreateAndGetDelegatedId().toPromise();
          this.router.navigate(['/'])
        }
      }
    } catch (error) {

    }

    return isAuth;
  }

  runDefaultHub(){

      this.accountsSettingsService.getDefaultHub().subscribe({
        next: (result) => {
   
          if (result) {
            this.zone.run(() => {
              this.router.navigate([result.Route]);
            });
            
          } else {
            var mainHub = this.accountsSettingsService.getMainDefaultHub();
            this.zone.run(() => {
              this.router.navigate([mainHub.Route]);                      
            });
          }                
          this.authService.isLoggedIn.next(true);
          this.authService.isLoggedInSignal.set(true);  
        },
        error: (err) => {
          var mainHub = this.accountsSettingsService.getMainDefaultHub();
          this.zone.run(() => {
            this.router.navigate([mainHub.Route]);                      
          });

          this.authService.isLoggedIn.next(true);
          this.authService.isLoggedInSignal.set(true);
        }
      });
    
           
  }
  // marketingSiteLoad(frame: HTMLIFrameElement) {
  //   setTimeout(async () => {
  //     try {
  //       if (await getCurrentUser()) {
  //         frame.contentWindow?.postMessage({ "isLoggedIn": true }, environment.marketing_url);
  //       } else {
  //         frame.contentWindow?.postMessage({ "isLoggedIn": false }, environment.marketing_url);
  //       }
  //     } catch {
  //       frame.contentWindow?.postMessage({ "isLoggedIn": false }, environment.marketing_url);
  //     }
  //   }, 1);

  //   this.authService.isLoggedIn.subscribe(result => {
  //     if (result) {
  //       frame.contentWindow?.postMessage({ "isLoggedIn": true }, environment.marketing_url);
  //     } else {
  //       frame.contentWindow?.postMessage({ "isLoggedIn": false }, environment.marketing_url);
  //     }
  //   }, err => {
  //     frame.contentWindow?.postMessage({ "isLoggedIn": false }, environment.marketing_url);
  //   });
  // }
  ngOnInit(): void {
  
    // this.progressRef = this.pro.ref('myProgress');
    // this.progressBar()?.start();
    console.log("Auth 11");


    if (document.location.search.indexOf("code=") <= 0 && document.location.href.indexOf("auth/validate") <= 0 && document.location.href.indexOf("/auth/callback/signout") <= 0) {
      this.Initialize();
    }
  }
}