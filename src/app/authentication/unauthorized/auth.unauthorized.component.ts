﻿import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
    selector: 'unauthorized',
    templateUrl: './auth.unauthorized.component.html',
    imports: [CommonModule]
})
export class UnauthorizedComponent implements OnInit {
  router = inject(ActivatedRoute);
  pageTitle: string | null = null;

  constructor() {}

  ngOnInit(): void {
    if(this.router.parent?.data){
      const {PageTitle} = this.router.parent?.snapshot.data;
      this.pageTitle = PageTitle;
    }
    
  }
}
