import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AuthSignoutCallbackComponent } from './auth-signout-callback.component';

describe('AuthSignoutCallbackComponent', () => {
  let component: AuthSignoutCallbackComponent;
  let fixture: ComponentFixture<AuthSignoutCallbackComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AuthSignoutCallbackComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AuthSignoutCallbackComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
