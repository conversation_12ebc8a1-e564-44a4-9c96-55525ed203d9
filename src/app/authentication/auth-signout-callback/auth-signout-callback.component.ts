import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { signOut } from 'aws-amplify/auth';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';

@Component({
    selector: 'app-auth-signout-callback',
    templateUrl: './auth-signout-callback.component.html',
    styleUrls: ['./auth-signout-callback.component.css'],
    imports: [CommonModule, CMLoaderModule]
})
export class AuthSignoutCallbackComponent implements OnInit {

  constructor() {}

  ngOnInit() {
    signOut();    
  }
}
