import { Component, OnInit, inject } from '@angular/core';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
    selector: 'app-signin',
    templateUrl: './signin.component.html',
    styleUrls: ['./signin.component.css'],
    imports: [CMLoaderModule]
})
export class SigninComponent implements OnInit {
  authService = inject(AuthService);
  constructor() {}

  ngOnInit() {
    this.authService.logIn();
  }
}
