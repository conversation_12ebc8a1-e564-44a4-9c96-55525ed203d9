import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';
import {
  fetchAuthSession,
  signInWithRedirect
} from 'aws-amplify/auth';
import 'aws-amplify/auth/enable-oauth-listener';

@Component({
    selector: 'app-auth-validation',
    templateUrl: './auth-validation.component.html',
    styleUrls: ['./auth-validation.component.css'],
    imports: [CommonModule, CMLoaderModule]
})
export class AuthValidationComponent implements OnInit {
  constructor(private aRoute: ActivatedRoute) {}

  ngOnInit() {
    var isInvitation = this.aRoute.snapshot.queryParams['invitation'];
    if(isInvitation){

      alert("has Invitation");
    }
    
    signInWithRedirect({customState: 'test'}).then((user) => {
      console.log("SigninWithRedirect", user);
    }).catch((e) => {
      console.log(e);
    });
  }
}
