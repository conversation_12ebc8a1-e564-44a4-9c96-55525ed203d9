import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AuthValidationComponent } from './auth-validation.component';

describe('AuthValidationComponent', () => {
  let component: AuthValidationComponent;
  let fixture: ComponentFixture<AuthValidationComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AuthValidationComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AuthValidationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
