import { Component, OnInit, inject } from '@angular/core';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';

@Component({
    selector: 'app-signout',
    templateUrl: './signout.component.html',
    styleUrls: ['./signout.component.css'],
    imports: [CommonModule, CMLoaderModule]
})
export class SignoutComponent implements OnInit {
  authService = inject(AuthService);
  constructor() {}

  ngOnInit() {
    this.authService.logOut();    
  }
}
