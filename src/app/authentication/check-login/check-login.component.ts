import { Component, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import { fetchAuthSession, getCurrentUser } from "aws-amplify/auth";
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';

@Component({
    selector: 'app-check-login',
    templateUrl: './check-login.component.html',
    styleUrls: ['./check-login.component.css'],
    imports: [CMLoaderModule]
})
export class CheckLoginComponent implements OnInit {
  router = inject(Router);
  private isLoading: boolean = false;
  constructor() {}

  ngOnInit() {
    getCurrentUser().then(result => {      
      if (result) {     
        this.isLoading = true;   
        fetchAuthSession()
          .then(async result => {
            if(result){
              this.isLoading = true;                                
              this.isLoading = false;               
              if (window.location.pathname === '/' || window.location.pathname === '/home') {
                this.router.navigate(['/user', 'projects']);
              }
            }else{
                this.isLoading = false; 
                this.router.navigate(['/login']);
            }
          })
          .catch(() => {
            this.isLoading = false;                        
          });
      }else{
          this.isLoading = false;  
          this.router.navigate(['/login']);         
      }
    }, err => {            
      this.router.navigate(['/login']);
    });
  }
}
