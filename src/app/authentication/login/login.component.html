<div class="container-fluid bg-civcast h-100 d-flex justify-content-center align-items-center">
   @if(isLoaded){
      <div class="text-center">
         <div class="alert alert-danger" *ngIf="message">{{message}}</div>
         <div class="logo mb-2">Please click here to sign in.</div>
         <button type="button" class="btn btn-outline-dark" (click)="signIn()">Sign In</button>
      </div>
   }@else {
      <div class="text-center">
         <div class="mb-4">
            <h1>Loading Application</h1>
         </div>
         <i class="fas fa-spinner-third fa-fw fa-spin fa-8x"></i>      
      </div>
   }
</div>