import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.css'],
    imports: [CommonModule]
})
export class LoginComponent implements OnInit {

  aRoute = inject(ActivatedRoute);
  authService = inject(AuthService);
  isLoaded: boolean = false;
  isLoggedIn: boolean = false;
  message: string | null = null;
  constructor() {     
    this.authService.isLoggedIn.subscribe({            
      next: (result) => {
        if(result != null){
          this.isLoggedIn = result;      
          this.isLoaded = true;
        }

      }, error: (err) => {
        this.isLoaded = true;
      }
    });
  }
  ngOnInit() {    
    var message = this.aRoute.snapshot.queryParams['message'];

    if(message){
      this.message = message;
    }
  }
  signIn(){
    this.authService.logIn();
  }

}
