import { Component, forwardRef, inject, input, signal } from '@angular/core';
import { MultiSelect, MultiSelectModule } from 'primeng/multiselect';
import { ProjectListDropdownService } from '../../shared/data-access/project-list-dropdown.service';
import { ConstructionProject } from '../../shared/interfaces/construction-project';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-projects-list-drop-down',
  imports: [CommonModule, FormsModule, MultiSelectModule],
  templateUrl: './projects-list-drop-down.component.html',
  styleUrl: './projects-list-drop-down.component.css',
    providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ProjectsListDropDownComponent),
      multi: true,
    },
  ],
})
export class ProjectsListDropDownComponent implements ControlValueAccessor {
  projectListDropdownService = inject(ProjectListDropdownService);
  projectsSelection = this.projectListDropdownService.projectsForSelection;
  projectSelectionLoading = this.projectListDropdownService.projectsSelectionLoading;
  selectedProjects = this.projectListDropdownService.selectedProjects;     
  size = input<string>("small");
  private onChange: (value: ConstructionProject[]) => void = () => {};
  private onTouched: () => void = () => {};

  constructor() {
    this.projectListDropdownService.initializeProjectsForSelections.set(true);
  }


  writeValue(value: ConstructionProject[]): void {
    this.selectedProjects.update((projects) => {
      return [...value];        
    });
  }

  registerOnChange(fn: (value: ConstructionProject[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    // Handle disabled state if necessary
  }

  onItemSelect(item: any) {
    //var ids = item.value.map((x: any) => x.Id) as Array<string>;
    this.projectListDropdownService.selectedProjects.set([...item.value as Array<ConstructionProject>]);
    this.onChange(this.selectedProjects());
  }
}
