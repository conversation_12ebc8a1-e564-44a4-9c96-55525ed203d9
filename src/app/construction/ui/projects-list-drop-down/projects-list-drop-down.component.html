
	<p-multiSelect size="small" [options]="projectsSelection()" optionLabel="Title" [(ngModel)]="selectedProjects"
								placeholder="Select Projects" [filter]="true" filterPlaceholder="Search..." [showToggleAll]="false"
								(onChange)="onItemSelect($event)" style="width: 100%; flex: 1" [loading]="projectSelectionLoading()"
								[selectedItemsLabel]="'{0} projects selected'" [showToggleAll]="true" [maxSelectedLabels]="1" [filterBy]="'Title,InternalId'">
								<!-- Custom Option Template -->
								<ng-template let-project pTemplate="item">
									<div class="d-flex align-items-center">
										<span class="me-2">{{ project.InternalId }}</span>
										<div>
											<small class="text-muted">{{ project.Title }}</small>
										</div>
									</div>
								</ng-template>
							</p-multiSelect>