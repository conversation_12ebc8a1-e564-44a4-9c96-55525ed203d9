<!-- header -->
<header class="bg-light mb-3">
	<div class="container p-4 ">
		<!-- page title -->
		<div class="d-flex align-items-center justify-content-between mb-3">
			<h1 class="fs-5 mb-0">OnSite</h1>
			<button type="button" class="btn btn-primary" routerLink="new">+ New
				Project</button>
		</div>
		<!-- filters -->
		<app-construction-projects-filter></app-construction-projects-filter>
	</div>
</header>
<div class="container px-4 mb-3">
	<!-- nav -->
	<nav class="d-flex align-items-center justify-content-end">
		@if (showMechanicsDiary()) {
			<a class="me-3" routerLink="/construction/mechanics-diary" routerLinkActive="active">Mechanics Diary</a>
		}
		<a class="me-3" routerLink="../diary" routerLinkActive="active">Diary</a>
		<app-recent-projects-menu></app-recent-projects-menu>
	</nav>
</div>