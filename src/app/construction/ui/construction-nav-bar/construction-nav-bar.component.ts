import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RecentProjectsMenuComponent } from '../../projects/ui/recent-projects-menu/recent-projects-menu.component';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { ConstructionProjectsFilterComponent } from '../../projects/ui/construction-projects-filter/construction-projects-filter.component';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessRequest } from 'src/app/shared/interfaces/access';
import { AccessEffects } from 'src/app/shared/interfaces/access';

@Component({
    selector: 'app-construction-nav-bar',
    imports: [CommonModule, RecentProjectsMenuComponent, RouterLink, RouterLinkActive, ConstructionProjectsFilterComponent],
    templateUrl: './construction-nav-bar.component.html',
    styleUrl: './construction-nav-bar.component.css'
})
export class ConstructionNavBarComponent {
  private accessService = inject(AccessService);

  /**
   * Check if user has access to a specific permission
   */
  hasAccess(permission: string): boolean {
    // For now, we'll implement a simple check
    // In a real implementation, this would check against the user's permissions
    // This is a placeholder that can be enhanced based on the actual permission system
    return true; // TODO: Implement actual permission checking
  }

  /**
   * Show mechanics diary in construction navigation
   * Based on user's access to mechanics diary functionality
   */
  showMechanicsDiary = computed(() =>
    this.hasAccess('access-mechanics-diary')
  );

  /**
   * Show project navigation
   * Based on user's access to project daily log functionality
   */
  showProjectNav = computed(() =>
    this.hasAccess('access-project-dailylog')
  );
}
