import { Component, forwardRef, inject, input, OnInit } from '@angular/core';
import { MultiSelectModule } from 'primeng/multiselect';
import { EquipmentDropdownService } from '../../shared/data-access/equipment-dropdown.service';
import { EquipmentInfo } from '../../shared/interfaces/equipment';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-equipment-dropdown',
  imports: [CommonModule, FormsModule, MultiSelectModule],
  templateUrl: './equipment-dropdown.component.html',
  styleUrl: './equipment-dropdown.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => EquipmentDropdownComponent),
      multi: true,
    },
  ],
})
export class EquipmentDropdownComponent implements ControlValueAccessor, OnInit {
  equipmentDropdownService = inject(EquipmentDropdownService);
  equipmentSelection = this.equipmentDropdownService.equipmentForSelection;
  equipmentSelectionLoading = this.equipmentDropdownService.equipmentSelectionLoading;
  selectedEquipment = this.equipmentDropdownService.selectedEquipment;
  size = input<string>("small");
  
  private onChange: (value: EquipmentInfo[]) => void = () => {};
  private onTouched: () => void = () => {};

  constructor() {
    this.equipmentDropdownService.initializeEquipmentData.set(true);
  }

  ngOnInit(): void {
    // Check if data is stale and trigger refresh only when component actually needs it
    // This handles the case where user navigates back to this component
    // and the equipment data has been marked as stale by the main service
    if (!this.equipmentDropdownService.initializeEquipmentData()) {
      this.equipmentDropdownService.initializeEquipmentData.set(true);
    }
  }

  writeValue(value: EquipmentInfo[]): void {
    this.selectedEquipment.update(() => {
      return [...(value || [])];
    });
  }

  registerOnChange(fn: (value: EquipmentInfo[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState?(_isDisabled: boolean): void {
    // Handle disabled state if necessary
  }

  onItemSelect(item: any) {
    this.equipmentDropdownService.selectedEquipment.set([...item.value as Array<EquipmentInfo>]);
    this.onChange(this.selectedEquipment());
    this.onTouched();
  }
}
