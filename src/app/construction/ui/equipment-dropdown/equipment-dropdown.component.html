<p-multiSelect 
  size="small" 
  [options]="equipmentSelection()" 
  optionLabel="Number"
  [(ngModel)]="selectedEquipment"
  placeholder="Select Equipment" 
  [filter]="true" 
  filterPlaceholder="Search equipment..."
  [showToggleAll]="true"
  [loading]="equipmentSelectionLoading()"
  [selectedItemsLabel]="'{0} equipment selected'" 
  [maxSelectedLabels]="1" 
  [filterBy]="'Number,Make,Model,Notes'"
  (onChange)="onItemSelect($event)"
  style="width: 100%; flex: 1">
  
  <ng-template let-equipment pTemplate="item">
    <div class="d-flex align-items-center">
      <span class="me-2">{{ equipment.Number }} - {{ equipment.Make }} {{ equipment.Model }}</span>
    </div>
  </ng-template>
</p-multiSelect>
