import { HttpClient } from '@angular/common/http';
import { Injectable, inject, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ConstructionProject } from '../interfaces/construction-project';
import { UserService } from 'src/app/shared/data-access/user.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectComponentsService {

  http = inject(HttpClient);
  teamProjectIds = signal<Array<string> | null>(null);
  userService = inject(UserService);  
  addComponentToStoreById<type>(projectId:string, componentIdentifier: string, isActive: boolean){
    var patchOptions = {
      FunctionName: "addbycomponentId",
      Data: {
        componentId: componentIdentifier,
        isActive: `${isActive}`
      }
    }
    return this.http.patch<type>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}`, patchOptions);
  }


  public GetTeamProjectIds(projectsIds: Array<string>): Observable<string[]> {
    if(this.teamProjectIds()){      
      return of(this.teamProjectIds() as Array<string>);
    }else{
      return new Observable<string[]>((obs) => {
        this.http.post<string[]>(`${environment.services_root_endpoints.project_components}/project-components/project?method=projects-team`, projectsIds).subscribe({
            next: (result) => {
              this.teamProjectIds.set(result);
              obs.next(result);
              obs.complete();
            },
            error: (err) => {
              obs.error(err);
            }
          });
        });
    }
    
  }

  filterOutTeamProject(projects: Array<ConstructionProject>): Observable<Array<ConstructionProject>> {
    return new Observable<Array<ConstructionProject>>(obs => {
   
      this.userService.getUser().subscribe({
        next: (user) => {
          let projectIds = new Array<string>();          
          let filterProjects = new Array<ConstructionProject>();
      
          
          if (user && projects) {
            for (let project of projects) {
              if (project.UserId != user._id) {
                projectIds.push(project.Id);
              } else {
                filterProjects.push(project);
              }
            }

            if (projectIds.length > 0) {
              this.GetTeamProjectIds(projectIds).subscribe({
                next: (teamProjectIds) => {
                  let projectsFinal = new Array<ConstructionProject>();                 

                  for (let projectId of teamProjectIds) {
                    projectsFinal = projects.filter(x => teamProjectIds.includes(x.Id));                    
                  }    

                   obs.next(projectsFinal);
                   obs.complete();
                },
                error: (err) => {
                  obs.error(err);
                }
              });
            }else{
              obs.next(filterProjects);
              obs.complete();
            }
          }else{
            obs.next([]);
            obs.complete();
          }
        },
        error: (err) => {
          console.log(err);
          obs.error(err);
        }
      });
    });
  }
}
