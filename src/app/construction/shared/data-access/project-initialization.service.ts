import { Injectable, inject, signal } from "@angular/core";
import { ConstructionTeamsService } from "./teams.service";
import { TeamMember } from "../interfaces/team";
import { Observable, delay, delayWhen, forkJoin, of } from "rxjs";


@Injectable({
	providedIn: 'root'
  })
  export class ConstructionProjectInitializationService {

	teamService = inject(ConstructionTeamsService);
	status = signal<string>("");
	initializeProject(projectId: string): Observable<boolean>{
		return new Observable<boolean>((obs) => {
			this.status.set("Initializing Project...");
			forkJoin({teamComponent: this.createTeamComponent(projectId)}).pipe(delay(1000)).subscribe({
				next: (result) => {
					this.status.set("Completed Initializing Project");
					of(true).pipe(delay(2000)).subscribe({
						next: (result) => {							
							this.status.set("");
							obs.next(true);
							obs.complete();						
						},
						error: (err) => {
							obs.error(err);
						}
					});				
				},
				error: (err) => {
					obs.error(err);
				}
			});		
		});
	}

	private createTeamComponent(projectId: string): Observable<any> {
		return new Observable<boolean>((obs) => {
			this.status.set("Synching Team Members...");
			forkJoin({ projectTeamComponent: this.teamService.getOrCreateTeamComponent(projectId), userTeamComponent: this.teamService.getUserTeamComponent() }).subscribe({
				next: ({ projectTeamComponent, userTeamComponent }) => {
					var activeTeamMembers = userTeamComponent.TeamMembers.filter(x => x.IsActive).map(x => {
						return { IsActive: true, UserId: x.UserId } as TeamMember
					});
					this.teamService.AddTeamMembers(projectId, activeTeamMembers).subscribe({
						next: (result) => {
							this.status.set("Completed Synching Team Members");
							obs.next(true);
							obs.complete();
						},
						error: (err) => {
							obs.error(err);
						}
					});
				},
				error: (err) => {
					obs.error(err);
				}
			});
		});
	}

	private createInfoComponent(projectId: string): Observable<any>{
		return new Observable<boolean>((obs) => {

		});
	};
}