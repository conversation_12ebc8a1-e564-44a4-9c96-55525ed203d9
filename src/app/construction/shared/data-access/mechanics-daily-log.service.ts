import { Injectable, signal, computed, inject, effect } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { rxResource } from '@angular/core/rxjs-interop';
import { firstValueFrom, from, of, switchMap, Observable, tap, catchError, finalize } from 'rxjs';
import {
  UserDailyLogComponent,
  UserDailyLogComponentType,
  CreateDailyLogRequest,
  UpdateDailyLogRequest,
  LatestDailyLogResponse,
  MechanicsTimeCardComponent,
  NotesComponent,
  PhotosComponent
} from '../interfaces/mechanics';
import { MechanicsDirtyService } from './mechanics-dirty.service';
import { DailyLogComponentIdentifiers, DailyLogInfoComponentSerializerInfo } from '../interfaces/daily-log-shared';
import { MechanicsComponentIdentifiers, MechanicsInfoComponentSerializerInfo } from '../interfaces/mechanics-shared';
import { IComponentService } from '../interfaces/component-service.interface';
import { ComponentView } from '../interfaces/project-components';
import { ConstructionPhotosService } from './photos.service';
import { Photo } from '../interfaces/photos';
import { ToastrService } from 'ngx-toastr';
import { MechanicsApprovalsService } from './mechanics-approvals.service';
import { MechanicsApproval } from '../interfaces/mechanics-approval';
import { AuthService } from 'src/app/shared/data-access/auth.service';

/**
 * Utility: Recursively remove specified fields from all objects/arrays in the payload
 * Following the same pattern as project daily log service
 */
function deepStripFields(obj: any, fields: string[] = []): any {
  if (Array.isArray(obj)) {
    return obj.map(item => deepStripFields(item, fields));
  } else if (obj && typeof obj === 'object') {
    const rest = { ...obj };
    for (const field of fields) {
      delete rest[field];
    }
    for (const key in rest) {
      if (rest.hasOwnProperty(key)) {
        rest[key] = deepStripFields(rest[key], fields);
      }
    }
    return rest;
  }
  return obj;
}


/**
 * Mechanics Daily Log Service
 *
 * Provides comprehensive state management for mechanics daily log functionality
 * 
 * NOTE: This service is NOT providedIn: 'root' because:
 * 1. It injects ConstructionPhotosService which must be component-level for photo isolation
 * 2. Follows the same pattern as project DailyLogService for consistency
 * 3. Allows each mechanics diary instance to have isolated state
 * 4. Prevents dependency injection errors between different injection contexts
 */
@Injectable()
export class MechanicsDailyLogService {
  private http = inject(HttpClient);
  private baseUrl = `${environment.services_root_endpoints.mechanics}/daily-log`;

  // Services for data management
  private dirtyService = inject(MechanicsDirtyService);
  private constructionPhotosService = inject(ConstructionPhotosService);
  private toastrService = inject(ToastrService);
  private mechanicsApprovalsService = inject(MechanicsApprovalsService);
  private authService = inject(AuthService);

  // Component services registry (following project daily log pattern)
  private componentServices: { [key: string]: IComponentService<any> } = {};

  // Signal-based state management
  private _currentDailyLog = signal<UserDailyLogComponent | null>(null);
  private _selectedDate = signal<Date>(new Date());
  private _isLoading = signal(false);
  private _isDirty = signal(false);
  private _shouldReloadResource = signal(false);
  private _shouldLoadLatestTimeCard = signal(false);
  private _alertMessage = signal('');
  private _approval = signal<MechanicsApproval | null>(null);
  private _isSubmitting = signal(false);
  private _isApproving = signal(false);
  private _isPersisting = signal(false);

  // Flag to prevent effect from setting isLoading to false during submit/unsubmit (following project pattern)
  private inSubmitOrUnsubmit = false;

  // Public readonly signals
  currentDailyLog = this._currentDailyLog.asReadonly();
  selectedDate = this._selectedDate.asReadonly();
  isLoading = this._isLoading.asReadonly();
  alertMessage = this._alertMessage.asReadonly();
  approval = this._approval.asReadonly();
  isSubmitting = this._isSubmitting.asReadonly();
  isApproving = this._isApproving.asReadonly();
  isPersisting = this._isPersisting.asReadonly();

  // Computed dirty state that combines local dirty state with component dirty states
  isDirty = computed(() => {
    const localDirty = this._isDirty();
    const dateString = this.currentDateString();
    const componentsDirty = this.dirtyService.anyDirty(dateString)();
    return localDirty || componentsDirty;
  });

  // Computed signals
  currentDailyLogId = computed(() => {
    const log = this._currentDailyLog();
    return log?._id || (log as any)?.Id;
  });
  hasTimeCardData = computed(() => 
    this._currentDailyLog()?.Components?.some(c => c.ComponentIdentifier === 'TimeCard') ?? false
  );

  currentDateString = computed(() => {
    const date = this._selectedDate();
    return date.toISOString().split('T')[0];
  });

  // rxResource for daily log data fetching  
  dailyLogResource = rxResource({
    request: () => ({
      date: this.currentDateString(),
      shouldReload: this._shouldReloadResource()
    }),
    loader: ({ request }) => from(this.fetchDailyLog(request.date))
  });

  // rxResource for latest timecard (copy last entry functionality)
  latestTimeCardResource = rxResource({
    request: () => ({
      shouldLoad: this._shouldLoadLatestTimeCard()
    }),
    loader: ({ request }) => {
      if (request.shouldLoad) {
        return from(this.fetchLatestTimeCard());
      }
      return of(null);
    }
  });

  constructor() {
    // Auto-load daily log when date changes or init is triggered
    effect(() => {
      this._isLoading.set(this.dailyLogResource.isLoading());
      const data = this.dailyLogResource.value() as UserDailyLogComponent | null;
      
      // Photos are now handled by isolated MechanicsPhotosService
      
      if (data) {
        this._currentDailyLog.set(data);
        // Update all child component services with new data
        this.updateAllComponentServices(data);
      } else if (!this.dailyLogResource.isLoading() && this.dailyLogResource.hasValue()) {
        // No daily log exists for this date, create an in-memory template (matching project diary pattern)
        const newDailyLog = this.createBasicDailyLogInMemory();
        this._currentDailyLog.set(newDailyLog);
        // Update all child component services with empty data
        this.updateAllComponentServices(newDailyLog);
      }
    });

    // Handle latest timecard data when it's loaded
    effect(() => {
      const latestData = this.latestTimeCardResource.value() as LatestDailyLogResponse | null;
      if (latestData?.dailyLog && latestData.hasTimeCard) {
        this.copyTimeCardData(latestData.dailyLog);
        // Reset the trigger signal after processing
        this._shouldLoadLatestTimeCard.set(false);
      }
    });
    
    // Register this service instance with the dirty service for date-based tracking
    effect(() => {
      const dateString = this.currentDateString();
      this.dirtyService.registerService(dateString, this);
    });
  }

  // Public methods
  setSelectedDate(date: Date): void {
    // Clear current state before switching to new date (prevent stale data)
    this._currentDailyLog.set(null);
    
    // Set new date which will automatically trigger resource reload due to computed dependencies
    this._selectedDate.set(date);
  }

  async saveDailyLog(dailyLog: UserDailyLogComponent): Promise<void> {
    this._isPersisting.set(true);
    this._isLoading.set(true);

    try {
      // FOLLOW PROJECT PATTERN: Always call processPhotosAndSave, let it decide internally
      // This ensures photos are always checked at the right time during the save process
      await this.processPhotosAndSave(dailyLog);

      // Clear dirty states
      const currentDateString = this.currentDateString();
      this.dirtyService.clearDirty(currentDateString);
      this._isDirty.set(false);

      this.reload();
    } finally {
      this._isPersisting.set(false);
      this._isLoading.set(false);
    }
  }

  /**
   * Deep field stripping utility - exact copy from project daily log service
   * Removes problematic fields that cause JsonElement serialization errors
   */
  private deepStripFields(obj: any, fields: string[] = []): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepStripFields(item, fields));
    }

    if (typeof obj === 'object') {
      const stripped: any = {};
      Object.keys(obj).forEach(key => {
        if (!fields.includes(key)) {
          stripped[key] = this.deepStripFields(obj[key], fields);
        }
      });
      return stripped;
    }

    return obj;
  }

  /**
   * Prepares daily log for saving - following exact project daily log pattern
   * Cleans up data structures and applies _t discriminators
   */
  private prepareComponentForSaving(dailyLog: UserDailyLogComponent): UserDailyLogComponent {
    // First prepare components like project daily log does
    const preparedLog = {
      ...dailyLog,
      Components: dailyLog.Components?.map(comp => {
        if (comp.ComponentIdentifier === MechanicsComponentIdentifiers.TIMECARD) {
          const timeCardComp = comp as MechanicsTimeCardComponent;
          return {
            ...timeCardComp,
            TimeCards: timeCardComp.TimeCards?.map((tc: any) => {
              // Remove the Id property from TimeCard
              const { Id, ...timeCardRest } = tc;

              // Process cost codes to transform frontend format to backend CostCodeTimeEntry format
              if (timeCardRest.CostCodes && timeCardRest.CostCodes.length > 0) {
                timeCardRest.CostCodes = timeCardRest.CostCodes.map((cc: any) => {
                  // Transform from frontend format to backend CostCodeTimeEntry structure (matching project CostCode pattern)
                  return {
                    CostCodeId: cc.CostCodeId || '',
                    Phase: cc.Phase || '',
                    Description: cc.Description || '',
                    UnitOfMeasure: cc.UnitOfMeasure || 'hour',
                    QtyCompl: cc.QtyCompl || cc.Hours || null, // decimal? type in backend
                    Hours: cc.Hours || null,                   // decimal? type in backend
                    Units: cc.Units || null,                   // decimal? type in backend  
                    UserValue: cc.UserValue || null           // decimal? type in backend
                  };
                });
              }

              // Process equipment trackers if present
              if (timeCardRest.EquipmentTrackers && timeCardRest.EquipmentTrackers.length > 0) {
                timeCardRest.EquipmentTrackers = timeCardRest.EquipmentTrackers.map((eq: any) => {
                  // Remove any properties that might cause serialization issues
                  const { 
                    _t, 
                    InternalId,
                    CustomId,
                    ...equipmentRest 
                  } = eq;
                  
                  // Clean equipment cost codes too
                  if (equipmentRest.CostCodes && equipmentRest.CostCodes.length > 0) {
                    equipmentRest.CostCodes = equipmentRest.CostCodes.map((cc: any) => {
                      const { _t, InternalId, CustomId, ...costCodeRest } = cc;
                      return costCodeRest;
                    });
                  }
                  
                  return equipmentRest;
                });
              }

              return timeCardRest;
            }) || []
          };
        } else if (comp.ComponentIdentifier === MechanicsComponentIdentifiers.PHOTOS) {
          const photosComp = comp as any;
          return {
            ...photosComp,
            Photos: photosComp.Photos?.map((photo: any) => {
              // Ensure DateTime fields are either null or valid date strings, never empty objects
              return {
                ...photo,
                UploadDate: (photo.UploadDate && typeof photo.UploadDate === 'object' && Object.keys(photo.UploadDate).length === 0) 
                  ? null 
                  : photo.UploadDate,
                LastModifiedDate: (photo.LastModifiedDate && typeof photo.LastModifiedDate === 'object' && Object.keys(photo.LastModifiedDate).length === 0) 
                  ? null 
                  : photo.LastModifiedDate
              };
            }) || []
          };
        }
        return comp;
      }) || []
    };

    // Then strip problematic fields from entire payload like project daily log
    const cleanedLog = this.deepStripFields(preparedLog, ['InternalId', 'CustomId']) as UserDailyLogComponent;
    
    // Apply _t discriminators to main component like project diary
    cleanedLog._t = ['BaseEntity', 'UserComponent', 'UserDailyLogComponent'];
    
    // Apply _t discriminators to Components like project diary
    if (cleanedLog.Components) {
      cleanedLog.Components.forEach((component: any) => {
        component._t = MechanicsInfoComponentSerializerInfo.getInstance()
          .getSerializerInfo(component.ComponentIdentifier);
      });
    }

    return cleanedLog;
  }


  importRecentTimeCard(): void {
    // Trigger latest timecard data load
    this._shouldLoadLatestTimeCard.set(true);
  }

  setCurrentDailyLog(dailyLog: UserDailyLogComponent): void {
    this._currentDailyLog.set(dailyLog);
  }

  reload(): void {
    // Clear current state and trigger resource reload
    this._currentDailyLog.set(null);
    
    // Toggle reload signal to trigger resource refresh
    this._shouldReloadResource.set(!this._shouldReloadResource());
  }

  /**
   * Required by MechanicsDirtyService interface to check if this service is persisting data
   */
  isPersistingSignal(): boolean {
    return this._isPersisting() || this._isLoading();
  }

  /**
   * Mark the current daily log as dirty (has unsaved changes)
   */
  markDirty(): void {
    this._isDirty.set(true);
  }

  /**
   * Parent-orchestrated data flow: Update all child component services with current daily log data
   * Following project daily log pattern for proper component initialization
   */
  private updateAllComponentServices(dailyLog: UserDailyLogComponent): void {
    // Update each component service with its corresponding data (following project pattern)
    this.updateNotesService(dailyLog);
    this.updatePhotosService(dailyLog);
    // TimeCard data is handled via component output events
  }

  /**
   * Register a component service (following project daily log pattern)
   */
  registerComponentService(identifier: string, service: IComponentService<any>): void {
    this.componentServices[identifier] = service;
  }

  /**
   * Update notes service with current daily log data (following project daily log pattern)
   */
  private updateNotesService(dailyLog: UserDailyLogComponent): void {
    const notesService = this.componentServices[MechanicsComponentIdentifiers.NOTES];
    if (!notesService) return;
    
    // Set current date for service if it has the method
    if ('setCurrentDate' in notesService) {
      const dateString = this.currentDateString();
      (notesService as any).setCurrentDate(dateString);
    }
    
    // Set initial data
    const notesComponent = dailyLog.Components?.find(c => c.ComponentIdentifier === MechanicsComponentIdentifiers.NOTES);
    notesService.setInitialData(notesComponent, ComponentView.Edit, 'Allow');
  }

  /**
   * Update notes service with current daily log data (following project daily log pattern)
   */
  private updatePhotosService(dailyLog: UserDailyLogComponent): void {
    const photosService = this.componentServices[MechanicsComponentIdentifiers.PHOTOS];
    if (!photosService) return;
    
    // Set current date for service if it has the method
    if ('setCurrentDate' in photosService) {
      const dateString = this.currentDateString();
      (photosService as any).setCurrentDate(dateString);
    }
    
    // Set initial data
    const photosComponent = dailyLog.Components?.find(c => c.ComponentIdentifier === MechanicsComponentIdentifiers.PHOTOS);
    photosService.setInitialData(photosComponent, ComponentView.Edit, 'Allow');
  }

  /**
   * Update notes component data from service (following project daily log pattern)
   */
  private updateNotesComponentData(dailyLog: UserDailyLogComponent): void {
    const notesService = this.componentServices[MechanicsComponentIdentifiers.NOTES];
    if (!notesService) return;

    const updatedComponent = notesService.getUpdatedComponent();
    const index = dailyLog.Components!.findIndex(c => c.ComponentIdentifier === MechanicsComponentIdentifiers.NOTES);
    
    if (index !== -1) {
      // Update existing component
      dailyLog.Components![index] = updatedComponent;
    } else {
      // Add new component if it doesn't exist
      dailyLog.Components!.push(updatedComponent);
    }
  }

  /**
   * Update photos component data from service (following project daily log pattern)
   */
  private updatePhotosComponentData(dailyLog: UserDailyLogComponent): void {
    const photosService = this.componentServices[MechanicsComponentIdentifiers.PHOTOS];
    if (!photosService) {
      console.log('MECHANICS: No photos service found during updatePhotosComponentData');
      return;
    }

    const updatedComponent = photosService.getUpdatedComponent();
    console.log('MECHANICS: Photos service returned component:', updatedComponent);
    
    const index = dailyLog.Components!.findIndex(c => c.ComponentIdentifier === MechanicsComponentIdentifiers.PHOTOS);
    
    if (index !== -1) {
      // Update existing component
      dailyLog.Components![index] = updatedComponent;
      console.log('MECHANICS: Updated existing photos component at index:', index);
    } else {
      // Add new component if it doesn't exist
      dailyLog.Components!.push(updatedComponent);
      console.log('MECHANICS: Added new photos component to daily log');
    }
  }

  /**
   * Update daily log components from external sources
   * Collects data from all child component services and updates the current daily log
   */
  updateFromChildServices(): void {
    const currentLog = this._currentDailyLog();
    if (!currentLog) return;

    // Create a copy of the current log to update
    const updatedLog = { ...currentLog };

    // Ensure Components array exists
    if (!updatedLog.Components) {
      updatedLog.Components = [];
    }

    // Update all component data from their respective services (following project pattern)
    this.updateNotesComponentData(updatedLog);
    this.updatePhotosComponentData(updatedLog);
    // TimeCard data is handled via component output events

    // Update the current daily log with collected data
    this._currentDailyLog.set(updatedLog);
  }



  private createBasicDailyLogInMemory(): UserDailyLogComponent {
    const selectedDate = this._selectedDate();
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth() + 1;
    const day = selectedDate.getDate();
    
    // Create in-memory template (no _id so it's treated as new when saved)
    return {
      _id: '', // Empty ID means this is a new log not yet saved to database
      ComponentIdentifier: 'MechanicsDailyLog',
      UserId: '', // Will be populated from auth when saving
      OwnerAccountId: '',
      DateCreated: new Date().toISOString(),
      ModifiedDate: new Date().toISOString(),
      CreatedBy: '',
      ModifiedBy: '',
      IsActive: true,
      Name: 'Mechanics Daily Log',
      Year: year,
      Month: month,
      Day: day,
      NoWork: false,
      Components: [
        // Create basic component shells for UI (no _t - added during save)
        {
          ComponentIdentifier: "Notes",
          Note: ""
        },
        {
          ComponentIdentifier: "Photos",
          Photos: []
        },
        {
          ComponentIdentifier: "TimeCard",
          TimeCards: []
        }
      ]
    };
  }

  // Private implementation methods
  private async fetchDailyLog(dateString: string): Promise<UserDailyLogComponent | null> {
    try {
      const params = new URLSearchParams({ date: dateString });
      const response = await firstValueFrom(
        this.http.get<any>(`${this.baseUrl}?${params}`)
      );
      
      // Map API response field names to interface field names
      if (response) {
        return {
          ...response,
          _id: response.Id || response._id, // Map Id to _id
          Id: undefined // Remove the original Id field to avoid confusion
        } as UserDailyLogComponent;
      }
      
      return response;
    } catch (error) {
      // If no daily log exists for this date, return null
      console.log('No daily log found for date:', dateString);
      return null;
    }
  }

  private async fetchLatestTimeCard(): Promise<LatestDailyLogResponse> {
    try {
      const params = new URLSearchParams({
        latest: 'true',
        includeTimeCard: 'true'
      });
      const response = await firstValueFrom(
        this.http.get<LatestDailyLogResponse>(`${this.baseUrl}?${params}`)
      );
      return response;
    } catch (error) {
      console.error('Error fetching latest timecard:', error);
      return { dailyLog: null, hasTimeCard: false, success: false };
    }
  }

  private async createDailyLog(request: CreateDailyLogRequest): Promise<UserDailyLogComponent> {
    const response = await firstValueFrom(
      this.http.post<UserDailyLogComponent>(this.baseUrl, request)
    );
    return response;
  }

  private async updateDailyLog(dailyLogId: string, request: UpdateDailyLogRequest): Promise<UserDailyLogComponent> {
    const response = await firstValueFrom(
      this.http.put<UserDailyLogComponent>(`${this.baseUrl}/${dailyLogId}`, request)
    );
    return response;
  }

  /**
   * Delete a daily log by ID (following project daily log business rules)
   */
  async deleteDailyLog(dailyLogId: string): Promise<void> {
    await firstValueFrom(
      this.http.delete(`${this.baseUrl}/${dailyLogId}`)
    );
  }

  private copyTimeCardData(source: UserDailyLogComponent): void {
    // Find the timecard component in the source daily log
    const timeCardComponent = source.Components?.find(c => 
      c.ComponentIdentifier === 'TimeCard'
    ) as MechanicsTimeCardComponent;
    
    if (timeCardComponent) {
      const copiedData = this.resetTimeCardValues(timeCardComponent);
      this.updateCurrentTimeCard(copiedData);
    }
  }

  private resetTimeCardValues(timeCard: MechanicsTimeCardComponent): MechanicsTimeCardComponent {
    // Copy structure but reset transactional values (hours/quantities)
    return {
      ...timeCard,
      TimeCards: timeCard.TimeCards.map(tc => ({
        ...tc,
        EquipmentTrackers: tc.EquipmentTrackers.map(et => ({
          ...et,
          TotalHours: "0",
          CostCodes: et.CostCodes.map(cc => ({ ...cc, QtyCompl: 0 }))
        }))
      }))
    };
  }

  private updateCurrentTimeCard(timeCard: MechanicsTimeCardComponent): void {
    const current = this._currentDailyLog();
    if (current) {
      const updated: UserDailyLogComponent = {
        ...current,
        Components: current.Components.map(c => 
          c.ComponentIdentifier === 'TimeCard' ? timeCard : c
        )
      };
      this._currentDailyLog.set(updated);
      this._isDirty.set(true);
    } else {
      // Create a new daily log with the copied timecard
      const newDailyLog: UserDailyLogComponent = {
        _id: '',
        _t: ['BaseEntity', 'UserComponent', 'UserDailyLogComponent'],
        ComponentIdentifier: 'MechanicsDailyLog',
        UserId: '',
        OwnerAccountId: '',
        DateCreated: new Date().toISOString(),
        ModifiedDate: new Date().toISOString(),
        CreatedBy: '',
        ModifiedBy: '',
        IsActive: true,
        Name: 'Mechanics Daily Log',
        Year: this.selectedDate().getFullYear(),
        Month: this.selectedDate().getMonth() + 1,
        Day: this.selectedDate().getDate(),
        NoWork: false,
        Components: [timeCard]
      };
      this._currentDailyLog.set(newDailyLog);
      this._isDirty.set(true);
    }
  }

  /**
   * Process photos and save daily log - mirrors project daily log pattern
   */
  private async processPhotosAndSave(dailyLog: UserDailyLogComponent): Promise<void> {
    const photosToUpload = this.constructionPhotosService.projectPhotosToAdd();
    const photosToRemove = this.constructionPhotosService.projectPhotosToRemove();

    // DEBUG: Log detailed photo detection info
    console.log('MECHANICS processPhotosAndSave DEBUG:', {
      photosToUpload: photosToUpload.length,
      photosToRemove: photosToRemove.length,
      constructionPhotosProjectId: this.constructionPhotosService.projectId(),
      photosToUploadData: photosToUpload,
      photosToRemoveData: photosToRemove
    });

    const hasPhotoChanges = photosToRemove.length > 0 || photosToUpload.length > 0;

    if (hasPhotoChanges) {
      console.log('MECHANICS: Setting alert message - Saving Photos...');
      this._alertMessage.set('Saving Photos...');
    } else {
      console.log('MECHANICS: No photo changes detected, proceeding with regular save');
    }

    // Process photos to remove regardless of whether there are photos to upload
    if (photosToRemove.length > 0) {
      // Update the photos component to remove marked photos
      this.updatePhotosComponent(dailyLog, []);
    }

    // Process photos to upload if there are any
    if (photosToUpload.length > 0) {
      try {
        // Get or generate component ID for photo upload
        const componentId = dailyLog._id || this.generateTempComponentId();

        // Process photos using construction photos service
        const photos = await firstValueFrom(
          this.constructionPhotosService.processPhotos(componentId).pipe(
            tap(photos => this.updatePhotosComponent(dailyLog, photos)),
            switchMap(() => from(this.saveDailyLogWithoutPhotos(dailyLog))),
            tap(() => {
              // Clear photos service and alert message
              this.constructionPhotosService.clear();
              this._alertMessage.set('');
            }),
            catchError(err => {
              this._alertMessage.set('');
              this.toastrService.error('There was an error saving the photos');
              throw err;
            })
          )
        );
      } catch (error) {
        this._alertMessage.set('');
        throw error;
      }
    } else {
      // If there are no photos to upload, just save the log
      try {
        await this.saveDailyLogWithoutPhotos(dailyLog);
        this.constructionPhotosService.clear();
        this._alertMessage.set('');
      } catch (error) {
        this._alertMessage.set('');
        this.toastrService.error('There was an error saving the log');
        throw error;
      }
    }
  }

  /**
   * Save daily log without photo processing - original save logic
   */
  private async saveDailyLogWithoutPhotos(dailyLog: UserDailyLogComponent): Promise<void> {
    // Prepare component for saving by cleaning up data structures (following project pattern)
    const cleanedDailyLog = this.prepareComponentForSaving(dailyLog);

    // CRITICAL: Set the _t field on the main component (following project pattern)
    cleanedDailyLog._t = ['UserDailyLogComponent'];

    // Check for both _id and Id fields (API returns Id, interface expects _id)
    const dailyLogId = cleanedDailyLog._id || (cleanedDailyLog as any).Id;

    if (dailyLogId) {
      // Update existing daily log with cleaned components
      await this.updateDailyLog(dailyLogId, {
        NoWork: cleanedDailyLog.NoWork,
        Components: cleanedDailyLog.Components || []
      });
    } else {
      // Create new daily log with properly cleaned components
      const savedLog = await this.createDailyLog({
        Year: cleanedDailyLog.Year,
        Month: cleanedDailyLog.Month,
        Day: cleanedDailyLog.Day,
        NoWork: cleanedDailyLog.NoWork,
        Components: cleanedDailyLog.Components || [] // Send cleaned components, not empty array
      });

      // Update the daily log with the returned ID
      const updatedLog = { ...dailyLog, _id: savedLog._id };
      this._currentDailyLog.set(updatedLog);
    }
  }

  /**
   * Update photos component with uploaded photos - mirrors project daily log pattern
   */
  private updatePhotosComponent(dailyLog: UserDailyLogComponent, photos: Photo[]): void {
    const photosComponent = dailyLog.Components?.find(
      c => c.ComponentIdentifier === MechanicsComponentIdentifiers.PHOTOS
    ) as PhotosComponent;

    if (photosComponent) {
      if (!photosComponent.Photos) photosComponent.Photos = [];
      if (photos?.length) {
        // Transform Photo objects to match backend PhotoInfo structure (remove Data field)
        const photoInfos = photos.map(photo => ({
          PhotoId: photo.PhotoId,
          FileName: photo.FileName || '',
          Title: photo.Title || '',
          Description: photo.Description || '',
          Height: photo.Height,
          Width: photo.Width,
          UploadDate: photo.UploadDate,
          ThumbnailStorage: photo.ThumbnailStorage,
          LargeStorage: photo.LargeStorage,
          LastModifiedDate: photo.LastModifiedDate,
          PhotoType: photo.PhotoType || '',
          PhotoSize: photo.PhotoSize || 0
          // Note: Deliberately omitting 'Data' field as backend PhotoInfo doesn't have it
        }));
        photosComponent.Photos.push(...photoInfos);
      }

      // Process photos marked for deletion
      // This removes them from the Photos array, which signals to the backend
      // that they should be deleted from S3 and the database
      const photosToRemove = this.constructionPhotosService.projectPhotosToRemove();
      if (photosToRemove.length > 0) {
        photosToRemove.forEach(photoId => {
          const index = photosComponent.Photos.findIndex(p => p.PhotoId === photoId);
          if (index > -1) {
            // Remove the photo from the array
            photosComponent.Photos.splice(index, 1);
          }
        });
      }
    }
  }

  /**
   * Generate temporary component ID for photo uploads
   *
   * This generates a temporary daily log component ID for photo uploads when
   * the daily log hasn't been saved yet (no _id). This follows the same pattern
   * as project daily logs.
   *
   * @returns Temporary component ID for unsaved daily logs
   */
  private generateTempComponentId(): string {
    // Generate a temporary ID similar to how MongoDB ObjectIds work
    // This will be replaced with the actual _id when the daily log is saved
    return `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Submits the current log for approval - following exact project pattern
   */
  public submit(): void {
    if (this._isPersisting() || this._isLoading()) return;

    if (this._approval()) return;

    const component = this._currentDailyLog();
    if (!component) {
      this.toastrService.error('No daily log to submit');
      return;
    }

    // Basic validation (can be enhanced later with more specific rules)
    if (!this.validateSubmissionPrerequisites(component)) {
      return;
    }

    this._isSubmitting.set(true);
    this.inSubmitOrUnsubmit = true;

    // Step 1: Save the daily log
    from(this.saveDailyLog(component)).pipe(
      // Step 2: Add approval
      switchMap(() => this.addApproval()),
      // Step 3: Show success message and update UI
      tap(approval => {
        if (!approval) {
          throw new Error('Failed to add approval');
        }

        // Set approval state
        this._approval.set(approval);
        this._isApproving.set(false);
        
        // Show success message
        this.toastrService.success('Log has been submitted');
        
        // Update view states (implement reactive reload)
        this.updateViewStates();
        
        // Clear the flag before releasing the loading state
        this.inSubmitOrUnsubmit = false;
        this._isSubmitting.set(false);
      }),
      catchError(error => {
        this.toastrService.error('There was an issue submitting the log');
        this._isApproving.set(false);
        
        // Clear the flag in case of error
        this.inSubmitOrUnsubmit = false;
        this._isSubmitting.set(false);
        
        return of(null);
      })
    ).subscribe();
  }

  /**
   * Adds approval to the current log - following exact project pattern
   */
  private addApproval(): Observable<MechanicsApproval> {
    this._isApproving.set(true);

    const component = this._currentDailyLog();
    if (!component?._id) {
      throw new Error('Missing daily log ID for approval');
    }

    return from(this.authService.getSubId()).pipe(
      switchMap(userId => {
        // Create approval info following exact project pattern
        const approvalInfo = {} as MechanicsApproval;
        approvalInfo.DailyLogId = component._id!;
        approvalInfo.EmployeeUserId = userId;
        approvalInfo.DailyLogTimeStamp = new Date(
          this._selectedDate().getFullYear(),
          this._selectedDate().getMonth(),
          this._selectedDate().getDate()
        );

        // Let the service add approval levels
        return this.mechanicsApprovalsService.AddApproval(userId, approvalInfo);
      }),
      tap(approval => this._approval.set(approval)),
      catchError(err => {
        throw err;
      })
    );
  }

  /**
   * Removes approval from the current log - following exact project pattern
   */
  unSubmit(): void {
    const approvalId = this._approval()?.Id;
    if (!approvalId) return;

    this._isSubmitting.set(true);
    this.inSubmitOrUnsubmit = true;

    this.mechanicsApprovalsService.UnSubmit(approvalId).pipe(
      tap(() => {
        // Set approval state immediately to prevent UI flicker
        this._approval.set(null);

        // Show success message
        this.toastrService.success('Log has been unsubmitted');

        // Update view states (reactive state management)
        this.updateViewStates();
        
        // Clear the flag before releasing the loading state
        this.inSubmitOrUnsubmit = false;
        this._isSubmitting.set(false);
      }),
      catchError(error => {
        this.toastrService.error('There was an issue unsubmitting the log');
        
        // Clear the flag in case of error
        this.inSubmitOrUnsubmit = false;
        this._isSubmitting.set(false);
        
        return of(null);
      })
    ).subscribe();
  }

  /**
   * Updates view states - reactive state management following project pattern
   */
  private updateViewStates(): void {
    // Trigger reactive updates similar to project pattern
    this.mechanicsApprovalsService.reload();
    this.reload();
  }

  /**
   * Validates submission prerequisites - following project pattern
   */
  private validateSubmissionPrerequisites(component: UserDailyLogComponent): boolean {
    if (!component.Components) {
      this.toastrService.error('Missing data. Could not submit');
      return false;
    }

    // Add any mechanics-specific validation rules here
    // For now, basic validation passes - can be enhanced later
    return true;
  }

  /**
   * Set "No Work" status for the current daily log
   * Following the same pattern as project daily-log service
   */
  setNoWork(isNoWork: boolean): void {
    const currentLog = this._currentDailyLog();
    if (!currentLog) {
      // Create a new daily log with NoWork status
      const newLog = this.createBasicDailyLogInMemory();
      newLog.NoWork = isNoWork;
      this._currentDailyLog.set(newLog);
      this._isDirty.set(true);
      return;
    }

    // Update existing log
    const updatedLog: UserDailyLogComponent = {
      ...currentLog,
      NoWork: isNoWork,
      ModifiedDate: new Date().toISOString()
    };

    if (currentLog._id) {
      // Existing log - call backend endpoint
      this.http.patch(`${this.baseUrl}/no-work?dailyLogId=${currentLog._id}&isNoWork=${isNoWork}`, null)
        .pipe(
          tap(() => {
            this._currentDailyLog.set(updatedLog);
            this.toastrService.success('No Work status updated', 'Success');
          }),
          catchError(error => {
            this.toastrService.error('Failed to update No Work status', 'Error');
            console.error('Error updating No Work status:', error);
            return of(null);
          })
        ).subscribe();
    } else {
      // New log - save first, then set NoWork
      this.saveDailyLog(updatedLog).then(() => {
        // After save, the NoWork status will be persisted as part of the full save
        this.toastrService.success('No Work status updated', 'Success');
      }).catch(error => {
        this.toastrService.error('Failed to update No Work status', 'Error');
        console.error('Error saving new log with NoWork status:', error);
      });
    }
  }
}
