import { Injectable, inject, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { UserProjectComponentsService } from './user-project-components.service';
import { ProjectComponentIdentifiers, ProjectComponentSerializerInfo } from '../interfaces/project-components';
import { DailyLogProjectComponent, DailyLogUserProjectComponent, SiteConditionsComponent, TimeCardComponent, WeatherComponent } from '../interfaces/daily-log-user-project';
import { ConstructionGlobalService } from './global.service';
import { BaseService } from './base.service';
import { DailyLogComponentIdentifiers, DailyLogInfoComponentSerializerInfo } from '../interfaces/daily-log-shared';

@Injectable({
  providedIn: 'root'
})
export class ConstructionDailyLogService extends BaseService {
  globalProjectService = inject(ConstructionGlobalService);
  userProjectComponentsService = inject(UserProjectComponentsService);
  dailyLogUserComponent = signal<DailyLogUserProjectComponent | null>(null);
  weatherComponent = signal<WeatherComponent | null>(null);
  siteConditionsComponent = signal<SiteConditionsComponent | null>(null);
  timeCardComponent = signal<TimeCardComponent | null>(null);

  getSubComponent<Type extends DailyLogProjectComponent>(componentIdentifier: DailyLogComponentIdentifiers): Observable<DailyLogProjectComponent | undefined> {
    if(componentIdentifier === DailyLogComponentIdentifiers.WEATHER && this.weatherComponent()){   
        return of(this.weatherComponent() as WeatherComponent);      
    }else if (componentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS && this.siteConditionsComponent()){    
        return of(this.siteConditionsComponent() as SiteConditionsComponent);      
    }else if (componentIdentifier === DailyLogComponentIdentifiers.TIMECARD && this.timeCardComponent()){    
      return of(this.timeCardComponent() as TimeCardComponent);      
     }else{
      return new Observable<Type>((observer) => {
        this.getUserStoreDailyLogComponent().subscribe({
          next: (result) => {
            if (result) {
              var component = result.Components.find(x => x.ComponentIdentifier === componentIdentifier) as DailyLogProjectComponent | undefined;
              switch (componentIdentifier) {
                case DailyLogComponentIdentifiers.WEATHER:
                  this.weatherComponent.set(component as WeatherComponent);
                  break;
                case DailyLogComponentIdentifiers.SITE_CONDITIONS:
                  this.siteConditionsComponent.set(component as SiteConditionsComponent);
                  break;
                case DailyLogComponentIdentifiers.TIMECARD:
                  this.timeCardComponent.set(component as TimeCardComponent);
                  break;
              }
              observer.next(component as Type);
              observer.complete();
            } else {
              observer.error('Daily Log component not found');
            }
          },
          error: (err) => {
            observer.error(err);
          }
        });
      });
    }

  }

  updateSubComponent<Type extends DailyLogProjectComponent>(component: Type): Observable<Type> {

    return new Observable<Type>((observer) => {
      this.getUserStoreDailyLogComponent().subscribe({
        next: (result) => {
          if (result) {
            var subComponent = result.Components.find(x => x.ComponentIdentifier === component.ComponentIdentifier) as DailyLogProjectComponent;
            if (subComponent && component) {
              subComponent = { ...component as Type };

              this.updateDailyLogComponent(result).subscribe({
                next: (info) => {
                  switch (component.ComponentIdentifier) {
                    case DailyLogComponentIdentifiers.WEATHER:
                      component._t = ["WeatherInfoComponent"];
                      this.weatherComponent.set(subComponent as WeatherComponent);
                      break;
                    case DailyLogComponentIdentifiers.SITE_CONDITIONS:
                      component._t = ["SiteConditionInfoComponent"];
                      this.siteConditionsComponent.set(subComponent as SiteConditionsComponent);
                      break;
                  }
                  observer.next(subComponent as Type);
                  observer.complete();
                },
                error: (err) => {
                  observer.error(err);
                }
              });
            }
          } else {
            observer.error('Daily Log component not found');
          }
        },
        error: (err) => {
          observer.error(err);
        }
      });
    });


  }
  updateDailyLogComponent(component: DailyLogUserProjectComponent) {
    return new Observable<DailyLogUserProjectComponent>((observer) => {
      this.serializeDailyLogUserProjectComponent(component);
      this.userProjectComponentsService.replaceComponentToStore(component).subscribe({
        next: (result) => {
          this.dailyLogUserComponent.set(component);
          observer.next(result);
          observer.complete();
        },
        error: (err) => {
          observer.error(err);
        }
      });
    });
  }

  serializeDailyLogComponent(component: DailyLogUserProjectComponent) {

    component._t = ProjectComponentSerializerInfo.getInstance().getSerializerInfo(ProjectComponentIdentifiers.DAILY_LOG);

    for (let c of component.Components) {
      c._t = DailyLogInfoComponentSerializerInfo.getInstance().getSerializerInfo(c.ComponentIdentifier);
    }
  }

  serializeDailyLogUserProjectComponent(component: DailyLogUserProjectComponent) {

    component._t =  ['DailyLogComponent'];

    for (let c of component.Components) {
      c._t = DailyLogInfoComponentSerializerInfo.getInstance().getDailyLogUserProjectSerializerInfo(c.ComponentIdentifier);
    }
  }

  getUserStoreDailyLogComponent(): Observable<DailyLogUserProjectComponent | null> {
    if (this.dailyLogUserComponent()) {
      return of(this.dailyLogUserComponent());
    } else {
      return new Observable<DailyLogUserProjectComponent | null>((observer) => {
        this.userProjectComponentsService.getComponentStoreCached().subscribe({
          next: (result) => {
            if (result) {
              var dailyLogComponent = result.Components.find(x => x.ComponentIdentifier === ProjectComponentIdentifiers.DAILY_LOG) as DailyLogUserProjectComponent | undefined;

              if (dailyLogComponent) {
                this.dailyLogUserComponent.set(dailyLogComponent as DailyLogUserProjectComponent);
                observer.next(dailyLogComponent as DailyLogUserProjectComponent);
                observer.complete();


              } else {
                this.globalProjectService.getDefaultComponent<DailyLogUserProjectComponent>(ProjectComponentIdentifiers.DAILY_LOG).subscribe({
                  next: (globalDailyLogComponent) => {
                    this.dailyLogUserComponent.set(globalDailyLogComponent as DailyLogUserProjectComponent);

                    observer.next(globalDailyLogComponent as DailyLogUserProjectComponent);
                    observer.complete();
                  },
                  error: (err) => {
                    observer.error(err);
                  }
                });
              }
            } else {
              this.globalProjectService.getDefaultComponent<DailyLogUserProjectComponent>(ProjectComponentIdentifiers.DAILY_LOG).subscribe({
                next: (globalDailyLogComponent) => {
                  this.dailyLogUserComponent.set(globalDailyLogComponent as DailyLogUserProjectComponent);

                  observer.next(globalDailyLogComponent as DailyLogUserProjectComponent);
                  observer.complete();
                },
                error: (err) => {
                  observer.error(err);
                }
              });
            }
          }
        });
      });
    }

  }


  // getLastDailyLog(projectId: string, year: number, month: number, day: number): Observable<LatestDailyLogReponse>{
  //   return this.http.get<LatestDailyLogReponse>(`${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/last-daily-log?year=${year}&month=${month}&day=${day}`);
  // }
}