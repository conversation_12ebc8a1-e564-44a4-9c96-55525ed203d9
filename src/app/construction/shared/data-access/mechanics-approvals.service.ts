import { Injectable, computed, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { rxResource } from '@angular/core/rxjs-interop';
import { of, tap, catchError, forkJoin, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { ToastrService } from 'ngx-toastr';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessRequest } from '../../../shared/interfaces/access';
import { 
  MechanicsApproval, 
  MechanicsApprovalListResponse, 
  MechanicsApprovalsFilter,
  MechanicsApprovalStatus,
  MechanicsApprovalAccess,
  MechanicsApprovalInfo
} from '../interfaces/mechanics-approval';
import { GetApprovalLevelResponse, ApprovalLevel, approvalLevelsInfos } from '../interfaces/approval';

@Injectable({
  providedIn: 'root'
})
export class MechanicsApprovalsService {
  
  // Follow exact project-based approval service pattern
  payrollLevel: number = 999;
  
  private http = inject(HttpClient);
  private toastrService = inject(ToastrService);
  private router = inject(Router);
  private accessService = inject(AccessService);

  // Match ApprovalService signal structure exactly
  approvalAccess = signal<MechanicsApprovalAccess | null>(null);
  userApprovalLevels = signal<Array<ApprovalLevel> | null>(null);
  userApprovalLevel = computed(() => this.userApprovalLevelResource.value()?.Level);
  startDate = signal<Date | null>(null);
  endDate = signal<Date | null>(null);
  singleDate = signal<Date | null>(null);
  search = signal<string | null>(null);
  sort = signal<number>(0);
  sortName = signal<string>("Date");
  currentPage = signal<number>(1);
  limit = signal<number>(50);
  initializeApprovals = signal<boolean>(false);
  approvalType = signal<string | null>(null);
  approvalLevel = signal<number>(-1);
  roleId = signal<string | null>(null);
  approvals = computed(() => this.approvalsResource.value()?.Approvals || []);
  approvalsTotal = computed(() => this.approvalsResource.value()?.Total || 0);
  isLoading = computed(() => this.approvalsResource.isLoading() || this.userApprovalLevelResource.isLoading());
  selectedProjectsIds = signal<string[]>([]);
  initializeUserApprovalLevel = signal<boolean>(false);
  isApproving = signal<boolean>(false);
  unApprovingIds = signal<string[]>([]);
  
  // Removed selection management from service to match project-based pattern
  // Selection will be managed at component level like project-based approvals
  private _isBulkProcessing = signal(false);
  private _expandAllApprovals = signal<boolean>(false);
  
  // Public readonly signals for UI state only
  isBulkProcessing = this._isBulkProcessing.asReadonly();
  expandAllApprovals = this._expandAllApprovals.asReadonly();

  // rxResource for user approval level - match project-based pattern exactly
  userApprovalLevelResource = rxResource({
    request: () => ({
      initializeUserApprovalLevel: this.initializeUserApprovalLevel(),
    }),
    loader: (request) => {
      if (request.request.initializeUserApprovalLevel) {
        // Use same endpoint structure as project-based approvals
        return this.http.get<GetApprovalLevelResponse>(`${environment.services_root_endpoints.mechanics}/approval-level`);
      }
      return of(null);
    }
  });

  // Match project-based approval service pattern exactly but adapted for mechanics (user-based)
  approvalsResource = rxResource({
    request: () => ({
      search: this.search(),
      startDate: this.startDate(),      
      endDate: this.endDate(),      
      sortName: this.sortName(),
      sort: this.sort(),
      currentPage: this.currentPage(),
      limit: this.limit(),
      initializeApprovals: this.initializeApprovals(),
      approvalType: this.approvalType(),
      approvalLevel: this.approvalLevel(),
      selectedProjectsIds: this.selectedProjectsIds(),
      userApprovalLevel: this.userApprovalLevel(),
      roleId: this.roleId()
    }),
    loader: (request) => {
      // Match project-based pattern exactly, but without requiring selectedProjectsIds
      if (request.request.initializeApprovals && request.request.userApprovalLevel 
        && (request.request.approvalLevel > 0 || request.request.approvalType === 'all')) {
        
        var url = `${environment.services_root_endpoints.mechanics}/approvals/search`;
        
        let queryParams = new HttpParams();

        if (request.request.sort) {
          let reversed = false;
          if (request.request.sort === 0) {
            reversed = false;
          } else if (request.request.sort === 1) {
            reversed = true;
          }
          queryParams = queryParams.set('reversed', reversed);
        }
        
        if (request.request.search) {
          queryParams = queryParams.set('search', request.request.search);
        }
        
        if (request.request.approvalLevel) {
          queryParams = queryParams.set('level', request.request.approvalLevel.toString());
        }
        
        if (request.request.currentPage) {
          queryParams = queryParams.set('page', request.request.currentPage.toString());
        }
        
        if (request.request.limit) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }

        if (request.request.approvalType) {
          queryParams = queryParams.set('approval-type', request.request.approvalType);
        }

        if (request.request.startDate) {          
          queryParams = queryParams.set('date-start', `${request.request.startDate}`);
        }

        if (request.request.endDate) {
          queryParams = queryParams.set('date-end', `${request.request.endDate}`);
        }

        if (request.request.sortName) {
          queryParams = queryParams.set('sort', request.request.sortName);
        }

        if (request.request.roleId) {
          queryParams = queryParams.set('roleId', request.request.roleId);
        }

        // For mechanics, we don't need ProjectIds - it's user-based
        // Use GET request instead of POST since no body is needed
        return this.http.get<MechanicsApprovalListResponse>(url, { params: queryParams });
      }
      return of(null);
    }
  });

  constructor() {
    // Clean constructor - no effects needed with proper signal-based rxResource
  }

  // Selection management removed from service to match project-based pattern
  // All selection logic moved to component level for consistency

  // UI state management
  toggleExpandAll(): void {
    this._expandAllApprovals.update(expanded => !expanded);
  }
  
  // Data reload - match project-based pattern
  reload(): void {
    this.approvalsResource.reload();
  }

  // Filter management with URL updates
  setSort(sortName: string): void {
    const currentSort = this.sort();
    const newSort = this.sortName() === sortName ? (currentSort === 0 ? 1 : 0) : 0;
    
    this.sortName.set(sortName);
    this.sort.set(newSort);
    this.currentPage.set(1);
    
    this.updateUrlParams({ 
      sortOrder: newSort, 
      sortName: sortName, 
      page: 1 
    });
  }
  
  setPage(page: number): void {
    this.currentPage.set(page);
    this.updateUrlParams({ page });
  }
  
  setSearch(search: string): void {
    this.search.set(search || null);
    this.currentPage.set(1);
    this.updateUrlParams({ search: search || null, page: 1 });
  }
  
  setSingleDate(date: Date): void {
    this.startDate.set(date);
    this.endDate.set(date);
    this.currentPage.set(1);
    this.updateUrlParams({ 
      startDate: this.formatDate(date), 
      endDate: this.formatDate(date),
      page: 1 
    });
  }
  
  setDateRange(startDate: Date, endDate: Date): void {
    this.startDate.set(startDate);
    this.endDate.set(endDate);
    this.currentPage.set(1);
    this.updateUrlParams({ 
      startDate: this.formatDate(startDate), 
      endDate: this.formatDate(endDate),
      page: 1 
    });
  }
  
  clearDate(): void {
    this.startDate.set(null);
    this.endDate.set(null);
    this.currentPage.set(1);
    this.updateUrlParams({ 
      startDate: null, 
      endDate: null,
      page: 1 
    });
  }
  
  // Helper methods
  private formatDate(date: Date): string {
    if (!date) return '';
    return `${date.getMonth() + 1}-${date.getDate()}-${date.getFullYear()}`;
  }
  
  // Navigation and URL management
  updateUrlParams(params: Record<string, any>): void {
    const queryParams = { ...params };
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });
    
    this.router.navigate([], {
      queryParams,
      queryParamsHandling: 'merge'
    });
  }

  // Component-compatible methods - match project-based pattern
  initializeApprovalAccess(): void {
    var accessMulti = new Array<AccessRequest>();
    accessMulti.push({
      Resource: "approval",
      Action: "removeapproval"
    });

    accessMulti.push({
      Resource: "projectusercomponents-dailyLog",
      Action: "removedailylog"
    });

    accessMulti.push({
      Resource: "route-access",
      Action: "access-delete-approval"
    });

    var getAccessMutli = new Array<AccessRequest>();
    getAccessMutli.push({
      Resource: "approval-edit-info",
      Action: "approval-edit-qty-notes"
    });

    forkJoin({ 
      mutliAccess: this.accessService.GetAccessMulti(getAccessMutli), 
      deleteApprovalAccess: this.accessService.CheckAccessMulti(accessMulti) 
    }).subscribe({
      next: (result) => {
        var access = {
          EditQtyNotes: result.mutliAccess[0].Access,
          DeleteApproval: result.deleteApprovalAccess.Access
        } as MechanicsApprovalAccess;

        this.approvalAccess.set(access);
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  initializeUserApprovalLevels() {
    var levels = new Array<ApprovalLevel>();

    approvalLevelsInfos.map(result => {
      var approvalLevel: ApprovalLevel = {
        Level: result.Level,
        Name: result.Name,
        ApprovedDate: null,
        UserApproved: null
      }

      levels.push(approvalLevel);
    });

    this.userApprovalLevels.set(levels);
    this.initializeUserApprovalLevel.set(true);

    return levels;
  }

  // HTTP methods - match project-based approval pattern exactly
  public GetApproval(dailyLogId: string): Observable<MechanicsApproval> {
    return this.http.get<MechanicsApproval>(`${environment.services_root_endpoints.mechanics}/approvals/dailylog/${dailyLogId}`);
  }
  
  public GetApprovalsByEmployeeUserId(employeeUserId: string): Observable<MechanicsApproval[]> {
    return this.http.get<MechanicsApproval[]>(`${environment.services_root_endpoints.mechanics}/approvals?employeeUserId=${employeeUserId}`);
  }

  public AddApproval(employeeUserId: string, approval: MechanicsApproval): Observable<MechanicsApproval> {
    console.log('DEBUG - AddApproval service called with:', { employeeUserId, approval });
    
    // Set approval levels like project pattern
    approval.ApprovalLevels = this.GetApprovalLevels();
    
    console.log('DEBUG - Request body:', JSON.stringify(approval, null, 2));
    return this.http.post<MechanicsApproval>(`${environment.services_root_endpoints.mechanics}/approvals?employeeUserId=${employeeUserId}`, approval);
  }

  public RemoveApproval(approvalId: string): Observable<boolean> {
    return this.http.delete<boolean>(`${environment.services_root_endpoints.mechanics}/approvals/${approvalId}`);
  }

  public Approve(approvalId: string, currentLevel: number): Observable<any> {
    const lvl = {
      Level: currentLevel
    };
    
    this.unApprovingIds.update(ids => {
      if (ids) {
        return [...ids, approvalId];
      }
      return [approvalId];
    });

    return this.http.put<any>(`${environment.services_root_endpoints.mechanics}/approvals/${approvalId}/approve`, lvl);
  }

  public Unapprove(approvalId: string, currentLevel: number): Observable<MechanicsApproval> {
    const lvl = {
      Level: currentLevel
    };

    this.unApprovingIds.update(ids => {
      if (ids) {
        return [...ids, approvalId];
      }
      return [approvalId];
    });

    return this.http.put<MechanicsApproval>(`${environment.services_root_endpoints.mechanics}/approvals/${approvalId}/unapprove`, lvl).pipe(
      tap((result) => {
        // Update local state on successful unapproval
        this.approvalsResource.update(approvals => {
          if (approvals) {
            return {
              ...approvals,
              Approvals: approvals.Approvals.map(x => 
                x.Id === approvalId ? { ...x, ...result } : x
              )
            };
          }
          return approvals;
        });

        this.unApprovingIds.update(ids => {
          if (ids) {
            return ids.filter(x => x !== approvalId);
          }
          return ids;
        });

        this.removeApproval(approvalId);
      }),
      catchError((err) => {
        // Clean up loading state on error
        this.unApprovingIds.update(ids => {
          if (ids) {
            return ids.filter(x => x !== approvalId);
          }
          return ids;
        });
        console.log(err);
        throw err; // Re-throw for caller to handle
      })
    );
  }

  public UnSubmit(approvalId: string): Observable<any> {
    return this.http.delete(`${environment.services_root_endpoints.mechanics}/approvals/${approvalId}`);
  }

  public GetApprovalsByDailyLogIds(dailyLogIds: string[]): Observable<any> {
    const url = `${environment.services_root_endpoints.mechanics}/approvals/dailylog?dailyLogIds=${dailyLogIds.join(',')}`;
    return this.http.get(url);
  }

  // Helper method to remove approval from list (matching project pattern)
  removeApproval(approvalId: string): void {
    if (approvalId) {
      this.approvalsResource.update(approvals => {
        if (approvals) {
          return {
            ...approvals,
            Approvals: approvals.Approvals.filter(x => x.Id !== approvalId),
            Total: approvals.Total - 1
          };        
        }
        return approvals;
      });
    }
  }


  public GetApprovalLevels(): ApprovalLevel[] {
    let approvalLevels = new Array<ApprovalLevel>();
    for(let level of approvalLevelsInfos){
      approvalLevels.push({
        Level: level.Level,
        Name: level.Name,
        ApprovedDate: null,
        UserApproved: null
      })
    }
    return approvalLevels;
  }
}