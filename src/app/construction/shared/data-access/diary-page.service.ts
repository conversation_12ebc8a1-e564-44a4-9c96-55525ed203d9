import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { DailyLogService } from "../../project/daily-log/shared/services/daily-log.service";
import { ToastrService } from "ngx-toastr";

@Injectable()

export class DiaryPageService{
	toasterService = inject(ToastrService);
	loadedDiaryServices = signal<Map<string, DailyLogService>>(new Map<string, DailyLogService>());
	totalDailyLogsSubmitted = computed(() =>
		Array.from(this.loadedDiaryServices().values()).filter(item => item.approval() != null).length
	);

	totalDailyLogsNotSubmitted = computed(() =>
		Array.from(this.loadedDiaryServices().values()).filter(item => item.approval() == null).length
	);

	isSubmitting = computed(() =>
		Array.from(this.loadedDiaryServices().values()).some(item => item.isSubmitting())
	);

	constructor(){
		effect(() => {
			const services = this.loadedDiaryServices();	
				
			console.log("Loaded Diary Services: ", services);
			
		});
	}

	registerService(projectId: string, service: DailyLogService){
		this.loadedDiaryServices.update(services => {
			const newServices = new Map(services);
			newServices.set(projectId, service);		
			return newServices;
		});
	}

	save(){
		if(this.totalDailyLogsNotSubmitted() === 0){
			this.toasterService.warning("All daily logs are submitted, cannot save", "info");
			return;
		}
		for(var item of this.loadedDiaryServices().values()){
			item.save().subscribe();
		}
	}

	submit(){
		if(this.totalDailyLogsNotSubmitted() === 0){
			this.toasterService.warning("All daily logs are submitted, cannot save", "info");
			return;
		}

		for(var item of this.loadedDiaryServices().values()){
			item.submit();
		}
	}

	clear(){
		this.loadedDiaryServices.set(new Map<string, DailyLogService>());
	}
}