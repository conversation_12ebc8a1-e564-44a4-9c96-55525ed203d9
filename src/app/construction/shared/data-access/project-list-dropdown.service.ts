import { HttpClient, HttpParams } from "@angular/common/http";
import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { environment } from "src/environments/environment";
import { ConstructionProject, ConstructionProjectResponse } from "../interfaces/construction-project";

@Injectable({
	providedIn: 'root'
})
export class ProjectListDropdownService {
	private http = inject(HttpClient);	
	projectsForSelection = computed(() => this.projectsForSelectionsResource.value()?.Projects);
	projectsSelectionLoading = computed(() => this.projectsForSelectionsResource.isLoading());
	initializeProjectsForSelections = signal<boolean>(false);
	selectionProjectType = signal<string>('default');
	selectionProjectPage = signal<number>(1);	  	  
	selectedProjects = signal<Array<ConstructionProject>>([]);
	deactivated = signal<boolean | null>(false);

	constructor() {

	}
	projectsForSelectionsResource = rxResource({
		request: () => ({
			initialized: this.initializeProjectsForSelections(),
			projectType: this.selectionProjectType(),
			selectionProjectPage: this.selectionProjectPage(),
			deactivated: this.deactivated()
		}),
		loader: (request) => {
			if (request.request.initialized) {
				var url = `${environment.services_root_endpoints.projects}/projects`;
				let queryParams = new HttpParams(); // Initialize HttpParams 


				queryParams = queryParams.set('limit', "1000");
				queryParams = queryParams.set('orderBy', "Title");
				queryParams = queryParams.set('projectType', request.request.projectType);

				if(request.request.deactivated !== null) {
					queryParams = queryParams.set('deactivated', request.request.deactivated);
				}
				
				queryParams = queryParams.set('page', request.request.selectionProjectPage.toString());
				queryParams = queryParams.set('isReversed', "false");

				return this.http.get<ConstructionProjectResponse>(url, { params: queryParams });
			}

			return of(null);
		}
	});
}