import { HttpClient } from '@angular/common/http';
import { Injectable, inject, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AccessRequest } from 'src/app/models/access/access-request';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { environment } from 'src/environments/environment';
import { PunchListPermissions, PunchList, PunchListTask, PunchListComponentInfo } from '../interfaces/punch-lists';
import { ProjectComponentsService } from './project-components.service';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';

@Injectable({
  providedIn: 'root'
})
export class ConstructionPunchListsService {

  http = inject(HttpClient);
  accessService = inject(AccessService);
  projectComponentService = inject(ProjectComponentsService); 
  punchListPermissions = signal<PunchListPermissions | null>(null);  

  getOrCreatePunchListComponent(projectId: string): Observable<PunchListComponentInfo> {
	return new Observable<PunchListComponentInfo>(obs => {
		this.getPunchListComponent(projectId).subscribe({
			next: (punchListComponent) => {
				if(punchListComponent){
					obs.next(punchListComponent);
					obs.complete();
				}else{
					this.projectComponentService.addComponentToStoreById<PunchListComponentInfo>(projectId, ProjectComponentIdentifiers.PUNCH_LIST, true).subscribe({
						next: (result) => {
							obs.next(result);
							obs.complete();
						},
						error: (err) => {
							obs.error(err);
						}
					});
				}
			},
			error: (err) => {
				obs.error(err);
			}
		});
	});
  }
  getPunchListComponent(projectId: string): Observable<PunchListComponentInfo> {	
	return this.http.get<PunchListComponentInfo>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.PUNCH_LIST}`);
	}
	getPunchLists(projectId: string): Observable<Array<PunchList>> {	
		return this.http.get<Array<PunchList>>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/get-punchlist`);	
	}

	getPunchListItem(projectId: string, punchListItemId: string): Observable<PunchList> {
		var request = {
			PunchListItemId: punchListItemId
		};

		return this.http.patch<PunchList>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/get-punchlist`, request);
	}

	addPunchListItem(projectId: string, punchList: PunchList){
		var request = {
			PunchList: punchList
		};		

		return this.http.patch<PunchList>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/add-punchlist`, request);
	}

	updatePunchList(projectId: string, punchLIst: PunchList){
		var request = {
			PunchList: punchLIst
		};		

		return this.http.patch<string>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/edit-punchlist`, request);		
	}

	deletePunchList(projectId: string, punchListId: string){
		var request = {
			PunchListId: punchListId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/remove-punchlist`, request);
	}

	editPunchListTask(projectId: string, punchListId: string, task: PunchListTask){
		var request = {
			PunchListId: punchListId,
			PunchListTask: task
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/edit-task`, request);
	}

	addPunchListTask(projectId: string, punchListId: string, task: PunchListTask){
		var request = {
			PunchListId: punchListId,
			PunchListTask: task
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/add-task`, request);
	}

	deletePunchListTask(projectId: string, punchListId:string, taskId:string){
		var request = {
			PunchListId: punchListId,
			PunchListTaskId: taskId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/remove-task`, request);
	}

	markTask(projectId: string, punchListId: string, taskId: string, isCompleted: boolean){
		var request = {
			PunchListId: punchListId,
			PunchListTaskId: taskId,
			Completed: isCompleted
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/PunchList/mark-task`, request);
	}

	getPunchListPermissions(): Observable<PunchListPermissions> {
    if(this.punchListPermissions()){
      return of(this.punchListPermissions() as PunchListPermissions);
    }else{
      return new Observable<PunchListPermissions>(obs => {
        var requests = new Array<AccessRequest>();
        
        requests.push({
          Action: 'updatepunchlist',
          Resource: 'project-components-punchlist'
        });
  
        requests.push({
          Action: 'addpunchlist',
          Resource: 'project-components-punchlist'
        });
  
        requests.push({
          Action: 'updatepunchlisttask',
          Resource: 'project-components-punchlist'
        });
  
        requests.push({
          Action: 'deletepunchlist',
          Resource: 'project-components-punchlist'
        });
  
        requests.push({
          Action: 'markpunchlisttask',
          Resource: 'project-components-punchlist'
        });
  
  
        this.accessService.GetAccessMulti(requests).subscribe(result => {
          const permission ={
            updatePunchlist: result[0].Access,
            addPunchList: result[1].Access,
            updatepunchlisttask: result[2].Access,
            deletePunchList: result[3].Access,
            markTask: result[4].Access,
          } as PunchListPermissions;
          
          this.punchListPermissions.set(permission);
        
          obs.next(permission);
          obs.complete();
  
        });
      });
    }

	}
}
