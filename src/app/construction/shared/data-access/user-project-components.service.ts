import { Injectable, inject, signal } from '@angular/core';
import { Observable, forkJoin, of, tap, shareReplay, catchError } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { orderBy } from 'lodash';
import { UserProjectComponent, UserProjectComponentStore } from '../interfaces/dashboard';

@Injectable({
  providedIn: 'root'
})
export class UserProjectComponentsService {
  http = inject(HttpClient);
  cachedDefaultUserStoreComponents = signal<UserProjectComponentStore | null>(null);
  cachedUserAllComponents = signal<Array<UserProjectComponent>>([]);

  // Cache signal for managing user-project API calls
  private userProjectCache = signal<{
    results: UserProjectComponentStore | null; // Cached user-project result
    ongoingRequest: Observable<UserProjectComponentStore> | null; // Tracks in-flight user-project request
  }>({ results: null, ongoingRequest: null });

  // Method: Updates the cached list of all user components
  updateCache(components: Array<UserProjectComponent>) {
    this.cachedUserAllComponents.set(components);
  }

  // Method: Adds a component to the store by ID
  addComponentToStoreById(componentIdentifier: string, isActive: boolean) {
    const patchOptions = {
      FunctionName: "addbycomponentId",
      Data: {
        componentId: componentIdentifier,
        isActive: `${isActive}`
      }
    };
    return this.http.patch<UserProjectComponent>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, patchOptions);
  }

  // Method: Retrieves a component by its identifier
  getComponentById<T>(componentIdentifier: string): Observable<T> {
    return new Observable<T>((obs) => {
      this.getAllUserStoreComponents().subscribe({
        next: (result) => {
          const component = result.find(x => x.ComponentIdentifier === componentIdentifier) as T | undefined;
          if (component) {
            obs.next(component);
            obs.complete();
          } else {
            obs.error(`Component ${componentIdentifier} not found`);
          }
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }

  // Method: Removes a component from the store by identifier
  removeComponentFromStore(componentIdentifier: string): Observable<any> {
    return this.http.delete(`${environment.services_root_endpoints.user_project_components}/project-components/user-project?componentId=${componentIdentifier}`);
  }

  // Method: Reorders components in the store
  reorderComponents(componentReorderIds: Array<string>): Observable<any> {
    const patchOptions = {
      FunctionName: "reorder",
      Data: componentReorderIds
    };
    return this.http.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, patchOptions);
  }

  // Method: Removes a list of components from the store
  removeComponentListFromStore(compIds: Array<string>) {
    const patchOptions = {
      FunctionName: "removelist",
      Data: compIds
    };
    return this.http.patch<UserProjectComponent>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, patchOptions);
  }

  // Method: Replaces a component in the store
  replaceComponentToStore<T>(component: T): Observable<T> {
    return this.http.put<T>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, component);
  }

  // Method: Adds a list of components to the store
  addComponentListFromStore(compIds: Array<string>) {
    const patchOptions = {
      FunctionName: "addlist",
      Data: compIds
    };
    return this.http.patch<UserProjectComponent>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, patchOptions);
  }

  // Method: Retrieves all user components, using cache if available
  getUserComponentsAllCached(): Observable<Array<UserProjectComponent>> {
    if (this.cachedUserAllComponents().length > 0) {
      return of(this.cachedUserAllComponents());
    } else {
      return this.getAllUserStoreComponents();
    }
  }

  // Method: Resets all caches to fetch fresh data on page refresh
  resetCache() {
    this.cachedUserAllComponents.set([]); // Clear user components cache
    this.cachedDefaultUserStoreComponents.set(null); // Clear legacy cache
    this.userProjectCache.set({ results: null, ongoingRequest: null }); // Clear user-project cache
  }

  // Method: Retrieves all user store components, combining store and project info
  getAllUserStoreComponents(): Observable<Array<UserProjectComponent>> {
    return new Observable<Array<UserProjectComponent>>(obs => {
      forkJoin({ componentStore: this.getComponentStore(), componentInfo: this.getProjectComponentsInfo() }).subscribe({
        next: (result) => {
          let order = 0;
          result.componentStore.Components.forEach((comp) => { comp.Order = order++; });
          let pComponents = new Array<UserProjectComponent>();

          let componentInfo = result.componentInfo.project_component_info;
          //loop through all global project components. If any are missing from list above, add a new project component to list;
          Object.keys(componentInfo).forEach((key) => {
            var comp = componentInfo[key];
            
            var projectComponent = result.componentStore.Components.find(x => x.ComponentIdentifier === comp.componentIdentifier);

            const pComp: UserProjectComponent = {
              ComponentIdentifier: comp.componentIdentifier,
              AllowDeletion: !comp.isStatic,
              Name: comp.name,
              UrlLink: comp.url,
            };

            if (!projectComponent) {
              pComp.IsActive = false;
              pComp.Order = 99;
            } else {
              pComp.IsActive = projectComponent.IsActive;
              pComp.Order = projectComponent.Order;
            }

            pComponents.push(pComp);
          });

          let components = orderBy([...pComponents], item => item.Order, ['asc']);
          this.cachedUserAllComponents.set(components);

          obs.next(components);
          obs.complete();
        }
      });
    });
  }

  // Method: Retrieves project components info from a static JSON file
  getProjectComponentsInfo(): Observable<any> {
    const headers = new HttpHeaders({
      'ContentType': 'application/json'
    });
    return this.http.get<any>('./assets/data/project-component-info.json', { headers });
  }

  // Method: Retrieves the user project component store with caching to prevent redundant API calls
  getComponentStoreCached(): Observable<UserProjectComponentStore | null> {
    const cache = this.userProjectCache(); // Get current cache state

    const ongoingRequest = cache.ongoingRequest; // Check for an ongoing request
    if (ongoingRequest) {
      return ongoingRequest; // Return shared Observable for concurrent calls
    }

    const cachedData = cache.results; // Check for cached result
    if (cachedData) {
      return of(cachedData); // Return cached data if available
    }

    const request$ = this.getComponentStore().pipe(
      shareReplay(1), // Share the HTTP response with all subscribers
      tap(result => {
        // Update cache with new result and clear ongoing request
        this.userProjectCache.set({ results: result, ongoingRequest: null });
        this.cachedDefaultUserStoreComponents.set(result); // Update legacy cache for compatibility
      }),
      catchError(error => {
        // Clear ongoing request on error
        this.userProjectCache.update(current => ({ ...current, ongoingRequest: null }));
        throw error; // Propagate error
      })
    );

    this.userProjectCache.set({ results: null, ongoingRequest: request$ }); // Store new ongoing request
    return request$;
  }

  // Method: Fetches the user project component store directly from the API
  getComponentStore() {
    return this.http.get<UserProjectComponentStore>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`);
  }

  // Method: Retrieves team project IDs for a list of projects
  getTeamProjectIds(projectsIds: Array<string>): Observable<string[]> {
    return this.http.post<string[]>(`${environment.services_root_endpoints.project_components}/project-components/project?method=projects-team`, projectsIds);
  }

  // Method: Fetches and caches the user project component store (legacy method)
  private getComponentStoreInfo() {
    return new Observable<UserProjectComponentStore>((obs) => {
      this.getComponentStore().subscribe({
        next: (result) => {
          this.cachedDefaultUserStoreComponents.set(result);
          obs.next(result);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }
}