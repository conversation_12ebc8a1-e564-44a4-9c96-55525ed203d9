import { Injectable, inject, signal } from '@angular/core';
import { ConstructionTeamPermissions, TeamComponentInfo, TeamMember, TeamMemberComponentInfo, TeamMemberInfo, TeamUserProjectComponentInfo } from '../interfaces/team';
import { AccessRequest } from 'src/app/models/access/access-request';
import { Observable, Subject, forkJoin, of } from 'rxjs';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessResponse } from 'src/app/models/access/access-response';
import { environment } from 'src/environments/environment';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';
import { HttpClient } from '@angular/common/http';
import { ProjectComponentsService } from './project-components.service';
// import { DelegateService } from 'src/app/user/shared/data-access/delegate.service';
import { UserProjectComponentsService } from './user-project-components.service';
import { AccountDelegateStore } from 'src/app/shared/interfaces/delegate';
import { orderBy } from 'lodash';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { AccountProfile, CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { DelegateService } from 'src/app/account/user/shared/data-access/delegate.service';

@Injectable({
  providedIn: 'root'
})
export class ConstructionTeamsService {
  http = inject(HttpClient);
  projectComponentService = inject(ProjectComponentsService); 
  userProjectComponentService = inject(UserProjectComponentsService);
  accountService = inject(AccountService);
  accessService = inject(AccessService);
  delegateService = inject(DelegateService);
  teamPermissions = signal<ConstructionTeamPermissions | null>(null);
  teamMemberDataStore = signal<Array<TeamMemberData>>([]);
  teamComponentSettings = signal<TeamUserProjectComponentInfo | null>(null);
  userTeamMemberInfo = signal<Array<TeamMemberInfo> | null>(null);

  constructor() { }
  getOrCreateTeamComponent(projectId: string): Observable<TeamComponentInfo> {
    return new Observable<TeamComponentInfo>(obs => {
      this.getTeamComponent(projectId).subscribe({
        next: (teamComponent) => {
          if (teamComponent) {
            obs.next(teamComponent);
            obs.complete();
          } else {
            this.projectComponentService.addComponentToStoreById<TeamComponentInfo>(projectId, ProjectComponentIdentifiers.TEAM, true).subscribe({
              next: (result) => {
                obs.next(result);
                obs.complete();
              },
              error: (err) => {
                obs.error(err);
              }
            });
          }
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }

  getTeamComponentWithMemberInfo(projectId: string): Observable<TeamMemberComponentInfo> {
    return new Observable<TeamMemberComponentInfo>(obs => {
        forkJoin({accountDelegateStore: this.delegateService.GetDelegates(), userProjectComponentStore: this.getOrCreateTeamComponent(projectId)}).subscribe({
        next: (result) => {
          let accountDelegateStore = result.accountDelegateStore;
          let teamComponent = result.userProjectComponentStore;          

          if(teamComponent !== null){     
            this.setupTeamMembers(accountDelegateStore, teamComponent).subscribe({
              next: (teamMemberInfo) => {
                let teamMemberComponentInfo: TeamMemberComponentInfo = {} as TeamMemberComponentInfo;
                teamMemberComponentInfo.TeamMembers = [...teamMemberInfo];
                obs.next(teamMemberComponentInfo);
                obs.complete();
              },
              error: (err) => {
                obs.error(err);
              }
            });
          }else{
            obs.error("Team component not found");            
          }      
        },
        error: (err) => {
          console.log(err);          
        }
      });
    });
  }

  getUserTeamComponent(): Observable<TeamUserProjectComponentInfo> {
    return new Observable<TeamUserProjectComponentInfo>(obs => {
      this.userProjectComponentService.getComponentStoreCached().subscribe({
        next: (componentStore) => {
          if(componentStore){
            let teamComponentInfo = componentStore?.Components?.find(x => x.ComponentIdentifier == ProjectComponentIdentifiers.TEAM) as TeamUserProjectComponentInfo;          
            obs.next(teamComponentInfo);
            obs.complete();
          }else{
            obs.error("Team component not found");
          }
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }

  getUserTeamComponentSettingsWithMemberInfo(): Observable<Array<TeamMemberInfo>> {
    if(this.userTeamMemberInfo()){
      return of(this.userTeamMemberInfo() as Array<TeamMemberInfo>);
    }else{
      return new Observable<Array<TeamMemberInfo>>(obs => {
        forkJoin({delegateStore: this.delegateService.GetDelegates(), userTeamComponent: this.getUserTeamComponent()}).subscribe({
          next: ({delegateStore, userTeamComponent}) => {
            if(delegateStore && userTeamComponent){ 
              this.setupTeamMembers(delegateStore, userTeamComponent).subscribe({
                next: (teamMembers: Array<TeamMemberInfo>) => {
                  this.userTeamMemberInfo.set(teamMembers);  
                  obs.next();
                  obs.complete();         
                },
                error: (err) => {
                  console.log(err);
                }
              });
            }else{
              this.userTeamMemberInfo.set([]);  
              obs.next();
              obs.complete();
            }
    
          }
        });
      });
    }

  }

  private setupTeamMembers(accountDelegateStore:AccountDelegateStore,  teamComponent: TeamComponentInfo | TeamUserProjectComponentInfo) : Observable<Array<TeamMemberInfo>>{
    return new Observable<Array<TeamMemberInfo>>(obs => {
      let teamMemberInfo: Array<TeamMemberInfo> = new Array<TeamMemberInfo>();

      let userIds = new Array<string>();

      accountDelegateStore.Users.map(delegateUser => {

        if (!delegateUser.Deactivated) {
          let memberInfo: TeamMemberInfo = {
            DelegateInfo: delegateUser,
            IsActive: false,
            AccountInfo: null,
            IsLoading: false
          };

          userIds.push(delegateUser.UserId);

          if (teamComponent.TeamMembers) {
            var hasTeamMember = teamComponent.TeamMembers.filter(x => x.UserId == delegateUser.UserId);

            if (hasTeamMember.length > 0) {
              memberInfo.IsActive = true;
            }
          }

          teamMemberInfo.push(memberInfo);
        }
      });

      this.accountService.GetAccounts(userIds).subscribe({
        next: (accounts) => {
          for (let member of teamMemberInfo) {
            var aInfo = accounts.find(x => x.CognitoUserId == member.DelegateInfo?.UserId);

            if (aInfo) {
              member.AccountInfo = aInfo;
            } else {
              if (member.DelegateInfo) {
                member.AccountInfo = {
                  Profile: {} as AccountProfile
                } as CivCastAccount;
                member.DelegateInfo.UserId = member.DelegateInfo.UserId;
                member.AccountInfo.Profile.FirstName = "Account";
                member.AccountInfo.Profile.LastName = `Not Found (${member.DelegateInfo?.UserId})`
              }
            }
          }

          teamMemberInfo = orderBy([...teamMemberInfo], item => item.AccountInfo?.Profile.LastName, ['asc']);
          obs.next(teamMemberInfo);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }

  public AddTeamMembers(projectId: string, members: TeamMember[]): Observable<TeamMember> {
    return this.http.patch<TeamMember>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Team/add-team-members`, members);  
  }

  public RemoveTeamMembers(projectId:string, members: Array<string>): Observable<any> {
    return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Team/remove-team-members`, members);
  }

  public AddTeamMembersSettings(members: TeamMember[]): Observable<TeamMember> {
    return new Observable<TeamMember>((obs) => {
      this.http.patch<TeamMember>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project/component/Team/add-team-members`, members).subscribe({
        next: (result) => {
          this.userProjectComponentService.resetCache();
          obs.next(result);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    }); 
	}
	public RemoveTeamMembersSettings(members: Array<string>): Observable<any> {
    return new Observable<TeamMember>((obs) => {
      this.http.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project/component/Team/remove-team-members`, members).subscribe({
        next: (result) => {
          this.userProjectComponentService.resetCache();
          obs.next(result);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
		
	}

  public IsTeamMember(projectId: string): Observable<boolean> {
    const hasStore = this.teamMemberDataStore().find(x => x.projectId === projectId);
    console.log(this.teamMemberDataStore());
    if(hasStore){
      return of(hasStore.isTeamMember);
    }else{
      return new Observable<boolean>((observer) => {
        this.http.get<TeamMemberResponse>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Team/is-team-member`).subscribe({
          next: (result) => {
            this.teamMemberDataStore.update((dataStore) => {
              return [...dataStore, { isTeamMember: result.IsTeamMember, projectId: projectId }]; 
            });
            
            observer.next(result.IsTeamMember);
            observer.complete();
          },
          error: (err) => {
            observer.error(false);
          }
        });
      });
    }

  }

  getTeamComponent(projectId: string): Observable<TeamComponentInfo> {	
    return this.http.get<TeamComponentInfo>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.TEAM}`);
  }

  getTeamPermissions() {
    if (this.teamPermissions()) {
      return of(this.teamPermissions());
    } else {
      return new Observable<ConstructionTeamPermissions>(observer => {
        var requests = new Array<AccessRequest>();

        requests.push({
          Action: 'removeteammember',
          Resource: 'project-components-team'
        });

        requests.push({
          Action: 'allow-team-updates',
          Resource: 'user-settings-team-checks'
        });

        this.accessService.GetAccessMulti(requests).subscribe({
          next: (result: AccessResponse[]) => {
            const permission = {
              removeTeamMember: result[0]?.Access,
              allowTeamUpdates: result[1]?.Access
            } as ConstructionTeamPermissions;

            this.teamPermissions.set(permission);

            observer.next(permission);
            observer.complete();
          },
          error: (error: any) => {
            console.log(error);
            observer.error(error);
          }
        });
      });
    }


  }
}

interface TeamMemberData {
  isTeamMember: boolean;
  projectId:string;
}


interface TeamMemberResponse {
  IsTeamMember: boolean;  
}
