import { Injectable, inject, signal } from '@angular/core';
import { ConstructionProject, ConstructionProjectPermissions, ProjectUpdateOptions } from '../interfaces/construction-project';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { environment } from 'src/environments/environment';
import { delay, Observable, of, switchMap, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConstructionRecentProjectsService } from './recent-projects.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessRequest } from 'src/app/models/access/access-request';
import { ConstructionProjectsService } from './projects.service';
import { ConstructionProjectInitializationService } from './project-initialization.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class ConstructionProjectService {
  http = inject(HttpClient);
  accessService = inject(AccessService);
  toastrService = inject(ToastrService);
  recentProjectService = inject(ConstructionRecentProjectsService);    
  projectInitializeService = inject(ConstructionProjectInitializationService);
  projectsService = inject(ConstructionProjectsService); 
  router = inject(Router);
  projectEndpoint = `${environment.services_root_endpoints.projects}/projects`;
  project = signal<ConstructionProject | null>(null);
  projectPermissions = signal<ConstructionProjectPermissions | null>(null);
  readOnlyPermissions = toSignal(toObservable(this.project).pipe(switchMap((project) => this.getProjectPermissions())));
  projectOwnerChecks = signal<Array<IsProjectOwnerCheck>>([]);
  isSaving = signal<boolean>(false);
  
  getProject(projectId: string): Observable<ConstructionProject> {
    if (this.project() && this.project()?.Id === projectId) {
      return of(this.project() as ConstructionProject);
    } else {
      return new Observable<ConstructionProject>(observer => {
        const endPoint = `${this.projectEndpoint}/${projectId}`
        this.http.get<ConstructionProject>(endPoint).subscribe({
          next: (project) => {
            this.project.set(project);
            observer.next(project);
            observer.complete();
          },
          error: (err) => {
            observer.error(err);
          }
        });
      });
    }

  }

  getProjectPermissions() {
    if(this.projectPermissions()){
      return of(this.projectPermissions());
    }else{
      return new Observable<ConstructionProjectPermissions>(observer => {
        var requests = new Array<AccessRequest>();
        
        requests.push({
          Action: 'setactivestatus',
          Resource: 'projects'
        });


        requests.push({
          Action: 'addproject',
          Resource: 'projects'
        });
  
        this.accessService.GetAccessMulti(requests).subscribe(result => {
          const permission = {
            closeProject: result[0].Access,
            addProject: result[1].Access            
          } as ConstructionProjectPermissions;
    
          this.projectPermissions.set(permission);  
  
          observer.next(permission);
          observer.complete();
        }, error => {
          console.log(error);
          observer.error(error);
        });
      });
    }


  }

  IsProjectOwner(projectId: string): Observable<boolean> {  
      const hasStore = this.projectOwnerChecks().find(x => x.projectId === projectId);
      
      if(hasStore){
        return of(hasStore.isProjectOwner);
      }else{
        return new Observable<boolean>(observer => {
        this.http.get<ProjectOwner>(`${environment.services_root_endpoints.projects}/projects/${projectId}/is-project-owner`).subscribe({
          next: (result) => {
            this.projectOwnerChecks.update((checks) => {
              return [...checks, { isProjectOwner: result.isOwner, projectId: projectId }];
            });

            observer.next(result.isOwner);
            observer.complete();
          },
          error: (err) => {
            observer.error(err);
          }
        });
      });       
    }       
  }
  
  public addProject(project: ConstructionProject, aRoute: ActivatedRoute | null) {
    this.isSaving.set(true);
    this.projectInitializeService.status.set("Saving Project...");

    this.http.post<ConstructionProject>(`${environment.services_root_endpoints.projects}/projects`, project).pipe(delay(1000)).subscribe({
      next: (project: ConstructionProject) => {
        this.projectsService.projectsResource.update((currentResponse) => {
          if (!currentResponse) return null;
          const updatedProjects = [...currentResponse.Projects, project];
          return { ...currentResponse, Projects: updatedProjects };
        });
        
        this.projectInitializeService.status.set("Project Saved!");   
        this.projectInitializeService.initializeProject(project.Id).subscribe({
          next: (result) => {            
            this.projectInitializeService.status.set("Navigating to Project...");
            this.router.navigate([project.Id], { relativeTo: aRoute }).then(() => {
              this.isSaving.set(false);
              this.projectInitializeService.status.set("");
            });
          },
          error: (err) => {
            console.log(err);
            this.isSaving.set(false);          
            this.toastrService.error("There was an issue initializing this project.  You may have to setup your project settings manually.");
            this.router.navigate([project.Id], { relativeTo: aRoute });
          }
        });
      },
      error: (err) => {
        console.log(err);
      }
    });    
  }
  public saveProject(project: ConstructionProject): Observable<ConstructionProject> {
    return this.http.put<ConstructionProject>(`${environment.services_root_endpoints.projects}/projects/${project.Id}`, project);
  }

  highlightProject(projectId: string, isHighlighted: boolean) {
    const patchUpdateOptions: Partial<ProjectUpdateOptions> = {
      isHighlighted: isHighlighted
    };

    return this.updateProject(projectId, patchUpdateOptions);
  }
  updateProject(projectId:string, projectUpdateOptions: Partial<ProjectUpdateOptions>) {
      
    return this.http.patch<ConstructionProject>(`${environment.services_root_endpoints.projects}/projects/${projectId}`, projectUpdateOptions);
 }

 
  setProjectStatus(projectId:string, status: boolean) {
    var request = {
      ActiveStatus: status
    };

    this.isSaving.set(true);
    this.http.patch<ConstructionProject>(`${environment.services_root_endpoints.projects}/projects/${projectId}/update-project-status`, request).subscribe({
      next: (result) => {
          this.project.update((currentResponse) => {
            if (!currentResponse) return null;
            return { ...currentResponse, Deactivated: status };
          });        

        this.isSaving.set(false);

      },error: (err) => {
        this.isSaving.set(false);
        console.log(err);
      }
    });
  }
}

export interface ProjectOwner{
  isOwner: boolean;

}


export interface IsProjectOwnerCheck{
  isProjectOwner: boolean;
  projectId: string;
}