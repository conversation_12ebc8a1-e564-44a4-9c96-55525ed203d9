import { effect, inject, Injectable, signal, computed, OnDestroy } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, of, tap, throwError, Subscription } from 'rxjs';
import { ConstructionProject } from '../../shared/interfaces/construction-project';
import { DailyLogProjectUserService } from '../../shared/data-access/daily-log-project-user.service';
import { rxResource } from '@angular/core/rxjs-interop';
import { DailyLogProjectUserComponent } from '../../shared/interfaces/daily-log-project-user';
import { DateSelectorService } from './date-selector.service';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { DailyLogDirtyService } from '../../project/daily-log/shared/services/daily-log-dirty.service';
import { ConfirmationMessages } from '../../shared/utils/confirmation-messages';

@Injectable({
  providedIn: 'root'
})
export class DiaryProjectsSelectorService implements OnDestroy {
  private dailyLogProjectUserService = inject(DailyLogProjectUserService);
  private dateSelectorService = inject(DateSelectorService);
  private router = inject(Router);
  private confirmService = inject(ConfirmService);
  private dirtyService = inject(DailyLogDirtyService);
  
  // Store subscriptions for cleanup
  private routerSubscription: Subscription | null = null;

  // Signals for mode and state
  private isSingleProjectMode = signal<boolean>(false);
  private singleProjectId = signal<string | null>(null);
  loadLogs = signal<boolean | null>(null);
  selectedProjects = signal<ConstructionProject[]>([]);
  year = signal<number>(this.dateSelectorService.selectedYear());
  month = signal<number>(this.dateSelectorService.selectedMonth());
  day = signal<number>(this.dateSelectorService.selectedDay());

  // Expose mode signals as readonly
  isSingleProjectModeSignal = this.isSingleProjectMode.asReadonly();
  singleProjectIdSignal = this.singleProjectId.asReadonly();

  // Store the current logs value to avoid circular reference
  // Make it public so DiaryService can access it directly
  currentLogsValue: DailyLogProjectUserComponent[] | null = null;
  
  // Debug flag to track API calls
  private debugApiCalls = false;

  constructor() {
    this.initializeRouteListener();
    this.initializeEffects();
    
    // Initialize mode based on current URL on service creation
    // This ensures correct mode is set on page refresh
    this.setModeFromUrl(this.router.url);
  }
  
  /**
   * Clean up subscriptions when the service is destroyed
   */
  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
      this.routerSubscription = null;
    }
  }

  private initializeRouteListener(): void {
    this.routerSubscription = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      const url = event.urlAfterRedirects;
      this.setModeFromUrl(url);
    });
  }
  
  /**
   * Resets all logs cache to ensure no stale data is displayed
   * This clears both the currentLogsValue and forces the selectedProjectsResource to re-evaluate
   */
  resetLogsCache(): void {
    this.currentLogsValue = null;
    // Force the selectedProjectsResource to re-evaluate by changing a dependency
    this.loadLogs.set(null);
  }

  private setModeFromUrl(url: string): void {
    const isDiaryRoute = url === '/diary' || url === '/construction/diary';
    const isSingleProjectRoute = url.includes('/projects/') && url.includes('/daily-log');

    if (isDiaryRoute) {
      this.isSingleProjectMode.set(false);
      this.singleProjectId.set(null);
      this.selectedProjects.set([]); // Purge selected projects
      this.resetLogsCache(); // Reset all caches
      
      // Reset date to today when entering diary view
      const today = new Date();
      this.dateSelectorService.setDate(today);
    } else if (isSingleProjectRoute) {
      const projectIdMatch = url.match(/\/projects\/([^/]+)\/daily-log/);
      const projectId = projectIdMatch ? projectIdMatch[1] : null;
      this.isSingleProjectMode.set(true);
      this.singleProjectId.set(projectId);
      
      // Clear any previously selected projects to ensure we only show the single project
      this.selectedProjects.set([]);
      
      // Reset date to today when entering single project mode
      const today = new Date();
      this.dateSelectorService.setDate(today);
      
      // Find the project in teamProjects and set it as the selected project
      if (projectId) {
        // We need to trigger the fetch for single project mode
        this.loadLogs.set(true);
      }
    }
  }

  private initializeEffects(): void {
    // Effect to synchronize date signals and trigger data fetching
    effect(() => {
      const newYear = this.dateSelectorService.selectedYear();
      const newMonth = this.dateSelectorService.selectedMonth();
      const newDay = this.dateSelectorService.selectedDay();

      // Check if date has changed
      const dateChanged = this.year() !== newYear || this.month() !== newMonth || this.day() !== newDay;
      
      // Sync date signals
      this.year.set(newYear);
      this.month.set(newMonth);
      this.day.set(newDay);

      // Clear cached logs when date changes
      if (dateChanged) {
        this.currentLogsValue = null;
      }

      // Trigger fetch if date changed and projects are selected or in single project mode
      if (
        ((this.selectedProjects().length > 0 || this.singleProjectId()) && dateChanged)
      ) {
        this.loadLogs.set(true);
      }
    });

    // Effect to handle errors from the selectedProjectsResource
    effect(() => {
      if (this.selectedProjectsResource.error()) {
        this.handleError(this.selectedProjectsResource.error());
      }
    });

    // Effect to store the current logs value
    effect(() => {
      const value = this.selectedProjectsResource.value();
      if (value !== undefined && value !== null) {
        this.currentLogsValue = value;
      }
    });
  }

  // rxResource for fetching daily logs
  selectedProjectsResource = rxResource({
    request: () => {
      const requestParams = {
        loadLogs: this.loadLogs(),
        year: this.year(),
        month: this.month(),
        day: this.day(),
        singleProjectId: this.singleProjectId()
      };
      return requestParams;
    },
    loader: () => {
      // Add guard to prevent execution if triggered by loadLogs resetting to null
      if (!this.loadLogs()) {
        // Return the current value or null to avoid disrupting the stream unnecessarily
        if (this.debugApiCalls) {
          console.log('Skipping API call due to loadLogs being false/null, returning cached value:', this.currentLogsValue);
        }
        
        // If we have a cached value, return it
        if (this.currentLogsValue && this.currentLogsValue.length > 0) {
          // Check if the cached logs match the current date
          const currentDate = `${this.month()}/${this.day()}/${this.year()}`;
          const cachedDate = this.currentLogsValue.length > 0 ? 
            `${this.currentLogsValue[0].Month}/${this.currentLogsValue[0].Day}/${this.currentLogsValue[0].Year}` : 
            'unknown';
          
          // If the dates don't match, don't use the cached value
          if (this.currentLogsValue[0].Year !== this.year() || 
              this.currentLogsValue[0].Month !== this.month() || 
              this.currentLogsValue[0].Day !== this.day()) {
            return of(null);
          }
          
          return of(this.currentLogsValue);
        }
        
        // If we don't have a cached value, return null to avoid disrupting the stream
        return of(null);
      }

      if (this.debugApiCalls) {
        console.log('Making API call with loadLogs =', this.loadLogs());
      }

      const selected = this.selectedProjects();
      const singleProjectId = this.singleProjectId();
      
      // Handle both multi-select mode and single project mode
      if (selected.length > 0 || singleProjectId) {
        const projectIds = selected.length > 0 
          ? selected.map((p) => p.Id) 
          : (singleProjectId ? [singleProjectId] : []);
          
        if (projectIds.length === 0) {
          this.loadLogs.set(null);
          return of(null);
        }
        
        return this.dailyLogProjectUserService.dailyLogsGetOrCreate(
          projectIds,
          this.month(),
          this.day(),
          this.year()
        ).pipe(
          tap({
            next: (logs) => {
              if (this.debugApiCalls) {
                console.log('API call successful, received logs:', logs);
              }
              // Store the logs in currentLogsValue directly
              this.currentLogsValue = logs;
              this.loadLogs.set(null);
            },
            error: (err) => {
              this.handleError(err);
              this.loadLogs.set(null);
            }
          })
        );
      } 
      this.loadLogs.set(null);
      return of(null);
    }
  });

  setSelectedProjects(projects: ConstructionProject[]): void {
    this.selectedProjects.set(projects);
  }
  
  /**
   * Confirms project deselection with the user if any of the deselected projects have dirty logs
   * @param currentSelection The current selection of projects
   * @param newSelection The new selection of projects
   * @returns A Promise that resolves to the final selection of projects
   */
  confirmProjectDeselection(
    currentSelection: ConstructionProject[],
    newSelection: ConstructionProject[]
  ): Promise<ConstructionProject[]> {
    // If any log is currently persisting, don't allow deselection
    if (this.dirtyService.anyPersisting()()) {
      return Promise.resolve(currentSelection);
    }
    
    // Find deselected projects (projects in current but not in new)
    const deselectedProjects = this.findDeselectedProjects(currentSelection, newSelection);
    
    // If no projects were deselected, just return the new selection
    if (deselectedProjects.length === 0) {
      return Promise.resolve(newSelection);
    }
    
    // Check if any deselected project has dirty logs
    if (this.anyDeselectedProjectIsDirty(deselectedProjects)) {
      // Show confirmation dialog
      return this.showDeselectionConfirmation(currentSelection, newSelection);
    } else {
      // No dirty logs, return the new selection
      return Promise.resolve(newSelection);
    }
  }
  
  /**
   * Finds projects that have been deselected
   * @param currentSelection The current selection of projects
   * @param newSelection The new selection of projects
   * @returns Array of deselected projects
   */
  private findDeselectedProjects(
    currentSelection: ConstructionProject[],
    newSelection: ConstructionProject[]
  ): ConstructionProject[] {
    return currentSelection.filter(
      current => !newSelection.some(newProj => newProj.Id === current.Id)
    );
  }
  
  /**
   * Checks if any of the deselected projects has dirty logs
   * @param deselectedProjects Array of deselected projects
   * @returns True if any deselected project has dirty logs
   */
  private anyDeselectedProjectIsDirty(deselectedProjects: ConstructionProject[]): boolean {
    // First check using the anyDirty signal for efficiency
    for (const project of deselectedProjects) {
      if (this.dirtyService.anyDirty(project.Id)()) {
        return true;
      }
    }
    
    // If not dirty from signal, check the dirtyMap directly as a fallback
    const dirtyMap = this.dirtyService.dirtyMap();
    
    for (const project of deselectedProjects) {
      const projectMap = dirtyMap.get(project.Id);
      
      if (projectMap) {
        for (const [, isDirtyValue] of projectMap.entries()) {
          if (isDirtyValue) {
            return true;
          }
        }
      }
    }
    
    return false;
  }
  
  /**
   * Shows a confirmation dialog for project deselection
   * @param currentSelection The current selection of projects
   * @param newSelection The new selection of projects
   * @returns Promise that resolves to the final selection of projects
   */
  private showDeselectionConfirmation(
    currentSelection: ConstructionProject[],
    newSelection: ConstructionProject[]
  ): Promise<ConstructionProject[]> {
    return this.confirmService.open(
      ConfirmationMessages.UNSAVED_CHANGES_PROJECT_DESELECTION,
      'Confirm Project Deselection'
    ).result.then(result => {
      // Return new selection if confirmed, current selection otherwise
      return result === 'yes' ? newSelection : currentSelection;
    }).catch(() => {
      // Dialog dismissed, return the current selection
      return currentSelection;
    });
  }

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred.';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
