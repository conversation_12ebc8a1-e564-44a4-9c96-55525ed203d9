import { HttpClient } from '@angular/common/http';
import { Injectable, computed, inject, signal } from '@angular/core';
import { Employee, EmployeeStore } from '../interfaces/employee';
import { Observable, of} from 'rxjs';
import { environment } from 'src/environments/environment';
import { rxResource } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  client = inject(HttpClient);
  router = inject(Router);
  toastrService = inject(ToastrService);
  employeeStore = computed(() => this.employeeStoreResource.value()) //signal<Array<EmployeeStoreCache> | null>(null);
  executeEmployeeStore = signal<boolean | null>(null);
  status = signal<EmployeeStoreStatus>(EmployeeStoreStatus.Active);
  isSavingEmployee = signal<boolean>(false);
  isAddingEmployee = signal<boolean>(false);
  error = signal<string | null>(null);
  isLoading = computed(() => this.employeeStoreResource.isLoading());
  employeeId = signal<string | null>(null);
  isEmployeeLoading = computed(() => this.employeeResource.isLoading());
  employee = computed(() => this.employeeResource.value());

  employeeStoreResource = rxResource({
    request: () => ({
      execute: this.executeEmployeeStore(),
      storeStatus: this.status()
    }),
		loader: (request) => {
			if (request.request?.execute) {
        const url = `${environment.services_root_endpoints.employees}/employees?status=${request.request.storeStatus}`;
        return this.client.get<EmployeeStore>(url);				
			}

			return of(null);
		}  
  });

  employeeResource = rxResource({
    request: () => this.employeeId(),
    loader: (request) => {
      if (request.request) {
        const url = `${environment.services_root_endpoints.employees}/employees/${request.request}`;
        return this.client.get<Employee>(url);
      }
      return of(null);
    }
  });

  // Method: Retrieves a single employee by ID
  public GetEmployee(employeeId: string): Observable<Employee> {
    return this.client.get<Employee>(`${environment.services_root_endpoints.employees}/employees/${employeeId}`);
  }

  // Method: Saves an employee and updates cache if applicable
  public SaveEmployee(item: Employee, redirect: boolean = false, activatedRoute: ActivatedRoute | null = null) {
    this.isSavingEmployee.set(true);
      this.client.put<Employee>(`${environment.services_root_endpoints.employees}/employees`, item).subscribe({
        next: (result: Employee) => {
          this.employeeStoreResource.update((store) => {
            if(store){
              store.Employees = store.Employees.map(emp => emp.InternalId === result.InternalId ? result : emp);
            }

            return store;
          });

          this.isSavingEmployee.set(false);
          this.employeeResource.set(result); // Update the employee resource with the saved employee

          if (redirect) {                   
            this.router.navigate(["./employees"], {relativeTo: activatedRoute})
          }

   
        },
        error: (err: any) => {
          console.log(err);
          this.error.set(err.error || "Could not save employee");
          this.isSavingEmployee.set(false);
          this.toastrService.error(err.error);
        }
      });

  }

  // Method: Adds a new employee and updates cache if applicable
  public AddEmployee(item: Employee, redirect: boolean = false, activatedRoute: ActivatedRoute | null = null) {
    this.isSavingEmployee.set(true);
      this.client.post<Employee>(`${environment.services_root_endpoints.employees}/employees`, item).subscribe({
        next: (result: Employee) => {
          this.employeeStoreResource.update((store) => {
            if(store){
              store.Employees = [...store.Employees, result];
            }
            
            return store
          });

          this.isSavingEmployee.set(false);

          if (redirect) {                   
            this.router.navigate(["./employees"], {relativeTo: activatedRoute})
          }

        },
        error: (err: any) => {
          console.log(err);
          this.isSavingEmployee.set(false);
          this.error.set(err.error || "Could not add employee");
          this.toastrService.error(err.error);
        }

    });
  }


  public SaveEmployeeList(employeeList: Array<Employee>, redirect: boolean = false, activatedRoute: ActivatedRoute | null = null) {
    this.isSavingEmployee.set(true);

    this.client.patch<any>(`${environment.services_root_endpoints.employees}/employees?method=update-add-list`, employeeList).subscribe({
      next: (result) => {
        this.employeeStoreResource.reload(); // Reload the employee store to refresh cache
        this.isSavingEmployee.set(false);

        if(redirect) {
          this.router.navigate(["employees"], { relativeTo: activatedRoute });
        }
      },
      error: (err: any) => {
        console.log(err);
        this.isSavingEmployee.set(false);
        this.toastrService.error("Could not save employees");
      }
    });
  }
}

export enum EmployeeStoreStatus {
  All = "all",
  Active = "active",
  Inactive = "inactive"
}