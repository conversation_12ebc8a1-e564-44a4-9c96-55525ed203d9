import { Injectable, Signal, inject, signal, effect } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { GalleryImage, PhotoInfo, PhotoProcessorInfo, PhotoProcessorStatus } from 'src/app/construction/shared/interfaces/photos';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';
import { PhotosComponent } from 'src/app/construction/shared/interfaces/mechanics';
import { MechanicsComponentIdentifiers } from 'src/app/construction/shared/interfaces/mechanics-shared';
import { IComponentService } from 'src/app/construction/shared/interfaces/component-service.interface';
import { MechanicsDirtyService } from 'src/app/construction/shared/data-access/mechanics-dirty.service';
import { v4 } from 'uuid';

/**
 * Mechanics Photos Service
 * 
 * Follows the exact same pattern as DailyLogPhotosService but for mechanics.
 * Integrates with the ConstructionPhotosService using magic string routing
 * and properly manages mechanics photo data.
 */
@Injectable()
export class MechanicsPhotosService implements IComponentService<PhotosComponent> {
  // Injected services
  private constructionPhotosService = inject(ConstructionPhotosService);
  private sanitizer = inject(DomSanitizer);
  private dirtyService = inject(MechanicsDirtyService);
  
  // Public signals
  component = signal<PhotosComponent | null>(null);
  view = signal<ComponentView>(ComponentView.ReadOnly);
  access = signal<string>('Allow');
  mainView = signal<string>('gallery');
  galleryImages = signal<Array<GalleryImage>>([]);
  isLoading = signal<boolean>(false);
  projectId = signal<string>(''); // This will be the mechanics "fake" project ID
  
  // Implement isDirty signal required by IComponentService
  private readonly _isDirty = signal(false);
  readonly isDirty: Signal<boolean> = this._isDirty.asReadonly();
  
  // User and date tracking for mechanics isolation
  private currentUserId = signal<string>('');
  private currentDate = signal<string>('');

  constructor() {
    // Auto-update projectId when user or date changes (using effect for side effects)
    effect(() => {
      const userId = this.currentUserId();
      const dateString = this.currentDate();
      if (userId && dateString) {
        // Create magic string for routing: mechanics-{userId}-{dateString}
        const mechanicsProjectId = `mechanics-${userId}-${dateString}`;
        this.projectId.set(mechanicsProjectId);
        
        // Set it in the construction photos service for proper routing
        this.constructionPhotosService.projectId.set(mechanicsProjectId);
      }
    });
  }

  setCurrentUser(userId: string): void {
    this.currentUserId.set(userId);
  }

  setCurrentDate(dateString: string): void {
    this.currentDate.set(dateString);
  }

  setInitialData(photosComponent: PhotosComponent | null, view: ComponentView, access: string): void {
    this.component.set(photosComponent);
    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
    this.mainView.set('gallery');
    
    // Reset dirty state
    this._isDirty.set(false);
    
    // Initialize gallery images from component photos
    if (photosComponent?.Photos?.length) {
      const images = photosComponent.Photos.map(photo => {
        const image = {} as GalleryImage;
        image.Id = photo.PhotoId as string;
        image.Description = photo.Description as string;
        image.Title = photo.Title as string;
        image.LargeUrl = `${photo.LargeStorage.Location}`;
        image.ThumbUrl = `${photo.ThumbnailStorage.Location}`;
        image.ThumbSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`${photo.ThumbnailStorage.Location}`);
        image.LargeSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`${photo.LargeStorage.Location}`);
        image.IsEditing = false;
        image.IsUploaded = true;
        image.IsProcessing = false;
        image.UploadDate = photo.UploadDate as Date;
        return image;
      });
      this.galleryImages.set(images);
    } else {
      this.galleryImages.set([]);
    }
  }

  getUpdatedComponent(): PhotosComponent {
    return {
      _t: ['PhotosComponent'],
      ComponentIdentifier: MechanicsComponentIdentifiers.PHOTOS,
      Photos: this.component()?.Photos || []
    };
  }

  setView(view: string): void {
    this.mainView.set(view);
  }

  initializeFiles(files: FileList): void {
    Array.from(files).forEach((file) => {
      const id = v4();
      const fileUrl = URL.createObjectURL(file);
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileUrl);

      // Create gallery image
      const image = {
        Id: id,
        ThumbSafeUrl: sanitizedUrl,
        LargeSafeUrl: sanitizedUrl,
        LargeUrl: fileUrl,
        ThumbUrl: fileUrl,
        IsUploaded: false, // Set to false for non-uploaded photos
        IsProcessing: false,
        IsEditing: false,
      } as GalleryImage;

      // Add to gallery images
      this.galleryImages.update((images) => [...images, image]);
      
      // Create processor info
      const photo = {
        Id: id,
        FileUrl: fileUrl,
        Status: PhotoProcessorStatus.Created,
        Name: file.name,
        ContentType: file.type,
        LargePhotoInfo: { Progress: 0 } as PhotoInfo,
        ThumbPhotoInfo: { Progress: 0 } as PhotoInfo,
        IsUploading: false
      } as PhotoProcessorInfo;

      // Get current mechanics project ID (magic string)
      const currentProjectId = this.projectId();
      
      // Add to photos to upload with mechanics project ID
      this.constructionPhotosService.addPhotosToUpload(photo, currentProjectId);
      
      // Set dirty state
      this._isDirty.set(true);
      
      // Update dirty state in the DirtyService if we have a date string
      const dateString = this.currentDate();
      if (dateString) {
        this.dirtyService.setDirty(dateString, MechanicsComponentIdentifiers.PHOTOS, true);
      }
    });  
  }

  markPhotoForDeletion(image: GalleryImage, mark: boolean): void {
    if (image.IsUploaded) {
      image.MarkForDeletion = mark;
      
      // Get current mechanics project ID (magic string)
      const currentProjectId = this.projectId();
      
      if (mark) {
        // Add to photos to remove
        this.constructionPhotosService.addPhotoToRemove(image.Id, currentProjectId);
      } else {
        // Remove from photos to remove
        this.constructionPhotosService.removePhotoFromRemove(image.Id, currentProjectId);
      }
      
      // Set dirty state
      this._isDirty.set(true);
      
      // Update dirty state in the DirtyService
      const dateString = this.currentDate();
      if (dateString) {
        this.dirtyService.setDirty(dateString, MechanicsComponentIdentifiers.PHOTOS, true);
      }
    } else {
      // For non-uploaded photos: remove from gallery and photos to upload
      this.galleryImages.update((images) => images.filter((img) => img.Id !== image.Id));
      // Remove from construction photos service
      const currentProjectId = this.projectId();
      this.constructionPhotosService.projectPhotosToAdd.update(photos => 
        photos.filter(photo => photo.Id !== image.Id));
    }
  }
  
  cancelUpload(): void {
    // Clear all non-uploaded photos
    this.galleryImages.update((images) => images.filter((img) => img.IsUploaded));
    // Clear construction photos service uploads
    this.constructionPhotosService.clear();
  }
}