import { HttpClient } from "@angular/common/http";
import { computed, inject, Injectable, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { environment } from "src/environments/environment";
import { OnSiteFavorite } from "../interfaces/favorite";

@Injectable({
	providedIn: 'root'
})
export class ConstructionFavoritesService {
	http = inject(HttpClient);
	favorites = computed(() => this.favoritesResource.value() ?? []);
	projectIds = signal<string[]>([]);
	isLoading = computed(() => this.favoritesResource.isLoading());
	isAdding = signal<boolean>(false);
	isRemoving = signal<boolean>(false);
	favoritingProjects = signal<Array<string>>([]);
	initialize = signal<boolean>(false);

	private favoritesResource = rxResource({
		request: () => this.initialize(),
		loader: (request) => {
			if (request.request) {
				return this.http.get<Array<OnSiteFavorite>>(`${environment.services_root_endpoints.onsite_favorites}/`);
			}

			return of([]);
		}
	});

	addFavorite(projectId: string): void {
		this.isAdding.set(true);

		const request = {
			ProjectId: projectId
		};

		this.favoritingProjects.update(ids => [...ids, projectId]);

		this.http.post<OnSiteFavorite>(`${environment.services_root_endpoints.onsite_favorites}/`, request).subscribe({
			next: (response) => {
				if (response) {
					this.isAdding.set(false);
					this.favoritingProjects.update(ids => ids.filter(id => id !== projectId));
					this.favoritesResource.update(favorites => [ ...(favorites ?? []), response ]);
				}
			},
			error: (error) => {
				console.error('Error adding favorite:', error);
				this.isAdding.set(false);
				this.favoritingProjects.update(ids => ids.filter(id => id !== projectId));
			}
		});		
	}

	isFavoritingProject(projectId: string): boolean {
		return this.favoritingProjects().includes(projectId);
	}

	removeFavorite(projectId: string): void {
		this.isRemoving.set(true);		
		this.favoritingProjects.update(ids => [...ids, projectId]);

		this.http.delete(`${environment.services_root_endpoints.onsite_favorites}/${projectId}`).subscribe({
			next: (response) => {
				if (response) {
					this.isRemoving.set(false);
					this.favoritingProjects.update(ids => ids.filter(id => id !== projectId));
				//	this.favoritesResource.reload();
					this.favoritesResource.update(favorites => 
					{
						const fav = favorites?.filter(favorite => favorite.ProjectId !== projectId) ?? [];
						return [...fav];
					});
				}
			},
			error: (error) => {
				console.error('Error removing favorite:', error);
				this.isRemoving.set(false);
				this.favoritingProjects.update(ids => ids.filter(id => id !== projectId));
			}
		});
	}

}