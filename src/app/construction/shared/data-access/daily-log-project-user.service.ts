import { HttpClient } from '@angular/common/http';
import { Injectable, inject, signal } from '@angular/core';
import { catchError, Observable, of, tap } from 'rxjs';
import {
  BaseDailyLogProjectUserComponent,
  DailyLogProjectUserComponent,
  DailyLogProjectUserPermissions,
  LatestDailyLogReponse,
  NotesComponentProjectUser,
  PhotosComponentProjectUser,
  SiteConditionsComponentProjectUser,
  TimeCardComponentProjectUser,
  WeatherComponentProjectUser
} from '../interfaces/daily-log-project-user';
import { UserProjectComponentsService } from './user-project-components.service';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';
import { DailyLogUserProjectComponent } from '../interfaces/daily-log-user-project';
import { environment } from 'src/environments/environment';
import { DailyLogComponentIdentifiers } from '../interfaces/daily-log-shared';
import { TimeCard } from '../interfaces/timecard';
import { AccessRequest } from '../../../shared/interfaces/access';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { Approval } from '../interfaces/approval';

@Injectable({
  providedIn: 'root'
})
export class DailyLogProjectUserService {

  http = inject(HttpClient);
  accessService = inject(AccessService);
  userProjectComponentsService = inject(UserProjectComponentsService);

  fetchedLogs = signal<DailyLogProjectUserComponent[]>([]);
  dailyLogPermissions = signal<DailyLogProjectUserPermissions | null>(null);

  dailyLogsGetOrCreate(projectIds: string[], month: number, day: number, year: number): Observable<DailyLogProjectUserComponent[]> {
    return new Observable<DailyLogProjectUserComponent[]>((observer) => {
      this.getDailyLogs(projectIds, month, day, year).subscribe({
        next: (results) => {
          const logs: DailyLogProjectUserComponent[] = [];
          let completedRequests = 0;

          const handleCompletion = () => {
            completedRequests++;
            if (completedRequests === projectIds.length) {
              observer.next(logs);
              observer.complete();
            }
          };

          projectIds.forEach((projectId) => {
            const result = results.find(log => log.ProjectId === projectId && log.Year === year && log.Month == month && log.Day == day) || null;
            if (!result) {
              this.createDailyLog().subscribe({
                next: (dailyLog) => {
                  if (dailyLog) {
                    dailyLog.ProjectId = projectId; // Assign the projectId to the created log
                    logs.push(dailyLog);
                  } else {
                    observer.error(`Daily Log component not found for project ${projectId}`);
                  }
                  handleCompletion();
                },
                error: () => {
                  handleCompletion();
                }
              });
            } else {
              logs.push(result);
              handleCompletion();
            }
          });
        },
        error: (err) => {
          observer.error(err);
        }
      });
    });
  }

  createDailyLog(): Observable<DailyLogProjectUserComponent | null> {
    return new Observable<DailyLogProjectUserComponent | null>((observer) => {
      this.userProjectComponentsService.getComponentStoreCached().subscribe({
        next: (result) => {
          if (result) {
            var dailyLogComponent = result.Components.find(x => x.ComponentIdentifier === ProjectComponentIdentifiers.DAILY_LOG) as DailyLogUserProjectComponent | undefined;

            if (dailyLogComponent) {
              var dailyLogProjectUser = {} as DailyLogProjectUserComponent;
              dailyLogProjectUser.Components = [];
              for (let prop of dailyLogComponent.Components) {
                let baseComponent = { ComponentIdentifier: prop.ComponentIdentifier, IsActive: prop.IsActive } as BaseDailyLogProjectUserComponent;
                switch (prop.ComponentIdentifier) {
                  case DailyLogComponentIdentifiers.NOTES:
                    dailyLogProjectUser.Components.push(baseComponent as NotesComponentProjectUser)
                    break;
                  case (DailyLogComponentIdentifiers.PHOTOS):
                    dailyLogProjectUser.Components.push(baseComponent as PhotosComponentProjectUser)
                    break;
                  case (DailyLogComponentIdentifiers.SITE_CONDITIONS):
                    dailyLogProjectUser.Components.push(baseComponent as SiteConditionsComponentProjectUser)
                    break;
                  case (DailyLogComponentIdentifiers.TIMECARD):
                    var tc = baseComponent as TimeCardComponentProjectUser;
                    tc.TimeCards = [{} as TimeCard];
                    dailyLogProjectUser.Components.push(tc);
                    break;
                  case (DailyLogComponentIdentifiers.WEATHER):
                    dailyLogProjectUser.Components.push(baseComponent as WeatherComponentProjectUser);
                    break;
                  default:
                    throw new Error(`Component ${prop.ComponentIdentifier} not found`);
                }
              }

              observer.next(dailyLogProjectUser);
              observer.complete();
            } else {
              observer.error('Daily Log component not found');
            }
          } else {
            observer.next(null);
            observer.complete();
          }
        }
      });
    });
  }

  getDailyLogs(projectIds: string[], month: number, day: number, year: number): Observable<DailyLogProjectUserComponent[]> {
    const url = `${environment.services_root_endpoints.project_user_components}/project-components/project-user/multiple/component/DailyLog/date-multiple?projectIds=${projectIds.join(',')}&year=${year}&month=${month}&day=${day}`;

    return this.http.get<DailyLogProjectUserComponent[]>(url).pipe(
      tap((logs) => this.fetchedLogs.set(logs)), // ✅ Store logs in signal
      catchError((err) => {
        console.error("Failed to fetch daily logs:", err);
        return of([]);
      })
    );
  }

  getDailyLogById(projectId: string, dailyLogId: string): Observable<DailyLogProjectUserComponent> {
    return this.http.get<DailyLogProjectUserComponent>(`${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/id?dailyLogId=${dailyLogId}`);
  }

  saveDailyLog(projectId: string, component: DailyLogProjectUserComponent): Observable<DailyLogProjectUserComponent> {
    var url = `${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/save`

    if (component.Id) {
      url += `?dailyLogId=${component.Id}`
    }

    return this.http.post<DailyLogProjectUserComponent>(url, component);
  }

  saveDailyLogApprover(projectId: string, component: DailyLogProjectUserComponent): Observable<DailyLogProjectUserComponent> {
    var url = `${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/save-approver`

    if (component.Id) {
      url += `?dailyLogId=${component.Id}`
    }

    return this.http.post<DailyLogProjectUserComponent>(url, component);
  }

  deleteDailyLog(projectId: string, dailyLogId: string) {
    return this.http.get<DailyLogProjectUserComponent>(`${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/delete?dailyLogId=${dailyLogId}`);
  }

  setNoWork(projectId: string, dailyLogId: string, isNoWork: boolean) {
    return this.http.patch<DailyLogProjectUserComponent>(`${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/no-work?dailyLogId=${dailyLogId}&isNoWork=${isNoWork}`, null);
  }

  getLastDailyLog(projectId: string): Observable<LatestDailyLogReponse> {
    return this.http.get<LatestDailyLogReponse>(`${environment.services_root_endpoints.project_user_components}/project-components/project-user/${projectId}/component/DailyLog/last-daily-log`);
  }

  validateDailyLog(dluProjectUserComponent: DailyLogProjectUserComponent): { isValid: boolean, messages: Array<string> } {
    var isValid = true;
    var messages = new Array<string>();

    if (dluProjectUserComponent) {
      var timeCardComp = dluProjectUserComponent.Components.find(x => x.ComponentIdentifier == DailyLogComponentIdentifiers.TIMECARD) as TimeCardComponentProjectUser;
      if (timeCardComp) {
        if (timeCardComp) {
          for (let timeCard of timeCardComp.TimeCards) {
            if (timeCard.Employees) {
              for (let employee of timeCard.Employees) {
                var cTotal = 0;
                if (employee.CostCodes) {

                  for (let d of employee.CostCodes) {
                    cTotal += (d.UserValue) ? d.UserValue : 0;
                  }
                }
                employee.TotalHours = (employee.TotalHours) ? employee.TotalHours : 0; //TODO: this is kinda hacky. Need to think about this process. 

                if (employee.TotalHours != cTotal) {
                  isValid = false;
                  messages.push(`Hours and totals do not match for ${employee.FirstName} ${employee.LastName}`);
                }

                if ((!employee.TimeReason) || (employee.TimeReason == "Work Day" && employee.TotalHours <= 0)) {
                  isValid = false;
                  messages.push(`Work day has been selected for ${employee.FirstName} ${employee.LastName}. However, there are no hours assigned. Please select a valid reason for having no work hours`);
                }
              }
            }

          }
        }
      }
    }
    return { isValid: isValid, messages: messages };
  }

  getDailyLogPermissions() {
    if (this.dailyLogPermissions()) {
      return of(this.dailyLogPermissions() as DailyLogProjectUserPermissions);
    } else {
      return new Observable<DailyLogProjectUserPermissions>(obs => {
        var requests = new Array<AccessRequest>();

        requests.push({
          Action: 'access-project-dailylog',
          Resource: 'route-access'
        });


        this.accessService.GetAccessMulti(requests).subscribe(result => {
          const permission = {
            routeAccess: result[0].Access
          } as DailyLogProjectUserPermissions;

          this.dailyLogPermissions.set(permission);

          obs.next(permission);
          obs.complete();

        });
      });
    }
  }
}

export interface DailyLogProjectUserComponentDTO extends DailyLogProjectUserComponent {
  Approval: Approval
}