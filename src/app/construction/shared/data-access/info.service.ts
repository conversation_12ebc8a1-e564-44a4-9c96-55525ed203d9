import { Injectable, inject, signal } from '@angular/core';
import { Observable, forkJoin, of} from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import { InfoItem, InfoPermissions, ProjectInfoComponent } from '../interfaces/info';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';
import { UserProjectComponentsService } from './user-project-components.service';
import { ConstructionGlobalService } from './global.service';
import { ProjectComponentsService } from './project-components.service';
import { AccessRequest } from '../../../shared/interfaces/access';
import { AccessService } from 'src/app/shared/data-access/access-service';

@Injectable({
  providedIn: 'root'
})
export class ConstructionInfoService {
  http = inject(HttpClient);
  globalProjectService = inject(ConstructionGlobalService);    
  userProjectComponentsService = inject(UserProjectComponentsService);  
  infoComponent = signal<ProjectInfoComponent | null>(null);
  projectComponentService = inject(ProjectComponentsService); 
  accessService = inject(AccessService);
  infoPermissions = signal<InfoPermissions | null>(null);

  getOrCreateInfoComponent(projectId: string): Observable<ProjectInfoComponent> {
    return new Observable<ProjectInfoComponent>((obs) => {
      this.getInfoComponent(projectId).subscribe({
        next: (result) => {
          if(result){
            this.getUserStoreInfoComponent().subscribe({
              next: (userResult) => {
                if(userResult){
                  this.syncInfoComponent(result, userResult);
                  result.Properties = [...this.reOrderProperties(result, userResult)];            
                  obs.next(result);
                  obs.complete();
                }else{             
                  obs.next(result);
                  obs.complete();
                }
              },
              error: (err) => {
                obs.error(err);
              }
            });
       
          }else{
            forkJoin({
              projectInfoComponent: this.projectComponentService.addComponentToStoreById<ProjectInfoComponent>(projectId, ProjectComponentIdentifiers.INFO, true), 
              userProjectInfoComponent: this.getUserStoreInfoComponent()}).subscribe({
                next: (result) => {
                  if(result.projectInfoComponent && result.userProjectInfoComponent){
                    this.syncInfoComponent(result.projectInfoComponent, result.userProjectInfoComponent);
                    result.projectInfoComponent.Properties = [...this.reOrderProperties(result.projectInfoComponent, result.userProjectInfoComponent)];
                    obs.next(result.projectInfoComponent);
                    obs.complete();
                  }else{
                    obs.error("Error creating info component");
                  }
                },
                error: (err) => {
                  obs.error(err);
                }
              });         
          }
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }
  getUserStoreInfoComponent() : Observable<ProjectInfoComponent | null>{
    if(this.infoComponent()){
      return of(this.infoComponent());
    }else{
      return new Observable<ProjectInfoComponent | null>((observer) => {
        this.userProjectComponentsService.getComponentStoreCached().subscribe({
          next: (result) => {
            if(result){
              var infoComponent = result.Components.find(x => x.ComponentIdentifier === ProjectComponentIdentifiers.INFO) as ProjectInfoComponent | undefined;
         
              if(infoComponent){
                this.globalProjectService.getDefaultComponent<ProjectInfoComponent>(ProjectComponentIdentifiers.INFO).subscribe({
                  next: (globalInfoComponent) => {
                    var infoC = infoComponent as ProjectInfoComponent
                    if(result && globalInfoComponent){
                 
                      this.syncInfoComponent(infoC, globalInfoComponent);
              
                    }
  
                    this.infoComponent.set(infoC);
  
                    observer.next(infoC);
                    observer.complete();                  
                  },
                  error: (err) => { 
                    observer.error(err);
                  }
                });        
          
              }else{
                this.globalProjectService.getDefaultComponent<ProjectInfoComponent>(ProjectComponentIdentifiers.INFO).subscribe({
                  next: (result) => {
                    if(result){
                      this.infoComponent.set(result);
  
                      observer.next(result);
                      observer.complete();
                    }else{
                      observer.next(null);
                      observer.complete();
                    }
                  },
                  error: (err) => {
                    observer.error(err);
                  }
                });
              }
            }else{
              observer.next(null);
              observer.complete();
            }   
          }
        });
      });
    }

  }

  syncInfoComponent(user: ProjectInfoComponent, global:ProjectInfoComponent){
    let addProperties = new Array<InfoItem>();
    global.Properties.forEach((fromProp) => {
        let toProp = user.Properties.find(x => x.PropertyId === fromProp.PropertyId && x.IsHidden === false);
        if(!toProp){
          addProperties.push(fromProp);
        }
    });
    
    user.Properties.push(...addProperties);

  }

  reOrderProperties(user: ProjectInfoComponent, global:ProjectInfoComponent) : Array<InfoItem>{
    let addProperties = new Array<InfoItem>();
    var newOrder = new Array<InfoItem>();
    global.Properties.filter(x => x.IsHidden === false).forEach((fromProp) => {
        let toProp = user.Properties.find(x => x.PropertyId === fromProp.PropertyId);
        if(toProp){
          newOrder.push(toProp);
        }
    });
    
    return newOrder;

  }


  
  getInfoComponent(projectId:string){
    return this.http.get<ProjectInfoComponent>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.INFO}`);
  }

  updateInfoComponent(projectId:string, infoComponent:ProjectInfoComponent){
    infoComponent._t = ["InfoComponent"];    
    return this.http.put<ProjectInfoComponent>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}`, infoComponent);
  }

  addItem(infoItem: InfoItem) {
		var request = {
			Item: infoItem
		}
    return new Observable<InfoItem>((obs) => {
      this.http.patch<InfoItem>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project/component/Info/add-property`, request).subscribe({
        next: (result) => {
          this.infoComponent.update((component)=> {
            if(component){
              component.Properties = [...component.Properties, result];
            }

            return component;
          });
          obs.next(result);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });

		
	}

	removeItem(propertyId: string) {
		var request = {
			PropertyId: propertyId
		}

		return this.http.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project/component/Info/remove-property`, request);
	}

	getItem(propertyId: string) {
		var request = {
			PropertyId: propertyId
		}

		return this.http.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project/component/Info/get-property`, request);
	}


	updateItem(item: InfoItem){
		var request = {
			Item: item
		}

    return new Observable<InfoItem>((obs) => {
      this.http.patch<any>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project/component/Info/update-property`, request).subscribe({
        next: (result) => {
 
          this.infoComponent.update((infoItem) => {
            let prop = infoItem?.Properties.find(x => x.PropertyId === result.PropertyId);
            if(prop){
              prop.ValueType = result.ValueType;
              prop.Name = result.Name;
            }  
            
            return infoItem;
          });

          obs.next(result);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });


	}

	saveUserInfoComponent(component: ProjectInfoComponent): Observable<ProjectInfoComponent>{
		component._t = ['ProjectComponent', 'InfoComponent'];
    this.infoComponent.update(() => {
      return component;
    })

		return this.http.put<ProjectInfoComponent>(`${environment.services_root_endpoints.user_project_components}/project-components/user-project`, component);
	}

  getInfoPermissions(): Observable<InfoPermissions> {
    if(this.infoPermissions()){
      return of(this.infoPermissions() as InfoPermissions);
    }else{
      return new Observable<InfoPermissions>(obs => {
        var requests = new Array<AccessRequest>();

        requests.push({
          Action: 'access-project-info-update',
          Resource: 'project-components'
        });
  
  
        this.accessService.GetAccessMulti(requests).subscribe(result => {
          const permission ={
            editInfoAccess: result[0].Access
          } as InfoPermissions;
          
          this.infoPermissions.set(permission);
        
          obs.next(permission);
          obs.complete();
  
        });
      });
    }
  }
}
