import { Injectable, effect, inject, signal, computed } from '@angular/core';
import { ComponentView } from '../interfaces/project-components';
import { NotesComponent } from '../interfaces/mechanics';
import { DailyLogComponentIdentifiers } from '../interfaces/daily-log-shared';
import { IComponentService } from '../interfaces/component-service.interface';
import { MechanicsDirtyService } from './mechanics-dirty.service';
import { MechanicsDailyLogService } from './mechanics-daily-log.service';

/**
 * Mechanics Notes Service
 * 
 * Adapts the existing notes functionality to work with the mechanics daily log system.
 * Manages notes data within the UserDailyLogComponent structure using signal-based state management.
 * 
 * This service acts as a bridge between the existing DailyLogNotesComponent and the 
 * mechanics-specific data structures, ensuring seamless integration without modifying
 * the existing notes component.
 *
 * NOTE: This service is NOT providedIn: 'root' because:
 * 1. It injects MechanicsDailyLogService which is component-level for consistency
 * 2. Follows the same pattern as project DailyLogNotesService for consistency
 * 3. Allows each mechanics diary instance to have isolated state
 * 4. Prevents circular dependency issues between different injection contexts
 */
@Injectable()
export class MechanicsNotesService implements IComponentService<NotesComponent> {
  private dirtyService = inject(MechanicsDirtyService);
  private mechanicsService = inject(MechanicsDailyLogService);

  // Signal-based state management
  notes = signal<string>('');
  view = signal<ComponentView>(ComponentView.Edit);
  access = signal<string>('Allow');
  isLoading = signal<boolean>(false);

  // Track original note value to detect changes
  private originalNote = signal<string>('');

  // Track current date string for dirty state management
  private currentDateString = signal<string>('');

  // Computed signals
  readonly isDirty = computed(() => {
    const currentNotes = this.notes();
    const originalNotes = this.originalNote();
    return currentNotes !== originalNotes;
  });
  
  constructor() {
    // Track dirty state changes and update dirty service
    effect(() => {
      const currentNotes = this.notes();
      const originalNotes = this.originalNote();
      const dateString = this.currentDateString();

      if (dateString) {
        const isDirty = currentNotes !== originalNotes;
        this.dirtyService.setDirty(dateString, DailyLogComponentIdentifiers.NOTES, isDirty);
      }
    });

    // Observe daily log changes and update notes data automatically
    effect(() => {
      const dailyLog = this.mechanicsService.currentDailyLog();
      if (dailyLog) {
        const notesComponent = dailyLog.Components?.find(c => 
          c.ComponentIdentifier === DailyLogComponentIdentifiers.NOTES
        ) as NotesComponent | undefined;
        
        // Auto-update notes data when daily log changes (date navigation)
        this.setInitialData(notesComponent || null, ComponentView.Edit, 'Allow');
      }
    });
  }
  
  /**
   * Set the current date string for dirty state tracking
   * @param dateString The date string (YYYY-MM-DD)
   */
  setCurrentDate(dateString: string): void {
    this.currentDateString.set(dateString);
  }

  /**
   * Set initial data for the notes component (IComponentService implementation)
   * @param notesComponent The notes component data or null
   * @param view The component view mode
   * @param access The access level string
   */
  setInitialData(notesComponent: NotesComponent | null, view: ComponentView, access: string): void {
    if (notesComponent) {
      this.notes.set(notesComponent.Note || '');
      this.originalNote.set(notesComponent.Note || '');
    } else {
      this.notes.set('');
      this.originalNote.set('');
    }

    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
  }
  
  /**
   * Get updated component for saving
   * This method is called by the existing DailyLogNotesComponent
   */
  getUpdatedComponent(): NotesComponent {
    const component: NotesComponent = {
      _t: ["NotesComponent"],
      ComponentIdentifier: DailyLogComponentIdentifiers.NOTES,
      Note: this.notes()
    };
    
    // Update the original note to match the current value
    this.originalNote.set(this.notes());
    
    return component;
  }
  
  /**
   * Update notes value (called from component)
   */
  updateNotes(value: string): void {
    this.notes.set(value);
  }
  
}