import { Injectable, computed, inject, signal } from '@angular/core';
import { CostCode, CostCodeInfoComponent } from '../interfaces/cost-codes';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';
import { Observable, of } from 'rxjs';
import { ProjectComponentsService } from './project-components.service';

@Injectable({
  providedIn: 'root'
})
export class ConstructionCostCodesService {

  http = inject(HttpClient);
  projectComponentService = inject(ProjectComponentsService);
    
  private costCodesComponents = signal<Array<CostCodeCache>>([]);
  getCostCodeComponent(projectId:string): Observable<CostCodeInfoComponent>{  
      let costCodeComponent = this.costCodesComponents().find(x => x.projectId == projectId);
      if(costCodeComponent){
        return of(costCodeComponent.costCodeComponent);
      }else{
        return new Observable<CostCodeInfoComponent>(observer => {
          this.http.get<CostCodeInfoComponent>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.COSTCODES}`).subscribe({
            next: (result: CostCodeInfoComponent) => {

              if(result){        
                this.costCodesComponents.update((costCodes) => [...costCodes, {projectId: projectId, costCodeComponent: result}]);         
                observer.next(result);     
                observer.complete();     
              }else{
                this.projectComponentService.addComponentToStoreById<CostCodeInfoComponent>(projectId, ProjectComponentIdentifiers.COSTCODES, true).subscribe({                 
                  next: (result) => {     
                    if(!result.CostCodes){
                      result.CostCodes = [];
                    }                        
                    this.costCodesComponents.update((costCodes) => [...costCodes, {projectId: projectId, costCodeComponent: result}]);             
                    observer.next(result);     
                    observer.complete();
                  }, error: (err: any) => {
                    observer.error(err);
                    observer.complete();
                  }          
                });
              }
            }, error: (err: any) => {
              observer.error(err);
              observer.complete();
            }          
          });
        });        
        
      }
      
  }

  getCostCode(projectId: string, costCodeId: string){
    return this.http.get<CostCode>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.COSTCODES}/get-cost-code?costCodeId=${costCodeId}`);
  }

  updateCostCode(projectId:string, costCodeId: string, costCode:CostCode){
    return new Observable<CostCode>(observer => {
      this.http.patch<CostCode>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.COSTCODES}/update-cost-code`, costCode).subscribe({
        next: (result: CostCode) => {
          this.updateCostCodeCache(projectId, result);
          observer.next(result);
          observer.complete();
        }, error: (err: any) => {
          observer.error(err);
          observer.complete();
        }          
      });
    });    
  }

  addCostCode(projectId:string, costCode:CostCode){
    return new Observable<CostCode>(observer => {
      this.http.patch<CostCode>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.COSTCODES}/add-cost-code`, costCode).subscribe({
        next: (result: CostCode) => {
          this.updateCostCodeCache(projectId, result);
          observer.next(result);
          observer.complete();
        }, error: (err: any) => {
          observer.error(err);
          observer.complete();
        }          
      });
    });
  }

  importCostCode(key:string, bucket: string): Observable<Array<CostCode>> {
    return this.http.get<Array<CostCode>>(`${environment.services_root_endpoints.cost_code_import}/import?key=${key}&bucket=${bucket}`);
  }

  addManyCostCode(projectId:string, costCodes:Array<CostCode>): Observable<Array<CostCode>>{
    var request = {
      ProjectId: projectId,
      CostCodes: costCodes
    }

    return new Observable<Array<CostCode>>(observer => {
      return this.http.patch<Array<CostCode>>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/CostCodes/add-many-costcodes`, request).subscribe({
        next: (result: Array<CostCode>) => {
          for(let costCode of result){
            this.updateCostCodeCache(projectId, costCode);
          }

          observer.next(result);
          observer.complete();
        }, error: (err: any) => {
          observer.error(err);
          observer.complete();
        }                
      });
    });

  }

  deleteCostCode(projectId:string, costCodeId:string): Observable<any>{
    var request = {
      ProjectId: projectId,
      CostCodeId: costCodeId
    }

    return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/CostCodes/delete-cost-code`, request);
  }


  updateCostCodeCache(projectId:string, costCode:CostCode){
    let costCodeComponent = this.costCodesComponents().find(x => x.projectId == projectId);
    if(costCodeComponent){
      
      this.costCodesComponents.update((costCodeComponents) => {
        var costCodeComponentInfo = costCodeComponents.find(x => x.projectId == projectId);

        if (costCodeComponentInfo && costCodeComponentInfo.costCodeComponent) {

          if(!costCodeComponentInfo.costCodeComponent?.CostCodes){
            costCodeComponentInfo.costCodeComponent.CostCodes = [costCode];
          }else{
            let costCodeData =  costCodeComponentInfo.costCodeComponent.CostCodes.find(x => x.CostCodeId == costCode.CostCodeId);

            if (costCodeData) {
              var nCC = costCodeComponentInfo.costCodeComponent.CostCodes.map(x => {
                if (x.CostCodeId == costCode.CostCodeId) {
                  return costCode;
                } else {
                  return x;
                }
              });
              costCodeComponentInfo.costCodeComponent.CostCodes = nCC;
            } else {
              costCodeComponentInfo.costCodeComponent.CostCodes.push(costCode);
            }
          }
        }

        return costCodeComponents;
      });
    }
  }
}

export interface CostCodeCache{
  projectId: string;
  costCodeComponent: CostCodeInfoComponent;
}
