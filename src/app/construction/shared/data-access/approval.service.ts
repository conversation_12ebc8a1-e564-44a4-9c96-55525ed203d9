import { Injectable, computed, inject, signal } from '@angular/core';
import { Approval, ApprovalAccess, ApprovalInfo, ApprovalLevel, ApprovalListResponse, ApprovalsFilter, DailyLogApprovals, GetApprovalLevelResponse, approvalLevelsInfos } from '../interfaces/approval';
import { Observable, forkJoin, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessRequest } from '../../../shared/interfaces/access';
import { sign } from 'crypto';
import { rxResource } from '@angular/core/rxjs-interop';
import { NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class ApprovalService {
  
  payrollLevel: number = 999;
  accessService = inject(AccessService);
  http = inject(HttpClient);
  toastrService = inject(ToastrService);
  approvalAccess = signal<ApprovalAccess | null>(null);
  userApprovalLevels = signal<Array<ApprovalLevel> | null>(null);
  userApprovalLevel = computed(() => this.userApprovalLevelResource.value()?.Level);
  startDate = signal<Date | null>(null);
  endDate = signal<Date | null>(null);
  singleDate = signal<Date | null>(null);
  search = signal<string | null>(null);
  sort = signal<number>(0);
  sortName = signal<string>("ProjectId");
  currentPage = signal<number>(1);
  limit = signal<number>(50);
  initializeApprovals = signal<boolean>(false);
  approvalType = signal<string | null>(null);
  approvalLevel = signal<number>(-1);
  roleId = signal<string | null>(null);
  approvals = computed(() => this.approvalsResource.value()?.Approvals || []);
  approvalsTotal = computed(() => this.approvalsResource.value()?.Total || 0);
  isLoading = computed(() => this.approvalsResource.isLoading() || this.userApprovalLevelResource.isLoading());
  selectedProjectsIds = signal<string[]>([]);
  initializeUserApprovalLevel = signal<boolean>(false);
  isApproving = signal<boolean>(false);
  unApprovingIds = signal<string[]>([]);
  
  public GetApproval(dailyLogId: string): Observable<Approval> {
    return this.http.get<Approval>(`${environment.services_root_endpoints.approvals}/approvals/dailylog/${dailyLogId}`);
  }
  public AddApproval(projectId: string, approval:Approval): Observable<Approval> {
    approval.ApprovalLevels = this.GetApprovalLevels();
    return this.http.post<Approval>(`${environment.services_root_endpoints.approvals}/approvals?projectId=${projectId}`, approval);
  }
  public RemoveApproval(approvalId: string): Observable<boolean> {
    return this.http.delete<boolean>(`${environment.services_root_endpoints.approvals}/approvals/${approvalId}`);
  }  
  public Approve(approvalId: string, currentLevel: number): Observable<any> {
    var lvl = {
      Level: currentLevel
    }
    return this.http.put<any>(`${environment.services_root_endpoints.approvals}/approvals/${approvalId}/approve`, lvl);
  }
  
  public Unapprove(approvalId: string, currentLevel: number) {
    var lvl = {
      Level: currentLevel
    }

    this.unApprovingIds.update(ids => {
      if (ids) {
        return [...ids, approvalId];
      }
      return [approvalId];
    });

    this.http.put<Approval>(`${environment.services_root_endpoints.approvals}/approvals/${approvalId}/unapprove`, lvl).subscribe({
      next: (result) => {
            
        this.approvalsResource.update(approvals => {
          if (approvals) {
            return {
              ...approvals,
              Approvals: approvals.Approvals.map(x => 
                x.Id === approvalId ? { ...x, ...result } : x
              )
            };
          }
          return approvals;
        });

        this.unApprovingIds.update(ids => {
          if (ids) {
            return ids.filter(x => x !== approvalId);
          }
          return ids;
        });

        this.removeApproval(approvalId);
      },
      error: (err) => {
             this.unApprovingIds.update(ids => {
          if (ids) {
            return ids.filter(x => x !== approvalId);
          }
          return ids;
        });

        console.log(err);
      }
    });
  }



  public UnSubmit(approvalId: string){
    return this.http.delete(`${environment.services_root_endpoints.approvals}/approvals/${approvalId}`);
  }

  public GetApprovalsByDailyLogIds(dailyLogIds: string[]): Observable<DailyLogApprovals> {
    const url = `${environment.services_root_endpoints.approvals}/approvals/dailylog?dailyLogIds=${dailyLogIds.join(',')}`;
    return this.http.get<DailyLogApprovals>(url);
  }

 removeApproval(approvalId:string) {
    if (approvalId) {
      this.approvalsResource.update(approvals => {
        if (approvals) {
          return {
            ...approvals,
            Approvals: approvals.Approvals.filter(x => x.Id !== approvalId),
            Total: approvals.Total - 1
          };        
        }
  
        return approvals;
      });
      
    }
  }

  userApprovalLevelResource = rxResource({
    request: () => ({
      initializeUserApprovalLevel: this.initializeUserApprovalLevel(),
    }),
    loader: (request) => {
      if (request.request.initializeUserApprovalLevel) {
        return  this.http.get<GetApprovalLevelResponse>(`${environment.services_root_endpoints.approvals}/approval-level`);
      }

      return of(null);
    }
  });

  initializeUserApprovalLevels() {
    var levels = new Array<ApprovalLevel>();

    approvalLevelsInfos.map(result => {
      var approvalLevel: ApprovalLevel = {
        Level: result.Level,
        Name: result.Name,
        ApprovedDate: null,
        UserApproved: null
      }

      levels.push(approvalLevel);
    });

    this.userApprovalLevels.set(levels);

    return levels;
  }

  public GetApprovalLevels(): Array<ApprovalLevel>{
    let approvalLevels = new Array<ApprovalLevel>();

    for(let level of approvalLevelsInfos){
      approvalLevels.push({
        Level: level.Level,
        Name: level.Name,
        ApprovedDate: null,
        UserApproved: null
      })
    }

    return approvalLevels;
  }

  initializeApprovalAccess() {
    var accessMulti = new Array<AccessRequest>();
    accessMulti.push({
      Resource: "approval",
      Action: "removeapproval"
    });

    accessMulti.push({
      Resource: "projectusercomponents-dailyLog",
      Action: "removedailylog"
    });

    accessMulti.push({
      Resource: "route-access",
      Action: "access-delete-approval"
    });

    var getAccessMutli = new Array<AccessRequest>();
    getAccessMutli.push({
      Resource: "approval-edit-info",
      Action: "approval-edit-qty-notes"
    });

    forkJoin({ mutliAccess: this.accessService.GetAccessMulti(getAccessMutli), deleteApprovalAccess: this.accessService.CheckAccessMulti(accessMulti) }).subscribe({
      next: (result) => {
        var access = {
          EditQtyNotes: result.mutliAccess[0].Access,
          DeleteApproval: result.deleteApprovalAccess.Access
        } as ApprovalAccess;

        this.approvalAccess.set(access);
      },
      error: (err) => {
        console.log(err);
      }
    });
  }
  


  approvalsResource = rxResource({
    request: () => ({
      search: this.search(),
      startDate: this.startDate(),      
      endDate: this.endDate(),      
      sortName: this.sortName(),
      sort: this.sort(),
      currentPage: this.currentPage(),
      limit: this.limit(),
      initializeApprovals: this.initializeApprovals(),
      approvalType: this.approvalType(),
      approvalLevel: this.approvalLevel(),
      selectedProjectsIds: this.selectedProjectsIds(),
      userApprovalLevel: this.userApprovalLevel(),
      roleId: this.roleId()
    }),
    loader: (request) => {
      if (request.request.initializeApprovals && request.request.userApprovalLevel 
        && (request.request.approvalLevel > 0 || request.request.approvalType === 'all') && request.request.selectedProjectsIds.length > 0) {

        var url = `${environment.services_root_endpoints.approvals}/approvals/search`        

        let queryParams = new HttpParams(); // Initialize HttpParams

        if (request.request.sort) {
          let reversed = false;
          if (request.request.sort === 0) {
            reversed = false;
          } else if (request.request.sort === 1) {
            reversed = true;
          }
          queryParams = queryParams.set('reversed', reversed);
        }
        if (request.request.search) {
          queryParams = queryParams.set('search', request.request.search);
        }
        if (request.request.approvalLevel) {
          queryParams = queryParams.set('level', request.request.approvalLevel.toString());
        }
        if (request.request.currentPage) {
          queryParams = queryParams.set('page', request.request.currentPage.toString());
        }
        if (request.request.limit) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }

        if (request.request.approvalType) {
          queryParams = queryParams.set('approval-type', request.request.approvalType);
        }

        if (request.request.startDate) {          
          queryParams = queryParams.set('date-start', `${request.request.startDate}`);
        }

        if(request.request.endDate) {
          queryParams = queryParams.set('date-end', `${request.request.endDate}`);
        } 

        if (request.request.currentPage) {
          queryParams = queryParams.set('page', request.request.currentPage.toString());
        }

        if (request.request.limit) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }

        if (request.request.sortName) {
          queryParams = queryParams.set('sort', request.request.sortName);
        }

        if(request.request.roleId) {
          queryParams = queryParams.set('roleId', request.request.roleId);
        }


        return this.http.post<ApprovalListResponse>(url, {ProjectIds: request.request.selectedProjectsIds}, { params: queryParams});
      }

      return of(null);
    }
  });
}
