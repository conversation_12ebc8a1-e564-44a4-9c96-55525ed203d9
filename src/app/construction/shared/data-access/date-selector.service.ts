import { Injectable, signal, computed } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DateSelectorService {

  selectedDate = signal<Date>(new Date());

  formattedDate = computed(() => this.selectedDate().toLocaleDateString());
  selectedYear = computed(() => this.selectedDate().getFullYear());
  selectedMonth = computed(() => this.selectedDate().getMonth() + 1);
  selectedDay = computed(() => this.selectedDate().getDate());

  setDate(date: Date): void {
    this.selectedDate.set(date);
  }

  moveDate(dayOffset: number): void {
    const currentDate = new Date(this.selectedDate());
    currentDate.setDate(currentDate.getDate() + dayOffset);
    this.selectedDate.set(currentDate);
  }

  setToday(): void {
    this.selectedDate.set(new Date());
  }
}