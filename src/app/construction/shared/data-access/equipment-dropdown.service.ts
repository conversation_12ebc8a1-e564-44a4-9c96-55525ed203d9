import { HttpClient, HttpParams } from "@angular/common/http";
import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { environment } from "src/environments/environment";
import { EquipmentInfo, EquipmentStore } from "../interfaces/equipment";
import { EquipmentService } from "./equipment.service";

@Injectable({
	providedIn: 'root'
})
export class EquipmentDropdownService {
	private http = inject(HttpClient);
	private equipmentService = inject(EquipmentService);

	equipmentForSelection = computed(() => {
		const equipments = this.equipmentForSelectionsResource.value()?.Equipments;
		if (!equipments) return [];

		// Sort by equipment number for better user experience
		return [...equipments].sort((a, b) => {
			const numA = a.Number || '';
			const numB = b.Number || '';
			return numA.localeCompare(numB, undefined, { numeric: true, sensitivity: 'base' });
		});
	});
	equipmentSelectionLoading = computed(() => this.equipmentForSelectionsResource.isLoading());
	initializeEquipmentData = signal<boolean>(false); // true = trigger API call for dropdown data, false = data stale/don't call API
	selectedEquipment = signal<Array<EquipmentInfo>>([]);

	constructor() {
		// Simple cache invalidation: when main service data becomes stale, mark dropdown data as stale
		// Component-driven refresh will handle the actual API call timing
		effect(() => {
			if (!this.equipmentService.initialized()) {
				this.initializeEquipmentData.set(false);
			}
		}, { allowSignalWrites: true });
	}

	equipmentForSelectionsResource = rxResource({
		request: () => this.initializeEquipmentData(),
		loader: (request) => {
			if (request.request) {
				// Direct API call following projects-list-dropdown pattern
				const url = `${environment.services_root_endpoints.equipment}/equipment`;
				let queryParams = new HttpParams();
				queryParams = queryParams.set('limit', '200');
				queryParams = queryParams.set('orderBy', 'Make');
				queryParams = queryParams.set('status', 'active');

				return this.http.get<EquipmentStore>(url, { params: queryParams });
			}
			return of(null);
		}
	});
}
