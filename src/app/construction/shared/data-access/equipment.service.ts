import { Injectable, inject, signal, computed, OnDestroy } from '@angular/core';
import { of } from 'rxjs';
import { AccessRequest } from '../../../shared/interfaces/access';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { EquipmentPermissions, EquipmentStore, EquipmentInfo } from '../interfaces/equipment';
import { rxResource } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root'
})
export class EquipmentService implements OnDestroy {
  private accessService = inject(AccessService);
  private client = inject(HttpClient);

  equipmentPermissions = signal<EquipmentPermissions | null>(null);
  limit = signal<number>(100);
  status = signal<EquipmentStoreStatus>(EquipmentStoreStatus.Active);
  search = signal<string | null>(null);
  currentPage = signal<number>(1);
  initialized = signal<boolean>(false); // true = service ready to make API calls, false = data stale/not ready
  isSavingEquipment = signal<boolean>(false);
  equipmentId = signal<string | null>(null);

  // Debounced reload mechanism for handling multiple rapid status changes
  private reloadTimeoutId: ReturnType<typeof setTimeout> | null = null;
  private readonly RELOAD_DEBOUNCE_MS = 800;

  // Computed signals for accessing rxResource data
  equipmentGrid = computed(() => this.equipmentResource.value());
  isEquipmentLoading = computed(() => this.equipmentResource.isLoading());
  equipment = computed(() => this.equipmentItemResource.value());
  isEquipmentItemLoading = computed(() => this.equipmentItemResource.isLoading());

  equipmentResource = rxResource({
    request: () => {
      const req = {
        search: this.search(),
        limit: this.limit(),
        status: this.status(),
        page: this.currentPage()
      };
      return req;
    },
    loader: (request) => {
      if (this.initialized()) {
        let queryParams = new HttpParams();

        if (request.request.search) {
          queryParams = queryParams.set('search', request.request.search || '');
        }
        if (request.request.page) {
          queryParams = queryParams.set('page', request.request.page.toString());
        }
        if (request.request.limit !== null) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }
        if (request.request.status !== null) {
          queryParams = queryParams.set('status', request.request.status.toString());
        }
        return this.client.get<EquipmentStore>(`${environment.services_root_endpoints.equipment}/equipment`, { params: queryParams });
      }
      return of(null);
    }
  });

  /**
   * Separate rxResource for individual equipment items instead of using main equipmentResource
   * Rationale: Different use cases require different caching strategies and API endpoints
   * - Main grid: Paginated list with filtering, search, status - optimized for bulk display
   * - Single item: Fresh detailed data for edit forms, modals - guarantees latest state
   * - Prevents cache conflicts between grid operations and item detail operations
   * - Follows employee service pattern for clear separation of concerns
  */
  equipmentItemResource = rxResource({
    request: () => this.equipmentId(),
    loader: (request) => {
      if (request.request) {
        return this.client.get<EquipmentInfo>(`${environment.services_root_endpoints.equipment}/equipment/${request.request}`);
      }
      return of(null);
    }
  });

  // Public methods that hide rxResource complexity from consumers
  loadEquipment(): void {
    // If data is marked as stale (initialized=false), force a reload to get fresh data
    if (!this.initialized()) {
      this.equipmentResource.reload();
    }
    this.initialized.set(true);
  }

  /**
   * Loads a single equipment item by ID using API call and signals/rxResource.
   * Use this when you need to fetch fresh data from the server.
   * Access the result via equipment() and isEquipmentItemLoading() signals.
   */
  loadEquipmentItem(equipmentId: string): void {
    this.equipmentId.set(equipmentId);
  }

  /**
   * Resets user filter state to defaults when navigating to equipment page
   * Prevents stale search/status filters from persisting across navigation
   * while preserving rxResource caching to avoid unnecessary API calls
  */
  resetState(): void {
    this.search.set(null);
    this.status.set(EquipmentStoreStatus.Active);
    this.currentPage.set(1);
    // Keep limit and initialized unchanged
  }

  /**
   * Retrieves a single equipment item by ID from current grid cache (no API call).
   * Returns undefined if the item is not in the currently loaded page.
   * Use this for quick lookups when you know the item should be in the current grid.
   * For guaranteed fresh data, use loadEquipmentItem() instead.
   */
  GetEquipment(equipmentId: string): EquipmentInfo | undefined {
    const store = this.equipmentGrid();
    return store?.Equipments?.find((x: EquipmentInfo) => x.InternalId === equipmentId);
  }

  ImportData(data: Array<EquipmentInfo>): void {
    this.isSavingEquipment.set(true);

    this.client.patch<EquipmentStore>(`${environment.services_root_endpoints.equipment}/equipment?method=importdata`, data).subscribe({
      next: () => {
        // Optimistic update: Replace all equipment data in local cache immediately for instant UI feedback
        this.equipmentResource.update((store) => {
          if (store) {
            return { ...store, Equipments: data };
          }
          return store;
        });

        // Mark data as stale so other consumers will refresh on next use
        this.initialized.set(false);
        this.isSavingEquipment.set(false);
      },
      error: (error) => {
        console.error('Failed to import equipment:', error);
        this.isSavingEquipment.set(false);
      }
    });
  }

  AddEquipment(item: EquipmentInfo): void {
    this.isSavingEquipment.set(true);
    const equipments = [item];

    this.client.post<Array<EquipmentInfo>>(`${environment.services_root_endpoints.equipment}/equipment`, equipments).subscribe({
      next: () => {
        // Optimistic update: Add item to local cache immediately for instant UI feedback
        this.equipmentResource.update((store) => {
          if (store) {
            return { ...store, Equipments: [...store.Equipments, item] };
          }
          return store;
        });

        // Mark data as stale so other consumers will refresh on next use
        this.initialized.set(false);
        this.isSavingEquipment.set(false);
      },
      error: (error) => {
        console.error('Failed to add equipment:', error);
        this.isSavingEquipment.set(false);
      }
    });
  }

  SaveEquipmentItem(item: EquipmentInfo): void {
    this.isSavingEquipment.set(true);

    this.client.put<EquipmentInfo>(`${environment.services_root_endpoints.equipment}/equipment`, item).subscribe({
      next: (result: EquipmentInfo) => {
        // Update grid cache: Update item in local cache immediately for instant UI feedback
        this.equipmentResource.update((store) => {
          if (store) {
            const updatedEquipments = store.Equipments.map(eq =>
              eq.InternalId === item.InternalId ? result : eq
            );
            return { ...store, Equipments: updatedEquipments };
          }
          return store;
        });

        // Update single item cache: Following employee service pattern
        if (this.equipmentId() === item.InternalId) {
          this.equipmentItemResource.set(result);
        }

        // Mark data as stale so other consumers will refresh on next use
        this.initialized.set(false);
        this.isSavingEquipment.set(false);
      },
      error: (error) => {
        console.error('Failed to save equipment:', error);
        this.isSavingEquipment.set(false);
      }
    });
  }

  RemoveEquipmentItem(item: EquipmentInfo): void {
    this.isSavingEquipment.set(true);

    this.client.delete<any>(`${environment.services_root_endpoints.equipment}/equipment?equipmentId=${item.InternalId}`).subscribe({
      next: () => {
        // Optimistic update: Remove item from local cache immediately for instant UI feedback
        this.equipmentResource.update((store) => {
          if (store) {
            const filteredEquipments = store.Equipments.filter(eq => eq.InternalId !== item.InternalId);
            return { ...store, Equipments: filteredEquipments };
          }
          return store;
        });

        // Mark data as stale so other consumers will refresh on next use
        this.initialized.set(false);
        this.isSavingEquipment.set(false);
      },
      error: (error) => {
        console.error('Failed to remove equipment:', error);
        this.isSavingEquipment.set(false);
      }
    });
  }

  ChangeEquipmentStatus(equipmentId: string, status: boolean): void {
    this.isSavingEquipment.set(true);

    this.client.patch<any>(`${environment.services_root_endpoints.equipment}/equipment?method=change-status&status=${status}&equipmentId=${equipmentId}`, null).subscribe({
      next: () => {
        const currentStatus = this.status();

        // Handle optimistic updates based on current filter status
        this.equipmentResource.update((store) => {
          if (store) {
            if (currentStatus === EquipmentStoreStatus.All) {
              // For 'all' tab: Update the item's status in place, don't remove it
              const updatedEquipments = store.Equipments.map(eq =>
                eq.InternalId === equipmentId
                  ? { ...eq, IsActive: status }
                  : eq
              );
              return { ...store, Equipments: updatedEquipments };
            } else {
              // For active/inactive tabs: Remove item since it no longer matches the filter
              // This will trigger backfilling behavior when data is reloaded
              const filteredEquipments = store.Equipments.filter(eq => eq.InternalId !== equipmentId);
              return {
                ...store,
                Equipments: filteredEquipments,
                Total: Math.max(0, store.Total - 1) // Decrease total count for pagination
              };
            }
          }
          return store;
        });

        // Mark data as stale so other consumers will refresh on next use
        this.initialized.set(false);
        
        // Use debounced reload to handle multiple rapid status changes efficiently
        // This will trigger backfilling and proper pagination behavior
        this.scheduleReload();

        this.isSavingEquipment.set(false);
      },
      error: (error) => {
        console.error('Failed to change equipment status:', error);
        this.isSavingEquipment.set(false);
      }
    });
  }

  getEquipmentPermissions(): void {
    const cachedPermissions = this.equipmentPermissions();
    if (cachedPermissions) {
      return;
    }

    const requests: AccessRequest[] = [
      { Action: 'addequipment', Resource: 'equipment' },
      { Action: 'replaceequipment', Resource: 'equipment' }
    ];

    this.accessService.GetAccessMulti(requests).subscribe({
      next: (result) => {
        const permission = {
          AddEquipment: result[0].Access,
          ReplaceEquipment: result[1].Access
        } as EquipmentPermissions;
        this.equipmentPermissions.set(permission);
      },
      error: (error) => {
        console.error('Failed to load equipment permissions:', error);
      }
    });
  }

  /**
   * Schedules a debounced reload to handle multiple rapid status changes efficiently.
   * This prevents excessive API calls when multiple items are changed quickly.
   * The reload will trigger backfilling and proper pagination behavior.
   */
  private scheduleReload(): void {
    // Clear any existing timeout
    if (this.reloadTimeoutId) {
      clearTimeout(this.reloadTimeoutId);
    }

    // Schedule a new reload after the debounce period
    this.reloadTimeoutId = setTimeout(() => {
      try {
        // Set initialized to true to allow the reload to execute
        // This ensures the rxResource loader will make the API call
        this.initialized.set(true);
        this.equipmentResource.reload();

        this.reloadTimeoutId = null;
      } catch (error) {
        console.error('Failed to reload equipment data:', error);
        this.reloadTimeoutId = null;
      }
    }, this.RELOAD_DEBOUNCE_MS);
  }

  /**
   * Cleanup method to prevent memory leaks from pending timeouts.
   * Called when the service is destroyed (though root services rarely are).
   */
  ngOnDestroy(): void {
    if (this.reloadTimeoutId) {
      clearTimeout(this.reloadTimeoutId);
      this.reloadTimeoutId = null;
    }
  }
}

export enum EquipmentStoreStatus {
  All = "all",
  Active = "active",
  Inactive = "nonactive"
}
