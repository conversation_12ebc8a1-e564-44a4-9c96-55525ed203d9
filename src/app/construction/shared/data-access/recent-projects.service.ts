import { Injectable, InjectionToken, PLATFORM_ID, computed, effect, inject, signal } from "@angular/core";
import { ConstructionProject } from "../interfaces/construction-project";

export const LOCAL_STORAGE = new InjectionToken<Storage>(
  "window local storage object",
  {
    providedIn: "root",
    factory: () => {
      return inject(PLATFORM_ID) === "browser"
        ? window.localStorage
        : ({} as Storage);
    },
  }
);

@Injectable({
  providedIn: "root"
})
export class ConstructionRecentProjectsService {

  storage = inject(LOCAL_STORAGE);
  recentProjectsList = signal<Array<ConstructionProject>>([]);
  maxProjectsInList = 5;

  constructor(){
    // effect(() => {
    //   const delegateId = this.storage.getItem("cm-delegated-identity");

    //   if (delegateId) {      
     
    //     var projects = JSON.stringify([...this.recentProjectsList()]);        
    //     localStorage.setItem("rprojects-" + delegateId, projects);
    //   }
    // });
  }
  load() {
    //var rList = new Array<Project>();
    const delegateId = this.storage.getItem("cm-delegated-identity");

    if (delegateId) {
      const recentProjects = this.storage.getItem("rprojects-" + delegateId);
      var list = recentProjects ? JSON.parse(recentProjects) : [];
      this.recentProjectsList.set(list);

    } else {
      console.log("delegated id not found for storage retrieval (rpcload)");
    }

 
  }

  addRecentProject(project: ConstructionProject) { 
    if(project){
      this.recentProjectsList.update((recentProjects) => {
 

        var removedProjectList = [...recentProjects.filter(x => x.Id != project.Id)];      
        removedProjectList.unshift(project);         
  
        if(removedProjectList.length > this.maxProjectsInList){
          var removedItems =  removedProjectList.slice(this.maxProjectsInList);
          removedItems.forEach(x => {
            removedItems = [...removedProjectList.filter(z => z.Id != x.Id)];
          });
  
          return removedItems;
        }else{
          return removedProjectList;
        }
      
      });

      const delegateId = this.storage.getItem("cm-delegated-identity");

      if (delegateId) {
        var projects = JSON.stringify([...this.recentProjectsList()]);        
        localStorage.setItem("rprojects-" + delegateId, projects);
      }

      // console.log("Project added to recent project: " + project.Title);
    }else{
      console.log("project is null. cannot add it to recent projects list");
    }  
  }

  removeRecentProject(projectId: string) {
    this.recentProjectsList.update(recentProjects => recentProjects.filter(x => x.Id !== projectId));
  }
}