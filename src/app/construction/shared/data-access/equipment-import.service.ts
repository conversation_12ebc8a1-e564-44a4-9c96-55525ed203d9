import { Injectable, inject } from '@angular/core';
import { EquipmentGetherInfo, EquipmentImportRequest, EquipmentImportResponse, EquipmentImportSettings } from '../interfaces/equipment-import';
import { EquipmentInfo } from '../interfaces/equipment';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class EquipmentImportService {

  client = inject(HttpClient);

  public ImportEquipment(): Observable<EquipmentImportResponse> {
    return this.client.get<EquipmentImportResponse>(`${environment.services_root_endpoints.equipment_import}/import-data`);
  }

  public GetSampleEquipment(key: string, bucket:string, request: EquipmentImportRequest): Observable<EquipmentInfo> {
    return this.client.post<EquipmentInfo>(`${environment.services_root_endpoints.equipment_import}/sample-equipment?bucket=${bucket}&key=${key}`, request);
  }  

  public GatherEquipment(key:string, bucket:string, request: EquipmentImportRequest): Observable<EquipmentGetherInfo> {
    return this.client.post<EquipmentGetherInfo>(`${environment.services_root_endpoints.equipment_import}/gather-equipment?bucket=${bucket}&key=${key}`, request);
  }

  public SaveSettings(settings:EquipmentImportSettings): Observable<any> {
    return this.client.post<any>(`${environment.services_root_endpoints.equipment_import}/settings`,settings);
  }  

  public DeleteSettings(): Observable<any> {
    return this.client.delete<any>(`${environment.services_root_endpoints.equipment_import}/settings`);
  }  

  public GetSettings(): Observable<EquipmentImportSettings> {
    return this.client.get<EquipmentImportSettings>(`${environment.services_root_endpoints.equipment_import}/settings`);
  } 


  public GetImportedData(key:string, bucket:string, request: EquipmentImportRequest): Observable<EquipmentImportResponse>{
    return this.client.post<EquipmentImportResponse>(`${environment.services_root_endpoints.equipment_import}/gather?bucket=${bucket}&key=${key}`, request);
  }
}
