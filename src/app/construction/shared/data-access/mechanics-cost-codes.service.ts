
import { Injectable, signal, computed, inject, effect } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import {
  MechanicsCostCodeComponent,
  MechanicsCostCodesListResponse,
  CreateCostCodeRequest,
  UpdateCostCodeRequest,
  ImportCostCodesResponse
} from '../interfaces/mechanics';
import { rxResource } from '@angular/core/rxjs-interop';
import { firstValueFrom } from 'rxjs';

/**
 * Mechanics Cost Codes Service
 *
 * Provides comprehensive data management for mechanics cost codes following
 * the existing project cost codes pattern with modern Angular 19 architecture.
 *
 * Features:
 * - Signal-based reactive state management
 * - rxResource pattern for efficient data fetching
 * - Backend API integration matching CM.Mechanics.Serverless
 * - Computed signals for filtered and transformed data
 * - Loading state management per cost code
 */
@Injectable({
  providedIn: 'root'
})
export class MechanicsCostCodesService {
  private http = inject(HttpClient);
  private baseUrl = `${environment.services_root_endpoints.mechanics}/cost-codes`;

  // Signal-based state management
  private _costCodes = signal<MechanicsCostCodeComponent[]>([]);
  private _isLoading = signal(false);
  private _searchFilter = signal<string>('');
  private _refreshData = signal(true);

  // Public readonly signals
  costCodes = this._costCodes.asReadonly();
  isLoading = this._isLoading.asReadonly();

  filteredCostCodes = computed(() => {
    const filter = this._searchFilter().toLowerCase();
    if (!filter) return this._costCodes();

    return this._costCodes().filter(cc =>
      cc.CostCode.toLowerCase().includes(filter) ||
      cc.Description.toLowerCase().includes(filter)
    );
  });

  costCodesForDropdown = computed(() =>
    this._costCodes().map(cc => ({
      value: cc.CostCodeId,
      label: `${cc.CostCode} - ${cc.Description}`
    }))
  );

  // rxResource for data fetching
  costCodesResource = rxResource({
    request: () => ({ refreshData: this._refreshData() }),
    loader: () => this.fetchCostCodes()
  });

  constructor() {
    // Auto-load cost codes
    effect(() => {
      this._isLoading.set(this.costCodesResource.isLoading());
      const data = this.costCodesResource.value() as MechanicsCostCodesListResponse | null;
      if (data && data.Components && data.Components.length > 0) {
        // Extract cost codes from the nested Components structure
        const costCodesComponent = data.Components.find(c => c.ComponentIdentifier === 'CostCodes');
        if (costCodesComponent && costCodesComponent.CostCodes) {
          this._costCodes.set(costCodesComponent.CostCodes);
        } else {
          this._costCodes.set([]);
        }
      } else {
        this._costCodes.set([]);
      }
    });
  }

  // Public methods for loading state management
  setLoadingState(costCodeId: string, isLoading: boolean): void {
    this._costCodes.update(costCodes =>
      costCodes.map(cc =>
        cc.CostCodeId === costCodeId ? { ...cc, IsLoading: isLoading } : cc
      )
    );
  }
  
  reload(): void {
    // Use the resource's built-in reload functionality if available
    // Otherwise trigger refresh by toggling the refresh signal  
    if (this.costCodesResource.reload) {
      this.costCodesResource.reload();
    } else {
      this._refreshData.set(!this._refreshData());
    }
  }

  /**
   * Get a specific cost code by ID
   */
  async getCostCode(costCodeId: string): Promise<MechanicsCostCodeComponent> {
    try {
      const response = await firstValueFrom(
        this.http.get<{ComponentIdentifier: string, CostCodes: MechanicsCostCodeComponent[]}>(`${this.baseUrl}/${costCodeId}`)
      );

      // Extract the cost code from the response structure
      if (response && response.CostCodes && response.CostCodes.length > 0) {
        const costCode = response.CostCodes.find(cc => cc.CostCodeId === costCodeId);
        if (costCode) {
          return costCode;
        }
      }

      throw new Error(`Cost code with ID ${costCodeId} not found`);
    } catch (error) {
      console.error('Error fetching cost code:', error);
      throw error;
    }
  }

  /**
   * Set search filter
   */
  setSearchFilter(filter: string): void {
    this._searchFilter.set(filter);
  }





  /**
   * Create a new cost code
   */
  async createCostCode(costCode: CreateCostCodeRequest): Promise<MechanicsCostCodeComponent> {
    try {
      const response = await firstValueFrom(
        this.http.post<MechanicsCostCodeComponent>(`${this.baseUrl}`, costCode)
      );

      // Reload data from server to get the updated list
      this.reload();

      return response;
    } catch (error) {
      console.error('Error creating cost code:', error);
      throw error;
    }
  }

  /**
   * Update an existing cost code
   */
  async updateCostCode(costCode: UpdateCostCodeRequest & { CostCodeId: string }): Promise<MechanicsCostCodeComponent> {
    try {
      const response = await firstValueFrom(
        this.http.put<MechanicsCostCodeComponent>(`${this.baseUrl}/${costCode.CostCodeId}`, costCode)
      );

      // Reload data from server to get the updated list
      this.reload();

      return response;
    } catch (error) {
      console.error('Error updating cost code:', error);
      throw error;
    }
  }

  /**
   * Delete a cost code
   */
  async deleteCostCode(costCodeId: string): Promise<void> {
    try {
      await firstValueFrom(
        this.http.delete(`${this.baseUrl}/${costCodeId}`)
      );

      // Reload data from server to get the updated list
      this.reload();
    } catch (error) {
      console.error('Error deleting cost code:', error);
      throw error;
    }
  }

  /**
   * Import cost codes from uploaded file
   * Returns detailed import response with success/error feedback
   * Follows service-centric approach - all business logic in service
   */
  async importCostCodes(key: string, bucket: string): Promise<ImportCostCodesResponse> {
    try {
      const response = await firstValueFrom(
        this.http.get<ImportCostCodesResponse>(`${this.baseUrl}/import?key=${key}&bucket=${bucket}`)
      );

      // Reload data from server to get the updated list using existing signal pattern
      this.reload();

      return response;
    } catch (error) {
      console.error('Error importing cost codes:', error);
      throw error;
    }
  }

  // Private implementation methods
  private fetchCostCodes() {
    return this.http.get<MechanicsCostCodesListResponse>(this.baseUrl);
  }
}
