import { Injectable, inject, signal } from '@angular/core';
import { Observable, map } from 'rxjs';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';
import { FileInfoSB, FilePermissions, FilesComponentInfo, FolderSB, PresignedUrlDownloadRequest, PresignedUrlUploadRequest } from '../interfaces/project-files';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import { ProjectComponentsService } from './project-components.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessRequest } from 'src/app/models/access/access-request';

@Injectable({
  providedIn: 'root'
})
export class ConstructionFilesService {
  projectComponentService = inject(ProjectComponentsService); 
  http = inject(HttpClient);
  accessService = inject(AccessService);
  filePermissions = signal<FilePermissions | null>(null);

  getOrCreateFilesComponent(projectId: string): Observable<FilesComponentInfo> {
    return new Observable<FilesComponentInfo>(obs => {
      this.getFilesComponent(projectId).subscribe({
        next: (filesComponentInfo) => {
          if (filesComponentInfo) {
            obs.next(filesComponentInfo);
            obs.complete();
          } else {
            this.projectComponentService.addComponentToStoreById<FilesComponentInfo>(projectId, ProjectComponentIdentifiers.FILES, true).subscribe({
              next: (result) => {
                obs.next(result);
                obs.complete();
              },
              error: (err) => {
                obs.error(err);
              }
            });
          }
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }

  getFilesComponent(projectId: string): Observable<FilesComponentInfo> {
    return this.http.get<FilesComponentInfo>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.FILES}`);
  }

	getFilesServicePermissions(): Observable<FilePermissions> {
		return new Observable(obs => {
			var requests = new Array<AccessRequest>();

			requests.push({
				Action: 'downloadfile',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'uploadfile',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'deletefiile',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'addfolder',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'updatefolder',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'deletefolder',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'addfileinfolder',
				Resource: 'project-components-files'
			});

			requests.push({
				Action: 'updatefile',
				Resource: 'project-components-files'
			});

			this.accessService.GetAccessMulti(requests).subscribe({
				next: (result) => {
					var data = {
						downloadFilePermission: result[0].Access,
						uploadFilePermission: result[1].Access,
						deleteFilePermission: result[2].Access,
						addFolderPermission: result[3].Access,
						updateFolderPermission: result[4].Access,
						deleteFolderPermission: result[5].Access,
						moveToFolderPermission: result[6].Access,
						updateFilePermission: result[7].Access
					}

          this.filePermissions.set(data);

					obs.next(data);
					obs.complete();
				},
				error: (err) => {
					obs.error(err);
				}
			});
		});
	}

  generateUploadURL(projectId: string, key: string, contentType: string): Observable<any> {

		var request: PresignedUrlUploadRequest = {
			ContentType: contentType,
			Key: key
		}


		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/upload-file-url`, request);
	}

	generateDownloadUrl(projectId: string, key: string, contentType: string, fileName: string): Observable<any> {
		var request: PresignedUrlDownloadRequest = {
			Key: key,
			ContentType: contentType,
			FileName: fileName
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/download-file-url`, request);
	}


	uploadFileWithSignedURL(url: string, file: File): Observable<string> {
		// const headers = { 'Content-Type': file.type };\
		var headers = new HttpHeaders().set("x-auth", "false").set("Content-Type", file.type);

		return this.http.put(url, file, { headers, reportProgress: true, observe: 'events' }).pipe(
			map((event: any) => {
				if (!event) return;
				switch (event.type) {
					case HttpEventType.UploadProgress:
						return Math.round((event.loaded / event.total) * 100);
					case HttpEventType.Response:
						return event.body;
				}
			})
		);
	}

	getFiles(projectId: string): Observable<Array<FileInfoSB>> {
		// let request = {
		// 	ProjectId: projectId
		// }
		return this.http.get<Array<FileInfoSB>>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/get-files`);
	}

	addFile(projectId: string, fileInfo: FileInfoSB) {
		var request = {
			FileInfo: fileInfo
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/add-file`, request);
	}

	deleteFile(projectId: string, fileInfo: FileInfoSB) {
		var request = {
			FileId: fileInfo.FileId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/delete-file`, request);
	}

	addFolder(projectId: string, folder: FolderSB) {
		var request = {
			Folder: folder,
			ProjectId: projectId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/add-folder`, request);
	}


	addFileToFolder(projectId: string, folderId: string, fileId: string) {
		var request = {
			FolderId: folderId,
			FileId: fileId,
			ProjectId: projectId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/add-file-to-folder`, request);
	}

	deleteFileFromFolder(projectId: string, folderId: string, fileId: string) {
		var request = {
			FolderId: folderId,
			FileId: fileId,
			ProjectId: projectId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/delete-file-from-folder`, request);
	}

	addFilesToFolder(projectId: string, folderId: string, fileIds: Array<string>) {
		var request = {
			FolderId: folderId,
			FileIds: fileIds,
			ProjectId: projectId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/add-files-to-folder`, request);
	}

	deleteFilesToFolder(projectId: string, folderId: string, fileIds: Array<string>) {
		var request = {
			FolderId: folderId,
			FileIds: fileIds,
			ProjectId: projectId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/delete-files-from-folder`, request);
	}

	deleteFolder(projectId: string, folderId: string) {
		var request = {
			ProjectId: projectId,
			FolderId: folderId
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/delete-folder`, request);
	}

	deleteFolders(projectId: string, folderIds: Array<string>) {
		var request = {
			ProjectId: projectId,
			FolderIds: folderIds
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/delete-folders`, request);
	}

	updateFolder(projectId: string, folder: FolderSB) {
		var request = {
			ProjectId: projectId,
			Folder: folder
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/update-folder`, request);
	}

	updateFile(projectId: string, file: FileInfoSB) {


		var request = {
			FileInfo: file
		};

		return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Files/update-file`, request);
	}
}
