import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Employee, EmployeeImportInfo, EmployeeImportRequest, EmployeeImportResponse, ImportSettings } from '../interfaces/employee';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class EmployeeImportService {

  http = inject(HttpClient);
  getSampleEmployee(employeeImportRequest: EmployeeImportRequest) : Observable<Employee>{
    return this.http.patch<Employee>(`${environment.services_root_endpoints.employees_import}/wizard/sample-employee`, employeeImportRequest);
  }

  import(employeeImportRequest: EmployeeImportRequest) : Observable<EmployeeImportResponse>{
      return this.http.patch<EmployeeImportResponse>(`${environment.services_root_endpoints.employees_import}/wizard/import`, employeeImportRequest);
  }

  gatherEmployees(employeeImportRequest: EmployeeImportRequest): Observable<EmployeeImportInfo>{
      return this.http.patch<EmployeeImportInfo>(`${environment.services_root_endpoints.employees_import}/wizard/gather`, employeeImportRequest);
  }

  getSettings(): Observable<ImportSettings>{
      return this.http.get<ImportSettings>(`${environment.services_root_endpoints.employees_import}/settings`);
  }

  saveSettings(importSettings: ImportSettings): Observable<any>{
      return this.http.post(`${environment.services_root_endpoints.employees_import}/settings`, importSettings);
  }

  deleteSettings(): Observable<any>{
      return this.http.delete(`${environment.services_root_endpoints.employees_import}/settings`);
  }
}
