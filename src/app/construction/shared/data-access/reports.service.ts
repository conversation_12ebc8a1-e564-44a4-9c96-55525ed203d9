import { HttpClient } from '@angular/common/http';
import { Injectable, inject, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AccessRequest, AccessResponse } from '../../../shared/interfaces/access';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { ReportPermissions } from '../interfaces/report';

@Injectable({
  providedIn: 'root'
})
export class ConstructionReportsService {
  accessService = inject(AccessService);
  http = inject(HttpClient);
  reportPermissions = signal<ReportPermissions | null>(null);
  
  RunCostCodeQuantityReport(filterOptions: any): Observable<any> {

    var reportInfo = {
      "ReportRequestData": JSON.stringify(filterOptions),
      "ReportSQSUrl": environment.reports.project.costcodeqty_report.sqs_url,
      "ReportName": environment.reports.project.costcodeqty_report.name
    };   

    return this.http.post(`${environment.services_root_endpoints.reports.service}/reports-service`, reportInfo);
      
  }

  RunCostCodeQtyReportUrl(filterOptions: any){
    return this.http.post("https://azzmvr34qagdgtbp6byucig7640uokhl.lambda-url.us-east-1.on.aws/", filterOptions);
  }

  GetReportInfo(reportId:string): Observable<any>{
    return this.http.get(`${environment.services_root_endpoints.reports.service}/reports-service/${reportId}`);
  }

  RunDailyLogReport(filterOptions: any): Observable<any>{

    var reportInfo = {
      "ReportRequestData": JSON.stringify(filterOptions),
      "ReportSQSUrl": environment.reports.shared.dailylog_report.sqs_url,
      "ReportName": environment.reports.shared.dailylog_report.name
    }
    return this.http.post(`${environment.services_root_endpoints.reports.service}/reports-service`, reportInfo);

  }

  
  RunTimeCardReport(startDate: Date, endDate: Date): Observable<any> {
    
    var data = {
      StartDate: startDate,
      EndDate: endDate
    }

    var reportInfo = {
      "ReportRequestData": JSON.stringify(data),
      "ReportSQSUrl": environment.reports.global.timecard_report.sqs_url,
      "ReportName": environment.reports.global.timecard_report.name
    }
    return this.http.post(`${environment.services_root_endpoints.reports.service}/reports-service`, reportInfo);    
  }

  RunProjectListReport(filterOptions: any): Observable<any>{

    var reportInfo = {
      "ReportRequestData": JSON.stringify(filterOptions),
      "ReportSQSUrl": environment.reports.global.project_list.sqs_url,
      "ReportName": environment.reports.global.project_list.name
    }
    return this.http.post(`${environment.services_root_endpoints.reports.service}/reports-service`, reportInfo);

  }

  RunUserRolesReport(filterOptions: any): Observable<any>{

    var reportInfo = {
      "ReportRequestData": JSON.stringify(filterOptions),
      "ReportSQSUrl": environment.reports.global.user_roles.sqs_url,
      "ReportName": environment.reports.global.user_roles.name
    }
    return this.http.post(`${environment.services_root_endpoints.reports.service}/reports-service`, reportInfo);

  }

  getReportPermissions(): Observable<any>{
    if(this.reportPermissions()){
      return of(this.reportPermissions());
    }else{
      return new Observable<any>(observer => {
        //"report-costcodeqty", "runreport" (resource, action)
        var requests = new Array<AccessRequest>();
  
        requests.push({
          Action: 'runreport',
          Resource: 'report-costcodeqty'
        });

        requests.push({
          Action: 'runreportproject',
          Resource: 'report-dailylog-global'
        });

        requests.push({
          Action: 'runreport',
          Resource: 'report-projectlist'
        });

        requests.push({
          Action: 'runreport',
          Resource: 'report-roles'
        });

        requests.push({
          Action: 'runreport',
          Resource: 'report-payroll'
        });

        requests.push({
          Action: 'runreportproject',
          Resource: 'report-dailylog-project'
        });

        this.accessService.GetAccessMulti(requests).subscribe({
          next: (result: AccessResponse[]) => {
            const permission = {
              reportCostCodeQty: result[0]?.Access,
              reportDailyLogGlobal: result[1]?.Access,
              reportProjectList: result[2]?.Access,
              reportRoles: result[3]?.Access, 
              reportPayroll: result[4]?.Access,
              reportDailyLogProject: result[5]?.Access,
            } as ReportPermissions;
  
            this.reportPermissions.set(permission);
  
            observer.next(permission);
            observer.complete();
          },
          error: (error: any) => {
            console.log(error);
            observer.error(error);
          }
        });
      });
    }

  }
  // public GetProjectListReport(projectFitlerTypes: Array<string>, reportProjectLists: Array<ReportInfoFilter>): Observable<any>{

  //   return this.http.post(`${environment.services_root_endpoints.reports.projectlist}/project-list`, reportRequest);
  // }

}
