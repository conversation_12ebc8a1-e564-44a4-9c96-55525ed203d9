import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, inject, signal } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';


@Injectable({
  providedIn: 'root'
})
export class ConstructionGlobalService {
  
  projectDefaultComponents = signal<any | null>(null);
  http = inject(HttpClient);

  getDefaultComponent<T>(componentId: ProjectComponentIdentifiers): Observable<T>{
    return new Observable<T>((obs) => {
      this.getProjectDefaultComponents().subscribe({
        next: (result) => {
          if(result){            
            var component = result.Components.find((x: T & { ComponentIdentifier: string }) => x.ComponentIdentifier === componentId) as T | undefined;
            if(component){
              obs.next(component);
              obs.complete();
            }else{
              obs.error(`Component ${componentId} not found`);
            }
          }else{
            obs.error('No components found');
          }
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
      

  }

  getProjectDefaultComponents(){
    if(this.projectDefaultComponents()){
      return of(this.projectDefaultComponents());
    }else{
      const headers = new HttpHeaders({
        'ContentType': 'application/json',
        'cache-control': 'no-cache'
      });
  
      return this.http.get<any>('./assets/data/user-project-default-settings.json', { headers });
    }

  }


}

