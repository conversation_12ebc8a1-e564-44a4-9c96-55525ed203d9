import { Injectable, Signal, computed, signal } from '@angular/core';

// Forward declaration to avoid circular dependency
interface MechanicsDailyLogServiceInterface {
  isPersistingSignal(): boolean;
}

/**
 * Service responsible for tracking and exposing dirty state for mechanics daily log
 * per date and per child component.
 * 
 * Follows the exact same pattern as DailyLogDirtyService but adapted for mechanics diary.
 */
@Injectable({
  providedIn: 'root'
})
export class MechanicsDirtyService {
  /**
   * Map to track dirty state
   * First key = dateString (YYYY-MM-DD format)
   * Second key = component ID like 'notes', 'photos', 'timecard'
   */
  public readonly dirtyMap = signal<Map<string, Map<string, boolean>>>(new Map());

  /**
   * Registry to track MechanicsDailyLogService instances by date
   * Used to check persisting state across all logs
   */
  private readonly serviceRegistry = signal<Map<string, MechanicsDailyLogServiceInterface>>(new Map());

  constructor() {
  }

  /**
   * Registers a MechanicsDailyLogService instance for a specific date
   * @param dateString The date string (YYYY-MM-DD)
   * @param service The MechanicsDailyLogService instance
   */
  registerService(dateString: string, service: MechanicsDailyLogServiceInterface): void {
    this.serviceRegistry.update(registry => {
      const newRegistry = new Map(registry);
      newRegistry.set(dateString, service);
      return newRegistry;
    });
  }

  /**
   * Unregisters a MechanicsDailyLogService instance for a specific date
   * @param dateString The date string (YYYY-MM-DD)
   */
  unregisterService(dateString: string): void {
    this.serviceRegistry.update(registry => {
      const newRegistry = new Map(registry);
      newRegistry.delete(dateString);
      return newRegistry;
    });
  }

  /**
   * Returns a signal indicating whether any registered service is persisting
   */
  anyPersisting(): Signal<boolean> {
    return computed(() => {
      const registry = this.serviceRegistry();
      for (const service of registry.values()) {
        if (service.isPersistingSignal()) {
          return true;
        }
      }
      return false;
    });
  }

  /**
   * Sets the dirty state for a specific component for a date
   * @param dateString The date string (YYYY-MM-DD)
   * @param componentId The component ID (e.g., 'Notes', 'Photos', 'TimeCard')
   * @param isDirty Whether the component is dirty
   */
  setDirty(dateString: string, componentId: string, isDirty: boolean): void {
    this.dirtyMap.update(map => {
      // Create a new Map to ensure reactivity
      const newMap = new Map(map);
      
      // Get or create the date map
      let dateMap = newMap.get(dateString);
      if (!dateMap) {
        dateMap = new Map<string, boolean>();
        newMap.set(dateString, dateMap);
      } else {
        // Create a new Map for the date to ensure reactivity
        dateMap = new Map(dateMap);
        newMap.set(dateString, dateMap);
      }
      
      // Set the dirty state for the component
      dateMap.set(componentId, isDirty);
      
      return newMap;
    });
  }

  /**
   * Returns a signal indicating whether any component for a specific date is dirty
   * @param dateString The date string (YYYY-MM-DD)
   * @returns Signal<boolean>
   */
  anyDirty(dateString: string): Signal<boolean> {
    return computed(() => {
      const map = this.dirtyMap();
      const dateMap = map.get(dateString);
      if (!dateMap) return false;
      
      for (const [, isDirty] of dateMap.entries()) {
        if (isDirty) return true;
      }
      return false;
    });
  }

  /**
   * Returns a signal indicating whether a specific component for a date is dirty
   * @param dateString The date string (YYYY-MM-DD)
   * @param componentId The component ID
   * @returns Signal<boolean>
   */
  isComponentDirty(dateString: string, componentId: string): Signal<boolean> {
    return computed(() => {
      const map = this.dirtyMap();
      const dateMap = map.get(dateString);
      return dateMap?.get(componentId) ?? false;
    });
  }

  /**
   * Returns a signal indicating whether any date has dirty components
   * @returns Signal<boolean>
   */
  anyDateDirty(): Signal<boolean> {
    return computed(() => {
      const map = this.dirtyMap();
      for (const [, dateMap] of map.entries()) {
        for (const [, isDirty] of dateMap.entries()) {
          if (isDirty) return true;
        }
      }
      return false;
    });
  }

  /**
   * Clears all dirty state for a specific date
   * @param dateString The date string (YYYY-MM-DD)
   */
  clearDirty(dateString: string): void {
    this.dirtyMap.update(map => {
      const newMap = new Map(map);
      newMap.delete(dateString);
      return newMap;
    });
  }

  /**
   * Clears dirty state for a specific component for a date
   * @param dateString The date string (YYYY-MM-DD)
   * @param componentId The component ID
   */
  clearComponentDirty(dateString: string, componentId: string): void {
    this.setDirty(dateString, componentId, false);
  }
}