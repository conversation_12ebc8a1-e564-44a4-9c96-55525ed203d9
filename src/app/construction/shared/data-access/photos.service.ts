import { Injectable, inject, signal, computed } from '@angular/core';
import { Photo, PhotoInfo, PhotoProcessorInfo, PhotoProcessorStatus, PhotoStorage, PhotoUserInfo } from '../interfaces/photos';
import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import Pica from 'pica';
import { Observable, catchError, concat, forkJoin, map, mergeMap, of, switchMap, take, tap, toArray } from 'rxjs';
import { CivCastPicaService } from '../../utils/pica/pica.service';
import { CivCastPicaErrorType } from '../../utils/pica/pica.error.interface';
import { v4 } from 'uuid';
import { environment } from 'src/environments/environment';
import { PresignedUrlDownloadRequest, PresignedUrlUploadRequest } from '../interfaces/project-files';
import { ProjectComponentIdentifiers } from '../interfaces/project-components';
import { ToastrService } from 'ngx-toastr';
import { error } from 'console';
window.pica.prototype.debug = console.log.bind(console);
@Injectable()
export class ConstructionPhotosService {

  picaService = inject(CivCastPicaService);
  toastService = inject(ToastrService);
  picaResizer = new Pica();
  http = inject(HttpClient);  
  projectId = signal<string | null>(null);
  // Project-specific maps for photos and upload state
  projectPhotosToAdd = signal<Array<PhotoProcessorInfo>>([]);
  projectPhotosToRemove = signal<Array<string>>([]);
  isUploading = signal<boolean>(false);
  photosCompletedUpload = signal<Array<PhotoProcessorInfo>>([]);
  photosTotalProgress = signal<Map<string, number>>(new Map<string, number>());
  totalProgress = computed(() => {
    let totalProgress = 0;
    if(this.projectPhotosToAdd().length > 0 ){
      for(let progressItem of this.photosTotalProgress().values()){
        totalProgress += (progressItem / this.projectPhotosToAdd().length) / 2;
     }
    }

    return Math.floor(totalProgress);
    
  });

  constructor() {

  }

  generateUploadURL(key: string, contentType: string): Observable<any> {

		var request: PresignedUrlUploadRequest = {
			ContentType: contentType,
			Key: key
		}

		const projectId = this.projectId();

		/**
		 * CURRENT APPROACH: Magic String Routing (Pragmatic Solution)
		 *
		 * This service handles both project and mechanics photo uploads by detecting
		 * a 'mechanics-' prefix in the projectId parameter. While this works and
		 * provides code reuse, it's not ideal architecture.
		 *
		 * HOW IT WORKS:
		 * - Project photos: projectId = actual project ID (e.g., "proj123")
		 * - Mechanics photos: projectId = "mechanics-{userId}-{date}" (fake ID for routing)
		 *
		 * PROS:
		 * - Code reuse (same photo service for both contexts)
		 * - Consistent UX across project and mechanics
		 * - No breaking changes to existing project functionality
		 *
		 * CONS:
		 * - Magic string dependency ('mechanics-' prefix)
		 * - Tight coupling between service and context detection
		 * - projectId parameter that's not always a project ID
		 * - Hard to extend for additional photo contexts
		 *
		 * FUTURE REFACTOR RECOMMENDATION: Strategy Pattern
		 *
		 * interface PhotoUploadStrategy {
		 *   getUploadUrl(key: string, contentType: string): Observable<any>;
		 * }
		 *
		 * class ProjectPhotosStrategy implements PhotoUploadStrategy {
		 *   getUploadUrl(key: string, contentType: string): Observable<any> {
		 *     return this.http.patch(`${env.project_components}/...`);
		 *   }
		 * }
		 *
		 * class MechanicsPhotosStrategy implements PhotoUploadStrategy {
		 *   getUploadUrl(key: string, contentType: string): Observable<any> {
		 *     return this.http.patch(`${env.mechanics}/photos/upload-url`);
		 *   }
		 * }
		 *
		 * class PhotosService {
		 *   constructor(private strategy: PhotoUploadStrategy) {}
		 *   generateUploadURL(key: string, contentType: string): Observable<any> {
		 *     return this.strategy.getUploadUrl(key, contentType);
		 *   }
		 * }
		 *
		 * This would provide:
		 * - Clean separation of concerns
		 * - Easy unit testing
		 * - Extensibility for new photo contexts
		 * - Explicit dependencies
		 */

		// Route to mechanics API if this is a mechanics photo upload
		if (projectId && projectId.startsWith('mechanics-')) {
			return this.http.patch<any>(`${environment.services_root_endpoints.mechanics}/photos/upload-url`, request);
		} else {
			// Use existing project components API for project photos
			return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Photos/upload-file-url`, request);
		}
	}

	generateDownloadUrl(key: string, contentType: string, fileName: string): Observable<any> {
		var request: PresignedUrlDownloadRequest = {
			Key: key,
			ContentType: contentType,
			FileName: fileName
		};

		const projectId = this.projectId();

		/**
		 * NOTE: Same magic string routing approach as generateUploadURL() above.
		 * See detailed explanation and refactoring recommendations in generateUploadURL().
		 */

		// Route to mechanics API if this is a mechanics photo download
		if (projectId && projectId.startsWith('mechanics-')) {
			return this.http.patch<any>(`${environment.services_root_endpoints.mechanics}/photos/download-url`, request);
		} else {
			// Use existing project components API for project photos
			return this.http.patch<any>(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/Photos/download-file-url`, request);
		}
	}

  uploadFileWithSignedURL(url: string, file: File): Observable<string> {
		// const headers = { 'Content-Type': file.type };\
		var headers = new HttpHeaders().set("x-auth", "false").set("Content-Type", file.type);

		return this.http.put(url, file, { headers, reportProgress: true, observe: 'events' }).pipe(
			map((event: any) => {
				if (!event) return;
				switch (event.type) {
					case HttpEventType.UploadProgress:
						return Math.round((event.loaded / event.total) * 100);
					case HttpEventType.Response:
						return event.body;
				}
			})
		);
	}

  
  getPhotosFromDailyLogs() : Observable<Array<PhotoUserInfo>>{
    return this.http.get<Array<PhotoUserInfo>>(`${environment.services_root_endpoints.project_user_components}/project-components/project-user/${this.projectId()}/component/Photos/get-dailylog-photos`);
  }


  addPhotosToUpload(photo: PhotoProcessorInfo, projectId: string = ''){
    this.projectPhotosToAdd.update(photos => [...photos, photo]);
  }

  addPhotoToRemove(photoId: string, projectId: string = ''){
    this.projectPhotosToRemove.update(photos => [...photos, photoId]);
  }

  removePhotoFromRemove(photoId: string, projectId: string = ''){
    this.projectPhotosToRemove.update(photos => photos.filter(id => id !== photoId));
  }

  clear(){
    this.projectPhotosToAdd.set([]);
    this.projectPhotosToRemove.set([]);
    this.photosTotalProgress.set(new Map<string, number>());
  }

  processPhotos(componentId:string): Observable<Array<Photo>> {
    return this.processUploadedPhotos(componentId);
  }

  /**
   * This internal method will process the photos that are in the project's photos map.
   */
  private processUploadedPhotos(componentId:string): Observable<Array<Photo>> {
    return new Observable<Array<Photo>>((obs) => {
      // const projectPhotos = this.projectPhotosMap().get(projectId) || [];
      this.isUploading.set(true);


        
        let resize = new Array<Observable<PhotoResizeResponse>>();

        for (let photo of this.projectPhotosToAdd()) {
          const resizeThumbnail = this.resizeImagePica(photo, 1280, 720).pipe(tap((result) => {
            console.log("Resized Image: " + result.Url);
            var processedPhoto = this.projectPhotosToAdd().find(x => x.Id === result.PhotoInfoId);
            if (processedPhoto) {
              processedPhoto.ThumbPhotoInfo.Url = result.Url;
            }
  
          }));
  
          resize.push(resizeThumbnail);
  
          const resizeLarge = this.resizeImagePica(photo, 1920, 1080).pipe(tap((result) => {
            console.log("Resized Image: " + result.Url);
            var processedPhoto = this.projectPhotosToAdd().find(x => x.Id === result.PhotoInfoId);
            if (processedPhoto) {
              processedPhoto.LargePhotoInfo.Url = result.Url;
            }
  
          }));
  
          resize.push(resizeLarge);
        }
  
        concat(...resize).pipe(toArray()).subscribe({
          next: (photoResizeResponses: Array<PhotoResizeResponse>) => {
            console.log(photoResizeResponses);
            let finalPhotos = new Array<Photo>();
            let upload = new Array<Observable<Photo>>();
  
            for (let uploadPhoto of this.projectPhotosToAdd()) {
              const uploadPhotoObs = this.uploadAndCreatePhotoProcessor(uploadPhoto, componentId).pipe(
                tap((photo) => {
                  console.log("Uploaded Photo: " + photo.ThumbnailStorage.Location);
                })
              );  
              
              upload.push(uploadPhotoObs);           
            }
  
            concat(...upload).pipe(toArray()).subscribe({
              next: (photos: Array<Photo>) => {
                this.isUploading.set(false);
                obs.next(photos);
                obs.complete();
              },
              error: (error) => {    
                this.isUploading.set(false);
                obs.error(error);
              }
            });
          }, error: (err) => {
            console.log(err);            
            this.toastService.info(`
              There was an error with uploading the photos. 
              This is an issue with the browser blocking features we need to upload the photos. 
              Please try again or use a different browser. You can also, remove photos for now and upload them later on a different browser.`);
            this.isUploading.set(false);
            obs.error(err);
          }
       });
      
    });
  }

  uploadAndCreatePhotoProcessor(
    photoProcessorInfo: PhotoProcessorInfo,
    componentId: string): Observable<Photo> {
      return new Observable<Photo>((obs) => {

        if(photoProcessorInfo.ThumbPhotoInfo.Url && photoProcessorInfo.LargePhotoInfo.Url && componentId && photoProcessorInfo.Name && photoProcessorInfo.ContentType){
          var thumbFileUrlToBlob = this.http.get(photoProcessorInfo.ThumbPhotoInfo.Url, { responseType: 'blob' });
          var largeFileUrlToBlob = this.http.get(photoProcessorInfo.LargePhotoInfo.Url, { responseType: 'blob' });
            
          forkJoin({thumb: thumbFileUrlToBlob, large: largeFileUrlToBlob}).subscribe({
            next: (results) => {
              // Generate S3 keys based on whether this is mechanics or project photos
              const projectId = this.projectId();
              let thumbkey: string;
              let largeKey: string;

              if (projectId && projectId.startsWith('mechanics-')) {
                // For mechanics: public/mechanics/{userId}/{dateString}/Photos/thumbnail/{filename}
                // Extract userId and dateString from mechanics-{userId}-{dateString}
                const parts = projectId.replace('mechanics-', '').split('-');
                const userId = parts[0];
                const dateString = parts.slice(1).join('-'); // Handle dates with dashes
                thumbkey = `public/mechanics/${userId}/${dateString}/Photos/thumbnail/${photoProcessorInfo.Name}`;
                largeKey = `public/mechanics/${userId}/${dateString}/Photos/large/${photoProcessorInfo.Name}`;
              } else {
                // For projects: public/{projectId}/{componentId}/thumbnail|large/{filename}
                thumbkey = `public/${projectId}/${componentId}/thumbnail/${photoProcessorInfo.Name}`;
                largeKey = `public/${projectId}/${componentId}/large/${photoProcessorInfo.Name}`;
              }

              let thumbFile = new File([results.thumb], photoProcessorInfo.Name, { type: results.thumb.type});
              const uploadThumb = this.uploadFile(thumbkey, thumbFile, photoProcessorInfo.ThumbPhotoInfo).pipe(
                tap((result) => {
                  console.log("Thumb Uploaded: " + result.Key);
                }),
                catchError((error) => of(obs.error(error)))
              );

              let largeFile = new File([results.large], photoProcessorInfo.Name, { type: results.thumb.type});
              const uploadLarge = this.uploadFile(largeKey, largeFile, photoProcessorInfo.LargePhotoInfo).pipe(
                tap((result) => {
                  console.log("Large Uploaded: " + result.Key);
                }),
                catchError((error) => of(obs.error(error)))
              );
    
              forkJoin({thumb: uploadThumb, large: uploadLarge}).subscribe({
                next: (results) => {
                  var photo = {} as Photo;
                  photo.FileName = photoProcessorInfo.Name;
                  photo.PhotoId = v4();
                  photo.PhotoSize = largeFile.size;
                  photo.PhotoType = photoProcessorInfo.ContentType;
                  photo.Width = 0; // TODO: Extract from image metadata
                  photo.Height = 0; // TODO: Extract from image metadata
                  photo.ThumbnailStorage = {} as PhotoStorage;
                  photo.ThumbnailStorage.Bucket = results.thumb?.Bucket as string;
                  photo.ThumbnailStorage.Key = results.thumb?.Key as string;
                  photo.ThumbnailStorage.Location = `https://${results.thumb?.Bucket}.s3.amazonaws.com/${results.thumb?.Key}`;
                  photo.ThumbnailStorage.ETag = '';
                  photo.LargeStorage = {} as PhotoStorage;
                  photo.LargeStorage.Bucket = results.large?.Bucket as string;
                  photo.LargeStorage.Key = results.large?.Key as string;
                  photo.LargeStorage.Location = `https://${results.large?.Bucket}.s3.amazonaws.com/${results.large?.Key}`;
                  photo.LargeStorage.ETag = '';
                  var date = new Date();
                  var now_utc = Date.UTC(date.getUTCFullYear(), date.getUTCMonth(),
                                  date.getUTCDate(), date.getUTCHours(),
                                  date.getUTCMinutes(), date.getUTCSeconds());
                  photo.UploadDate = new Date(now_utc);
                  photo.LastModifiedDate = new Date(now_utc);
                  photo.Title = '';
                  photo.Description = '';

                  this.photosCompletedUpload.update(photos => [...photos, photoProcessorInfo]);

                  obs.next(photo);
                  obs.complete();
                },
                error: (error) => {
                  obs.error(error);
                }
              });
            },
            error: (error) => {
              obs.error(error);
            }
          });
        }else{
          obs.error("Missing Parameters");
        }
    });
    }

  private uploadFile(key:string, image: File, photoProcessorInfo: PhotoInfo): Observable<PhotoInfo> {
    return new Observable<PhotoInfo>((obs) => {
      
      this.generateUploadURL(key, image.type).subscribe((result) => {
        this.uploadFileWithSignedURL(result.PresignedUrl, image).subscribe(
          {
            next: (event) => {
              if (event && typeof event === 'number') {
                photoProcessorInfo.IsUploading = true;
                photoProcessorInfo.Progress = +event as number;
                
                this.photosTotalProgress.update((cProgress) => {

                    const newProgress = new Map(cProgress);
                    if (newProgress.has(key)) {
                    newProgress.set(key, photoProcessorInfo.Progress);
                    } else {
                    newProgress.set(key, photoProcessorInfo.Progress);
                    }
                    return newProgress;
                });
                
              } else if (photoProcessorInfo.Progress === 100 && !event) {
                photoProcessorInfo.IsUploading = false
                photoProcessorInfo.Key = key;
                photoProcessorInfo.Bucket = result.Bucket;
                
    
                obs.next(photoProcessorInfo);
                obs.complete();
              }
            }, error: (err) => {
              obs.error(err);
              photoProcessorInfo.IsUploading = false;
              photoProcessorInfo.Status = PhotoProcessorStatus.Error;              
            }
          })
      });
    });
  }

  private getUploadUrl(projectId: string, componentId: string, fileName:string, contentType: string): Observable<PhotoUploadResponse>{
    let queryParams = `file-name=${fileName}&content-type=${encodeURIComponent(contentType)}&component-identifier=${componentId}`;
    var uri = encodeURI(`${environment.services_root_endpoints.project_components}/project-components/project/${projectId}/component/${ProjectComponentIdentifiers.DAILY_LOG}/get-presigned-upload-url?${queryParams}`);
    return this.http.get<PhotoUploadResponse>(uri);
  }


  private compressImageRecursion(file: File, sizeInMB: number) {

    return new Observable<string>((obs) => {
      this.picaService.compressImage(file, sizeInMB, true).pipe(
        mergeMap((result) => {
          var url = URL.createObjectURL(result);
          return of(url);
        }),
        catchError((error) => {
          if (error == CivCastPicaErrorType.NOT_BE_ABLE_TO_COMPRESS_ENOUGH) {
            return this.compressImageRecursion(file, sizeInMB + 0.2);
          } else {
            return of('');
          }
        })
      ).subscribe({
        next: (result) => {
          obs.next(result);
          obs.complete();
        },
        error: (error) => {
          console.log(error);
          obs.error(error);
        }
      })
    });
  } 


  compressImagePica(url: string, name: string, sizeInMB: number): Observable<string> {
    return new Observable<string>((obs) => {
      this.http.get(url, { responseType: 'blob' }).subscribe({
        next: (result) => {
          var file = new File([result], name as string, { type: result.type });
          this.compressImageRecursion(file, sizeInMB).subscribe({
            next: (result) => {
              obs.next(result);
              obs.complete();
            },
            error: (error) => {
              console.log(error);
            }
          });
        },
        error: (error) => {
          console.log(error);
          obs.error(error);
        }
      });

    });

  }

  resizeImagePica(info: PhotoProcessorInfo, width: number, height: number): Observable<PhotoResizeResponse> {
    return new Observable<PhotoResizeResponse>((obs) => {
      this.http.get(info.FileUrl, { responseType: 'blob' }).subscribe({
        next: (result) => {
          var file = new File([result], info.Name, { type: result.type });

          this.picaService.resizeImage(file, width, height, false, { aspectRatio: { keepAspectRatio: true } }).subscribe({
            next: (result) => {

              obs.next({ Url: URL.createObjectURL(result), PhotoInfoId: info.Id });
              obs.complete();
            },
            error: (error) => {
              console.log(error);
              this.toastService.error("There was an error resizing the image.");            
              obs.error(error);
            }
          });

        },
        error: (error) => {
          console.log(error);
          obs.error(error);
        }
      });

    });

  }
}


export interface PhotoUploadInfo {
  PhotoInfo: PhotoProcessorInfo;

}

export interface PhotoResizeResponse {
  PhotoInfoId: string;
  Url: string;
}

export interface PhotoUploadResponse {
  PresignedUrl: string;
  Key: string;
  Bucket:string;
}
