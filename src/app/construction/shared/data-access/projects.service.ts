import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { environment } from 'src/environments/environment';
import { ConstructionProject, ConstructionProjectResponse, FilterOptions, ProjectsView, ProjectUpdateOptions } from '../interfaces/construction-project';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { switchMap, finalize, tap, Observable, of, throwError } from 'rxjs';
import { ProjectComponentsService } from './project-components.service';
import { rxResource } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root'
})
export class ConstructionProjectsService {
  http = inject(HttpClient)
  private projectComponentService = inject(ProjectComponentsService);
  
  showHelpVideo = computed(() => !this.deactivated() && !this.isFavorites() && !this.isLoading() && (!this.title() || !this.internalId) && this.projects().length <= 0);
  projectsEndpoint = `${environment.services_root_endpoints.projects}/projects`; 
  projectsTotal = computed(() => this.projectsResource.value()?.Total || 0);
  loadProjects = signal<boolean | null>(null);
  projects = computed(() => this.projectsResource.value()?.Projects || []);
  filteredProjects = signal<ConstructionProject[]>([]);
  title = signal<string | null>(null);
  internalId = signal<string | null>(null);
  deactivated = signal<boolean>(false);
  currentPage = signal<number>(1);
  orderBy = signal<string>('Title');
  isReversed = signal<boolean>(false);
  limit = signal<number>(50);
  isFavorites = signal<boolean>(false);
  includeComponents = signal<string | null>(null);
  initialize = signal<boolean>(false);  
  projectType = signal<string>('default');
  isLoading = computed(() => this.projectsResource.isLoading());
  // teamProjects = computed(() => this.filteredProjects());
  reopeningProjects = signal<Array<string>>([]);  
  currentView = signal<ProjectsView>(ProjectsView.LIST);
  isProjectActiveStatusChanging = computed(() => this.reopeningProjects());   
  allowedComponents = "info";


  projectsResource = rxResource({
    request: () => ({
      title: this.title(),
      internalId: this.internalId(),
      deactivated: this.deactivated(),
      currentPage: this.currentPage(),
      orderBy: this.orderBy(),
      isReversed: this.isReversed(),
      includeComponents: this.includeComponents(),
      limit: this.limit(),
      initialize: this.initialize(),
      projectType: this.projectType(),
      isFavorites: this.isFavorites()
    }),
    loader: (request) => {
      if(this.initialize()){
        var url = `${environment.services_root_endpoints.projects}/projects`;
        let queryParams = new HttpParams(); // Initialize HttpParams
  
        if (request.request.title) {
          queryParams = queryParams.set('title', request.request.title);
        }
        if (request.request.internalId) {
          queryParams = queryParams.set('internalId', request.request.internalId);
        }
   
          
        queryParams = queryParams.set('deactivated', request.request.deactivated.toString());
        
        if (request.request.currentPage) {
          queryParams = queryParams.set('page', request.request.currentPage.toString());
        }
        if (request.request.limit) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }
  
        if (request.request.orderBy) {
          queryParams = queryParams.set('orderBy', request.request.orderBy);
        }
  
        if (request.request.isReversed) {
          queryParams = queryParams.set('isReversed', request.request.isReversed.toString());
        }

        if (request.request.includeComponents) {
          queryParams = queryParams.set('includeComponents', request.request.includeComponents);
        }

        if (request.request.projectType) {
          queryParams = queryParams.set('projectType', request.request.projectType);
        }

        if (request.request.isFavorites) {
          queryParams = queryParams.set('isFavorites', request.request.isFavorites.toString());
        }
  
        return this.http.get<ConstructionProjectResponse>(url, { params: queryParams });
      }

      return of(null);    
    }
  });


  resetDefaultValues() {
    this.title.set('');
    this.internalId.set('');
    this.deactivated.set(false);
    this.currentPage.set(1);
    this.orderBy.set('Title');
    this.isReversed.set(false);
    this.limit.set(250);
    this.isFavorites.set(false);
    this.includeComponents.set(null);    
  }
  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred.';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }  

  setProjectStatus(projectId:string, status: boolean) {
    var request = {
      ActiveStatus: status
    };
    
    this.reopeningProjects.update((current) => {
      return [...current, projectId]
    });


    setTimeout(() => {
          this.http.patch<ConstructionProject>(`${this.projectsEndpoint}/${projectId}/update-project-status`, request).subscribe({
      next: (result) => {
          this.projectsResource.update((currentResponse) => {
          if (!currentResponse) return null;
          const updatedProjects = currentResponse.Projects.map(project => {
            if (project.Id === projectId) {
              return { ...project, Deactivated: status };
            }
            return project;
          });

          return { ...currentResponse, Projects: updatedProjects };          
        });

        this.reopeningProjects.update((current) => {
          return current.filter(id => id !== projectId);
        });
      },
      error: (err) => {
        this.handleError(err);
      }
    });  
    }, 3000);
  
  }  

  isProjectStatusChanging(projectId: string): boolean {
    return this.reopeningProjects().includes(projectId);
  }

  

  getProjectsByIdSet(projectIds: Array<string>): Observable<Array<ConstructionProject>>{
    var request = {
      data: projectIds
    }
     
    return this.http.post<Array<ConstructionProject>>(`${environment.services_root_endpoints.projects}/projects?method=get-by-ids`, request);
  }

  updateProject(projectId:string, projectUpdateOptions: Partial<ProjectUpdateOptions>) {      
     return this.http.patch<ConstructionProject>(`${this.projectsEndpoint}/${projectId}`, projectUpdateOptions);
  }
}

