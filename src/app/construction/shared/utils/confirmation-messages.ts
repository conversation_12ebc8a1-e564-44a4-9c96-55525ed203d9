/**
 * Centralized confirmation messages for the application
 * This file contains all confirmation messages used across the application
 * to ensure consistency and make maintenance easier.
 */

export class ConfirmationMessages {
  /**
   * Message for unsaved changes when navigating to a different date
   */
  static readonly UNSAVED_CHANGES_DATE_NAVIGATION_TITLE = "Change Date? (Unsaved Data)"
  static readonly UNSAVED_CHANGES_DATE_NAVIGATION = 
    '<i class="fa fa-exclamation-triangle text-warning"></i> One or more of your daily logs have unsaved changes. Would you still like to go to another date?<br><br><b><i>Changes will be lost if you select \'Yes\'.</i></b>';  

  /**
   * Message for unsaved changes when deselecting projects
   */
  static readonly UNSAVED_CHANGES_PROJECT_DESELECTION_TITLE = "Unselect Project? (Unsaved Data)"
  static readonly UNSAVED_CHANGES_PROJECT_DESELECTION = 
    '<i class="fa fa-exclamation-triangle text-warning"></i> One or more projects you are unselecting have unsaved changes. Continue with unselecting the project(s)?<br><br><b><i>Changes will be lost if you select \'Yes\'.</i></b>';
  
  /**
   * Message for unsaved changes when reloading projects
   */
  static readonly UNSAVED_CHANGES_RELOAD_PROJECTS_TITLE = "Reload Projects? (Unsaved Data)" 
  static readonly UNSAVED_CHANGES_RELOAD_PROJECTS = 
    '<i class="fa fa-exclamation-triangle text-warning"></i> One or more of your daily logs have unsaved changes. Would you still like to reload the projects?<br><br><b><i>Changes will be lost if you select \'Yes\'.</i></b>';
}
