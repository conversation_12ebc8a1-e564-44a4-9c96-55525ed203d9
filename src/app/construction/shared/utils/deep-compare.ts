/**
 * Performs a deep comparison between two values to determine if they are equivalent.
 * @param value1 First value to compare
 * @param value2 Second value to compare
 * @returns true if the values are deeply equal, false otherwise
 */
export function deepEqual(value1: any, value2: any): boolean {
  // If values are strictly equal (includes handling of null/undefined)
  if (value1 === value2) {
    return true;
  }

  // If either value is null/undefined but not both (we already checked === above)
  if (value1 == null || value2 == null) {
    return false;
  }

  // If they are different types
  if (typeof value1 !== typeof value2) {
    return false;
  }

  // Handle Date objects (comparing their time values)
  if (value1 instanceof Date && value2 instanceof Date) {
    return value1.getTime() === value2.getTime();
  }

  // Handle Array objects
  if (Array.isArray(value1) && Array.isArray(value2)) {
    if (value1.length !== value2.length) {
      return false;
    }

    // Compare each element
    for (let i = 0; i < value1.length; i++) {
      if (!deepEqual(value1[i], value2[i])) {
        return false;
      }
    }
    return true;
  }

  // Handle plain objects (not Array, Date, etc.)
  if (typeof value1 === 'object' && typeof value2 === 'object') {
    const keys1 = Object.keys(value1);
    const keys2 = Object.keys(value2);

    // If number of properties is different
    if (keys1.length !== keys2.length) {
      return false;
    }

    // Check if every key in one object is in the other and has the same value
    for (const key of keys1) {
      if (!keys2.includes(key) || !deepEqual(value1[key], value2[key])) {
        return false;
      }
    }
    return true;
  }

  // For primitives (strings, numbers, booleans)
  return value1 === value2;
}