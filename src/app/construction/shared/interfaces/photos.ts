import { SafeUrl } from "@angular/platform-browser";
import { DailyLogProjectComponent } from "./daily-log-user-project";

export interface DailyLogPhotoComponent extends DailyLogProjectComponent {
	Photos: Array<Photo>;
	
}

export interface PhotoStorage {
	Key: string | null;
	Bucket: string | null;
	Location: string | null;
	ETag: string | null;
}

export interface Photo {
	PhotoId: string;
	FileName?: string;
	LargeStorage: PhotoStorage;
	ThumbnailStorage: PhotoStorage;
	Title?: string | null;
	Description?: string | null;
	Data: any;
	Width: number;
	Height: number;
	UploadDate?: Date | null;
	LastModifiedDate?: Date | null;
	PhotoType?: string;
	PhotoSize?: number;
}


export interface PhotoProcessorInfo{
	Id: string;	
	FileUrl:string;	
	ContentType: string;
	Status: PhotoProcessorStatus;
	ThumbPhotoInfo: PhotoInfo;
	LargePhotoInfo: PhotoInfo;	
	Name: string;
	IsUploading: boolean;

}

export interface PhotoInfo{
	Url: string;
	Status: PhotoProcessorStatus;	
	Progress: number;
	IsUploading: boolean;
	Key:string;
	Bucket: string;
}

export enum PhotoProcessorStatus{
	Created = "Created",
	Processing = "Processing",
	Complete = "Complete",
	Error = "Error"
}
export interface PhotoUserInfo{
	UserId: string;
	Photos: Array<Photo>;
	DailyLogTimeStamp: Date;
}

export interface GalleryImage{
	Id: string;
	ThumbSafeUrl: SafeUrl;
	LargeSafeUrl: SafeUrl;
	UploadDate: Date;
	LargeUrl: string;
	ThumbUrl: string;
	Title:string;
	Description:string;
	MarkForDeletion: boolean;
	IsUploaded: boolean;
	IsProcessing: boolean;
	ShowOverlay: boolean;
	IsEditing: boolean;
  }
  