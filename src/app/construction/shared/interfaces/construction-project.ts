export interface ConstructionProject{
	Id: string;
	Title: string;
	InternalId: string;
	IsFavorite: boolean;
	isLoading: boolean;
	Components: Array<any>;
	Deactivated: boolean;
	UserId:string;
}

export interface ConstructionProjectResponse{
	Projects: Array<ConstructionProject>
	Total: number;
}
  
export enum ProjectsView{
	DETAILS = "details",
	LIST = "list"
  }

  export interface FilterOptions {
	title: string;
	internalId: string;
	deactivated: boolean;
	orderBy: string;
	isReversed: boolean;
	includeComponents: string | null;
	currentPage: number;
  }

  export interface ProjectUpdateOptions{
	title: string | null;
	internalId: string | null;
	isHighlighted: boolean | null;
	deactive: boolean | null;
  }

  export interface ConstructionProjectPermissions{
	closeProject: string;
	addProject: string;
  }
  