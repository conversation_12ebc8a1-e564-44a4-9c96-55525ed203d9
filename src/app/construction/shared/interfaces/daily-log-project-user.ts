//Purpose: Daily Log Project Components

import { CostCode } from "./cost-codes";
import { TimeCard } from "./timecard";
import { ConstructionProjectComponent } from "./project-components";
import { Photo } from "./photos";

export interface DailyLogProjectUserComponent extends ConstructionProjectComponent {
	Id: string;
	NoWork: boolean;
	ProjectId: string;
	OwnerAccountId: string;	
	UserId: string;
	Year: number;
	Month: number;
	Day: number;
	DateCreated: Date;
	LastUpdate: Date;
	Components: Array<BaseDailyLogProjectUserComponent>;
}


export interface BaseDailyLogProjectUserComponent {
	_t: Array<string>;
	ComponentIdentifier: string;	
	IsActive: boolean;
	IsDeleted: boolean;
	Name: string;	
}
export interface PhotosComponentProjectUser extends BaseDailyLogProjectUserComponent {
	Photos: Array<Photo>;
	SelectedPhotos: Array<Photo>;
}

export interface NotesComponentProjectUser  extends BaseDailyLogProjectUserComponent {
    Note: string;
}

export interface WeatherComponentProjectUser  extends BaseDailyLogProjectUserComponent {
	SelectedSkys: Array<string>;
	Temperature: string;
}

export interface TimeCardComponentProjectUser  extends BaseDailyLogProjectUserComponent {	 
	TimeCards: Array<TimeCard>;
}

export interface SiteConditionsComponentProjectUser  extends BaseDailyLogProjectUserComponent {
	SiteCondition: string;
}

export interface LatestDailyLogReponse{
	LatestDailyLogOnAccount: DailyLogProjectUserComponent;
	LatestDailyLogOnProject: DailyLogProjectUserComponent;
  }

  export interface DailyLogProjectUserPermissions{
	routeAccess: string;
  }