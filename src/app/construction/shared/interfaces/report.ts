import { InfoItem } from "./info";

export interface ReportInfoFilter{
	InfoItem: InfoItem;
	FilterType: string;
}

export interface ReportProjectListRequest {
	ProjectFilterTypes: string[];
	ReportInfoFilters: ReportInfoFilter[];
}

export interface ReportPermissions{
	reportRoles: string;
	reportDailyLogGlobal:string;
	reportProjectList: string;
	reportPayroll: string;
	reportCostCodeQty: string;
	reportDailyLogProject: string;
}