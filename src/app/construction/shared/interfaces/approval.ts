import { NgbDate } from "@ng-bootstrap/ng-bootstrap";

export interface Approval {
	Id: string;
	ProjectId: string;
	DailyLogId: string;
	UserId: string;
	History: ApprovalHistory[];
	CurrentApprover: Approver | null;
	ApprovalLevels: ApprovalLevel[];
	SubmitterInfo: SubmitterUserInfo | null;
	DailyLogTimeStamp: Date | null;
	FinalApprovalDate: Date | null;
	CreateDate: Date | null;
	ProjectTitle: string | null;
	ProjectInternalId: string | null;
}

export interface DailyLogApprovals {
	[dailyLogId: string]: Approval | null;
  }

export interface ApprovalHistory {
	User: Approver;
	ApprovedDate: Date;
	HistoryType: ApprovalHistoryType;
}

export interface Approver {
	UserId: string;
	FirstName: string;
	LastName: string;
	Level: number;

}

export interface ApprovalLevel {
	Name: string;
	Level: number;
	UserApproved: Approver | null;
	ApprovedDate: Date | null;
}

export enum ApprovalHistoryType {
	Submitted,
	Approved,
	Edited,
	Final,
	Unapproved,
	Unlock
}

export enum ApprovalEditMode {
	Edit = "Edit",
	NonEdit = "NonEdit",
	ReadOnly = "ReadOnly"
}


export interface ApprovalInfo {
	Approval: Approval;
	ProjectTitle: string;
	ProjectInternalId: string;
	ProjectId: string;
	ShowTimeCard: boolean;
	IsSelected: boolean;
	
	//DailyLog: DailyLogComponent;
}

export interface ApprovalListResponse{
    Approvals: Array<Approval>;
    Total: number;
}

export const approvalLevelsInfos: ApprovalLevelInfo[] = [
    {
      Level: 1,
      Name: "Level 1"
    },
    {
      Level: 2,
      Name: "Level 2"
    },
    {
      Level: 3,
      Name: "Level 3"
    },
    {
      Level: 4,
      Name: "Level 4"
    },
    {
      Level: 5,
      Name: "Level 5"
    },
    {
      Level: 999,
      Name: "Payroll"
    }
  ]

export interface ApprovalLevelInfo{
	Name: string;
	Level: number;
}
export interface GetApprovalLevelResponse{
  Level: number;
}

export interface ApprovalSearchOptions{
  ProjectIds: Array<string>
  SearchedProjectIds: Array<string>;
  UserIds: Array<string>;
}

export interface ApprovalsFilter {
	Sort: number;
	SortName?: string;
	Search?: string;
	Level?: number;
	DateInfo?: DateFilterInfo;
	ApprovalSearchOptions?: ApprovalSearchOptions;
	CurrentPage: number;
	Limit: number;
}
export interface DateFilterInfo {
	dateRangeStart: NgbDate | null;
	dateRangeEnd: NgbDate | null;
	singleDate: NgbDate | null;
}


export interface SubmitterUserInfo {
	FirstName: string;
	LastName: string;
}

export interface ApprovalAccess{
	EditQtyNotes: string;
	DeleteApproval: string;
}
