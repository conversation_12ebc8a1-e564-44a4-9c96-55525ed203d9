import { Approver, ApprovalLevel, ApprovalHistory, ApprovalHistoryType, SubmitterUserInfo, ApprovalAccess } from './approval';

export interface MechanicsApproval {
  Id: string;
  DailyLogId: string;
  EmployeeUserId: string; // User-based context instead of ProjectId
  OwnerAccountId: string; // Additional field specific to mechanics
  Status: MechanicsApprovalStatus;
  _t?: string[]; // MongoDB discriminator field
  ApprovalLevels: ApprovalLevel[]; // Use standard ApprovalLevel
  History: ApprovalHistory[]; // Use standard ApprovalHistory
  SubmitterInfo: SubmitterUserInfo | null; // Align with standard interface
  CurrentApprover: Approver | null; // Use standard Approver
  DailyLogTimeStamp: Date | null;
  FinalApprovalDate: Date | null;
  CreateDate: Date | null;
  // Additional mechanics-specific fields
  TimesheetDate: string;
  CurrentLevel: number;
  MaxLevel: number;
  TotalHours: number;
  Comments?: string;
  TimeCardData?: any;
  CreatedBy: string;
  CreatedDate: Date;
  ModifiedBy: string;
  ModifiedDate: Date;
  // Additional properties used by service filters
  EmployeeName: string;
  Year: number;
  Month: number;
  Day: number;
}

// Use standard ApprovalHistory interface with mechanics-specific extensions
export interface MechanicsApprovalHistoryEntry extends ApprovalHistory {
  ApprovalId: string;
  Level: number;
  Comments?: string;
}

// Use standard Approver interface - remove MechanicsApprovalCurrentApprover
// export interface MechanicsApprovalCurrentApprover extends Approver {}

// Use standard ApprovalLevel interface with mechanics-specific extensions
export interface MechanicsApprovalLevel extends ApprovalLevel {
  // Inherits: Name, Level, UserApproved, ApprovedDate from ApprovalLevel
  IsRequired?: boolean; // Additional mechanics-specific field
}

// Use standard SubmitterUserInfo interface
// export interface MechanicsSubmitterInfo extends SubmitterUserInfo {}

export enum MechanicsApprovalStatus {
  Pending = "Pending",
  Approved = "Approved",
  Rejected = "Rejected"
}

// Use standard ApprovalHistoryType enum instead
// export enum MechanicsApprovalHistoryType extends ApprovalHistoryType {}

export interface MechanicsApprovalListResponse {
  Approvals: Array<MechanicsApproval>;
  Total: number;
}

export interface MechanicsApprovalsFilter {
  Sort: number;
  SortName?: string;
  Search?: string;
  Level?: number;
  StartDate?: Date;
  EndDate?: Date;
  EmployeeName?: string;
  Status?: MechanicsApprovalStatus | 'All';
  CurrentPage: number;
  Limit: number;
}

// Use standard ApprovalAccess interface
export interface MechanicsApprovalAccess extends ApprovalAccess {
  // Inherits: EditQtyNotes, DeleteApproval from ApprovalAccess
}

export interface BulkApprovalRequest {
  approvalIds: string[];
  comment?: string;
}

export interface BulkApprovalResult {
  successCount: number;
  failedIds: string[];
  message: string;
}

// Align with standard ApprovalInfo structure
export interface MechanicsApprovalInfo {
  Approval: MechanicsApproval;
  EmployeeName: string; // User-based context instead of ProjectTitle
  TimesheetDate: string; // Mechanics-specific instead of ProjectInternalId
  UserId: string; // Align with standard ApprovalInfo.ProjectId pattern
  ShowTimeCard: boolean;
  IsSelected: boolean;
}

export interface MechanicsApprovalRequest {
  StartDate?: string;
  EndDate?: string;
  EmployeeName?: string;
  Status?: string;
  Sort?: number;
  SortName?: string;
  Search?: string;
  CurrentPage?: number;
  Limit?: number;
  RoleId?: string;
}

export interface MechanicsApprovalActionRequest {
  Level: number; // Add level for consistency with project-based pattern
  Comments?: string;
}

export interface BulkMechanicsApprovalRequest {
  ApprovalIds: string[];
  Comments?: string;
}

export interface BulkMechanicsApprovalResponse {
  TotalCount: number;
  SuccessCount: number;
  FailedCount: number;
  SuccessfulIds: string[];
  Errors: BulkMechanicsApprovalError[];
}

export interface BulkMechanicsApprovalError {
  ApprovalId: string;
  ErrorMessage: string;
  EmployeeName?: string;
  TimesheetDate?: string;
}