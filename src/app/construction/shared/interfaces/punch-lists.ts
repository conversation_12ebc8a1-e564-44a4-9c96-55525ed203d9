import { ConstructionProjectComponent } from "./project-components";

export interface PunchListComponentInfo extends ConstructionProjectComponent {
	PunchList: Array<PunchList>;
}
export interface PunchList{

	PunchListId: string;
	Description: string;
	CreateByUserId: string;
	CreatedByName: string;
	PunchListTasks: Array<PunchListTask>;
}

export interface PunchListTask{
	PunchListTaskId: string;
	DateCompleted: Date | null;
	Description: string | null;
	CompletedByUserId: string | null;
}

export interface PunchListPermissions{
	updatePunchlist: string;
	addPunchList: string;
	updatepunchlisttask: string;
	deletePunchList: string;
	markTask:string;
}