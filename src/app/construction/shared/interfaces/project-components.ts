//Purpose: Root components for the Project Component System
//These components are usually extended by other components


export interface ConstructionProjectComponent{
	_t: Array<string>; //used for polymorphism
	IsActive: boolean;
	Name: string;
	AllowDeletion: boolean;
	ComponentIdentifier: string;
}

export interface Employee{
	EmployeeId: string;
	FirstName: string;
	LastName: string;
	Classification: string;
	// CustomId: string;
}

export interface Equipment{
	EquipmentInternalId: string;
	Make: string | null;
	Model: string | null;
	Number: string | null;
  }
  
  
  export enum ProjectComponentIdentifiers {
	TEAM = 'Team',
	ACTIVITY = 'Activity',
	DAILY_LOG = 'DailyLog',
	COSTCODES = 'CostCodes',
	INFO = 'Info',
	MENU = 'Menu',
	PHOTOS = 'Photos',
	REPORT = 'Report',
	PUNCH_LIST = "PunchList",
	FILES= "Files",
	SIDE_BAR = "SideBar",
  }

  export enum ComponentView {
    ReadOnly = "ReadOnly",
	Edit = "Edit",
    Single = "Single",    
    FinalApprover = "FinalApprover",
    EditorQtyNotesOnly = "EditorQtyNotesOnly"
}

export class ProjectComponentSerializerInfo {
	private static instance: ProjectComponentSerializerInfo;

	public static getInstance(): ProjectComponentSerializerInfo {
        if (!ProjectComponentSerializerInfo.instance) {
            ProjectComponentSerializerInfo.instance = new ProjectComponentSerializerInfo();
        }

        return ProjectComponentSerializerInfo.instance;
    }

	getSerializerInfo(componentIdentifier: string) : Array<string>{
	  let serializerArray = ['BaseEntity', 'ProjectComponent'];
  
	  if(componentIdentifier == ProjectComponentIdentifiers.INFO){
		  serializerArray.push("InfoComponent");
	  }
	  else if(componentIdentifier == ProjectComponentIdentifiers.ACTIVITY){
		serializerArray.push("HistoryComponent");
	  } 
	  else if(componentIdentifier == ProjectComponentIdentifiers.DAILY_LOG){
		serializerArray.push("DailyLogComponent");
	  }
	  else if(componentIdentifier == ProjectComponentIdentifiers.PHOTOS){
		serializerArray.push("PhotosComponent");
	  }
	  else if(componentIdentifier == ProjectComponentIdentifiers.TEAM){
		serializerArray.push("TeamComponent");
	  }
	  else if(componentIdentifier == ProjectComponentIdentifiers.REPORT){
		serializerArray.push("ReportComponent");
	  }
	  else if(componentIdentifier == ProjectComponentIdentifiers.PUNCH_LIST){
		serializerArray.push("PunchListComponent");
	  } 
	  else if(componentIdentifier == ProjectComponentIdentifiers.FILES){
		serializerArray.push("FilesComponent");
	  } else if(componentIdentifier == ProjectComponentIdentifiers.COSTCODES){
		serializerArray.push("CostCodeComponent");
	  }
	  
  
	  return serializerArray;
	}
}