import { ConstructionProjectComponent } from "./project-components";

export interface FilesComponentInfo extends ConstructionProjectComponent {
	Files: Array<FileInfoSB>;
	Folders: Array<FolderSB>;
}

export interface FileInfoSB {
	FileName: string;
	OriginalFileName: string | null;
	FileId: string;
	UserId: string | null;
	DateCreated: Date | null;
	Extension: string;
	Key: string;
	ApplicationType: string;
	Bucket: string | null;
	Size: number;
	isLoading: boolean | null;
}

export interface FolderSB {
	Name: string;
	FolderId: string;
	Description: string | null;
	DateCreated: Date | null;
	Files: Array<string>;
}

export interface PresignedUrlUploadRequest {
	Key: string;
	ContentType: string;
}

export interface PresignedUrlDownloadRequest {
	Key: string;
	ContentType: string;
	FileName: string;
}

export interface FilePermissions {
	downloadFilePermission: string;
	uploadFilePermission: string;
	updateFilePermission: string;
	deleteFilePermission: string;
	addFolderPermission: string;
	updateFolderPermission: string;
	moveToFolderPermission: string;
	deleteFolderPermission: string;
}
