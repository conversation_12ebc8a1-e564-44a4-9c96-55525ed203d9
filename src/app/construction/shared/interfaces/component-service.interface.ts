/**
 * Interface for component services that manage the state and behavior of specific project components.
 * 
 * @template T - The type of the component data managed by the service.
 * 
 * Methods:
 * - setInitialData: Initializes the service with the provided component data, view mode, and access level.
 * - getUpdatedComponent: Retrieves the updated component data after modifications.
 * - isDirty: Signal indicating whether the component has unsaved changes.
 */
import { Signal } from '@angular/core';

export interface IComponentService<T> {
  setInitialData(data: T | null, view: any, access: string): void;
  getUpdatedComponent(): T;
  readonly isDirty: Signal<boolean>;
}