import { CostCode } from "./cost-codes";
import { Employee, Equipment } from "./project-components";

export interface TimeCard {
	Id: string;
	Employees: Array<TimeCardEmployee>;
	CostCodes: Array<CostCode>;
	EquipmentTrackers: Array<EquipmentTracker>;
}

export interface TimeCardEmployee extends Employee {
	CostCodes: Array<TimeCardCostCode>;
	TimeReason: string | null;
	TotalHours: number | null;
}

export interface TimeCardCostCode extends CostCode {
	UserValue: number | null;
}

export interface EquipmentTracker extends Equipment {
	TimeReason: string | null;
	TotalHours: number | null;
	CostCodes: Array<TimeCardCostCode>;
}


export interface TimeInfo {
	Display: string;
	Value: number | null;
}

export interface TimeCardApprovalPermissionSystem{
    isEnabled:boolean;
    approvalTimeCardEdit: any;
    approvalTimeCardQtyEdit: any;
    approvalTimeCardEmployeeTime: any;
}

export interface EquipmentTracker {
	EquipmentInternalId: string;
	TimeReason: string | null;
	TotalHours: number | null;
	Make: string | null;
	Model: string | null;
	Number: string | null;
	CostCodes: Array<TimeCardCostCode>;
}