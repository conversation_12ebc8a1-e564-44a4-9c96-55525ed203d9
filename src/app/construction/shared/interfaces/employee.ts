export interface EmployeeStore {
	UserId: string;
	Employees: Array<Employee>;
}

export interface Employee {
	_t: Array<string>;
	CustomId: string;
	ForemanId: string | null;
	FirstName: string;
	LastName: string;
	Classification: string;
	IsActive: boolean;
	InternalId: string;
}


export class EmployeeImportRequest{
    Bucket:string = "";
    Key: string = "";    
    HasHeader: boolean = true;    
    CurrentEmployees: Array<Employee> = [];
    EmployeeId: ColumnHeading = {
        Cell: "A",
        Name: "Employee Id"
    };
    FirstName: ColumnHeading = {
        Cell: "B",
        Name: "First Name"
    };
    LastName: ColumnHeading = {
        Cell: "C",
        Name: "Last Name"
    };
    Classification: ColumnHeading = {
        Cell: "D",
        Name: "Classification"
    };
    WorkSheetName: string = "";
}

export interface ColumnHeading {
	Name: string;
	Cell: string;
}

export interface EmployeeImportResponse {
	Worksheets: Array<string>;
	SampleEmployee: Employee;
	IsPartial: boolean;
}

export class ImportSettings {
	HasHeader: boolean = true;
	EmployeeId: ColumnHeading = { Name: "EmployeeId", Cell: "A" };
	FirstName: ColumnHeading = { Name: "FirstName", Cell: "B" };
	LastName: ColumnHeading = { Name: "LastName", Cell: "C" };
	Classification: ColumnHeading = { Name: "Classification", Cell: "D" };
	WorkSheetName: string = ""
}

export interface EmployeeImportInfo {
	NewEmployees: Employee[];
	EmployeeValidation: any[];
	InActiveEmployees: Employee[];
}
