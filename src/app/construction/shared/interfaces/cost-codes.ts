import { UserProjectComponent } from "./dashboard";

export interface CostCode {
	CostCodeId: string;
	Phase: string;
	Description: string;
	Hours: number;
	Units: number
	UnitOfMeasure: string;
	QtyCompl: number | null;
	IsLoading: boolean;
}

export interface CostCodeInfoComponent extends UserProjectComponent {
	CostCodes: Array<CostCode>;
}


export class CostCodeWizard {
	constructor() {
		this.SheetInfo = new CostCodeSheetData();
		this.NewCostCodes = new Array<CostCode>();
		this.MissingCostCodes = new Array<CostCode>();
	}

	ProjectId: string | null = null;
	FileLocation: string | null = null;
	CurrentStep: CostCodeEnum | null = null;
	IsCustom: boolean | null = null;
	SheetInfo: CostCodeSheetData;
	NewCostCodes: Array<CostCode>;
	MissingCostCodes: Array<CostCode>;
}

export class CostCodeSheetData {
	constructor() {
		this.Code = new ColumnHeading();
		this.Description = new ColumnHeading();
		this.Hours = new ColumnHeading();
		this.Units = new ColumnHeading();
		this.UM = new ColumnHeading();

		this.Sheets = new Array<string>();
	}

	Code: ColumnHeading;
	Description: ColumnHeading;
	Hours: ColumnHeading;
	Units: ColumnHeading;
	UM: ColumnHeading;
	SelectedSheetName: string | null = null;
	SampleCostCode: CostCode | null = null;
	Sheets: Array<string> = [];
	HasHeader: boolean = false;
}

export class ColumnHeading {
	constructor(name: string = "", cell: string = "") {
		this.Name = name;
		this.Cell = cell;
	}
	Name: string;
	Cell: string;
}

export enum CostCodeEnum {
	Import,
	CustomImport,
	GetSampleData,
	Save
}
