import { ConstructionProjectComponent } from "./project-components";

export interface ProjectInfoComponent extends ConstructionProjectComponent{	
	Properties:Array<InfoItem>;
}

export interface InfoItem{
	PropertyId: string;
	Name: string;
	IsLocked: boolean;
	IsHidden: boolean;
	Value: string;
	ValueType: InfoValueTypes;
}

//TODO: Fix valuetypes to string and fix database
export enum InfoValueTypes {  
	String = 0,
	Decimal = 1,
	Boolean = 2,
	Integer = 3,
	Date = 4,
	Currency = 5
  }

export interface InfoPermissions{
	editInfoAccess: string;
}