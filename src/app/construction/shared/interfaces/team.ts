import { ConstructionProjectComponent } from "./project-components";
import { UserProjectComponent } from './dashboard';
import { Delegate } from "src/app/shared/interfaces/delegate";
import { CivCastAccount } from "src/app/shared/interfaces/account-profile";

export interface TeamComponentInfo extends ConstructionProjectComponent {
	TeamMembers: TeamMember[];
}

export interface TeamUserProjectComponentInfo extends UserProjectComponent {
	TeamMembers: TeamMember[];
}

export interface TeamMemberComponentInfo {
	TeamMembers: TeamMemberInfo[];
}

export interface TeamMember {
	UserId: string;
	IsActive: boolean;
}

export interface TeamMemberInfo {
	DelegateInfo: Delegate | null;
	AccountInfo: CivCastAccount | null;
	IsActive: boolean;
	IsLoading: boolean;
}

export enum TeamPermissionType {
	None,
	ReadOnly,
	Full
}

export interface ConstructionTeamPermissions{
	removeTeamMember: string;
	allowTeamUpdates: string;
  }
  
  