import { Employee } from "./employee";
import { EquipmentInfo } from "./equipment";

export interface EquipmentImportRequest {
	CurrentEquipment: EquipmentInfo[];
	Number: ColumnHeading;
	Make: ColumnHeading;
	Model: ColumnHeading;
	Notes: ColumnHeading;
	HasHeader: boolean;
	WorksheetName: string;
}
  
  export interface EquipmentImportResponse{
	  EquipmentInfo: EquipmentGetherInfo;
	  Worksheets: Array<string>;
	  IsPartial: boolean;
  }

export interface EquipmentGetherInfo {
	NewEquipments: EquipmentInfo[];
	EquipmentValidation: EquipmentValidation[];
	InActiveEmployees: Employee[];
}

  
export interface EquipmentValidation {
	Equipment: EquipmentInfo;
	Validation: Validation[];
	IsVerify: boolean;
}
  
export interface Validation {
	ValidationType: EquipmentValidationType;
	ChangeInfo: string[];
	IsRequiredToFix: boolean;
}
  
export interface EquipmentImportSettings {
	Id: string;
	UserId: string;
	Number: ColumnHeading;
	Make: ColumnHeading;
	Model: ColumnHeading;
	Notes: ColumnHeading;
	WorksheetName: string;
	HasHeader: boolean;
}
  
  export enum EquipmentValidationType{
	New,
	NumberChange,
	MakeChange,
	ModelChange,
	NotesChange,
	MissingData,
	ActiveStatusChange,
	InActive 
  }

  export interface ColumnHeading {
	Name: string;
	Cell: string;
}