//Purpose: These are the interfaces for the project dashboard.These components are only used for client side rendering.
//DataTable: none


export interface UserProjectComponentStore{
	Id: string;
    UserId: string;
    Components: Array<UserProjectComponent>;
}

export interface UserProjectComponent {
  
	Id?: string;
	ComponentIdentifier: string;
	UrlLink: string;
	AllowDeletion:boolean;
	HasSettings?: boolean;
	Name: string;  
	CreateDate?: Date;
	IsActive?: boolean;
	isLoading?: boolean;
	Order?: number;
  }

  export interface ProjectComponentUI{
	project_component_info: {
		Info: ProjectComponentUIInfo;
		Reports: ProjectComponentUIInfo;
		Team: ProjectComponentUIInfo;
		CostCodes: ProjectComponentUIInfo;
		DailyLog: ProjectComponentUIInfo;
		Photos: ProjectComponentUIInfo;
		Settings:ProjectComponentUIInfo;
		PunchList: ProjectComponentUIInfo;
		Files: ProjectComponentUIInfo;
	},
	UnUsedComponentsData: {
		History: ProjectComponentUIInfo;
	}
  }

  export interface ProjectComponentUIInfo{
	name: string;
	icon: string;
	url: string;
	componentIdentifier: string;
	isStatic: false;

	infoComponents: Array<InfoComponent>;
  }

  export interface InfoComponent{
	_t: Array<string>;
	name: string;
	componentIdentifier: string;
  }
