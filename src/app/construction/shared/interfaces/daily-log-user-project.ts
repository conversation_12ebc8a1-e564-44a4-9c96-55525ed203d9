//Purpose: Daily Log Project Components

import { CostCode } from "./cost-codes";
import { TimeCard } from "./timecard";
import { ConstructionProjectComponent } from "./project-components";

export interface DailyLogUserProjectComponent extends ConstructionProjectComponent {
	Id: string;
	Components: Array<DailyLogProjectComponent>;
}

export interface DailyLogProjectComponent {
	_t: Array<string>;
	ComponentIdentifier: string;	
	IsActive: boolean;
	Name: string;
	AllowDeletion: boolean;
}
export interface DailyLogPhotosComponent extends DailyLogProjectComponent {
	Photos: Array<string>;
	SelectedPhotos: Array<string>;
}

export interface NotesComponent extends DailyLogProjectComponent {
    Note: string;
}

export interface WeatherComponent extends DailyLogProjectComponent {
	Skys: Array<string>;
	Temperatures: Array<string>;
}

export interface TimeCardComponent extends DailyLogProjectComponent {

	WorkDayOptions: Array<string>;
	EquipmentDayOptions: Array<string>;	    
}

export interface SiteConditionsComponent extends DailyLogProjectComponent {
	Conditions: Array<string>;
	SiteCondition: string;
}