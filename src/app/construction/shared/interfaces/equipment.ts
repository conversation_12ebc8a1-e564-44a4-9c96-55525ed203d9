export interface EquipmentStore{
    Id:string;
    UserId: string;
    Equipments: Array<EquipmentInfo>;
    Total: number;
}

export interface EquipmentInfo {
	InternalId: string;
	Number: string | null;
	Make: string;
	Model: string;
	Notes: string;
	IsActive: boolean;
	isLoading: boolean;
}


export interface Equipment{
	EquipmentInternalId: string;
	Make: string | null;
	Model: string | null;
	Number: string | null;
  }

  export interface EquipmentPermissions{
	AddEquipment: string;
	ReplaceEquipment: string;	
  }