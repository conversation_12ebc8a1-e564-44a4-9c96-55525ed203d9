
// Base User Component Interface
export interface UserComponent {
  _id: string;
  _t?: string[]; // Optional - added during save like project diary
  ComponentIdentifier: string;
  UserId: string;
  OwnerAccountId: string;
  DateCreated: string;
  ModifiedDate: string;
  CreatedBy: string;
  ModifiedBy: string;
  IsActive: boolean;
  Name: string;
  SubmittedDate?: string;
  ApprovalId?: string;
}

// Mechanics Cost Codes Component
export interface MechanicsCostCodesUserComponent extends UserComponent {
  ComponentIdentifier: "MechanicsCostCodes";
  Components: Array<MechanicsCostCodeComponent>;
}

export interface MechanicsCostCodeComponent {
  CostCodeId: string; // Backend uses CostCodeId, not Id
  CostCode: string;
  Phase: string;
  Description: string;
  Hours: number;
  Units: number;
  UnitOfMeasure: string;
  IsActive: boolean;
  IsLoading?: boolean; // For UI loading states
  DateCreated: string;
  ModifiedDate: string;
  CreatedBy: string;
  ModifiedBy: string;
}

// User Daily Log Component
export interface UserDailyLogComponent extends UserComponent {
  ComponentIdentifier: "MechanicsDailyLog";
  Year: number;
  Month: number;
  Day: number;
  NoWork: boolean;
  Components: Array<UserDailyLogComponentType>;
}

export type UserDailyLogComponentType = 
  | NotesComponent 
  | PhotosComponent 
  | MechanicsTimeCardComponent;

export interface MechanicsTimeCardComponent {
  _t?: string[]; // Optional - added during save like project diary
  ComponentIdentifier: "TimeCard";
  TimeCards: Array<{
    CostCodes: Array<MechanicsCostCodeComponent>;
    EquipmentTrackers: Array<EquipmentTracker>;
  }>;
}

// Mechanics Approval Interface
export interface MechanicsApproval {
  Id: string;
  UserId: string;
  OwnerAccountId: string;
  EmployeeName: string;
  Year: number;
  Month: number;
  Day: number;
  Status: ApprovalStatus;
  ApprovedBy?: string;
  ApprovedDate?: string;
  Comments?: string;
  TotalHours: number;
  TimeCardData: UserDailyLogComponent;
}

export enum ApprovalStatus {
  Pending = "Pending",
  Approved = "Approved",
  Rejected = "Rejected"
}

// Mechanics Payroll Report Interface
export interface MechanicsPayrollReport {
  Id: string;
  GeneratedDate: string;
  StartDate: string;
  EndDate: string;
  EmployeeId: string;
  EmployeeName: string;
  TotalHours: number;
  ReportData: Array<MechanicsPayrollEntry>;
}

export interface MechanicsPayrollEntry {
  Date: string;
  Hours: number;
  CostCodes: Array<string>;
  Equipment: Array<string>;
  Notes?: string;
}

// Supporting interfaces from other files that are needed
export interface NotesComponent {
    _t?: string[]; // Optional - added during save like project diary
    ComponentIdentifier: "Notes";
    Note: string;
}

export interface PhotosComponent {
    _t?: string[]; // Optional - added during save like project diary
    ComponentIdentifier: "Photos";
    Photos: Array<any>; // Use same Photo interface as project daily log
}

export interface EquipmentTracker {
    EquipmentInternalId: string;
    Make: string;
    Model: string;
    Number: string;
    TimeReason: string;
    TotalHours: string;
    CostCodes: Array<any>; // Replace with actual CostCode interface if available
}

// Backend API Response Interfaces
export interface MechanicsCostCodesListResponse {
  ComponentIdentifier: string;
  UserId: string;
  OwnerAccountId: string;
  Name: string;
  DateCreated: string;
  ModifiedDate: string;
  CreatedBy: string;
  ModifiedBy: string;
  IsActive: boolean;
  SubmittedDate?: string;
  ApprovalId?: string;
  Components: Array<{
    ComponentIdentifier: string;
    CostCodes: MechanicsCostCodeComponent[];
  }>;
  Id: string;
  IsDeleted?: boolean;
}

// Request Interfaces for API calls
export interface CreateCostCodeRequest {
  CostCode: string;
  Phase: string;
  Description: string;
  Hours?: number;
  Units?: number;
  UnitOfMeasure: string;
}

export interface UpdateCostCodeRequest {
  CostCode: string;
  Phase: string;
  Description: string;
  Hours?: number;
  Units?: number;
  UnitOfMeasure: string;
  IsActive: boolean;
}

// Daily Log Request Interfaces
export interface CreateDailyLogRequest {
  Year: number;
  Month: number;
  Day: number;
  NoWork: boolean;
  Components: any[]; // Backend expects List<object>, not typed components
}

export interface UpdateDailyLogRequest {
  NoWork: boolean;
  Components: any[]; // Backend expects List<object>, not typed components
}

// Daily Log Response Interfaces
export interface DailyLogResponse {
  dailyLog: UserDailyLogComponent;
  success: boolean;
  message?: string;
}

export interface LatestDailyLogResponse {
  dailyLog: UserDailyLogComponent | null;
  hasTimeCard: boolean;
  success: boolean;
}

// Import Cost Codes Response Interface (matching backend API specification)
export interface ImportCostCodesResponse {
  CostCodes: CostCodeInfo[];          // Direct array of imported cost codes (flattened)
  SuccessCount: number;               // Number of cost codes successfully imported
  ErrorCount: number;                 // Number of cost codes that failed to import
  TotalRowsProcessed: number;         // Total rows processed from Excel
  Errors: string[];                   // Array of error messages for failed imports
}

// Individual Cost Code Info from Import (matches backend CostCodeInfo)
export interface CostCodeInfo {
  CostCodeId: string;                 // Generated GUID (PascalCase)
  CostCode: string;                   // Cost code from Excel column A (PascalCase)
  Phase: string;                      // Same as CostCode (required field)
  Description: string;                // Description from Excel column B
  Hours: number | null;               // Hours from Excel column C
  Units: number | null;               // Units from Excel column D
  UnitOfMeasure: string;              // Unit of measure from Excel column E
  IsActive: boolean;                  // Always true for imports
  DateCreated: string;                // ISO date string
  ModifiedDate: string;               // ISO date string
  CreatedBy: string;                  // User ID who performed import
  ModifiedBy: string;                 // User ID who performed import
}
