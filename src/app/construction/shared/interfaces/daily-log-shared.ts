export enum DailyLogComponentIdentifiers {
	NOTES = "Notes",
	WEATHER = "Weather",
	TIMECARD = "TimeCard",
	SITE_CONDITIONS = "SiteConditions",
	PHOTOS = "Photos",
}


export class DailyLogInfoComponentSerializerInfo {
	private static instance: DailyLogInfoComponentSerializerInfo;

	public static getInstance(): DailyLogInfoComponentSerializerInfo {
        if (!DailyLogInfoComponentSerializerInfo.instance) {
            DailyLogInfoComponentSerializerInfo.instance = new DailyLogInfoComponentSerializerInfo();
        }

        return DailyLogInfoComponentSerializerInfo.instance;
    }
	getSerializerInfo(componentIdentifier: string) : Array<string>{
	  let serializerArray = [];
  
	  if(componentIdentifier == DailyLogComponentIdentifiers.WEATHER){
		  serializerArray.push("WeatherComponent");
	  }
	  else if(componentIdentifier == DailyLogComponentIdentifiers.NOTES){
		serializerArray.push("NotesComponent");
	  } 
	  else if(componentIdentifier == DailyLogComponentIdentifiers.SITE_CONDITIONS){
		serializerArray.push("SiteConditionComponent");
	  }
	  else if(componentIdentifier == DailyLogComponentIdentifiers.PHOTOS){
		serializerArray.push("PhotosComponent");
	  }
	  else if(componentIdentifier == DailyLogComponentIdentifiers.TIMECARD){
		serializerArray.push("TimeCardComponent");
	  }
  
	  return serializerArray;
	}

	getDailyLogUserProjectSerializerInfo(componentIdentifier: string) : Array<string>{
		let serializerArray = [];
	
		if(componentIdentifier == DailyLogComponentIdentifiers.WEATHER){
			serializerArray.push("WeatherInfoComponent");
		}
		else if(componentIdentifier == DailyLogComponentIdentifiers.NOTES){
		  serializerArray.push("NotesInfoComponent");
		} 
		else if(componentIdentifier == DailyLogComponentIdentifiers.SITE_CONDITIONS){
		  serializerArray.push("SiteConditionInfoComponent");
		}
		else if(componentIdentifier == DailyLogComponentIdentifiers.PHOTOS){
		  serializerArray.push("PhotosInfoComponent");
		}
		else if(componentIdentifier == DailyLogComponentIdentifiers.TIMECARD){
		  serializerArray.push("TimeCardInfoComponent");
		}
	
		return serializerArray;
	  }
	
  } 


  export enum DailyLogViews{
	Normal = "Normal",
	Approval = "Approval",
  }