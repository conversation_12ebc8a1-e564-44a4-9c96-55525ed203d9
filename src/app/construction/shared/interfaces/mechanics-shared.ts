export enum MechanicsComponentIdentifiers {
    NOTES = "Notes",
    TIMECARD = "TimeCard", 
    PHOTOS = "Photos"
}

export class MechanicsInfoComponentSerializerInfo {
    private static instance: MechanicsInfoComponentSerializerInfo;

    public static getInstance(): MechanicsInfoComponentSerializerInfo {
        if (!MechanicsInfoComponentSerializerInfo.instance) {
            MechanicsInfoComponentSerializerInfo.instance = new MechanicsInfoComponentSerializerInfo();
        }
        return MechanicsInfoComponentSerializerInfo.instance;
    }

    getSerializerInfo(componentIdentifier: string): Array<string> {
        const serializerArray = [];

        if (componentIdentifier === MechanicsComponentIdentifiers.NOTES) {
            serializerArray.push("NotesComponent");
        } else if (componentIdentifier === MechanicsComponentIdentifiers.PHOTOS) {
            serializerArray.push("PhotosComponent");
        } else if (componentIdentifier === MechanicsComponentIdentifiers.TIMECARD) {
            serializerArray.push("TimeCardComponent");
        }

        return serializerArray;
    }
}