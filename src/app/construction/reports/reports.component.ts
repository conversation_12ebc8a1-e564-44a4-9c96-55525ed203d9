import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DailyLogReportComponent } from './ui/daily-log/daily-log.component';
import { ProjectListReportComponent } from './ui/project-list/project-list.component';
import { RolesComponent } from './ui/roles/roles.component';
import { UserTimeCardReportComponent } from './ui/timecard/user.timecard.report.component';
import { ConstructionReportsService } from '../shared/data-access/reports.service';


@Component({
    selector: 'app-reports',
    imports: [CommonModule, DailyLogReportComponent, ProjectListReportComponent, RolesComponent, UserTimeCardReportComponent],
    templateUrl: './reports.component.html',
    styleUrl: './reports.component.css'
})
export class ReportsComponent implements OnInit {
  reportsService = inject(ConstructionReportsService);
  ngOnInit() {
    this.reportsService.getReportPermissions().subscribe();
  }

}
