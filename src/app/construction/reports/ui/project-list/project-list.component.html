<form #runProjeectListReport="ngForm" (ngSubmit)="RunReport()">
    <div class="border p-3 mb-3">
        <header>
            <h3 class="page-title fs-6">Project List</h3>
            <p>Download an Excel file with a list of your projects.</p>
        </header>
        @if(reportsPermissions()?.reportProjectList === ACCESS_EFFECTS.Allow){
        <div>
            <div class="col-12 col-md-7 col-lg-4">
                <div class="input-group mb-3">
                    <span class="input-group-text" id="inputGroup-sizing-sm">Project Status</span>
                    <select name="selectProjectType" class="form-select" [(ngModel)]="selectedProjectType">
                        <option value="active">Open</option>
                        <option value="inActive">Closed</option>
                        <option value="both">Both</option>
                    </select>
                </div>
            </div>
            <div>
                <label for="exampleInputEmail1" class="form-label">Add Filters (Optional)</label>
                <div class="d-flex align-items-center justify-content-between pb-2">
                    <div class="flex-grow-1 pe-1">
                        <ng-multiselect-dropdown name="selectInfo" [placeholder]="'Select Info'"
                            [settings]="selectedInfoOptions" [data]="selectedInfoData" [(ngModel)]="selectedInfo">
                        </ng-multiselect-dropdown>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-dark" (click)="addSelectedItems()">Add</button>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <table class="table">
                    <thead>
                        <tr>
                            <th scope="col">Name</th>
                            <th scope="col">Filter</th>
                            <th scope="col">Value</th>
                            <th scope="col"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @for(prop of infoProperties; track $index){
                        <tr>
                            @if(!prop.InfoItem?.IsHidden){
                            <td> {{ prop.InfoItem?.Name }}</td>
                            <td>
                                @if(prop.InfoItem?.ValueType === 4 || prop.InfoItem?.ValueType === 1 ||
                                prop.InfoItem?.ValueType === 3 || prop.InfoItem?.ValueType === 5){
                                <select class="form-control mb-2" [(ngModel)]="prop.FilterType"
                                    name="filterTypea{{$index}}" required>
                                    <option value="eq">Equals</option>
                                    <option value="gte">Greater Than Or Equal</option>
                                    <option value="gt">Greater Than</option>
                                    <option value="lt">Less Than</option>
                                    <option value="lte">Less Than Or Equal</option>
                                </select>
                                }@else if(prop.InfoItem?.ValueType === 0){
                                <select class="form-control mb-2" name="filterTypeb{{$index}}"
                                    [(ngModel)]="prop.FilterType" required>
                                    <option value="contains">Contains</option>
                                    <option value="eq">Equals</option>
                                </select>
                                }@else if(prop.InfoItem?.ValueType === 2){
                                <select class="form-control mb-2" name="filterTypec{{$index}}"
                                    [(ngModel)]="prop.FilterType" required>
                                    <option value="eq">Equals</option>
                                </select>
                                }
                            </td>
                            <td>
                                @switch (prop.InfoItem?.ValueType) {
                                @case (INFO_VALUE_TYPES.String)
                                {
                                <input class="form-control" [id]="prop.InfoItem?.PropertyId" type="text"
                                    [(ngModel)]="prop.InfoItem.Value" />
                                }
                                @case(INFO_VALUE_TYPES.Decimal){
                                <input class="form-control" [id]="prop.InfoItem?.PropertyId" type="number"
                                    class="form-control" [(ngModel)]="prop.InfoItem.Value" />
                                }
                                @case(INFO_VALUE_TYPES.Boolean){
                                <div class="form-check">
                                    <input class="form-check-input" [id]="prop.InfoItem?.PropertyId" type="checkbox"
                                        [(ngModel)]="prop.InfoItem.Value" />
                                    <label class="form-check-label" for="flexCheckDefault"></label>
                                </div>
                                }
                                @case(INFO_VALUE_TYPES.Integer){
                                <input class="form-control" [id]="prop.InfoItem?.PropertyId" type="number"
                                    class="form-control" [(ngModel)]="prop.InfoItem.Value" />
                                }
                                @case(INFO_VALUE_TYPES.Date){
                                <app-info-date-element [infoItem]="prop.InfoItem"></app-info-date-element>
                                }
                                @case(INFO_VALUE_TYPES.Currency){
                                <input class="form-control" class="form-control" currencyMask
                                    [(ngModel)]="prop.InfoItem.Value" />
                                }@default {
                                {{prop.InfoItem.Value}}
                                }
                                }
                                <!-- <parent-element [infoItem]="prop.InfoItem" mode="input"></parent-element> -->
                            </td>
                            <td class="text-end">
                                <button type="button" class="btn btn-danger float-right" (click)="removeProperty(prop)">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </td>
                            }
                        </tr>
                        }@empty{
                        <tr>
                            <td colspan="4">
                                <div class="alert alert-info mb-0" role="alert">
                                    No filters selected.
                                </div>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
        }@else{
        <div class="alert alert-info mb-0" role="alert">
            You do not have access to this report.
        </div>
        }
        <div class="d-flex justify-content-end">
            @if(reportsPermissions()?.reportProjectList === ACCESS_EFFECTS.Allow){
            @if(downloadUrl){
            <a href="{{downloadUrl}}" target="_blank" class="me-4 btn btn-outline-dark">Download File</a>
            }
            <button type="submit" class="btn btn-primary" [disabled]="!runProjeectListReport.form.valid || isRunning">
                @if(isRunning){
                <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
                }
                Run Report
            </button>
            }
        </div>
    </div>
</form>