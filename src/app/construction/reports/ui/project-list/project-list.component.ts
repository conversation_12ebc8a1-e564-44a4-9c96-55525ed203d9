import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input, OnInit, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { interval } from 'rxjs';
import { InfoDateElementComponent } from 'src/app/construction/project/info/ui/info-date-element/info-date-element.component';
import { ConstructionInfoService } from 'src/app/construction/shared/data-access/info.service';
import { ConstructionProjectsService } from 'src/app/construction/shared/data-access/projects.service';
import { ConstructionReportsService } from 'src/app/construction/shared/data-access/reports.service';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';
import { AccessEffects } from 'src/app/shared/interfaces/access';
import { InfoItem, InfoValueTypes, ProjectInfoComponent } from 'src/app/construction/shared/interfaces/info';
import { ProjectComponentIdentifiers } from 'src/app/construction/shared/interfaces/project-components';
import { ReportInfoFilter, ReportProjectListRequest } from 'src/app/construction/shared/interfaces/report';
import { SelectedData } from 'src/app/models/SelectedData';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-project-list-report',
    templateUrl: './project-list.component.html',
    styleUrls: ['./project-list.component.css'],
    imports: [CommonModule, FormsModule, NgMultiSelectDropDownModule, InfoDateElementComponent]
})
export class ProjectListReportComponent implements OnInit {
  accessService: AccessService = inject(AccessService);
  projectService = inject(ConstructionProjectsService);
  infoService = inject(ConstructionInfoService);
  reportsService = inject(ConstructionReportsService);
  reportsPermissions = this.reportsService.reportPermissions;
  lambdaAWSService = inject(LambdaAWSService);
  toastrService = inject(ToastrService);
  isAllowed: boolean = false;
  isRunning: boolean = false;
  isLoading: boolean = false;
  downloadUrl: string | null = null;
  globalInfoComponent: ProjectInfoComponent | null = null;
  selectedProjectType: string = "both";
  selectedInfo: Array<SelectedData> = new Array<SelectedData>();;
  selectedInfoOptions: any;
  infoProperties: Array<ReportInfoFilter> = new Array<ReportInfoFilter>();

  selectedInfoData: Array<SelectedData> = new Array<SelectedData>();
  public readonly INFO_VALUE_TYPES: typeof InfoValueTypes = InfoValueTypes;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;
  constructor(
  ) {
    this.selectedInfoOptions = {
      singleSelection: false,
      idField: 'ItemId',
      textField: 'ItemText',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 0,
      allowSearchFilter: true
    };

  }

  ngOnInit() {
    this.isLoading = true;

    this.infoService.getUserStoreInfoComponent().subscribe({
      next: (globalInfoComponent) => {
        if (globalInfoComponent) {
          var info = globalInfoComponent as ProjectInfoComponent;

          for (let item of info.Properties) {
            this.selectedInfoData.push({
              ItemId: item.PropertyId,
              ItemText: `${item.Name}` // (${item.ValueType.toString()})
            });
          }

          this.globalInfoComponent = info;
        }
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  removeProperty(prop: ReportInfoFilter) {
    var idx = this.infoProperties.indexOf(prop);
    this.infoProperties.splice(idx, 1);
  }

  addSelectedItems() {
    if (this.globalInfoComponent) {
      for (let item of this.selectedInfo) {
        var prop = this.globalInfoComponent.Properties.filter(x => x.PropertyId === item.ItemId);
        if (prop.length > 0) {
          var info = {} as ReportInfoFilter;
          info.InfoItem = Object.assign({}, prop[0]);
          this.infoProperties.push(info);
        }
      }
    }

    this.selectedInfo = [];
  }

  fixProperties(prop: InfoItem) {
    if (prop) {
      if (prop.ValueType == InfoValueTypes.Date) {
        var datePipe = new DatePipe('en');
        prop.Value = datePipe.transform(prop.Value, 'MM/dd/yyyy') as string;

      }

    }
  }

  downloadTimeOut: any;

  RunReport() {

    this.isRunning = true;
    var selectedOrderIds = new Array<string>();

    if (this.downloadTimeOut) {
      clearTimeout(this.downloadTimeOut);
    }

    if (this.selectedProjectType.toLowerCase() == "active") {
      selectedOrderIds.push("active");
    } else if (this.selectedProjectType.toLowerCase() === "inactive") {
      selectedOrderIds.push("inactive");
    } else if (this.selectedProjectType.toLowerCase() === "both") {
      selectedOrderIds.push("active");
      selectedOrderIds.push("inactive");
    } else {
      this.toastrService.error("Must select a project option");
      return;
    }

    for (let item of this.infoProperties) {
      if (item.InfoItem) {
        this.fixProperties(item.InfoItem);
      }
    }

    var reportRequest: ReportProjectListRequest = {
      ProjectFilterTypes: selectedOrderIds,
      ReportInfoFilters: this.infoProperties
    };

    this.reportsService.RunProjectListReport(reportRequest).subscribe({
      next: (store) => {
        if (store.Status !== "completed" && store.Status !== "error") {
          var check = interval(5000);
          var reportInfo = check.subscribe(() => {
            this.reportsService.GetReportInfo(store.Id).subscribe({
              next: (storeData) => {
                if (storeData.Status === "completed" && storeData.Status !== "error") {
                  reportInfo.unsubscribe();

                  this.lambdaAWSService.getDownloadPresignedUrl(storeData.Key, "project-list-report.xlsx").subscribe({
                    next: (url) => {
                      this.downloadUrl = url.PresignedUrl;
                      window.location.href = url.PresignedUrl;

                      this.downloadTimeOut = setTimeout(() => {
                        this.downloadUrl = null;
                      }, environment.s3_link_expiration);

                      this.isRunning = false;
                    }
                  });
                } else if (storeData.Status === "error") {
                  reportInfo.unsubscribe();
                  this.toastrService.error("There was an issue creating this report");
                  this.isRunning = false;
                }
              },
              error: (err) => {
                reportInfo.unsubscribe();
                this.toastrService.error("There was an issue creating this report");
                this.isRunning = false;
              }
            });
          });
        } else {
          this.toastrService.error("There was an issue creating this report");
          this.isRunning = false;
        }
      }
    });

  }
}