@if(isLoading()){
<!-- <app-card-skeleton *ngIf="isLoading"></app-card-skeleton> -->
}@else {
<div class="border p-3 mb-3">
    <header>
        <h1 class="page-title fs-6">Diary</h1>
    </header>
    @if(reportPermissions()?.reportDailyLogGlobal === ACCESS_EFFECTS.Allow){
    <div class="row">
        <div class="col-12 col-lg-4 mb-3 d-flex flex-row align-items-start">
            <div class="position-absolute">
                <input name="datepicker" ngbDatepicker class="dp-hidden" #datepicker="ngbDatepicker"
                    [autoClose]="'outside'" (dateSelect)="onDateSelection($event)" [displayMonths]="3" [dayTemplate]="t"
                    outsideDays="hidden" [startDate]="fromDate!" />
                <ng-template #t let-date let-focused="focused">
                    <span class="custom-day" [class.focused]="focused" [class.range]="isRange(date)"
                        [class.faded]="isHovered(date) || isInside(date)" (mouseenter)="hoveredDate = date"
                        (mouseleave)="hoveredDate = null">
                        {{ date.day }}
                    </span>
                </ng-template>
            </div>
            <div class="input-group me-1">
                <input #dpFromDate class="form-control" placeholder="Start Date" name="dpFromDate"
                    [value]="momentFormatter.format(dateFilterInfo!.dateRangeStart)"
                    (input)="fromDate = validateInput(dateFilterInfo!.dateRangeStart, dpFromDate.value)" readonly />
            </div>
            <div class="input-group me-1">
                <input #dpToDate class="form-control" placeholder="End Date" name="dpToDate"
                    [value]="momentFormatter.format(dateFilterInfo!.dateRangeEnd)"
                    (input)="toDate = validateInput(dateFilterInfo!.dateRangeEnd, dpToDate.value)" readonly />
            </div>
            <div class="btn-group" role="group" aria-label="Basic example">
                <button class="btn btn-outline-secondary" (click)="datepicker.toggle()" type="button">
                    <i class="fa fa-calendar"></i>
                </button>
                <button class="btn btn-outline-secondary" (click)="clearDateFilterRange()">
                    Clear
                </button>
            </div>
        </div>
        <div class="col-12 col-lg-4 mb-3">
            <div class="">
                <div class="input-group">
                    <span class="input-group-text" id="inputGroup-sizing-sm">Project Status</span>
                    <select class="form-select" (change)="getProjects($any($event.target).value)">
                        <option value="active">Open</option>
                        <option value="inactive">Closed</option>
                        <option value="both">Both</option>
                    </select>
                </div>
            </div>
            <!-- <div class="btn-group btn-group-toggle" ngbRadioGroup name="radioBasic"
                            [(ngModel)]="projectFilterInfo">
                            <label ngbButtonLabel class="btn-outline-secondary">
                                <input ngbButton type="radio" value="active" (click)="getProjects('active')" /> Active
                            </label>
                            <label ngbButtonLabel class="btn-outline-secondary">
                                <input ngbButton type="radio" value="inactive" (click)="getProjects('inactive')" />
                                Inactive
                            </label>
                            <label ngbButtonLabel class="btn-outline-secondary">
                                <input ngbButton type="radio" value="both" (click)="getProjects('both')" /> Both
                            </label>
                        </div> -->
        </div>
        <div class="col-12 col-lg-4 mb-3">
            <app-projects-list-drop-down style="width: 100%"></app-projects-list-drop-down>  
            <!-- <ng-multiselect-dropdown [placeholder]="'Select Projects'" [settings]="s2ProjectOptions"
                [data]="projectSelection" [(ngModel)]="selectedProjects" [disabled]="!projectsLoaded">
            </ng-multiselect-dropdown> -->
        </div>
        <div class="col-12 col-lg-4 mb-3 mb-lg-0">
            <ng-multiselect-dropdown [placeholder]="'Select Users'" [settings]="s2UserOptions" [data]="usersS2"
                [(ngModel)]="selectedUsers">
            </ng-multiselect-dropdown>
        </div>
        <div class="col-12 col-lg-4 mb-3 mb-lg-0">
            <div class="">
                <div class="input-group">
                    <span class="input-group-text" id="inputGroup-sizing-sm">Photos</span>
                    <!-- <label for="exampleFormControlSelect1">Show Photos?</label> -->
                    <select class="form-select" [(ngModel)]="showPhotoOption">
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-4 mb-3 mb-lg-0">
            <div class="">
                <div class="input-group mb-3">
                    <span class="input-group-text" id="inputGroup-sizing-sm">Page Layout</span>
                    <!-- <label for="exampleFormControlSelect1">Page Layout</label> -->
                    <select class="form-select" [(ngModel)]="pageLayout">
                        @for (c of layoutOptions; track $index) {
                        <option [ngValue]="c.Value">{{ c.Name }}</option>
                        }
                    </select>
                </div>
            </div>
        </div>
    </div>
    }@else {
    <div class="alert alert-info mb-0" role="alert">
        You do not have access to this report.
    </div>
    }
    <div class="d-flex justify-content-end">
        @if(reportPermissions()?.reportDailyLogGlobal === ACCESS_EFFECTS.Allow){
        <a href="{{downloadUrl}}" target="_blank" *ngIf="downloadUrl" class="me-4 btn btn-outline-dark">Download
            File</a>
        <button type="button" class="btn btn-primary" (click)="runReport()" [disabled]="isRunning">
            <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isRunning"></i>
            Run Report
        </button>
        }
    </div>
</div>
}