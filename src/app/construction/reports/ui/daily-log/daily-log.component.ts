import { Component, Input, OnInit, inject, signal } from '@angular/core';
import { Ng<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgbDateParserFormatter, NgbDatepickerModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { OrderByPipe } from 'ngx-pipes';
import { environment } from 'src/environments/environment';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, interval } from 'rxjs';
import { SelectedData } from 'src/app/models/SelectedData';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';

import { CommonModule } from '@angular/common';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule } from '@angular/forms';
import { ConstructionReportsService } from 'src/app/construction/shared/data-access/reports.service';
import { ConstructionProjectsService } from 'src/app/construction/shared/data-access/projects.service';
import { ConstructionProject } from 'src/app/construction/shared/interfaces/construction-project';
import { AccessEffects } from 'src/app/shared/interfaces/access';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { DelegateService } from 'src/app/account/user/shared/data-access/delegate.service';
import { ProjectsListDropDownComponent } from 'src/app/construction/ui/projects-list-drop-down/projects-list-drop-down.component';
import { ProjectListDropdownService } from 'src/app/construction/shared/data-access/project-list-dropdown.service';

@Component({
    selector: 'user-daily-log-report',
    templateUrl: './daily-log.component.html',
    styleUrls: ['./daily-log.component.css'],
    imports: [CommonModule, NgbTooltipModule, NgbDatepickerModule, NgMultiSelectDropDownModule, FormsModule, ProjectsListDropDownComponent],
    providers: [OrderByPipe]
})
export class DailyLogReportComponent implements OnInit {
  momentFormatter = inject(MomentDateFormatterService);
  formatter = inject(NgbDateParserFormatter);
  //private accessControlService: AccessControlService,
  delegateService = inject(DelegateService);
  orderBy = inject(OrderByPipe);
  reportsService = inject(ConstructionReportsService);
  accountsService = inject(AccountService);
  lambdaAWSService = inject(LambdaAWSService);
  accessService = inject(AccessService);
  toastrService = inject(ToastrService);
  projectListDropdownService  = inject(ProjectListDropdownService)  
  reportPermissions = this.reportsService.reportPermissions;
  pageLayout: number = 0;
  dateFilterInfo: DateFilterInfo | null = null;
  fromDate: NgbDate | null = null;
  toDate: NgbDate | null = null;
  hoveredDate: NgbDate | null = null;
  isAllowed: boolean = false;
  projectFilterInfo: string = "active";
  projectsLoaded: boolean = false;
  usersS2: Array<SelectedData> = [];
  selectedUsers: Array<SelectedData> = new Array<SelectedData>();
  s2UserOptions: any = {};
  layoutOptions: Array<any> = [];
  isRunning: boolean = false;
  isLoading = signal<boolean>(false);
  calendar: NgbCalendar | null = null;
  showPhotoOption: string = "No";
  projectFilterOptions: any = {
    deactivated: false,
    title: '',
    internalId: ''
  };
  downloadUrl: string | null = null;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar?.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
  }

  constructor() {
    this.projectListDropdownService.selectedProjects.set([]);
    this.projectListDropdownService.initializeProjectsForSelections.set(true);
    this.projectListDropdownService.selectionProjectType.set("all");    
  }

  ngOnInit() {
   
    this.dateFilterInfo = new DateFilterInfo();

    var sDate = new Date();
    sDate.setDate(sDate.getDate() - 7);
    var eDate = new Date();

    var startDate = new NgbDate(sDate.getFullYear(), sDate.getMonth() + 1, sDate.getDate());
    var endDate = new NgbDate(eDate.getFullYear(), eDate.getMonth() + 1, eDate.getDate());


    this.dateFilterInfo.dateRangeStart = startDate;
    this.dateFilterInfo.dateRangeEnd = endDate;


    this.s2UserOptions = {
      singleSelection: false,
      idField: 'ItemId',
      textField: 'ItemText',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 5,
      allowSearchFilter: true
    };

    this.getDelegates();

    this.layoutOptions = [
      {
        Name: 'Portrait',
        Value: 0
      },
      {
        Name: 'Landscape',
        Value: 1
      }
    ];

    this.pageLayout = this.layoutOptions[0].Value;

    
  }

  getProjects(filter: string) {
    this.projectFilterInfo = filter;

    if (this.projectFilterInfo == "inactive") {
        this.projectListDropdownService.deactivated.set(true);
    } else if (this.projectFilterInfo == "active") {
      this.projectListDropdownService.deactivated.set(false);
    }else{
      this.projectListDropdownService.deactivated.set(null);
    }
  }

  getDelegates() {

    forkJoin({ accountByIdentity: this.accountsService.GetAccountByIdentity(), delegates: this.delegateService.GetDelegates() }).subscribe({
      next: (accountInfos) => {
        var userIds = new Array<string>();

        if (accountInfos.accountByIdentity) {
          userIds.push(accountInfos.accountByIdentity.CognitoUserId);
        }

        if (accountInfos.delegates) {
          for (let item of accountInfos.delegates.Users) {
            userIds.push(item.UserId);
          }
        }

        this.accountsService.GetAccounts(userIds).subscribe({
          next: (accounts) => {

            accounts = this.orderBy.transform(accounts, "Profile.LastName");

            this.usersS2 = new Array();

            for (let c of accounts) {
              var pData = {
                ItemId: c.CognitoUserId,
                ItemText: c.Profile.FirstName + ' ' + c.Profile.LastName
              };

              this.usersS2.push(pData);
            }
          },
          error: (err) => {
            console.log(err);
          }
        });
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  clearDateFilterRange() {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.dateRangeStart = null;
      this.dateFilterInfo.dateRangeEnd = null;
    }

  }


  onDateSelection(date: NgbDate) {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.singleDate = null;
      if (!this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd) {
        this.dateFilterInfo.dateRangeStart = date;
      } else if (this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd && date.after(this.dateFilterInfo.dateRangeStart)) {
        this.dateFilterInfo.dateRangeEnd = date;
      } else {
        this.dateFilterInfo.dateRangeEnd = null;
        this.dateFilterInfo.dateRangeStart = date;
      }

      if (this.dateFilterInfo.dateRangeStart && this.dateFilterInfo.dateRangeEnd) {
        // this.currentPage = 1;
        // this.filter(this.currentType, this.currentUserLevel);
      }
    }

  }

  isHovered(date: NgbDate): boolean | null {
    if (this.dateFilterInfo) {
      return this.dateFilterInfo.dateRangeStart
        && !this.dateFilterInfo.dateRangeEnd
        && this.hoveredDate
        && date.after(this.dateFilterInfo.dateRangeStart)
        && date.before(this.hoveredDate)
    }

    return false;
  }


  isInside(date: NgbDate) {
    return date.after(this.dateFilterInfo?.dateRangeStart) && date.before(this.dateFilterInfo?.dateRangeEnd);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.dateFilterInfo?.dateRangeStart) ||
      date.equals(this.dateFilterInfo?.dateRangeEnd) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  downloadTimeOut: any;

  runReport() {
    this.downloadUrl = null;
    this.isRunning = true;
    if (this.downloadTimeOut) {
      clearTimeout(this.downloadTimeOut);
    }


    if (this.dateFilterInfo?.dateRangeStart === null || this.dateFilterInfo?.dateRangeEnd === null) {
      this.toastrService.error("Must have a start and end date");
      this.isRunning = false;
      return;
    }


    if ((this.projectListDropdownService.selectedProjects()?.length > 0 && this.selectedUsers.length <= 0)
      || (this.selectedUsers.length > 0 && this.projectListDropdownService.selectedProjects()?.length <= 0)
      || (this.projectListDropdownService.selectedProjects()?.length > 0 && this.selectedUsers.length > 0)) {

      if (this.dateFilterInfo) {
        var filterOptions: any = {
          ProjectIds: [],
          UserIds: [],
          StartDate: new Date(this.dateFilterInfo.dateRangeStart.year, this.dateFilterInfo.dateRangeStart.month - 1, this.dateFilterInfo.dateRangeStart.day).toISOString(),
          EndDate: new Date(this.dateFilterInfo.dateRangeEnd.year, this.dateFilterInfo.dateRangeEnd.month - 1, this.dateFilterInfo.dateRangeEnd.day).toISOString(),
          ShowPhotos: (this.showPhotoOption === "Yes") ? true : false,
          PDFLayout: this.pageLayout
        };

        if (this.projectListDropdownService.selectedProjects()) {
          for (var project of this.projectListDropdownService.selectedProjects() as Array<ConstructionProject>) {   
              filterOptions.ProjectIds.push(project.Id);                       
          }
        }

        if (this.selectedUsers) {
          for (var user of this.selectedUsers) {
            filterOptions.UserIds.push(user.ItemId);
          }
        }

        //TODO: Fix this who check if completed service
       this.reportsService.RunDailyLogReport(filterOptions).subscribe({
        next: (store) => {
          if (store.Status !== "completed" && store.Status !== "error") {
            var check = interval(5000);
            var reportInfo = check.subscribe(() => {
                this.reportsService.GetReportInfo(store.Id).subscribe({
                  next: (storeData) => {
                    if (storeData.Status === "completed" && storeData.Status !== "error") {
                      reportInfo.unsubscribe();
  
                      this.lambdaAWSService.getDownloadPresignedUrl(storeData.Key, "diary-report.pdf").subscribe({
                        next: (url) => {
                          this.downloadUrl = url.PresignedUrl;
                          window.location.href = url.PresignedUrl;
              
                          this.downloadTimeOut = setTimeout(() => {
                            this.downloadUrl = null;
                          }, environment.s3_link_expiration);
      
                          this.isRunning = false;
                        }
                      });
                    }else if(storeData.Status === "error"){
                      reportInfo.unsubscribe();
                      this.toastrService.error("There was an issue creating this report");
                      this.isRunning = false;
                    }
                  },
                  error: (err) => {
                    reportInfo.unsubscribe();
                    this.toastrService.error("There was an issue creating this report");
                    this.isRunning = false;
                  }
                });            
            });
          }else{
            this.toastrService.error("There was an issue creating this report");
            this.isRunning = false;
          }
        }
        });

      } else {
        this.isRunning = false;
        this.toastrService.error("You must have at least 1 project selected OR 1 user selected to run the report");
      }


    }
  }
}

export class DateFilterInfo {
  dateRangeStart: NgbDate | null = null;
  dateRangeEnd: NgbDate | null = null;
  singleDate: NgbDate | null = null;
}
