<form #runProjeectListReport="ngForm" (ngSubmit)="RunReport()">
    <div class="border p-3 mb-3">
        <header>
            <h1 class="page-title fs-6">Users, Roles, and Permissions</h1>
            <p>Download an Excel file containing a list of all your users, along with a list of all roles and their
                associated permissions.</p>
        </header>
        <div>
            @if(reportPermissions()?.reportRoles !== ACCESS_EFFECTS.Allow){
            <div class="alert alert-info mb-0" role="alert">
                You do not have access to this report.
            </div>
            }
        </div>
        <div class="d-flex justify-content-end">
            @if(reportPermissions()?.reportRoles === ACCESS_EFFECTS.Allow){
            @if(downloadUrl){
            <a href="{{downloadUrl}}" target="_blank" class="me-4 btn btn-outline-dark">Download File</a>
            }
            <button type="submit" class="btn btn-primary" [disabled]="!runProjeectListReport.form.valid || isRunning">
                <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isRunning"></i>
                Run Report
            </button>
            }
        </div>
    </div>
</form>