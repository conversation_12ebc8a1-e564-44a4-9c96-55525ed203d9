import { Component, OnInit, inject } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { interval } from 'rxjs';
import { environment } from 'src/environments/environment';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { ConstructionReportsService } from 'src/app/construction/shared/data-access/reports.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AccessEffects } from 'src/app/shared/interfaces/access';

@Component({
    selector: 'app-roles-report',
    templateUrl: './roles.component.html',
    styleUrls: ['./roles.component.css'],
    imports: [CommonModule, FormsModule]
})
export class RolesComponent implements OnInit {
  reportsService = inject(ConstructionReportsService);
  reportPermissions = this.reportsService.reportPermissions;
  isRunning: boolean = false;
  downloadUrl: string | null = null;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;
  constructor(
    private lambdaAWSService: LambdaAWSService,
    private toastrService: ToastrService) { }

  ngOnInit(): void {

  }

  downloadTimeOut: any;
  RunReport(){

    this.downloadUrl = null;
    this.isRunning = true;
    var reportRequest = {};

    if(this.downloadTimeOut){
      clearTimeout(this.downloadTimeOut);
    }

    this.reportsService.RunUserRolesReport(reportRequest).subscribe({
      next: (store) => {
        if (store.Status !== "completed" && store.Status !== "error") {
          var check = interval(5000);
          var reportInfo = check.subscribe(() => {
              this.reportsService.GetReportInfo(store.Id).subscribe({
                next: (storeData) => {
                  if (storeData.Status === "completed" && storeData.Status !== "error") {
                    reportInfo.unsubscribe();

                    this.lambdaAWSService.getDownloadPresignedUrl(storeData.Key, "roles-report.xlsx").subscribe({
                      next: (url) => {
                        this.downloadUrl = url.PresignedUrl;
                        window.location.href = url.PresignedUrl;
            
                        this.downloadTimeOut = setTimeout(() => {
                          this.downloadUrl = null;
                        }, environment.s3_link_expiration);
    
                        this.isRunning = false;
                      }
                    });
                  }else if(storeData.Status === "error"){
                    reportInfo.unsubscribe();
                    this.toastrService.error("There was an issue creating this report");
                    this.isRunning = false;
                  }
                },
                error: (err) => {
                  reportInfo.unsubscribe();
                  this.toastrService.error("There was an issue creating this report");
                  this.isRunning = false;
                }
              });            
          });
        }else{
          this.toastrService.error("There was an issue creating this report");
          this.isRunning = false;
        }
      }
    });


  }
}
