﻿@if(isLoading){
<app-card-skeleton *ngIf="isLoading"></app-card-skeleton>
}@else {
<div class="border p-3 mb-3">
  <header>
    <h1 class="page-title fs-6">Payroll</h1>
    <p>Download an Excel file containing timesheet data for all approved payroll entries within the selected date range.
      This file can then be imported into Viewpoint or other accounting software for seamless integration.</p>
  </header>
  @if(reportPermissions()?.reportPayroll === "Allow"){
  <div class="row">
    <div class="col-12 col-lg-4 mb-3 d-flex flex-row align-items-start">
      <div class="position-absolute">
        <input name="datepicker" class="dp-hidden" ngbDatepicker #datepicker="ngbDatepicker" [autoClose]="'outside'"
          (dateSelect)="onDateSelection($event)" [displayMonths]="3" [dayTemplate]="t" outsideDays="hidden"
          [startDate]="fromDate!" />
        <ng-template #t let-date let-focused="focused">
          <span class="custom-day" [class.focused]="focused" [class.range]="isRange(date)"
            [class.faded]="isHovered(date) || isInside(date)" (mouseenter)="hoveredDate = date"
            (mouseleave)="hoveredDate = null">
            {{ date.day }}
          </span>
        </ng-template>
      </div>
      <div class="input-group me-1">
        <input #dpFromDate class="form-control" placeholder="Start Date" name="dpFromDate"
          [value]="momentFormatter.format(dateFilterInfo?.dateRangeStart!)"
          (input)="fromDate = validateInput(dateFilterInfo?.dateRangeStart!, dpFromDate.value)" readonly />
      </div>
      <div class="input-group me-1">
        <input #dpToDate class="form-control" placeholder="End Date" name="dpToDate"
          [value]="momentFormatter.format(dateFilterInfo?.dateRangeEnd!)"
          (input)="toDate = validateInput(dateFilterInfo?.dateRangeEnd!, dpToDate.value)" readonly />
      </div>
      <div class="btn-group" role="group" aria-label="Basic example">
        <button class="btn btn-outline-secondary" (click)="datepicker.toggle()" type="button">
          <i class="fa fa-calendar"></i>
        </button>
        <button class="btn btn-outline-secondary" (click)="clearDateFilterRange()">
          Clear
        </button>
      </div>
    </div>
  </div>
  }@else {
  <div class="alert alert-info mb-0" role="alert">
    You do not have access to this report.
  </div>
  }
  <div class="d-flex justify-content-end">
    @if(reportPermissions()?.reportPayroll === "Allow"){
    <a href="{{downloadUrl}}" target="_blank" *ngIf="downloadUrl" class="me-2 btn btn-outline-dark">Download File</a>
    <button type="button" class="btn btn-primary" (click)="runTimeCardReport()" [disabled]="isRunning">
      <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isRunning"></i>
      Run Report
    </button>
    }
  </div>
</div>
}