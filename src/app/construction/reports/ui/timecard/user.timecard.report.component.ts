﻿import { Component, OnInit, Input, inject } from '@angular/core';
import { Ngb<PERSON><PERSON><PERSON><PERSON>, Ngb<PERSON>ate, NgbDateParserFormatter, Ng<PERSON><PERSON>ate<PERSON>truct, NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { interval } from 'rxjs';
import { environment } from 'src/environments/environment';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { ConstructionReportsService } from 'src/app/construction/shared/data-access/reports.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AccessEffects } from 'src/app/shared/interfaces/access';
@Component({
    selector: 'user-timecard-report',
    templateUrl: './user.timecard.report.component.html',
    styleUrls: ['./user.timecard.report.component.css'],
    imports: [CommonModule, FormsModule, NgbDatepickerModule]
})
export class UserTimeCardReportComponent implements OnInit {
  reportsService = inject(ConstructionReportsService);
  startDate: NgbDate | null = null;
  endDate: NgbDate | null = null;
  fromDate: NgbDate | null = null;
  toDate: NgbDate | null = null;
  hoveredDate: NgbDate | null = null;
  dateFilterInfo: TimeCardDateFilterInfo | null = null;
  isRunning: boolean = false;
  isLoading: boolean = false;
  downloadUrl: string | null = null;
  reportPermissions = this.reportsService.reportPermissions;
  private calendar: NgbCalendar | null = null;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;
  constructor(
    private toastr: ToastrService,
    private lambdaAWSService: LambdaAWSService,
    private toastrService: ToastrService,
    public formatter: NgbDateParserFormatter,
    public momentFormatter: MomentDateFormatterService) { }

  ngOnInit() {
    var endDate = new Date();
    var startDate = new Date();

    startDate = this.setToMonday(startDate);
    endDate = this.addDays(startDate, 4);

    var startDateFinal = new NgbDate(startDate.getFullYear(), startDate.getMonth() + 1, startDate.getDate());
    var endDateFinal = new NgbDate(endDate.getFullYear(), endDate.getMonth() + 1, endDate.getDate());

    this.dateFilterInfo = new TimeCardDateFilterInfo();

    this.dateFilterInfo.dateRangeStart = startDateFinal;
    this.dateFilterInfo.dateRangeEnd = endDateFinal;
  }

  setToMonday(date: any): Date {
    var day = date.getDay() || 7;
    if (day !== 1)
      date.setHours(-24 * (day - 1));
    return date;
  }

  addDays(date: any, days: any): Date {
    var result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    if (this.calendar) {
      const parsed = this.formatter.parse(input);
      return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
    }
    return null;
  }

  onDateSelection(date: NgbDate) {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.singleDate = null;
      if (!this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd) {
        this.dateFilterInfo.dateRangeStart = date;
      } else if (this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd && date.after(this.dateFilterInfo.dateRangeStart)) {
        this.dateFilterInfo.dateRangeEnd = date;
      } else {
        this.dateFilterInfo.dateRangeEnd = null;
        this.dateFilterInfo.dateRangeStart = date;
      }

      if (this.dateFilterInfo.dateRangeStart && this.dateFilterInfo.dateRangeEnd) {
        // this.currentPage = 1;
        // this.filter(this.currentType, this.currentUserLevel);
      }
    }

  }

  isHovered(date: NgbDate): boolean {
    if (this.dateFilterInfo) {
      if (
        this.dateFilterInfo.dateRangeStart &&
        !this.dateFilterInfo.dateRangeEnd &&
        this.hoveredDate &&
        date.after(this.dateFilterInfo.dateRangeStart) &&
        date.before(this.hoveredDate)
      ) {
        return true;
      }
    }

    return false;

  }

  isInside(date: NgbDate) {
    if (this.dateFilterInfo) {
      return date.after(this.dateFilterInfo.dateRangeStart) && date.before(this.dateFilterInfo.dateRangeEnd);
    }

    return false;

  }

  isRange(date: NgbDate) {
    if (this.dateFilterInfo) {
      if (
        date.equals(this.dateFilterInfo.dateRangeStart) ||
        date.equals(this.dateFilterInfo.dateRangeEnd) ||
        this.isInside(date) ||
        this.isHovered(date)
      ) {
        return true;
      }
    }

    return false;
  }

  clearDateFilterRange() {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.dateRangeStart = null;
      this.dateFilterInfo.dateRangeEnd = null;
    }

  }

  downloadTimeOut: any;

  //TODO: fix interval
  runTimeCardReport() {
    if (this.downloadTimeOut) {
      clearTimeout(this.downloadTimeOut);
    }

    this.downloadUrl = "";
    this.isRunning = true;



    if(this.dateFilterInfo){
      if (!this.dateFilterInfo.dateRangeEnd || !this.dateFilterInfo.dateRangeStart) {
        this.toastr.error("Dates are missing. Please select valid dates");
        this.isRunning = false;
        return;
      }

      var startDate = new Date(this.dateFilterInfo.dateRangeStart.year, this.dateFilterInfo.dateRangeStart.month - 1, this.dateFilterInfo.dateRangeStart.day, 0, 0, 0);
      var endDate = new Date(this.dateFilterInfo.dateRangeEnd.year, this.dateFilterInfo.dateRangeEnd.month - 1, this.dateFilterInfo.dateRangeEnd.day, 0, 0, 0);
  
      if (endDate < startDate) {
        this.toastrService.error("End date must be greater than or equal to start date");
        this.isRunning = false;
        return;
      }
  
      this.reportsService.RunTimeCardReport(startDate, endDate).subscribe({
        next: (store) => {
          if (store.Status !== "completed" && store.Status !== "error") {
            var check = interval(5000);
            var reportInfo = check.subscribe(() => {
                this.reportsService.GetReportInfo(store.Id).subscribe({
                  next: (storeData) => {
                    if (storeData.Status === "completed" && storeData.Status !== "error") {
                      reportInfo.unsubscribe();

                      this.lambdaAWSService.getDownloadPresignedUrl(storeData.Key, "payroll-report.xlsx").subscribe({
                        next: (url) => {
                          this.downloadUrl = url.PresignedUrl;
                          window.location.href = url.PresignedUrl;
              
                          this.downloadTimeOut = setTimeout(() => {
                            this.downloadUrl = null;
                          }, environment.s3_link_expiration);
      
                          this.isRunning = false;
                        }
                      });
                    }else if(storeData.Status === "error"){
                      reportInfo.unsubscribe();
                      this.toastr.error("There was an issue creating this report");
                      this.isRunning = false;
                    }
                  },
                  error: (err) => {
                    reportInfo.unsubscribe();
                    this.toastr.error("There was an issue creating this report");
                    this.isRunning = false;
                  }
                });            
            });
          }else{
            this.toastr.error("There was an issue creating this report");
            this.isRunning = false;
          }
        }
      });
    }
  }
}

export class TimeCardDateFilterInfo {
  dateRangeStart: NgbDate | null = null;
  dateRangeEnd: NgbDate | null = null;
  singleDate: NgbDate | null = null;
}