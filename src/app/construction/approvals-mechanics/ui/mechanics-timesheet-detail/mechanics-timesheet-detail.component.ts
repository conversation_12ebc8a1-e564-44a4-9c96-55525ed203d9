import { Component, input, computed, signal, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';

import { MechanicsApproval } from '../../../shared/interfaces/mechanics-approval';
import { MechanicsApprovalsService } from '../../../shared/data-access/mechanics-approvals.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessEffects } from 'src/app/shared/interfaces/access';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { OrderPipe } from 'src/app/shared/utils/pipes/order-by-pipe';

// Component mirrors project-based approval detail component patterns
@Component({
  selector: 'app-mechanics-timesheet-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgbTooltipModule,
    OrderPipe
  ],
  template: `
    <div class="mechanics-timesheet-detail">
      <!-- Header Section - Employee Info (vs Project Info in project-based) -->
      <div class="d-flex align-items-center justify-content-between mb-3">
        <div>
          <h5 class="mb-1">{{ approval().EmployeeName }}</h5>
          <small class="text-muted">{{ approval().TimesheetDate }} • {{ approval().TotalHours }} hours</small>
        </div>
        <div class="btn-group" role="group">
          @if (canApprove()) {
            <button 
              type="button" 
              class="btn btn-success btn-sm" 
              (click)="onApprove()"
              [disabled]="isApproving()"
              ngbTooltip="Approve Timesheet">
              @if (isApproving()) {
                <i class="fas fa-circle-notch fa-spin"></i>
              }
              Approve
            </button>
          }
          @if (canUnapprove()) {
            <button 
              type="button" 
              class="btn btn-warning btn-sm" 
              (click)="onUnapprove()"
              [disabled]="isUnapproving()"
              ngbTooltip="Unapprove Timesheet">
              @if (isUnapproving()) {
                <i class="fas fa-circle-notch fa-spin"></i>
              }
              Unapprove
            </button>
          }
          @if (canDelete()) {
            <button 
              type="button" 
              class="btn btn-danger btn-sm" 
              (click)="onDelete()"
              [disabled]="isDeleting()"
              ngbTooltip="Delete Approval">
              @if (isDeleting()) {
                <i class="fas fa-circle-notch fa-spin"></i>
              }
              Delete
            </button>
          }
        </div>
      </div>

      <!-- Approval Status Display -->
      <div class="row mb-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">Approval Status</h6>
            </div>
            <div class="card-body">
              <div class="row">
                @for (level of approval().ApprovalLevels | ccOrderBy: 'Level'; track level.Level) {
                  <div class="col-md-2 col-sm-4 mb-2">
                    <div class="text-center">
                      <div class="approval-level-indicator">
                        @if (level.UserApproved) {
                          <i class="fas fa-check-circle text-success fa-2x"></i>
                          <br>
                          <small class="text-success">
                            @if (level.Level < payrollLevel) {
                              L{{ level.Level }}
                            } @else {
                              Payroll
                            }
                          </small>
                          <br>
                          <small class="text-muted">
                            {{ level.UserApproved.FirstName }} {{ level.UserApproved.LastName }}
                          </small>
                        } @else {
                          <i class="fas fa-clock text-muted fa-2x"></i>
                          <br>
                          <small class="text-muted">
                            @if (level.Level < payrollLevel) {
                              L{{ level.Level }}
                            } @else {
                              Payroll
                            }
                          </small>
                          <br>
                          <small class="text-muted">Pending</small>
                        }
                      </div>
                    </div>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Timesheet Details Section -->
      <div class="row mb-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h6 class="mb-0">Timesheet Details</h6>
            </div>
            <div class="card-body">
              <!-- This would integrate with actual timesheet data display -->
              <!-- Following the pattern from project-based approval detail -->
              <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Detailed timesheet data would be displayed here (equipment, cost codes, hours allocation)
              </div>
              
              <!-- Future: Integrate with mechanics daily log display component -->
              <!-- Should show: Equipment used, Cost codes, Hours per equipment/cost code -->
              <!-- Pattern should match project-based daily log detail view -->
            </div>
          </div>
        </div>
      </div>

      <!-- Approval History Section -->
      @if (approval().History && approval().History.length > 0) {
        <div class="row mb-3">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0">Approval History</h6>
              </div>
              <div class="card-body">
                @for (historyItem of approval().History; track historyItem.ApprovedDate) {
                  <div class="d-flex justify-content-between border-bottom py-2">
                    <div>
                      <strong>{{ historyItem.User.FirstName }} {{ historyItem.User.LastName }}</strong>
                      <span class="text-muted ms-2">
                        @switch (historyItem.HistoryType) {
                          @case (0) { Submitted }
                          @case (1) { Approved }
                          @case (2) { Edited }
                          @case (3) { Final Approval }
                          @case (4) { Unapproved }
                          @case (5) { Unlocked }
                        }
                      </span>
                    </div>
                    <small class="text-muted">{{ historyItem.ApprovedDate | date:'short' }}</small>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      }
    </div>
  `,
  styles: [`
    .mechanics-timesheet-detail {
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 0.375rem;
    }
    
    .approval-level-indicator {
      padding: 0.5rem;
    }
    
    .card {
      border: 1px solid #dee2e6;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .card-header {
      background-color: #ffffff;
      border-bottom: 1px solid #dee2e6;
    }
  `]
})
export class MechanicsTimesheetDetailComponent {
  // Input - the mechanics approval to display details for
  approval = input.required<MechanicsApproval>();
  
  // Services injection - following Angular 19 patterns
  private mechanicsApprovalsService = inject(MechanicsApprovalsService);
  private accessService = inject(AccessService);
  private toastrService = inject(ToastrService);
  private confirmService = inject(ConfirmService);
  
  // Constants matching project-based patterns
  payrollLevel: number = this.mechanicsApprovalsService.payrollLevel;
  
  // State management signals
  isApproving = signal(false);
  isUnapproving = signal(false);
  isDeleting = signal(false);
  
  // Computed permissions - following project-based approval component patterns
  userApprovalLevel = this.mechanicsApprovalsService.userApprovalLevel;
  
  canApprove = computed(() => {
    const approval = this.approval();
    const userLevel = this.userApprovalLevel();
    
    if (!userLevel || !approval) return false;
    
    // Can approve if user level is greater than current required level
    // and not already at final approval
    const currentRequiredLevel = approval.CurrentLevel || 1;
    return userLevel >= currentRequiredLevel && !approval.FinalApprovalDate;
  });
  
  canUnapprove = computed(() => {
    const approval = this.approval();
    const userLevel = this.userApprovalLevel();
    
    if (!userLevel || !approval) return false;
    
    // Can unapprove if there are approvals and user level allows
    return approval.ApprovalLevels.some(level => level.UserApproved) && 
           userLevel >= (approval.CurrentLevel || 1);
  });
  
  canDelete = computed(() => {
    const approval = this.approval();
    if (!approval) return false;
    
    // Delete permissions based on access service (matching project-based pattern)
    // Only allow delete if not at payroll level
    return approval.CurrentLevel < this.payrollLevel && 
           this.hasDeleteAccess();
  });
  
  // Permission checking - to be implemented based on access service
  private hasDeleteAccess(): boolean {
    // This should match the project-based access checking pattern
    // For now, return false until access service integration is complete
    return false;
  }
  
  // Action methods - matching project-based approval component signatures
  onApprove(): void {
    const approval = this.approval();
    const userLevel = this.userApprovalLevel();
    
    if (!userLevel || !approval) {
      this.toastrService.error("You do not have the correct level to approve time sheets.");
      return;
    }
    
    this.isApproving.set(true);
    
    this.mechanicsApprovalsService.Approve(approval.Id, userLevel).subscribe({
      next: (result) => {
        this.toastrService.success("Timesheet approved successfully");
        this.isApproving.set(false);
        // Note: The service should handle updating the approvals list
      },
      error: (error) => {
        console.error("Error approving timesheet:", error);
        this.toastrService.error("Error approving timesheet");
        this.isApproving.set(false);
      }
    });
  }
  
  onUnapprove(): void {
    const approval = this.approval();
    const userLevel = this.userApprovalLevel();
    
    if (!userLevel || !approval) {
      this.toastrService.error("You do not have the correct level to unapprove time sheets.");
      return;
    }
    
    this.confirmService.open('Are you sure you want to unapprove this timesheet?').result.then(confirmed => {
      this.isUnapproving.set(true);
      
      this.mechanicsApprovalsService.Unapprove(approval.Id, userLevel).subscribe({
        next: (result) => {
          this.toastrService.success("Timesheet unapproved successfully");
          this.isUnapproving.set(false);
        },
        error: (error) => {
          console.error("Error unapproving timesheet:", error);
          this.toastrService.error("Error unapproving timesheet");
          this.isUnapproving.set(false);
        }
      });
    });
  }
  
  onReject(): void {
    // Placeholder for reject functionality if needed
    // Would follow same pattern as approve/unapprove
    this.toastrService.info("Reject functionality not yet implemented");
  }
  
  onDelete(): void {
    const approval = this.approval();
    
    if (!approval) return;
    
    this.confirmService.open('Are you sure you want to delete this approval? This action cannot be undone.').result.then(confirmed => {
      this.isDeleting.set(true);
      
      this.mechanicsApprovalsService.RemoveApproval(approval.Id).subscribe({
        next: () => {
          this.toastrService.success("Approval deleted successfully");
          this.isDeleting.set(false);
        },
        error: (error) => {
          console.error("Error deleting approval:", error);
          this.toastrService.error("Error deleting approval");
          this.isDeleting.set(false);
        }
      });
    });
  }
}