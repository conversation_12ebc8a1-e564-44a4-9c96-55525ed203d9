<!-- page header -->
<header class="bg-light p-3 p-lg-4">
	<div class="container">
		<!-- page title -->
		<h1 class="page-title fs-5">Approve Time Sheets - Mechanics</h1>
		<div>
			<!--filters -->
		
				<div class="row">
					<!-- text search filter -->
					<div class="col-12 col-md-6 col-lg-4 mb-3">
						<form>
							<label for="" class="form-label">Text Search</label>
							<div class="input-group">
								<span class="input-group-text" id="basic-addon1"><i class="fas fa-search"></i></span>
								<input type="text" class="form-control" [formControl]="searchControl" name="txtSearch"
									placeholder="Search by employee name." />
							</div>
						</form>						
					</div>
					<!-- approval status filter -->
					<div class="col-12 col-md-6 col-lg-4 mb-3">
						<label for="" class="form-label">Select Approval Status</label>
						<select name="selectApprovalLevel" class="form-select"
							[(ngModel)]='selectedApprovalLevel'
							(ngModelChange)="filterApprovals($event)">
							@for (grp of approvalGroups; track $index) {
							<optgroup label="{{grp.group}}">
								@for(item of grp.items; track $index){
								<option [value]="item.id">{{item.name}}</option>
								}
							</optgroup>
							}
						</select>
					</div>
					<!-- calendar picker -->
					<div class="col-12 col-md-6 col-lg-4 mb-3">
						<label for="" class="form-label">Select Date or Date Range</label>
						<!--date inputs-->
						<div>
							<!--single day input-->
							@if(dateType() === 'SingleDay'){
								<p-datepicker [style]="{'width':'100%'}" [inputStyle]="{'width':'100%'}" size="small" [showIcon]="true"
								  	(ngModelChange)="onDateInputChange($event)" 
									[(ngModel)]="singleDateInfo" [showButtonBar]="true" (onSelect)="onSingleDateSelection($event)"  (onClearClick)="clearDate()" />
							}
							<!--range of days input-->
							@if(dateType() === 'RangeOfDays'){								
								<p-datepicker size="small" [showButtonBar]="true" [style]="{'width':'100%'}" [inputStyle]="{'width':'100%'}"
									[showIcon]="true" [(ngModel)]="rangeDatesInfo" selectionMode="range" [numberOfMonths]="2" [readonlyInput]="true"
									(onSelect)="onRangeDateSelection($event)"  (onClearClick)="clearDate()"  />
							}
						</div>
					</div>
					<!-- select calendar type -->
					<div class="col-12 col-md-6 col-lg-4 mb-3">
						<label for="" class="form-label">Calendar Type</label>
						<select name="calendarDateType" class="form-select"
							(ngModelChange)="setDateFilter($event)" [(ngModel)]="dateType">
							<option value="SingleDay">
								Single Calendar
							</option>
							<option value="RangeOfDays">
								Range Calendar
							</option>
						</select>
					</div>

					<!-- roles filter -->
					<div class="col-12 col-md-6 col-lg-4 mb-3">
						<label for="" class="form-label">Roles</label>
						<div>
							<app-roles-drop-down></app-roles-drop-down>
						</div>
					</div>
				</div>
		</div>
	</div>
</header>

<!--time-sheets -->
<section class="p-3 p-lg-4">
	<div class="container">
		<div class="mb-4">
			<!--sort + expand-all + payroll-tools-->
			<div class="row mb-3">
				<div class="col-12 col-md-auto mb-4 mb-md-0">
					<div><label>Sort</label></div>
					<div>
						<button type="button" class="btn btn-outline-dark" data-bs-toggle="dropdown"
							aria-haspopup="true" aria-expanded="false" id="dropdownMenuButton1">
							{{ sortName() }}
							<i class="fas ms-1"
								[ngClass]="{ 'fa-sort-down ': sortOrder() === 0, 'fa-sort-up': sortOrder() === 1 }"></i>
						</button>
			
						<ul class="dropdown-menu dropdown-menu-start" aria-labelledby="dropdownMenuButton1">
							@for (item of sortDataItems; track $index) {
							<li (click)="sortData(item)">
								<a class="dropdown-item" href="javascript:void(0)"
									[ngClass]="{'active': sortName() === item.Name}">
									{{ item.Name }}
									<i class="fas ms-1" [ngClass]="{ 
												'fa-sort-down ': sortOrder() === 0 && sortName() === item.Name, 
												'fa-sort-up': sortOrder() === 1 && sortName() === item.Name }">
									</i>
								</a>
							</li>
							}
						</ul>
					</div>
				</div>
				<div class="col-12 col-md-auto mb-4 mb-md-0">
					<div><label>View</label></div>
					<div>
						<button type="button" class="btn btn-outline-dark me-1" (click)="expand();">
							@if(expandAllApprovals()){
								<span>Close All</span>
							}@else {
								<span>Expand All</span>
							}				
						</button>
					</div>
				</div>
				<div class="col-12 col-md-auto mb-4 mb-md-0">
					@if(isPayroll()){
						<div><label>Payroll Tools</label></div>
						
						<div class="btn-group">
							<button type="button" class="btn btn-outline-dark" (click)="selectAll()"
								[disabled]="isApproving()">Select All</button>
								@if(hasSelectedApprovals()){
								<button type="button" class="btn btn-outline-dark" (click)="unSelectAll()"
									[disabled]="isApproving()">Unselect All</button>
								}
							<button type="button" class="btn btn-outline-dark" (click)="approveSelected()"
								[disabled]="isApproving()">
								@if(isApproving()){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
								}
								Approve Selected
							</button>
						</div>
					}
				</div>
			</div>
			<div class="table-responsive">
				<table class="table table-bordered">
					<!--column-headers-->
					<thead>
						<tr>
							<th scope="col">Date</th>
							<th scope="col">Employee</th>
							<th scope="col">User ID</th>
							<th scope="col">Timesheet Status</th>
							@for (lvl of ApprovalLevels(); track $index) {
							<th scope="col">
								@if(lvl.Level < payrollLevel){ L{{ lvl.Level }} }@else if (lvl.Level>= payrollLevel) {
									Payroll
									}
							</th>
							}
						</tr>
					</thead>
					<!-- time sheets -->
					<tbody>
						@if(isApprovalsLoading()){
						<tr class="placeholder-glow" *ngFor="let lvl of [1,2,3,4,5,6,7,8,9]">
							<td>
								<span class="placeholder w-100"></span>
							</td>
							<td>
								<span class="placeholder w-100"></span>
							</td>
							<td>
								<span class="placeholder w-100"></span>
							</td>
							<td>
								<span class="placeholder w-100"></span>
							</td>
							<td *ngFor="let lvl of [1,2,3,4,5,6]">
								<span class="placeholder w-100"></span>
							</td>
						</tr>
						}@else {
						@for (approval of Approvals(); track $index) {
						<tr>
							<!-- date -->
							<td>
								{{ approval.TimesheetDate | date: 'M/dd/yyyy': 'GMT'}}
							</td>
							<!-- employee -->
							<td>
								{{ approval.EmployeeName }}
							</td>
							<!-- user id -->
							<td>
								<a class="text-decoration-none" href="javascript:void(0)"
									(click)="approval.ShowTimeCard = !approval.ShowTimeCard">
									<div>{{ approval.UserId }}</div>
								</a>
							</td>
							<!-- status -->
							<td>
								<a class="text-decoration-none" href="javascript:void(0)"
									(click)="approval.ShowTimeCard = !approval.ShowTimeCard">
									<div>{{ approval.Approval?.Status || 'Pending' }}</div>
								</a>
							</td>
							<!-- approval level checks -->
							@for (item of approval.Approval?.ApprovalLevels | ccOrderBy: 'Level'; track $index) {
							<td>
								@if(item.UserApproved && item.Level === $index + 1){
								<div class="d-flex d-flex flex-column">
									<i class="far fa-check-circle fa-lg text-success mb-1"></i>
									<span class="text-secondary small">{{ item.ApprovedDate | date: 'M/dd/yyyy' }} -
										{{ item.UserApproved.FirstName[0] }}{{ item.UserApproved.LastName[0] }}</span>
								</div>
								}
								@if(item.Level === payrollLevel){
								<div class="align-items-center justify-content-center">
									@if(item.UserApproved){
									<div>
										<i class="far fa-check-circle text-success mb-1 me-2"></i>
										<span class="text-secondary small">{{ item.ApprovedDate | date: 'M/dd/yyyy' }} -
											{{ item.UserApproved.FirstName[0] }}{{ item.UserApproved.LastName[0] }}
										</span>
									</div>
									}
									@if (!approval.Approval?.FinalApprovalDate && isPayroll()) {
									<input class="form-check-input" type="checkbox" [checked]="approval.IsSelected" [disabled]="isApproving()"
										(change)="selectApproval(approval)" id="flexCheckDefault" />
									}
								</div>
								}
							</td>
							}
						</tr>
						@if(approval.ShowTimeCard){
						<tr>
							<td class="p-0" colspan="10">
								<app-mechanics-timesheet-detail [approval]="approval.Approval" />
							</td>
						</tr>
						}
						}@empty {
						<tr>
							<td colspan="10">
								<div class="alert alert-info mb-0" role="alert">
									No approvals found.
								</div>
							</td>
						</tr>
						}
						}
					</tbody>
				</table>
			</div>
			<!--pagination-->
			@if(total() > limit()){
			<ngb-pagination class="d-flex justify-content-end my-2" [(page)]="currentPage"
				[pageSize]="limit()" [maxSize]="5" [boundaryLinks]="true" [collectionSize]="total()"
				[boundaryLinks]="true" (pageChange)="changeApprovalListPage($event)">
				<ng-template ngbPaginationPrevious>Prev</ng-template>
				<ng-template ngbPaginationNext>Next</ng-template>
				<ng-template ngbPaginationFirst>First</ng-template>
				<ng-template ngbPaginationLast>Last</ng-template>
			</ngb-pagination>
			}
		</div>
	</div>
</section> 
