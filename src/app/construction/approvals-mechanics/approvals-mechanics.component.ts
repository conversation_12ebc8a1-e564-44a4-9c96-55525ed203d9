import { Component, OnInit, computed, effect, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { ActivatedRoute, Router } from '@angular/router';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { ToastrService } from 'ngx-toastr';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { AccessEffects } from '../../shared/interfaces/access';
import { Subscription, debounceTime, forkJoin, Observable } from 'rxjs';
import { DatePickerModule } from 'primeng/datepicker';
import { RolesDropDownComponent } from 'src/app/shared/ui/role-drop-down/roles-drop-down.component';
import { MechanicsApprovalsService } from '../shared/data-access/mechanics-approvals.service';
import { MechanicsApproval, MechanicsApprovalInfo } from '../shared/interfaces/mechanics-approval';
import { OrderPipe } from 'src/app/shared/utils/pipes/order-by-pipe';
import { MechanicsTimesheetDetailComponent } from './ui/mechanics-timesheet-detail/mechanics-timesheet-detail.component';

@Component({
  selector: 'app-approvals-mechanics',
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RolesDropDownComponent,
    DatePickerModule, NgbPaginationModule, OrderPipe, MechanicsTimesheetDetailComponent],
  standalone: true,
  templateUrl: './approvals-mechanics.component.html',
  styleUrl: './approvals-mechanics.component.css'
})
export class ApprovalsMechanicsComponent implements OnInit {
  momentFormatter = inject(MomentDateFormatterService);
  mechanicsApprovalsService = inject(MechanicsApprovalsService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  accessService = inject(AccessService);
  toastrService = inject(ToastrService);
  confirmService = inject(ConfirmService);
  
  // Match ApprovalComponent signal structure
  hasTeamProjects = signal<boolean>(true); // Not used for mechanics but keep for consistency
  ApprovalLevels = this.mechanicsApprovalsService.userApprovalLevels;
  access: string = AccessEffects.ReadOnly;
  searchControl = new UntypedFormControl();
  payrollLevel: number = this.mechanicsApprovalsService.payrollLevel;
  isApprovalsLoading = computed(() => this.mechanicsApprovalsService.isLoading());
  isApproving = signal<boolean>(false);
  
  // Add missing template properties
  canApprove = computed(() => {
    return this.userApprovalLevel() !== null && this.userApprovalLevel() !== undefined;
  });
  
  approvals = computed(() => this.mechanicsApprovalsService.approvals());
  
  // Match ApprovalComponent sort structure
  sortDataItems: Array<any> = [
    {
      Name: 'Date',
      Sort: 0
    },
    {
      Name: 'Employee', // User-based equivalent of 'Foreman'
      Sort: 0
    },
    {
      Name: 'Status', // Mechanics-specific instead of 'ProjectId'
      Sort: 0
    }
  ];

  // Match ApprovalComponent structure exactly
  approvalGroups: Array<any> = new Array<any>();
  isPayroll = computed(() => { return this.mechanicsApprovalsService.userApprovalLevel() === 999 });
  
  filterSubscription: Subscription | null = new Subscription();
  
  receivedApprovalLevels = false;
  sortName = this.mechanicsApprovalsService.sortName;
  sortOrder = this.mechanicsApprovalsService.sort;
  currentPage = this.mechanicsApprovalsService.currentPage;
  singleDate = this.mechanicsApprovalsService.singleDate;
  startDate = this.mechanicsApprovalsService.startDate;
  endDate = this.mechanicsApprovalsService.endDate;
  userApprovalLevel = this.mechanicsApprovalsService.userApprovalLevel;
  selectedApprovalLevel = signal<string>('unapproved');
  currentType = signal<string>('unapproved');
  currentFilter = signal<string>('Pending');
  dateType = signal<string>('SingleDay');
  expandAllApprovals = this.mechanicsApprovalsService.expandAllApprovals;
  rangeDatesInfo: Date[] = [];
  singleDateInfo: Date | undefined;
  total = this.mechanicsApprovalsService.approvalsTotal;
  limit = this.mechanicsApprovalsService.limit;
  
  // Match project-based pattern exactly - direct writable signal
  Approvals = signal<Array<MechanicsApprovalInfo>>([]);
  
  // Effect to sync with service data while preserving selection state
  private syncApprovalsEffect = effect(() => {
    const serviceApprovals = this.mechanicsApprovalsService.approvals();
    const currentApprovals = this.Approvals();
    
    const updatedApprovals = serviceApprovals.map(approval => {
      // Find existing approval to preserve selection state
      const existing = currentApprovals.find(a => a.Approval?.Id === approval.Id);
      return {
        Approval: approval,
        EmployeeName: approval.EmployeeName || '',
        TimesheetDate: approval.TimesheetDate || '',
        UserId: approval.EmployeeUserId || '',
        IsSelected: existing?.IsSelected || false, // Preserve selection state
        ShowTimeCard: this.expandAllApprovals()
      } as MechanicsApprovalInfo;
    });
    
    // Update signal directly like project-based pattern
    this.Approvals.set(updatedApprovals);
  });
  
  hasSelectedApprovals = computed(() => {
    let selectedApprovals = this.Approvals().filter(x => x.IsSelected === true);
    return selectedApprovals.length > 0;
  });

  constructor() {
    // Match ApprovalComponent initialization pattern
    this.mechanicsApprovalsService.initializeApprovals.set(true);
    this.mechanicsApprovalsService.initializeApprovalAccess();
    this.mechanicsApprovalsService.initializeUserApprovalLevels();
    this.mechanicsApprovalsService.initializeUserApprovalLevel.set(true);

    // Use computed signal instead of manual effect - following Angular 19 patterns
    // Remove manual signal setting in favor of computed signal

    // Match ApprovalComponent search pattern
    this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(value => {
      let searchValue = value?.trim() ?? '';
      if (!searchValue) {
        searchValue = null;
      }

      this.router.navigate([], {
        queryParams: { search: searchValue, page: 1 },
        queryParamsHandling: 'merge'
      });
    });

    // Match project-based query params pattern exactly (excluding project-specific params)
    this.aRoute.queryParams.subscribe(params => {
      if (params["approval-level"]) {
        this.mechanicsApprovalsService.approvalLevel.set(parseInt(params["approval-level"]));
      } else {
        this.mechanicsApprovalsService.approvalLevel.set(this.userApprovalLevel() ?? -1);
      }

      if (params["approval-type"]) {
        this.mechanicsApprovalsService.approvalType.set(params["approval-type"]);
      } else {
        this.mechanicsApprovalsService.approvalType.set("unapproved");
      }

      if (params["approval-type"] && params["approval-level"]) {
        if (params["approval-type"] === "all") {
          this.selectedApprovalLevel.set(params["approval-type"]);
        } else {
          this.selectedApprovalLevel.set(params["approval-type"] + params["approval-level"]);
        }
      } else {
        if (this.userApprovalLevel()) {
          this.selectedApprovalLevel.set("unapproved" + this.userApprovalLevel());
        }
      }

      if (params["currentFilter"]) {
        this.currentFilter.set(params["currentFilter"]);
      } else {
        this.currentFilter.set("Pending");
      }

      if (params["sortOrder"]) {
        this.mechanicsApprovalsService.sort.set(parseInt(params["sortOrder"]));
      } else {
        this.mechanicsApprovalsService.sort.set(0);
      }

      if (params["sortName"]) {
        this.mechanicsApprovalsService.sortName.set(params["sortName"]);
      } else {
        this.mechanicsApprovalsService.sortName.set("Date");
      }

      if (params["search"]) {
        this.mechanicsApprovalsService.search.set(params["search"]);
        this.searchControl.setValue(params["search"], { emitEvent: false });
      } else {
        this.mechanicsApprovalsService.search.set(null);
        this.searchControl.setValue(null, { emitEvent: false });
      }

      if (params["page"]) {
        this.mechanicsApprovalsService.currentPage.set(parseInt(params["page"]));
      } else {
        this.mechanicsApprovalsService.currentPage.set(1);
      }

      if (params["datetype"]) {
        this.dateType.set(params["datetype"]);
      } else {
        this.dateType.set("SingleDay");
      }

      if (params["role"]) {
        this.mechanicsApprovalsService.roleId.set(params["role"]);
      } else {
        this.mechanicsApprovalsService.roleId.set(null);
      }

      if (params["startDate"] && params["endDate"]) {
        this.mechanicsApprovalsService.startDate.set(new Date(params["startDate"]));
        this.mechanicsApprovalsService.endDate.set(new Date(params["endDate"]));

        if (params["datetype"] === "SingleDay") {
          this.singleDateInfo = new Date(params["startDate"]);
        } else if (params["datetype"] === "RangeOfDays") {
          this.rangeDatesInfo = [new Date(params["startDate"]), new Date(params["endDate"])];
        }
      } else {
        this.mechanicsApprovalsService.startDate.set(null);
        this.mechanicsApprovalsService.endDate.set(null);
      }
    });

    // Match ApprovalComponent user approval level effect
    effect(() => {
      if (this.userApprovalLevel()) {
        this.setupApprovalLevels(this.userApprovalLevel() ?? -1);
        this.selectedApprovalLevel.set('unapproved' + this.userApprovalLevel());
        this.mechanicsApprovalsService.approvalLevel.set(this.userApprovalLevel() ?? -1);
      }
    });

    // Match ApprovalComponent expand all effect
    effect(() => {
      if (this.expandAllApprovals()) {
        for (const item of this.Approvals()) {
          item.ShowTimeCard = true;
        }
      } else {
        for (const item of this.Approvals()) {
          item.ShowTimeCard = false;
        }
      }
    });
  }

  ngOnInit() {
    // Component initialization
  }

  onDateInputChange(value: any) {
    if (this.isValidDate(value)) {
      this.onSingleDateSelection(new Date(value));
    }
  }

  isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  onSingleDateSelection(date: Date) {
    this.mechanicsApprovalsService.setSingleDate(date);
    // Match project-based pattern: handle URL params in component
    this.router.navigate([], {
      queryParams: { page: 1, startDate: this.formatDate(date), endDate: this.formatDate(date) },
      queryParamsHandling: 'merge'
    });
  }

  onRangeDateSelection(date: Date) {
    if (this.rangeDatesInfo.length === 2) {
      if (this.rangeDatesInfo[0] && this.rangeDatesInfo[1]) {
        this.mechanicsApprovalsService.setDateRange(this.rangeDatesInfo[0], this.rangeDatesInfo[1]);
        // Match project-based pattern: handle URL params in component
        this.router.navigate([], {
          queryParams: { startDate: this.formatDate(this.rangeDatesInfo[0]), endDate: this.formatDate(this.rangeDatesInfo[1]), page: 1 },
          queryParamsHandling: 'merge'
        });
      }
    }
  }

  // formatDate method moved to service

  // Match ApprovalComponent filter pattern
  filterApprovals(approvalLevelId: any) {
    var approvalInfo: [string, number] | null = null;
    for (let group of this.approvalGroups) {
      if (approvalInfo !== null) {
        break;
      }

      for (let item of group.items) {
        if (item.id === approvalLevelId) {

          this.router.navigate([], {
            queryParams: { "approval-type": group.groupId, "approval-level": item.level, page: 1 },
            queryParamsHandling: 'merge'
          });
          break;
        }
      }
    }
  }

  // Match ApprovalComponent setupApprovalLevels pattern but adapted for mechanics
  setupApprovalLevels(level: number) {
    this.approvalGroups = [
      {
        group: "Show me all timesheets...",
        groupId: "all",
        items: [
          {
            name: "All Timesheets",
            id: "all",
            level: 999
          }
        ]
      },
      {
        group: "Unapproved",
        groupId: "unapproved",
        items: [
          {
            name: "Unapproved at L1",
            id: "unapproved1",
            level: 1
          },
          {
            name: "Unapproved at L2",
            id: "unapproved2",
            level: 2
          },
          {
            name: "Unapproved at L3",
            id: "unapproved3",
            level: 3
          },
          {
            name: "Unapproved at L4",
            id: "unapproved4",
            level: 4
          },
          {
            name: "Unapproved at L5",
            id: "unapproved5",
            level: 5
          },
          {
            name: "Unapproved at Payroll",
            id: "unapproved999",
            level: 999
          }
        ]
      },
      {
        group: "Approved",
        groupId: "approved",
        items: [
          {
            name: "Approved at L1",
            id: "approved1",
            level: 1
          },
          {
            name: "Approved at L2",
            id: "approved2",
            level: 2
          },
          {
            name: "Approved at L3",
            id: "approved3",
            level: 3
          },
          {
            name: "Approved at L4",
            id: "approved4",
            level: 4
          },
          {
            name: "Approved at L5",
            id: "approved5",
            level: 5
          },
          {
            name: "Approved at Payroll",
            id: "approved999",
            level: 999
          }
        ]
      }
    ];

    // Match ApprovalComponent level marking pattern
    for (let group of this.approvalGroups) {
      for (let item of group.items) {
        if (item.id != "all") {
          if (item.level === level) {
            item.name += " (My Level)";
          }
        }
      }
    }
  }

  setDateFilter(item: string) {
    this.rangeDatesInfo = [];
    this.singleDateInfo = undefined;
    this.dateType.set(item);
    this.mechanicsApprovalsService.clearDate();
    // Match project-based pattern: handle URL params in component
    this.router.navigate([], {
      queryParams: { datetype: item, page: 1, startDate: null, endDate: null, singleDate: null },
      queryParamsHandling: 'merge'
    });
  }

  expand() {
    this.mechanicsApprovalsService.toggleExpandAll();
  }

  sortData(sort: any) {
    this.mechanicsApprovalsService.setSort(sort.Name);
    // Match project-based pattern: handle URL params in component
    const currentSort = this.sortOrder();
    const newSort = this.sortName() === sort.Name ? (currentSort === 0 ? 1 : 0) : 0;
    this.router.navigate([], {
      queryParams: { sortOrder: newSort, sortName: sort.Name, page: 1 },
      queryParamsHandling: 'merge'
    });
  }

  selectAll() {
    // Match project-based pattern exactly
    this.Approvals.update(approvals => {
      for (const item of approvals) {
        if (item.Approval?.FinalApprovalDate === null) {
          item.IsSelected = true;
        }
      }
      return [...approvals];
    });
  }

  unSelectAll() {
    // Match project-based pattern exactly
    this.Approvals.update(approvals => {
      for (const item of approvals) {
        if (item.Approval?.FinalApprovalDate === null) {
          item.IsSelected = false;
        }
      }
      return [...approvals];
    });
  }

  selectApproval(approval: MechanicsApprovalInfo) {
    // Match project-based pattern exactly
    this.Approvals.update(approvals => {
      return approvals.map(a => {
        if (a.Approval?.Id === approval.Approval?.Id && a.Approval.FinalApprovalDate === null) {
          return { ...a, IsSelected: !a.IsSelected };
        }
        return a;
      });
    });
  }

  changeApprovalListPage(pageNum: any) {
    this.router.navigate([], {
      queryParams: { page: pageNum },
      queryParamsHandling: 'merge'
    });
  }

  // Match project-based component pattern for bulk approval
  approveSelected() {
    if (!this.userApprovalLevel()) {
      this.toastrService.error("You do not have the correct level to approve time sheets.");
      return;
    }
    this.isApproving.set(true);
    const approvals = Array<Observable<any>>();

    var selectedApprovals = new Array<MechanicsApprovalInfo>();
    for (const a of this.Approvals()) {
      if (a.Approval) {
        if (a.IsSelected) {
          selectedApprovals.push(Object.assign({}, a));
          approvals.push(this.mechanicsApprovalsService.Approve(a.Approval.Id, this.userApprovalLevel() ?? -1));
        }
      }
    }

    if (approvals.length > 0) {
      forkJoin(approvals).subscribe({
        next: (result) => {
          this.isApproving.set(false);
          for (let approval of selectedApprovals) {
            if (approval.Approval) {
              this.removeApproval(approval.Approval);
            }
          }
        },
        error: (err) => {
          console.log(err);
          this.toastrService.error("Error approving time sheets");
          this.isApproving.set(false);
        }
      });
    }
  }

  // Match project-based pattern exactly
  removeApproval(approval: MechanicsApproval) {
    if (approval) {
      this.Approvals.update(approvals => approvals.filter(x => x.Approval?.Id !== approval.Id));
    }
  }

  updateApproval(approval: any) {
    // Placeholder for future enhancement
  }

  // Match ApprovalComponent date methods
  formatDate(date: Date): string {
    if (!date)
      return '';
    return `${date.getMonth() + 1}-${date.getDate()}-${date.getFullYear()}`;
  }

  clearDate() {
    this.router.navigate([], {
      queryParams: { startDate: null, endDate: null, page: 1 },
      queryParamsHandling: 'merge'
    });
  }

  approve(approval: MechanicsApproval) {
    if (!this.userApprovalLevel()) {
      this.toastrService.error("You do not have the correct level to approve time sheets.");
      return;
    }
    
    this.confirmService.open('Do you want to approve this time sheet?').result.then(item => {
      if (item === 'yes') {
        this.mechanicsApprovalsService.Approve(approval.Id, this.userApprovalLevel() ?? -1).subscribe({
          next: (result) => {
            this.removeApproval(approval);
          },
          error: (err) => {
            console.log(err);
            this.toastrService.error("Error approving time sheet");
          }
        });
      }
    });
  }

  // Match ApprovalComponent unapprove pattern
  unapprove(approval: MechanicsApproval) {
    if (!this.userApprovalLevel()) {
      this.toastrService.error("You do not have the correct level to unapprove time sheets.");
      return;
    }

    this.confirmService.open('Do you want to unpprove this time sheet?').result.then(item => {
      this.mechanicsApprovalsService.Unapprove(approval.Id, this.userApprovalLevel() ?? -1).subscribe({
        next: (result) => {
          this.toastrService.success("Time sheet unapproved successfully");
        },
        error: (error) => {
          console.error("Error unapproving time sheet:", error);
          this.toastrService.error("Error unapproving time sheet");
        }
      });
    });
  }
}