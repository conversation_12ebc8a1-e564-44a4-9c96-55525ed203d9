/* Mechanics Approvals Component Styles */

.page-title {
  margin-bottom: 0;
  font-weight: 600;
}

.table th {
  font-weight: 600;
  background-color: var(--bs-dark);
  color: white;
  border-color: var(--bs-dark);
}

.table th[style*="cursor: pointer"]:hover {
  background-color: var(--bs-gray-800);
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--bs-border-radius);
  border-bottom-left-radius: var(--bs-border-radius);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--bs-border-radius);
  border-bottom-right-radius: var(--bs-border-radius);
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: rgba(0, 0, 0, 0.025);
}

.table-hover > tbody > tr:hover > td {
  background-color: rgba(0, 0, 0, 0.075);
}

.bg-light {
  background-color: #f8f9fa !important;
}

.form-check-input:checked {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.badge {
  font-size: 0.75em;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.text-muted {
  color: #6c757d !important;
}

.card-body {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
}

.container-fluid {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (max-width: 768px) {
  .btn-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-toolbar .btn-group {
    margin-bottom: 0.5rem;
    margin-right: 0 !important;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
}