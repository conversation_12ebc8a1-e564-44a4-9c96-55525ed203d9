import { Component, Input, OnChanges, SimpleChanges, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionPhotosService } from '../../shared/data-access/photos.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { of, switchMap, tap, mergeMap, toArray, Observable } from 'rxjs';
import { GalleryImage, Photo, PhotoStorage, PhotoUserInfo } from '../../shared/interfaces/photos';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Dictionary, groupBy as GB, orderBy} from 'lodash';
import { DailyLogPhotoGalleryComponent } from '../daily-log/ui/daily-log-photos/ui/daily-log-photo-gallery/daily-log-photo-gallery.component';
import { DailyLogPhotosService } from '../daily-log/shared/services/daily-log-photos.service';
import { UserProfile } from 'src/app/shared/interfaces/user';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { ComponentView } from '../../shared/interfaces/project-components';


@Component({
    selector: 'app-photos',
    imports: [CommonModule, DailyLogPhotoGalleryComponent],
    templateUrl: './photos.component.html',
    styleUrl: './photos.component.css',
    providers: [DailyLogPhotosService, ConstructionPhotosService]
})
export class PhotosComponent implements OnChanges {

  @Input() projectId: string = '';
  sanitizer = inject(DomSanitizer);
  aRoute = inject(ActivatedRoute);
  userService = inject(AccountService);
  projectIdLocal = signal<string>("");
  isLoading = signal<boolean>(false);
  photosService = inject(ConstructionPhotosService);
  dailyLogPhotosService = inject(DailyLogPhotosService);
  userGalleryImages = signal<Array<PhotoPageGalleryInfo>>([]);
  groupByValue = signal<GroupByPhotoOptions>(GroupByPhotoOptions.None);
  defatul = [];
  photoInfos = signal<Array<GroupedPhotos>>([]);
  public readonly PHOTO_OPTIONS: typeof GroupByPhotoOptions = GroupByPhotoOptions;

  constructor() {
    const params = this.aRoute.parent?.snapshot.paramMap;  
    if(params){
      var projectId = params.get("projectId");
      if(projectId){
        this.projectId = projectId;  
        this.projectIdLocal.set(this.projectId);
        
        // Initialize the DailyLogPhotosService with the project ID
        this.dailyLogPhotosService.projectId.set(this.projectId);
        this.photosService.projectId.set(this.projectId);
        this.dailyLogPhotosService.setInitialData(null, ComponentView.ReadOnly, 'Allow');
      }  
    }   
  }
  ngOnChanges(changes: SimpleChanges): void {
    if(changes['projectId'] && changes['projectId'].currentValue !== changes['projectId'].previousValue){
      this.projectIdLocal.set(this.projectId);
      
      // Initialize the DailyLogPhotosService with the project ID
      this.dailyLogPhotosService.projectId.set(this.projectId);
      this.dailyLogPhotosService.setInitialData(null, ComponentView.ReadOnly, 'Allow');
    }
  }

  photosReadOnly = toSignal(toObservable(this.projectIdLocal).pipe(
    tap((projectId) => {
      this.isLoading.set(true);
    }),
    switchMap((projectId) => (projectId) ? this.photosService.getPhotosFromDailyLogs() : of(null)),
    tap((photosInfos) => {
      if(photosInfos){
        let galleryInfo = new Array<Observable<PhotoPageGalleryInfo>>();
        for (let comp of photosInfos) {
          galleryInfo.push(this.gatherPhotosFromComp(comp));  
        }

        of(...galleryInfo).pipe(mergeMap(result=> result),toArray()).subscribe({
          next: (info) =>          
          {
            this.userGalleryImages.set(info);
            this.changeGroup(GroupByPhotoOptions.User);
            this.isLoading.set(false);
          },
          error: (err) => {
            console.log(err);
            this.isLoading.set(false);
          }
        });

     
      }
    })
  ));

  changeGroup(groupBy: GroupByPhotoOptions){ 
    this.groupByValue.set(groupBy);
  }

  groupByDataReadonly = toSignal(toObservable(this.groupByValue).pipe(
    tap((groupByData) => {
      if (this.userGalleryImages().length > 0) {
        var galleryInfoData = orderBy(this.userGalleryImages(), ["DailyLogTimeStamp"],['asc']);
        var finalData = {} as Dictionary<PhotoPageGalleryInfo[]>;
        if(groupByData === GroupByPhotoOptions.Date){
          finalData = GB(galleryInfoData, x => x.DailyLogTimeStamp);
        }else if(groupByData === GroupByPhotoOptions.User){
          finalData = GB(galleryInfoData, x => x.UserId); 
        }        
        var userGroupedPhotos = new Array<GroupedPhotos>();

        Object.entries(finalData).forEach(([key, value]) => {
          userGroupedPhotos.push({Key: key, GalleryInfo: value});
        });

        this.photoInfos.set(userGroupedPhotos);  
      }
    })));

  gatherPhotosFromComp(photoComp: PhotoUserInfo) : Observable<PhotoPageGalleryInfo> {
    return new Observable(obs => {
      var galleryInfo = {} as PhotoPageGalleryInfo;
      galleryInfo.UserId = photoComp.UserId;
      galleryInfo.DailyLogTimeStamp = photoComp.DailyLogTimeStamp;
      galleryInfo.Photos = new Array<GalleryImage>();
      if (photoComp.Photos) {

        for (let p of photoComp.Photos) {
          var gPhoto = this.convertAndAddPhoto(p);
          galleryInfo.Photos.push(gPhoto);
        }
      }

      this.userService.GetProfile(photoComp.UserId).subscribe({
        next: (profile) => {
          if(profile){
            galleryInfo.UserInfo = profile;
          }

          obs.next(galleryInfo);
          obs.complete();
        },
        error: (err) => {
          console.log(err);
        }
      });
    });
  }
  convertAndAddPhoto(photo: Photo) {
    var info = {} as GalleryImage; 
    var awsThumbnailStorage = photo.ThumbnailStorage as PhotoStorage;
    var awsLargeStorage = photo.LargeStorage as PhotoStorage;

    if(awsThumbnailStorage.Location){
      info.ThumbUrl = `https://${photo.ThumbnailStorage.Bucket}.s3.amazonaws.com/${photo.ThumbnailStorage.Key}`;
      info.ThumbSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`https://${photo.ThumbnailStorage.Bucket}.s3.amazonaws.com/${photo.ThumbnailStorage.Key}`);  
    }    

    if(awsLargeStorage.Location){
      info.LargeUrl = `https://${photo.LargeStorage.Bucket}.s3.amazonaws.com/${photo.LargeStorage.Key}`;
      info.LargeSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`https://${photo.LargeStorage.Bucket}.s3.amazonaws.com/${photo.LargeStorage.Key}`);
    }

    info.Id = photo.PhotoId;
    info.Title = photo.Title as string;
    info.Description = photo.Description as string;
    info.UploadDate = photo.UploadDate as Date;
    
    return info;
  }
}


export enum GroupByPhotoOptions{
  None = "None",
  User = "UserId",
  Date = "DateTimeInfo"
}

export interface PhotoPageGalleryInfo{
  UserId:string;
  UserInfo: UserProfile;
  Photos: Array<GalleryImage>;
  DailyLogTimeStamp: Date;
}

export interface GroupedPhotos{
  Key: string;
  GalleryInfo: Array<PhotoPageGalleryInfo>;
}
