<!--page-title-->
<h2 class="page-title fs-6">Photos</h2>
	@if(isLoading()){
	<!--place holder-->
	<div class="placeholder-glow" *ngIf="isLoading">
		<div class="col-12 mb-5">
			<div class="d-flex flex-column">
				<div class="fs-5 fw-bold border-bottom p-2 mb-4">
					<span class="placeholder col-4"></span>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="row">
				<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2,3,4]; let i = index">
					<div class="row">
						<div class="mb-4">
							<div class="mb-4">
								<span class="placeholder" style="width: 75px;"></span>
							</div>
							<div>
								<span class="placeholder" style="height: 100px; width: 100%;"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2]; let i = index">
					<div class="row">
						<div class="mb-4">
							<div class="mb-4">
								<span class="placeholder" style="width: 75px;"></span>
							</div>
							<div>
								<span class="placeholder" style="height: 100px; width: 100%;"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2,3]; let i = index">
					<div class="row">
						<div class="mb-4">
							<div class="mb-4">
								<span class="placeholder" style="width: 75px;"></span>
							</div>
							<div>
								<span class="placeholder" style="height: 100px; width: 100%;"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2,3,4]; let i = index">
					<div class="row">
						<div class="mb-4">
							<div class="mb-4">
								<span class="placeholder" style="width: 75px;"></span>
							</div>
							<div>
								<span class="placeholder" style="height: 100px; width: 100%;"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	}@else {
	<!--instructions-->
	<div class="mb-2">
		<span>View photos from the diary.</span>
	</div>
	<!--buttons-->
	<div class="mb-2">
		<div class="btn-group" role="group" aria-label="Basic example">
			<button class="btn btn-outline-dark" (click)="changeGroup(PHOTO_OPTIONS.User)"
				[ngClass]="{ active: groupByValue() ===  PHOTO_OPTIONS.User}">Foreman/Date</button>
			<button class="btn btn-outline-dark" (click)="changeGroup(PHOTO_OPTIONS.Date)"
				[ngClass]="{ active: groupByValue ()=== PHOTO_OPTIONS.Date}">
				Date/Foreman
			</button>
		</div>
	</div>
	<!--date-foreman-->
	@if(groupByValue() === PHOTO_OPTIONS.User){
	<!--forman-date-->
	<div class="mb-5">
		@for (comp of photoInfos(); track $index) {
		<div class="d-flex flex-column">
			<div class="fs-6 fw-bold border-bottom text-muted p-2 mb-4">
				{{comp.GalleryInfo[0].UserInfo?.FirstName}} {{comp.GalleryInfo[0].UserInfo?.LastName}}
			</div>
			@for (galleryInfo of comp.GalleryInfo; track $index) {
			<div class="d-flex flex-column mb-4">
				<div class="mb-4">
					<span class="badge rounded-pill bg-info">{{ galleryInfo.DailyLogTimeStamp | date:
						'M/dd/yyyy'}}</span>
				</div>
				<app-daily-log-photo-gallery [galleryImages]="galleryInfo.Photos"></app-daily-log-photo-gallery>
			</div>
			}
		</div>
		}
	</div>
	}@else if(groupByValue() === PHOTO_OPTIONS.Date){
	<div class="mb-4">
		@for (comp of photoInfos(); track $index) {
		<div class="d-flex flex-column">
			<div class="fs-6 fw-bold border-bottom text-muted p-2 mb-4">
				{{ comp.Key | date: 'M/dd/yyyy' }}
			</div>
			@for (galleryInfo of comp.GalleryInfo; track $index) {
			<div class="d-flex flex-column mb-4">
				<div class="mb-4">
					<span class="badge rounded-pill bg-info">{{galleryInfo.UserInfo.FirstName}}
						{{galleryInfo.UserInfo.LastName}}</span>
				</div>
				<app-daily-log-photo-gallery [galleryImages]="galleryInfo.Photos"></app-daily-log-photo-gallery>
			</div>
			}
		</div>
		}
	</div>
	}
	}