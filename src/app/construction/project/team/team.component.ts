import { Component, Input, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterLink } from '@angular/router';
import { map, of, switchMap, tap } from 'rxjs';
import { TeamMemberInfo, TeamMember, TeamMemberComponentInfo } from '../../shared/interfaces/team';import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ConstructionTeamsService } from '../../shared/data-access/teams.service';
import { ConstructionProjectService } from '../../shared/data-access/project.service';
import { ConstructionProject } from '../../shared/interfaces/construction-project';
;

@Component({
    selector: 'app-team',
    imports: [CommonModule, RouterLink],
    templateUrl: './team.component.html',
    styleUrl: './team.component.css'
})
export class TeamComponent {
  teamService = inject(ConstructionTeamsService);
  projectService = inject(ConstructionProjectService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  // aUser: User = {} as User;
  // user: CMUser = {} as CMUser;
  teamComponent = signal<TeamMemberComponentInfo>({} as TeamMemberComponentInfo);
  teamPermissions = this.teamService.teamPermissions;
  projectId: string = "";
  projectIdInfo = signal<string>("");
  project = this.projectService.project;
  isLoading = signal<boolean>(false);
  getTeamComponentReadOnly = toSignal(toObservable(this.projectIdInfo).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap(projectId => projectId ? this.teamService.getTeamComponentWithMemberInfo(projectId) : of(null)),
    tap((teamComponentInfo) => {
      if(teamComponentInfo){       
        this.teamComponent.set(teamComponentInfo as TeamMemberComponentInfo);        
      }
      this.isLoading.set(false);
    })

  ));
  constructor()
  {  
    this.teamService.getTeamPermissions().subscribe();

    if(this.project()){
      let project = this.project() as ConstructionProject;
      this.projectId = project.Id;
      this.projectIdInfo.set(project.Id);
    }else{
      if(this.aRoute.parent?.parent?.snapshot.params){
        const {projectId} = this.aRoute.parent?.parent?.snapshot.params;
        if(projectId){
          this.projectId = projectId;
          this.projectIdInfo.set(projectId);
        }       
      }
    }    
        
    
  }

  ngOnInit() {
    // this.userService.getUser().subscribe(aUser => {
    //   this.aUser = aUser;
    // });   
  }



  addAll() {
    this.isLoading.set(true);

    var teamMembers: Array<TeamMember> = [];
    
    for (let teamMember of this.teamComponent().TeamMembers) {
      if(teamMember.DelegateInfo){
        var tm: TeamMember = {} as TeamMember;
        tm.UserId = teamMember.DelegateInfo?.UserId;
        tm.IsActive = true;
        teamMembers.push(tm);
      }
    }

    this.teamService.AddTeamMembers(this.projectId, teamMembers).subscribe(member => {
      for (let tm of this.teamComponent().TeamMembers) {
        tm.IsActive = true;
      }

      this.isLoading.set(false);
    });

  }

  removeAll() {
    this.isLoading.set(true);
    var teamMemberIds: Array<string> = [];
    for (let teamMember of this.teamComponent().TeamMembers) {
      if(teamMember.DelegateInfo){
        teamMemberIds.push(teamMember.DelegateInfo.UserId);
      }

    }

    this.teamService.RemoveTeamMembers(this.projectId, teamMemberIds).pipe(
      map(result => {
        for (let teamMember of this.teamComponent().TeamMembers) {
          teamMember.IsActive = false;
        }

        this.isLoading.set(false) ;
      })
    ).subscribe();
  }

  addTeamMember(teamMember: TeamMemberInfo) {
    var teamMembers: Array<TeamMember> = [];

    if(teamMember.DelegateInfo){
      var tm: TeamMember = {} as TeamMember;
      tm.UserId = teamMember.DelegateInfo.UserId;
      tm.IsActive = true;

      teamMembers.push(tm);
  
      this.teamService.AddTeamMembers(this.projectId, teamMembers).subscribe({
        next: () => {
          teamMember.IsActive = true;
        },
        error: (err) => {
          // Handle error
        }
      });
    }


  }

  editTeamMember(teamMember: TeamMemberInfo) {
    if(teamMember.DelegateInfo){
      this.router.navigate(['/user', 'project', this.projectId, 'permission', teamMember.DelegateInfo.UserId]);
    }    
  }

  removeTeamMember(idx: number, teamMember: TeamMemberInfo) {
    var teamMemberIds: Array<string> = [];
    if(teamMember.DelegateInfo){
      teamMemberIds.push(teamMember.DelegateInfo.UserId);
  
      this.teamService.RemoveTeamMembers(this.projectId, teamMemberIds).pipe(
        map(result => {
          teamMember.IsActive = false;
        })
      ).subscribe();
    }

  }
}
