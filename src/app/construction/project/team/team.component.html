<!--page-title-->
<h3 class="page-title fs-6">Team</h3>
<!--instructions-->
@if(teamPermissions()?.removeTeamMember === "Allow"){
<p>
	Select the people you want on this project team. Only team members can see this project.
</p>
}
<!--team-->
<div>
	@if(isLoading()){
	<div>
		<ul class="list-group">
			<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
				<div class="placeholder-glow">
					<div class="row">
						<div class="col-6">
							<span class="placeholder col-12" style="height:20px;"></span>
						</div>
					</div>
					<div class="row">
						<div class="col-4">
							<span class="placeholder col-12"></span>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</div>
	}@else {
	<ul class="list-group">
		<li class="list-group-item d-flex justify-content-end"
			*ngIf="teamPermissions()?.removeTeamMember === 'Allow'">
			<div class="btn-group">
				<button type="button" class="btn btn-outline-dark" (click)="addAll()">Add All</button>
				<button type="button" class="btn btn-outline-dark" (click)="removeAll()">Remove All</button>
			</div>
		</li>
		@for (member of teamComponent().TeamMembers; track $index) {
		<li class="list-group-item">

			<div class="d-flex justify-content-between align-items-center">
				<div>
					<div class="fw-bold">{{ member.AccountInfo?.Profile?.FirstName }} {{
						member.AccountInfo?.Profile?.LastName }}</div>
					<div class="text-secondary small">{{ member.DelegateInfo?.Title }}</div>
				</div>
				@if(teamPermissions()?.removeTeamMember === 'Allow'){
				<div>
					<button type="button" class="btn btn-primary" (click)="removeTeamMember(i, member)"
						*ngIf="member?.IsActive">
						<i class="fal fa-check-square fa-lg"></i>
					</button>
					<button type="button" class="btn btn-outline-dark" (click)="addTeamMember(member)"
						*ngIf="!member?.IsActive">
						<i class="fal fa-square fa-lg"></i>
					</button>
				</div>
				}@else if(teamPermissions()?.removeTeamMember === 'ReadOnly'){
				<div>
					@if(member?.IsActive){
					<i class="fal fa-check-square"></i>
					}@else {
					<i class="fal fa-square text-secondary"></i>
					}
				</div>
				}

			</div>
		</li>
		}@empty {
		<li class="list-group-item">
			<div>
				There are currently users to add to this team.
			</div>
			<div>
				<a href="javascript:void(0)" [routerLink]="['/account', 'users', 'settings', 'people']">Click here to add users.</a>
			</div>
		</li>
		}


	</ul>
	}
</div>