import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CostCodeQuantityReportComponent } from './ui/quantity-report/costcode-quantity-report.component';
import { DailyLogReportComponent } from './ui/dailylog-report/dailylog.report.component';
import { ActivatedRoute } from '@angular/router';
import { ConstructionReportsService } from '../../shared/data-access/reports.service';

@Component({
    selector: 'app-reports',
    imports: [CommonModule, CostCodeQuantityReportComponent, DailyLogReportComponent],
    templateUrl: './reports.component.html',
    styleUrl: './reports.component.css'
})
export class ReportsComponent {
  aRoute = inject(ActivatedRoute);
  reportsService = inject(ConstructionReportsService);
  projectId = signal<string>('');

  constructor() {
    this.reportsService.getReportPermissions().subscribe();

    this.aRoute.parent?.paramMap.subscribe({
      next: (params) => {
        var projectId = params.get("projectId");
        if(projectId){
          this.projectId.set(projectId);          
        }
      }
    });
  }
}