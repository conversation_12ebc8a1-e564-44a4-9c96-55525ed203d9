<div class="card">
    <div class="card-body">
        <header>
            <h3 class="page-title fs-6">Cost Code Quantity</h3>
        </header>
        @if(isLoading){
        <div style="height: 100px">
            <span class="placeholder col-12" style="height: 100%;"></span>
        </div>
        }@else {
        @if(reportPermissions()?.reportCostCodeQty === ACCESS_EFFECTS.Allow){
        <div class="row">
            <label for="exampleSelect1">Date Range</label>
            <div class="col-12 col-lg-4 mb-3 d-flex flex-row align-items-start"> 
                    <div class="position-absolute">
                        <input name="costCodeQtyDatepicker" class="dp-hidden" ngbDatepicker
                            #costCodeQtyDatepicker="ngbDatepicker" [autoClose]="'outside'"
                            (dateSelect)="onDateSelection($event)" [displayMonths]="3" [dayTemplate]="cc"
                            outsideDays="hidden" [startDate]="fromDate!" />
                        <ng-template #cc let-date let-focused="focused">
                            <span class="custom-day" [class.focused]="focused" [class.range]="isRange(date)"
                                [class.faded]="isHovered(date) || isInside(date)" (mouseenter)="hoveredDate = date"
                                (mouseleave)="hoveredDate = null">
                                {{ date.day }}
                            </span>
                        </ng-template>
                    </div>
                    <div class="input-group me-1">
                        <input #dpFromDate class="form-control" placeholder="Start Date" name="dpFromDate"
                            [value]="momentFormatter.format(dateFilterInfo.dateRangeStart!)"
                            (input)="fromDate = validateInput(dateFilterInfo.dateRangeStart, dpFromDate.value)"
                            readonly />
                    </div>
                    <div class="input-group me-1">
                        <input #dpToDate class="form-control" placeholder="End Date" name="dpToDate"
                            [value]="momentFormatter.format(dateFilterInfo.dateRangeEnd!)"
                            (input)="toDate = validateInput(dateFilterInfo.dateRangeEnd, dpToDate.value)"
                            readonly />
                    </div>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-secondary" (click)="costCodeQtyDatepicker.toggle()"
                            type="button">
                            <i class="fa fa-calendar"></i>
                        </button>
                        <button class="btn btn-outline-secondary" (click)="clearDateFilterRange()">
                            Clear
                        </button>
                    </div>
            </div>
            <div class="col-12 col-lg-4 mb-3">
                <div class="form-check ps-0">
                    <div class="input-group">
                        <span class="input-group-text">Options</span>
                        <select class="form-select" [(ngModel)]="onlyPhaseCodesQty">
                            <option value="yes">Include Only Cost Codes with Quantities</option>
                            <option value="no">Include All Cost Codes</option>
                        </select>
                    </div>
                    <!-- <label ngbButtonLabel>
                                <input class="me-1" type="checkbox" ngbButton [(ngModel)]="onlyPhaseCodesQty" />
                                <span *ngIf="onlyPhaseCodesQty">Yes</span>
                                <span *ngIf="!onlyPhaseCodesQty">No</span>
                              </label> -->
                </div>
            </div>
        </div>
        }@else {
        <div class="alert alert-danger mb-0" role="alert">
            You do not have access to this report.
        </div>
        }
        <div class="d-flex justify-content-end">
            @if(reportPermissions()?.reportCostCodeQty === ACCESS_EFFECTS.Allow){
            <div>
                <a href="{{downloadUrl}}" target="_blank" *ngIf="downloadUrl" class="me-4 btn btn-dark">Download
                    File</a>
                <button type="button" class="btn btn-primary" (click)="runReport()" [disabled]="isRunning">
                    @if(isRunning){
                    <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
                    }

                    Run Report
                </button>
            </div>
            }
        </div>
        }
    </div>
</div>