import { Component, Input, OnChang<PERSON>, OnInit, SimpleChanges, inject } from '@angular/core';
import { Ngb<PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>ate, NgbDateParserFormatter, NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { environment } from 'src/environments/environment';
import { ToastrService } from 'ngx-toastr';
import { interval } from 'rxjs';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { ActivatedRoute } from '@angular/router';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { CommonModule } from '@angular/common';
import { ConstructionReportsService } from 'src/app/construction/shared/data-access/reports.service';
import { AccessEffects } from 'src/app/models/access/access-effects';

@Component({
    selector: 'costcode-quantity-report-component',
    imports: [CommonModule, NgbDatepickerModule],
    templateUrl: './costcode-quantity-report.component.html',
    styleUrls: ['./costcode-quantity-report.component.css']
})
export class CostCodeQuantityReportComponent implements OnInit, OnChanges {
  @Input() projectId: string = "";
  dateFilterInfo: DateFilterInfo;
  fromDate: NgbDate | null = null;
  toDate: NgbDate | null = null;
  hoveredDate: NgbDate | null = null;
  layoutOptions = [];
  isRunning: boolean = false;
  isLoading: boolean = false;
  onlyPhaseCodesQty: string = "yes";
  calendar: NgbCalendar | null = null;
  projectFilterOptions = {
    deactivated: false
  };
  downloadUrl: string | null = null;
  reportsService = inject(ConstructionReportsService);
  momentFormatter = inject(MomentDateFormatterService);
  formatter = inject(NgbDateParserFormatter);
  route = inject(ActivatedRoute);
  toastr = inject(ToastrService);
  lambdaAWSService = inject(LambdaAWSService);
  accessService = inject(AccessService);
  reportPermissions = this.reportsService.reportPermissions;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;

  constructor() {
    this.dateFilterInfo = new DateFilterInfo();

    var sDate = new Date();
    sDate.setDate(sDate.getDate() - 7);
    var eDate = new Date();

    var startDate = new NgbDate(sDate.getFullYear(), sDate.getMonth() + 1, sDate.getDate());
    var endDate = new NgbDate(eDate.getFullYear(), eDate.getMonth() + 1, eDate.getDate());

    this.dateFilterInfo.dateRangeStart = startDate;
    this.dateFilterInfo.dateRangeEnd = endDate;

  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['projectId'].currentValue && changes['projectId'].firstChange || changes['projectId'].previousValue !== changes['projectId'].currentValue) {
      this.initialize();
    }
  }

  ngOnInit() {
    this.reportsService.getReportPermissions().subscribe();
  }

  initialize() {

  }

  clearDateFilterRange() {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.dateRangeStart = null;
      this.dateFilterInfo.dateRangeEnd = null;
    }

  }

  onDateSelection(date: NgbDate) {
    this.dateFilterInfo.singleDate = null;
    if (!this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd) {
      this.dateFilterInfo.dateRangeStart = date;
    } else if (this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd && date.after(this.dateFilterInfo.dateRangeStart)) {
      this.dateFilterInfo.dateRangeEnd = date;
    } else {
      this.dateFilterInfo.dateRangeEnd = null;
      this.dateFilterInfo.dateRangeStart = date;
    }

    if (this.dateFilterInfo.dateRangeStart && this.dateFilterInfo.dateRangeEnd) {
      // this.currentPage = 1;
      // this.filter(this.currentType, this.currentUserLevel);
    }
  }

  isHovered(date: NgbDate) {
    return (
      this.dateFilterInfo.dateRangeStart &&
      !this.dateFilterInfo.dateRangeEnd &&
      this.hoveredDate &&
      date.after(this.dateFilterInfo.dateRangeStart) &&
      date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return date.after(this.dateFilterInfo.dateRangeStart) && date.before(this.dateFilterInfo.dateRangeEnd);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.dateFilterInfo.dateRangeStart) ||
      date.equals(this.dateFilterInfo.dateRangeEnd) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.formatter.parse(input);
    return parsed && this.calendar?.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
  }

  downloadTimeOut: any;

  runReport() {
    this.isRunning = true;

    if (this.dateFilterInfo.dateRangeStart === null || this.dateFilterInfo.dateRangeEnd === null) {
      this.toastr.error("Must have a start and end date");
      return;
    }

    if (this.downloadTimeOut) {
      clearTimeout(this.downloadTimeOut);
    }

    var filterOptions = {
      ProjectIds: [this.projectId],
      StartDate: new Date(this.dateFilterInfo.dateRangeStart.year, this.dateFilterInfo.dateRangeStart.month - 1, this.dateFilterInfo.dateRangeStart.day).toISOString(),
      EndDate: new Date(this.dateFilterInfo.dateRangeEnd.year, this.dateFilterInfo.dateRangeEnd.month - 1, this.dateFilterInfo.dateRangeEnd.day).toISOString(),
      OnlyPhaseCodesQty: (this.onlyPhaseCodesQty === "yes") ? true : false
    }; 


    this.reportsService.RunCostCodeQuantityReport(filterOptions).subscribe({
      next: (store) => {    
        if (store.Status !== "completed" && store.Status !== "error") {
          let checkStore = interval(5000);
          var reportInfo = checkStore.subscribe({
            next: () => {
              this.reportsService.GetReportInfo(store.Id).subscribe({
                next: (result) => {
                  if (result.Status === "completed" && store.Status !== "error") {
                    reportInfo.unsubscribe();

                    var key = result.Key;
                    console.log(key);

                    this.lambdaAWSService.getDownloadPresignedUrl(key, "cost-code-qty-report.xlsx").subscribe({
                      next: (url) => {
                        this.downloadUrl = url.PresignedUrl;
                        window.location.href = url.PresignedUrl;

                        this.downloadTimeOut = setTimeout(() => {
                          this.downloadUrl = null;
                        }, environment.s3_link_expiration);

                        this.isRunning = false;
                      },
                      error: (err) => {
                        this.toastr.error("There was an issue creating this report");
                        this.isRunning = false;
                      }
                    });
                  }else if(result.Status === "error"){
                    reportInfo.unsubscribe();
                    this.toastr.error("There was an issue creating this report");
                    this.isRunning = false;
                  }
                },
                error: (err) => {
                  this.toastr.error("There was an issue creating this report");
                  this.isRunning = false;
                }
              });
            },
            error: (err) => {
              this.toastr.error("There was an issue creating this report");
              this.isRunning = false;
            }
          })
        }
        // this.checkReportStatus(store);
      },
      error: (err) => {
        this.toastr.error("There was an issue creating this report");
        this.isRunning = false;
      }
    });  
  }
}

export class DateFilterInfo {
  dateRangeStart: NgbDate | null = null;
  dateRangeEnd: NgbDate | null = null;
  singleDate: NgbDate | null = null;
}
