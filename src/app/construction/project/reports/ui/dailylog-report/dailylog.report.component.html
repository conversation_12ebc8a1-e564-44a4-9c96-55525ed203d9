﻿<div class="card">
  <div class="card-body">
    <header>
      <h3 class="page-title fs-6">Diary</h3>
    </header>
    @if(isLoading){
    <div style="height: 100px">
      <span class="placeholder col-12" style="height: 100%;"></span>
    </div>
    }@else {
    @if(reportPermissions()?.reportDailyLogProject === ACCESS_EFFECTS.Allow){
    <div class="row">
      <label>Date Range</label>
      <div class="col-12 col-lg-4 mb-3 d-flex flex-row align-items-start">
        <div class="position-absolute">
          <input name="datepicker" class="dp-hidden" ngbDatepicker #datepicker="ngbDatepicker" [autoClose]="'outside'"
            (dateSelect)="onDateSelection($event)" [displayMonths]="3" [dayTemplate]="t" outsideDays="hidden"
            [startDate]="fromDate!" />
          <ng-template #t let-date let-focused="focused">
            <span class="custom-day" [class.focused]="focused" [class.range]="isRange(date)"
              [class.faded]="isHovered(date) || isInside(date)" (mouseenter)="hoveredDate = date"
              (mouseleave)="hoveredDate = null">
              {{ date.day }}
            </span>
          </ng-template>
        </div>
        <div class="input-group me-1">
          <input #dpFromDate class="form-control" placeholder="Start Date" name="dpFromDate"
            [value]="formatter.format(dateFilterInfo?.dateRangeStart!)"
            (input)="fromDate = validateInput(dateFilterInfo?.dateRangeStart!, dpFromDate.value)" readonly />
        </div>
        <div class="input-group me-1">
          <input #dpToDate class="form-control" placeholder="End Date" name="dpToDate"
            [value]="formatter.format(dateFilterInfo?.dateRangeEnd!)"
            (input)="toDate = validateInput(dateFilterInfo?.dateRangeEnd!, dpToDate.value)" readonly />
        </div>
        <div class="btn-group" role="group" aria-label="Basic example">
          <button class="btn btn-outline-secondary" (click)="datepicker.toggle()" type="button">
            <i class="fa fa-calendar"></i>
          </button>
          <button class="btn btn-outline-secondary" (click)="clearDateFilterRange()">
            Clear
          </button>
        </div>
      </div>
      <div class="col-12 col-lg-4 mb-3">
        <select class="form-select" [(ngModel)]="options.PageLayout">
          @for (c of layoutOptions; track $index) {
          <option [ngValue]="c.Value">{{ c.Name }}</option>
          }
        </select>
      </div>
      <div class="col-12 col-lg-4 mb-3">
        <div class="input-group">
          <span class="input-group-text" id="inputGroup-sizing-sm">Include Photos</span>
          <select class="form-select" [(ngModel)]="showPhotoOption">
            <option value="Yes">Yes</option>
            <option value="No">No</option>
          </select>
        </div>
      </div>
      <div class="col-12 col-lg-12 mb-3">
        <cm-loading [show]="isFormanRunning"></cm-loading>
        <div>
          <label for="exampleSelect1">Foreman</label>
        </div>
        <ng-multiselect-dropdown id="foremanData" name="foremanData" style="width: 100%;"
          [placeholder]="'Select Foremen'" [settings]="foremanOptions" [data]="foreman" [(ngModel)]="selectedFormen"
          (onSelect)="onItemSelect($event)" (onSelectAll)="onSelectAll($event)">
        </ng-multiselect-dropdown>
      </div>
    </div>
    }@else {
    <div class="alert alert-danger mb-0" role="alert">
      You do not have access to this report.
    </div>
    }
    <div class="d-flex justify-content-end">
      @if(reportPermissions()?.reportDailyLogProject === ACCESS_EFFECTS.Allow){
      <a href="{{downloadUrl}}" target="_blank" *ngIf="downloadUrl" class="btn btn-outline-dark me-4">Download File</a>
      <button type="button" class="btn btn-primary" (click)="runDailyLogReport()" [disabled]="isRunning">
        @if(isRunning){
        <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
        }
        Run Report
      </button>
      }
    </div>
    }
  </div>
</div>