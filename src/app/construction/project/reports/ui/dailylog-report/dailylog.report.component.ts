﻿import { Component, OnInit, Input, inject, OnChanges, SimpleChanges } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Ng<PERSON><PERSON><PERSON>nda<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ngb<PERSON>ate<PERSON>arserFormatter, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { forkJoin, interval } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';

import { SelectedData } from 'src/app/models/SelectedData';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { CommonModule } from '@angular/common';
import { ConstructionReportsService } from 'src/app/construction/shared/data-access/reports.service';
import { AccessEffects } from 'src/app/models/access/access-effects';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProjectComponentsService } from 'src/app/construction/shared/data-access/project-components.service';
import { TeamMemberInfo } from 'src/app/construction/shared/interfaces/team';
import { ConstructionTeamsService } from 'src/app/construction/shared/data-access/teams.service';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { DelegateService } from 'src/app/account/user/shared/data-access/delegate.service';

@Component({
    selector: 'dailylog-report-component',
    imports: [CommonModule, NgbDatepickerModule, NgMultiSelectDropDownModule, CMLoaderModule, FormsModule, ReactiveFormsModule],
    templateUrl: './dailylog.report.component.html',
    styleUrls: ['./dailylog.report.component.css']
})
export class DailyLogReportComponent implements OnInit, OnChanges {
  @Input() projectId: string = "";
  @Input() access: string = "none";
  reportService = inject(ConstructionReportsService);
  momentFormatter = inject(MomentDateFormatterService);
  formatter = inject(NgbDateParserFormatter);
  route = inject(ActivatedRoute);
  toastr = inject(ToastrService);
  lambdaAWSService = inject(LambdaAWSService);
  accessService = inject(AccessService);
  toastrService = inject(ToastrService);
  delegateService = inject(DelegateService);
  projectComponentService = inject(ProjectComponentsService);
  teamComponentService = inject(ConstructionTeamsService);
  accountsService = inject(AccountService);
  ngbFormatter = inject(NgbDateParserFormatter);
  reportPermissions = this.reportService.reportPermissions;
  hoveredDate: NgbDate | null = null;
  fromDate: any | null = null;
  toDate: any | null = null;
  dateFilterInfo: ProjectDailyLogDateFilterInfo | null = null;
  templateUrl: string = "default";
  teamMembers: Array<TeamMemberInfo> = [];
  isRunning: boolean = false;
  isEndDateChanged: boolean = false;
  isLoading: boolean = false;
  options: any = {
    PageLayout: 0,
    ShowPhotos: true
  };
  layoutOptions: Array<any> = [
    {
      Name: 'Portrait',
      Value: 0
    },
    {
      Name: 'Landscape',
      Value: 1
    }
  ];
  foremanOptions: any = {
    singleSelection: false,
    idField: 'ItemId',
    textField: 'ItemText',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 5,
    allowSearchFilter: true
  };
  
  current: string = "";
  foreman: Array<SelectedData> = new Array<SelectedData>();
  selectedFormen: Array<SelectedData> = new Array<SelectedData>();
  downloadUrl: string | null = null;
  isFormanRunning: boolean = false;
  showPhotoOption: string = "No";
  calendar: NgbCalendar | null = null;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;
  constructor() {}
  ngOnChanges(changes: SimpleChanges): void {
    if(changes['projectId'].currentValue && changes['projectId'].previousValue !== changes['projectId'].currentValue){
        this.initialize();
    }
  }

  ngOnInit() {
    
  }

  changeEndDate() {
    this.isEndDateChanged = true;
  }

  initialize() {
    this.isLoading = true;
    this.accessService.CheckAccess("report-dailylog-project", "runreportproject").subscribe(async result => {

      var d = new Date();
      d.setDate(d.getDate() - 1);

      this.dateFilterInfo = new ProjectDailyLogDateFilterInfo();

      var startDate = new NgbDate(d.getFullYear(), d.getMonth() + 1, d.getDate());
      var endDate = new NgbDate(d.getFullYear(), d.getMonth() + 1, d.getDate() + 1);

      this.dateFilterInfo.dateRangeStart = startDate;
      this.dateFilterInfo.dateRangeEnd = endDate;

      this.access = result.Access;

      if (this.access === AccessEffects.Allow) {
         this.getTeamMembers();
      }
      this.isLoading = false;
    }, err => {
      this.isLoading = true;
    });


  }

  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
    const parsed = this.ngbFormatter.parse(input);
    return parsed && this.calendar?.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;
  }

  onDateSelection(date: NgbDate) {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.singleDate = null;
      if (!this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd) {
        this.dateFilterInfo.dateRangeStart = date;
      } else if (this.dateFilterInfo.dateRangeStart && !this.dateFilterInfo.dateRangeEnd && date.after(this.dateFilterInfo.dateRangeStart)) {
        this.dateFilterInfo.dateRangeEnd = date;
      } else {
        this.dateFilterInfo.dateRangeEnd = null;
        this.dateFilterInfo.dateRangeStart = date;
      }
    }
  }

  isHovered(date: NgbDate) {
    return (
      this.dateFilterInfo?.dateRangeStart &&
      !this.dateFilterInfo?.dateRangeEnd &&
      this.hoveredDate &&
      date.after(this.dateFilterInfo?.dateRangeStart) &&
      date.before(this.hoveredDate)
    );
  }

  isInside(date: NgbDate) {
    return date.after(this.dateFilterInfo?.dateRangeStart) && date.before(this.dateFilterInfo?.dateRangeEnd);
  }

  isRange(date: NgbDate) {
    return (
      date.equals(this.dateFilterInfo?.dateRangeStart) ||
      date.equals(this.dateFilterInfo?.dateRangeEnd) ||
      this.isInside(date) ||
      this.isHovered(date)
    );
  }

  clearDateFilterRange() {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.dateRangeStart = null;
      this.dateFilterInfo.dateRangeEnd = null;
    }

  }

  getTeamMembers() {
    this.isFormanRunning = true;

    forkJoin({ accountDelegateStore: this.delegateService.GetDelegates(), teamComponent: this.teamComponentService.getTeamComponent(this.projectId), accountIdentity: this.accountsService.GetAccountByIdentity() }).subscribe({
      next: (data) => {
        let accountDelegateStore = data.accountDelegateStore;
        let teamComponent = data.teamComponent;
        var userIds = new Array<string>();        

        if (teamComponent) {
          this.teamMembers = [];

          teamComponent.TeamMembers.map(teamMember => {

            var hasDelegate = accountDelegateStore.Users.find((x:any) => x.UserId == teamMember.UserId);

            if (hasDelegate) {
              let memberInfo: TeamMemberInfo = {
                DelegateInfo: hasDelegate,
                AccountInfo: null,
                IsActive: true,
                IsLoading: false
              };

              this.teamMembers.push(memberInfo);
            }

          });

          for (let c of this.teamMembers) {
            if (c.DelegateInfo) {
              userIds.push(c.DelegateInfo.UserId)
            }
          }
        }

        if (data.accountIdentity) {
          userIds.unshift(data.accountIdentity.CognitoUserId);
        }

        var accountInfo = this.accountsService.GetAccounts(userIds).subscribe({
          next: (accountInfo) => {
            this.foreman= new Array<SelectedData>();
            this.selectedFormen = new Array<SelectedData>();

            for (let account of accountInfo) {
              var pData = {
                ItemId: account.CognitoUserId,
                ItemText: `${account.Profile.FirstName} ${account.Profile.LastName}`
              };

              this.foreman.push(pData);
              this.selectedFormen.push(pData);
            }

            this.isFormanRunning = false;
          }, error: (err) => {
            console.log(err);
            this.isFormanRunning = false;
          }
        });
      },
      error: (err) => {
        this.isFormanRunning = false;
        console.log(err);
      }
    });

  }

  downloadTimeOut: any;
  runDailyLogReport() {
    if (this.dateFilterInfo) {
      if (!this.dateFilterInfo.dateRangeStart == null) {
        this.toastrService.error('You must select a start date');
        return;
      }

      if (!this.dateFilterInfo.dateRangeEnd) {
        this.toastrService.error('You must select an end date');
        return;
      }

      if (this.downloadTimeOut) {
        clearTimeout(this.downloadTimeOut);
      }

      var sDate = new Date(this.dateFilterInfo.dateRangeStart!.year, this.dateFilterInfo.dateRangeStart!.month - 1, this.dateFilterInfo.dateRangeStart!.day).toISOString();
      var eDate = new Date(this.dateFilterInfo.dateRangeEnd.year, this.dateFilterInfo.dateRangeEnd.month - 1, this.dateFilterInfo.dateRangeEnd.day).toISOString();

      if (sDate > eDate) {
        this.toastrService.error('End date must be greater or equal to the start date');
        return;
      }
      this.downloadUrl = null;
      this.isRunning = true;

      var usersIdList = new Array<string>();

      for (let user of this.selectedFormen) {
        if(user.ItemId){
          usersIdList.push(user.ItemId);
        }        
      }

      var projectsIds = new Array();
      projectsIds.push(this.projectId);

      var filterOptions = {
        ProjectIds: projectsIds,
        UserIds: usersIdList,
        StartDate: sDate,
        EndDate: eDate,
        ShowPhotos: (this.showPhotoOption === "Yes") ? true : false,
        PDFLayout: this.options.PageLayout
      };

      this.reportService.RunDailyLogReport(filterOptions).subscribe({
        next: (store) => {
          var check = interval(5000);
          var reportSub = check.subscribe(async () => {
            if (store.Status !== "completed" && store.Status !== "error") {
              store = await this.reportService.GetReportInfo(store.Id).toPromise();
            } else {
              try {
                reportSub.unsubscribe();
    
                if (store.Status === "completed") {
                  var key = store.Key;
                  console.log(key);
    
                  var url = await this.lambdaAWSService.getDownloadPresignedUrl(key, "project-diary-report.pdf").toPromise();
    
                  if(url?.PresignedUrl){
                    this.downloadUrl = url?.PresignedUrl;
                    window.location.href = url.PresignedUrl;
                  }
    
    
                  this.downloadTimeOut = setTimeout(() => {
                    this.downloadUrl = null;
                  }, environment.s3_link_expiration);
                } else {
                  this.toastrService.error("There was an issue creating this report");
                }
    
                this.isRunning = false;
              } catch (error) {
                this.isRunning = false;
              }
            }
          });
    
          setTimeout(() => {
            reportSub.unsubscribe();
            this.isRunning = false;
          }, 900000);
        },
        error: (err) => {
          this.isRunning = false;
        }
      })


    }
  }


  onSelectAll(event: any) { }

  onItemSelect(event: any) { }

}


export class ProjectDailyLogDateFilterInfo {
  dateRangeStart: NgbDate | null = null;
  dateRangeEnd: NgbDate | null = null;
  singleDate: NgbDate | null = null;
}