import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProjectUnauthorizedComponentComponent } from './project-unauthorized-component.component';

describe('ProjectUnauthorizedComponentComponent', () => {
  let component: ProjectUnauthorizedComponentComponent;
  let fixture: ComponentFixture<ProjectUnauthorizedComponentComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ProjectUnauthorizedComponentComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProjectUnauthorizedComponentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
