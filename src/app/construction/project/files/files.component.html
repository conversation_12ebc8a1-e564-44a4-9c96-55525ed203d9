<app-files-skeleton *ngIf="isLoading"></app-files-skeleton>
<h3 class="page-title fs-6">Files</h3>
@if(isLoading()){
<div class="row my-3 placeholder-glow">
	<div class="col-12">
		<div class="row">
			<div class="col-12 col-md-4 col-lg-3">
				<ul class="list-group d-none d-md-block my-3">
					<li class="list-group-item d-flex align-items-center" *ngFor="let folder of [1,2,3,4]"
						style="cursor: pointer;">
						<span class="placeholder col-12" style="height:45px;"></span>
					</li>
				</ul>
			</div>
			<div class="col-12 col-md-8 col-lg-9">
				<ul class="list-group my-3">
					<li class="list-group-item" *ngFor="let fileInfo of [1,2,3,4,5]">
						<span class="placeholder col-12" style="height:35px;"></span>
					</li>
				</ul>
			</div>
		</div>
	</div>
</div>
}@else{
<!-- add folders or files button -->
<div class="mb-3">
	@if(filePermissions()?.addFolderPermission === 'Allow'){
	<div class="btn-group" role="group" aria-label="Basic example">
		<button class="btn btn-outline-dark" placement="bottom" ngbTooltip="Add Folder"
			(click)="changeShowFolder(true)">Add
			Folder</button>
		<button class="btn btn-outline-dark" placement="bottom" ngbTooltip="Upload" (click)="addFiles();">Add
			Files</button>
	</div>
	}
</div>
<!-- collapse: file uploader -->
@if(showUpload()){
<div class="border rounded p-3 mb-3">
	<!-- <div class="mb-1">Uploading Files to "{{currentFolder?.Name}}"</div> -->
	<label for="exampleInputEmail1" class="form-label">You can upload up to 20 files at a time.</label>
	<app-file-uploader [files]="filesComponentInfo().Files" [projectId]="projectId" [maxFiles]="20"
		[currentFolder]="currentFolder" (filesAddedEvent)="setupFiles($event)" [folders]="filesComponentInfo().Folders"
		(changeFolderEvent)="changeFolder($event)"></app-file-uploader>
</div>
}
@if(showFolder()){
<!-- collapse: add folder -->
<div class="mb-3">
	<app-folder-manager [projectId]="projectId" (addFolderEvent)="addFolder($event)">
	</app-folder-manager>
</div>
}
<!-- search -->
<div class="input-group mb-3">
	<span class="input-group-text" id="basic-addon1"><i class="fas fa-search"></i></span>
	<input type="text" class="form-control" placeholder="Search" [formControl]="search" />
</div>
<!-- folders and files -->
<div class="row">
	<div class="col-12 col-md-4 col-lg-3">
		<app-folder-view #folderView [folders]="filesComponentInfo().Folders" [files]="currentFiles"
			[filePermissions]="filePermissions()" (changeFolderEvent)="filterFiles($event)"></app-folder-view>
	</div>
	<div class="col-12 col-md-8 col-lg-9">
		<app-files-view [files]="currentFiles" [folders]="filesComponentInfo().Folders"
			(fileUpdateEvent)="fileUpdate($event)" [projectId]="projectId" [currentFolder]="currentFolder"
			(folderDeleteEvent)="deleteFolder($event)" (fileMoveEvent)="moveFile($event)"
			(fileDeleteEvent)="deleteFile($event)"></app-files-view>
	</div>
</div>
}