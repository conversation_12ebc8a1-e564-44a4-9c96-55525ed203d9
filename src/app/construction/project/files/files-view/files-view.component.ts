import { Component, EventEmitter, Input, OnInit, Output, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';

import { find } from 'lodash'
import { forkJoin } from 'rxjs';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { FileInfoSB, FilePermissions, FolderSB } from 'src/app/construction/shared/interfaces/project-files';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FileSize } from 'src/app/construction/utils/files-pipe';
import { ConstructionFilesService } from 'src/app/construction/shared/data-access/files.service';
@Component({
    selector: 'app-files-view',
    imports: [CommonModule, FormsModule, ReactiveFormsModule, FileSize],
    templateUrl: './files-view.component.html',
    styleUrls: ['./files-view.component.css']
})
export class FilesViewComponent implements OnInit {

  @Input() projectId: string = "";
  @Input() files: Array<FileInfoSB> = [];
  @Input() folders: Array<FolderSB> = [];
  @Input() currentFolder: FolderSB = {} as FolderSB;  
  @Output() fileMoveEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() fileDeleteEvent: EventEmitter<FileInfoSB> = new EventEmitter<FileInfoSB>();
  @Output() folderDeleteEvent: EventEmitter<string> = new EventEmitter<string>();
  @Output() fileUpdateEvent: EventEmitter<FileInfoSB> = new EventEmitter<FileInfoSB>();
  aRoute = inject(ActivatedRoute);
  filesService = inject(ConstructionFilesService);
  confirmService = inject(ConfirmService);
  toastrService = inject(ToastrService);
  selectedFile: FileInfoSB | null = null;
  currentFileToMove: FileInfoSB | null = null;
  selectedMoveFolder: FolderSB | null = null;
  filePermissions = this.filesService.filePermissions;
  isSavingFolder: boolean = false;
  isDeletingFolder: boolean = false;
  currentFolderName: string = "";
  
  constructor() { }
  ngOnInit(): void {
    if (!this.projectId) {
      if(this.aRoute.snapshot.parent?.parent?.parent){
        const {id} = this.aRoute.snapshot.parent.parent.parent.params;
        this.projectId = id;
      } 
    }
  }

  moveSelectedFile() {
  
    if(this.selectedMoveFolder && this.currentFileToMove){
      if(this.selectedMoveFolder.FolderId !== "All"){
        if(this.selectedMoveFolder.Files.length > 0){
          if(this.selectedMoveFolder.Files.indexOf(this.currentFileToMove.FileId) >= 0){
            return;
          }
        }     
      } 

    }

    if(this.currentFileToMove && this.projectId && this.currentFolder && this.selectedMoveFolder){
      this.currentFileToMove.isLoading = true;

      forkJoin([
        this.filesService.deleteFileFromFolder(this.projectId, this.currentFolder.FolderId, this.currentFileToMove.FileId),
        this.filesService.addFileToFolder(this.projectId, this.selectedMoveFolder.FolderId, this.currentFileToMove.FileId)]).subscribe({
          next: (result) => {
            if(this.currentFileToMove && this.selectedMoveFolder && this.currentFolder && this.projectId){
              const moveInfo = {
                FileId: this.currentFileToMove.FileId,
                FolderId: this.selectedMoveFolder.FolderId
              }
        
              if(this.currentFolder.FolderId !== "All"){
                var idx = this.currentFolder.Files.indexOf(this.currentFileToMove.FileId);
                this.currentFolder.Files.splice(idx, 1);
                var fileData = find(this.files, {"FileId": this.currentFileToMove.FileId});
                if(fileData){
                  var idx = this.files.indexOf(fileData);
                  this.files.splice(idx, 1);
                  if(this.selectedMoveFolder.FolderId !== "All"){
                    var folder = find(this.folders, {"FolderId": this.selectedMoveFolder.FolderId});
                    if(folder){
                      folder.Files.push(this.currentFileToMove.FileId);
                    }
                  }
                }
              }else{
                if(this.selectedMoveFolder.FolderId !== "All"){
                  var folder = find(this.folders, { "FolderId": this.selectedMoveFolder.FolderId });
                  if (folder) {
                    folder.Files.push(this.currentFileToMove.FileId);
                  }
                }
                
                if(this.currentFolder.FolderId === "All"){
                  
                  for(let folder of this.folders.filter(x => x.FolderId !== "All" && x.FolderId !== this.selectedMoveFolder?.FolderId)){
                    var idx = folder.Files.indexOf(this.currentFileToMove.FileId)
                    if(idx >= 0){              
                      this.filesService.deleteFileFromFolder(this.projectId, folder.FolderId, this.currentFileToMove.FileId).subscribe({
                        next: (result) => {
                          folder.Files.splice(idx, 1);
                        }
                      });                 
                      break;
                    }  
                  }
                }
              }

              this.fileMoveEvent.next(moveInfo);
              this.toastrService.success(`${this.currentFileToMove.FileName} was moved successfully to ${this.selectedMoveFolder.Name} folder`);
              this.currentFileToMove.isLoading = false;
            }           
          },
          error: (err) => {
            if(this.currentFileToMove){
              this.currentFileToMove.isLoading = false;
            }            
            console.log(err);
          }
        }); 
      }
    }
  

  deleteFile(file: FileInfoSB) {
    this.confirmService.open("Are you sure you want to delete this file?").result.then(async value => {
   
        if(this.projectId && this.currentFolder){
          file.isLoading = true;
        
          forkJoin([this.filesService.deleteFile(this.projectId, file), this.filesService.deleteFileFromFolder(this.projectId, this.currentFolder.FolderId, file.FileId)]).subscribe({
            next: (result) => {
              var idx = this.files.indexOf(file);
              this.files.splice(idx, 1);        
              this.fileDeleteEvent.next(file);
              file.isLoading = false;
            },
            error: (err) => {
              file.isLoading = false;
            }
          });    
        }
    });
  }

  editFile(file: FileInfoSB) {
    this.selectedFile = Object.assign({}, file);
  }

  saveFile(){
    if(this.selectedFile && this.projectId){
      this.selectedFile.isLoading = true;
      this.filesService.updateFile(this.projectId, this.selectedFile).subscribe({
        next: (result) => { 

          var f = this.files.find( x => x.FileId === this.selectedFile?.FileId);
          if(f && this.selectedFile){
            var idx = this.files.indexOf(f);
            this.files.splice(idx, 1, this.selectedFile);
                  
            this.fileUpdateEvent.next(this.selectedFile);
            this.toastrService.success("File updated"); 
            this.selectedFile.isLoading = false;  
          }else{
            this.toastrService.error("Issue Saving File"); 
          }    
        }
      })

    }
  }

  setupFolderForEdit(){
    if(this.currentFolder){
      this.currentFolderName = this.currentFolder.Name;
    }
    
  }

  saveFolder() {
    if(this.currentFolder && this.projectId){
      const oldFolderName = this.currentFolder.Name;


      this.isSavingFolder = true;
      this.currentFolder.Name = this.currentFolderName;
      
      this.filesService.updateFolder(this.projectId, this.currentFolder).subscribe({
        next: (result) => {
          if(this.currentFolder){
            this.toastrService.success(`Folder ${this.currentFolder.Name} has been saved`);            
          }
          this.isSavingFolder = false;
        },
        error: (err) => {
          console.log(err);
          if(this.currentFolder){
            this.currentFolder.Name = oldFolderName;
          }
     
          this.isSavingFolder = false;
        }
      });
    }

  }

  deleteFolder() {

  this.confirmService.open("Are you sure you want to delete this folder?").result.then(async value => {
    try {
    if(this.projectId && this.currentFolder){
      this.isDeletingFolder = true;
      this.filesService.deleteFolder(this.projectId, this.currentFolder.FolderId).subscribe({
        next: (result) => {
          if(this.currentFolder){
            this.toastrService.success(`Folder ${this.currentFolder.Name} has been removed`);
      
            var folder = find(this.folders, {"FolderId": this.currentFolder.FolderId});
            if(folder){
              var idx = this.folders.indexOf(folder);
              this.folders.splice(idx, 1);
            } 
      
            this.folderDeleteEvent.next(this.currentFolder.FolderId);
            this.currentFolder = {} as FolderSB;
          }      
        }
      })
   
    }

    } catch (error) {
      console.log(error);
    } finally {
      this.isDeletingFolder = false;
    }
  });
     
    

  }

  cancelEdit(evt: any) {
    this.selectedFile = null;
  }

  closeMove() {
    this.selectedMoveFolder = null;
  }
  moveFile(file: FileInfoSB) {
    this.currentFileToMove = file;
  }
  save(nFile: FileInfoSB) {
    let oFile: any | null = null;
    for (let file of this.files) {
      if (file.FileId === nFile.FileId) {
        oFile = file;
        break;
      }
    }

    if(oFile){
      var idx = this.files.indexOf(oFile);
      this.files.splice(idx, 1, nFile);
      this.cancelEdit(null);
    }
  }

  downloadFile(fileInfo: FileInfoSB) {
  
    if(this.projectId){
      fileInfo.isLoading = true;
      var file = fileInfo.FileName.split('.').pop();
      if(file !== fileInfo.Extension){
        file = `${fileInfo.FileName}.${fileInfo.Extension}`;
      }else{
        file = fileInfo.FileName;
      }
      
      this.filesService.generateDownloadUrl(this.projectId, fileInfo.Key, fileInfo.ApplicationType, file).subscribe({
        next: (fileUrl) => {
          console.log(fileUrl);
          document.location.href = fileUrl.PresignedUrl;
          fileInfo.isLoading = false;
        }, 
        error: (err) => {
          console.log(err);
          fileInfo.isLoading = false;
        }
      });
    }
  }
}