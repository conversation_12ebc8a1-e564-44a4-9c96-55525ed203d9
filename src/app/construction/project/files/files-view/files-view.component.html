<!-- <pre>
	{{ currentFolder | json}}
</pre> -->
<div class="card">
	<div class="card-body">
		@if(!isSavingFolder && !isDeletingFolder){
			<div class="d-flex justify-content-between align-items-center mb-3">
				<h4 class="page-title fs-6 mb-0">{{ currentFolder?.Name }}</h4>
				@if(currentFolder?.FolderId !== 'All'){
				<div>
					@if(filePermissions()?.updateFolderPermission ==='Allow'){
					<a class="btn btn-outline-dark me-2" href="javascript:void(0)"
						(click)="setupFolderForEdit()" data-bs-toggle="modal"
						data-bs-target="#changeFolderNameModel">Edit</a>
					}

					@if(filePermissions()?.deleteFolderPermission ==='Allow'){
					<a class="btn btn-outline-danger" (click)="deleteFolder()"><i class="fas fa-trash"
							style="cursor:pointer;"></i></a>
					}

					@if(isSavingFolder || isDeletingFolder){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}

				</div>
				}
			</div>
			}
		<ul class="list-group">
			@for (fileInfo of files; track $index) {
			<li class="list-group-item">
				<div class="row align-items-center">
					<div class="col-11 col-lg-7">
						@if(filePermissions()?.downloadFilePermission !=='Allow'){
						{{fileInfo.FileName}}
						}

						@if(!fileInfo.isLoading && filePermissions()?.downloadFilePermission ==='Allow'){
						<a class="text-decoration-none" href="javascript:void(0)"
							(click)="downloadFile(fileInfo)">{{fileInfo.FileName}}</a>
						}

						@if(fileInfo.isLoading){
						<span>
							<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						</span>
						}

					</div>
					<div class="d-none d-lg-block col-lg-2 text-end">
						<span class="text-secondary small">{{ fileInfo.Size | filesize}}</span>
					</div>
					<div class="d-none d-lg-block col-lg-2">
						<span class="text-secondary small">{{fileInfo.Extension}}</span>
					</div>
					<div class="col-1 col-lg-1 text-end">

						@if(fileInfo.isLoading){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}

						@if(!fileInfo.isLoading){
						<div class="dropdown">
							<a class="" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown"
								aria-expanded="false">
								<i class="fas fa-ellipsis-v text-secondary"></i>
							</a>
							<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink">
								@if(filePermissions()?.updateFilePermission === 'Allow'){
								<li>
									<a class="dropdown-item" href="javascript:void(0)" (click)="editFile(fileInfo)"
										data-bs-toggle="modal" data-bs-target="#editFileModal">Edit</a>
								</li>
								}

								@if(filePermissions()?.moveToFolderPermission === 'Allow'){
								<li>
									<a class="dropdown-item" href="javascript:void(0)" data-bs-toggle="modal"
										data-bs-target="#moveFileModal" (click)="moveFile(fileInfo)">Move</a>
								</li>
								}

								@if(filePermissions()?.deleteFilePermission === 'Allow'){
								<li>
									<a class="dropdown-item" href="javascript:void(0)"
										(click)="deleteFile(fileInfo)">Delete</a>
								</li>
								}
							</ul>
						</div>
						}

					</div>
				</div>
			</li>
			}@empty {
			<li class="list-group-item" *ngIf="files.length <= 0">
				There are no files in this folder yet.
			</li>
			}
		</ul>
	</div>
</div>

<div class="modal" id="moveFileModal">
	<div class="modal-dialog">
		<div class="modal-content">
			<form #moveFileModalForm="ngForm" (ngSubmit)="moveSelectedFile()">
				<div class="modal-header">
					<h5 class="modal-title">
						Move File
					</h5>
				</div>
				<div class="modal-body">
					<p>{{ currentFileToMove?.FileName }}</p>
					<!-- <p>Which folder do you want to put this file in?</p> -->
					<div>
						<label for="floatingFolderSelect">Where do you want to move this file?</label>
						<select class="form-select" id="floatingFolderSelect" name="folderSelect"
							aria-label="Floating label select example" [(ngModel)]="selectedMoveFolder" required>
							@for (folder of folders; track $index) {
							<option [ngValue]="folder">
								{{ folder.Name}}
							</option>
							}
						</select>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"
						(click)="closeMove()">Close</button>
					<button type="button" type="submit" class="btn btn-primary" data-bs-dismiss="modal"
						[disabled]="!moveFileModalForm.form.valid && !selectedMoveFolder">
						Move File
					</button>
				</div>
			</form>
		</div>
	</div>
</div>

<div class="modal" id="editFileModal">
	<div class="modal-dialog">
		<div class="modal-content">
			<form #editFileModalForm="ngForm" (ngSubmit)="saveFile()">
				<div class="modal-header">
					<h5 class="modal-title">
						File Name
					</h5>
				</div>
				<div class="modal-body" *ngIf="selectedFile">
					<input class="form-control" name="fileName" [(ngModel)]="selectedFile.FileName" required />
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"
						(click)="closeMove()">Close</button>
					<button type="button" type="submit" class="btn btn-primary" data-bs-dismiss="modal"
						[disabled]="!editFileModalForm.form.valid">Save
						File</button>
				</div>
			</form>
		</div>
	</div>
</div>

<div class="modal" id="changeFolderNameModel">
	<div class="modal-dialog">
		<div class="modal-content">
			<form #editFolderNameModalForm="ngForm" (ngSubmit)="saveFolder()">
				<div class="modal-header">
					<h5 class="modal-title">
						Folder Name
					</h5>
				</div>
				<div class="modal-body" *ngIf="currentFolder">
					<input class="form-control" name="fileName" [(ngModel)]="currentFolderName" required />
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
					<button type="button" type="submit" class="btn btn-primary" data-bs-dismiss="modal"
						[disabled]="!editFolderNameModalForm.form.valid">Save</button>
				</div>
			</form>
		</div>
	</div>
</div>