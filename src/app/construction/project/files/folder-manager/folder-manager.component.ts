import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';

import { CommonModule } from '@angular/common';
import { FolderSB } from 'src/app/construction/shared/interfaces/project-files';
import { ConstructionFilesService } from 'src/app/construction/shared/data-access/files.service';

@Component({
    selector: 'app-folder-manager',
    imports: [CommonModule, FormsModule],
    templateUrl: './folder-manager.component.html',
    styleUrls: ['./folder-manager.component.css']
})
export class FolderManagerComponent implements OnInit {

  @Input() folder: FolderSB = {} as FolderSB;
  @Input() projectId: string | null = null;
  @Output() addFolderEvent: EventEmitter<FolderSB> = new EventEmitter<FolderSB>();
  @ViewChild("folderSB") folderSB: NgForm | null = null;
  isAdding: boolean = false;
  constructor(private projectFilesService: ConstructionFilesService, private toastrService: ToastrService) {
   
   }

  ngOnInit(): void {
  }

  addFolder() {
    if (this.folderSB) {
      if (!this.folderSB.valid) {
        this.toastrService.error("Form is not valid");
        return;
      }
    }

    if (!this.projectId) {
      this.toastrService.error("Project Id is required");
      return;
    }

    if (this.projectId && this.folder) {
      this.isAdding = true;

      this.projectFilesService.addFolder(this.projectId, this.folder).subscribe({
        next: (nFolder) => {
          if(this.folder){
            this.toastrService.success(`${this.folder.Name} folder added successfully`)
            this.folder = {} as FolderSB;
            this.isAdding = false;
            this.addFolderEvent.next(nFolder);
          }    
        }, error: (err) => {
          this.isAdding = false;
          console.log(err);
        }
      });
    }
  }

  validateMax(maxLength: number){
    if(this.folder){
      if(this.folder.Name.length > maxLength){
        return false
      }
    }

    return true;
  }
}
