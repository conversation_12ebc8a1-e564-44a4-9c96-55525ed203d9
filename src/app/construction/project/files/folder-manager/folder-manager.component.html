<div class="border rounded p-3">
	<form #folderSB="ngForm" (ngSubmit)="addFolder()">
		<label for="exampleInputEmail1" class="form-label">Folder Name</label>
		<div class="input-group">
			<input autocomplete="false" class="form-control" type="text" placeholder="Folder name" name="folderName"
				[(ngModel)]="folder.Name" required />
			<button class="btn btn-outline-dark" type="submit" [disabled]="!folderSB?.form?.valid || isAdding">
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isAdding"></i>
				Add
			</button>
		</div>
	</form>
</div>