import { Component, Input, OnInit, ViewChild, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FileInfoSB, FilePermissions, FilesComponentInfo, FolderSB } from '../../shared/interfaces/project-files';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { debounceTime, find, of, switchMap, tap } from 'rxjs';
import { FolderViewComponent } from './folder-view/folder-view.component';
import { ProjectComponentIdentifiers } from '../../shared/interfaces/project-components';
import { FolderManagerComponent } from './folder-manager/folder-manager.component';
import { FilesViewComponent } from './files-view/files-view.component';
import { FileUploaderComponent } from './file-uploader/file-uploader.component';
import { ConstructionFilesService } from '../../shared/data-access/files.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';

@Component({
    selector: 'app-files',
    imports: [
        CommonModule,
        FolderViewComponent,
        FolderManagerComponent,
        FilesViewComponent,
        FileUploaderComponent,
        FormsModule,
        ReactiveFormsModule,
        CMLoaderModule
    ],
    templateUrl: './files.component.html',
    styleUrl: './files.component.css'
})
export class FilesComponent {

  @Input() projectId: string = "";  

  aRoute = inject(ActivatedRoute);
  filesService = inject(ConstructionFilesService);
  filesComponentInfo = signal<FilesComponentInfo>({} as FilesComponentInfo);
  showUpload = signal<boolean>(false);
  showFolder = signal<boolean>(false);
  currentFiles: Array<FileInfoSB> = [];
  nCurrentFiles: Array<FileInfoSB> = [];  
  search = new UntypedFormControl();
  currentFolder: FolderSB = {} as FolderSB;  
  isLoading = signal<boolean>(false);
  filePermissions = this.filesService.filePermissions;
  @ViewChild("folderView") folderView: FolderViewComponent | null = null;  
  
  projectIdInfo = signal<string>("");
  getFilesComponent = toSignal(toObservable(this.projectIdInfo).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap(projectId => projectId ? this.filesService.getOrCreateFilesComponent(projectId) : of(null)),
    tap((filesComponentInfo) => {
      if(filesComponentInfo){  
             
        this.filesComponentInfo.set(filesComponentInfo as FilesComponentInfo);    
        this.setupDataSets(filesComponentInfo.Files);   
      }
      this.isLoading.set(false);
    })

  ));

  constructor() {
    this.filesService.getFilesServicePermissions().subscribe();

    if(this.projectId){
      this.projectIdInfo.set(this.projectId);
    }else{
      this.aRoute.parent?.parent?.paramMap.subscribe({
        next: (params) => {
          let projectId =  params.get("projectId") as string;
          this.projectIdInfo.set(projectId);
          this.projectId = projectId;          
        }
      })
    }

    this.search.valueChanges.pipe(debounceTime(400)).subscribe({
      next: (value) => {      
        const v = value.toLowerCase().trim();
        this.currentFiles = this.nCurrentFiles.filter(
          item =>
            item.FileName.toLowerCase().indexOf(v) > -1 || 
            item.Extension.toLowerCase().indexOf(v) > -1
        );
      },
      error: (err) => {
        console.log(err);
      }
    });

   }

   addFiles(){
    this.showUpload.set(!this.showUpload()); 
    this.showFolder.set(false);
   }

  changeFolder(folder:FolderSB){
    this.currentFolder = folder;
    if(this.folderView){
      this.folderView.changeFolder(folder);
    }

  }

  changeShowFolder(isShow:boolean){
    this.showFolder.set(isShow);   
    this.showUpload.set(false);

  }

  fileUpdate(file:FileInfoSB){
    let filesComponentInfo = this.filesComponentInfo() as FilesComponentInfo;

      var foundFile = filesComponentInfo.Files.find(x => x.FileId == file.FileId);

      if(foundFile){
        var idx = filesComponentInfo.Files.indexOf(foundFile);
        filesComponentInfo.Files.splice(idx, 1, file);
      }
    
  }

  setupFiles(files: Array<FileInfoSB>){
    let filesComponentInfo = this.filesComponentInfo() as FilesComponentInfo;

    if(this.currentFolder && this.projectId){
      var nFiles = [...this.currentFiles, ...files];
      this.setupDataSets(nFiles);

      if(this.currentFolder.FolderId !== "All"){
        this.filesService.addFilesToFolder(this.projectId, this.currentFolder.FolderId, files.map(x => x.FileId)).subscribe({
          next: (result) => {
            if(this.currentFolder && filesComponentInfo){
              this.currentFolder.Files = [...this.currentFolder.Files, ...files.map(x => x.FileId)];
              filesComponentInfo.Folders[0].Files = [...filesComponentInfo.Folders[0].Files, ...files.map(x => x.FileId)];           
            }
          }, error: (err) => {
              console.log(err);
          }
        });
      }else{
        if(filesComponentInfo){
          filesComponentInfo.Folders[0].Files = [...filesComponentInfo.Folders[0].Files, ...files.map(x => x.FileId)];
        }        
      } 
      
    }

  }


  moveFile(fileMoveInfo:any){
  
  }

  deleteFolder(folderId: string){
    if(this.folderView){
      this.folderView.setFolderIndex(0);
    }
    
  }

  deleteFile(file: FileInfoSB){
    let filesComponentInfo = this.filesComponentInfo() as FilesComponentInfo;

    if(this.currentFolder && filesComponentInfo){
      if(this.currentFolder.FolderId !== "All"){
        var idx = this.currentFolder.Files.indexOf(file.FileId);
        this.currentFolder.Files.splice(idx, 1); 
         
        var idx = filesComponentInfo.Folders[0].Files.indexOf(file.FileId);    
        filesComponentInfo.Folders[0].Files.splice(idx, 1);
      }else{
        if(filesComponentInfo && this.projectId){
          for(let folder of filesComponentInfo.Folders.filter(x => x.FolderId !== "All" && x.FolderId !== this.currentFolder?.FolderId)){
            var idx = folder.Files.indexOf(file.FileId);
            if(idx >= 0){          
              this.filesService.deleteFileFromFolder(this.projectId, folder.FolderId, file.FileId).subscribe({
                next: (result) => {
                  folder.Files.splice(idx, 1);
                },
                error: (err) => {
                  console.log(err);
                }
              });    
            }
          }
        }    
      }
    }
  }

  setupDataSets(dataSet: Array<FileInfoSB>){
    this.currentFiles = [...dataSet];
    this.nCurrentFiles = Object.assign([], dataSet);
  }
  
  filterFiles(folder:FolderSB){
    let filesComponentInfo = this.filesComponentInfo() as FilesComponentInfo;
    this.currentFolder = folder;
    var nFiles: Array<FileInfoSB> = [];
    for(var fileId of folder.Files){
      var filter = filesComponentInfo?.Files.find(x => x.FileId == fileId);
      if(filter){
        nFiles.push({...filter});
      }
    }
    this.setupDataSets(nFiles);
  } 

  addFolder(folder:FolderSB){
    let filesComponentInfo = this.filesComponentInfo() as FilesComponentInfo;
    filesComponentInfo.Folders.push(folder);        
  }
}
