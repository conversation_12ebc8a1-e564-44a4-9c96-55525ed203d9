import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FileInfoSB, FilePermissions, FolderSB } from 'src/app/construction/shared/interfaces/project-files';

@Component({
    selector: 'app-folder-view',
    imports: [CommonModule],
    templateUrl: './folder-view.component.html',
    styleUrls: ['./folder-view.component.css']
})
export class FolderViewComponent implements OnInit, OnChanges {

  @Input() folders: Array<FolderSB> = [];
  @Input() files: Array<FileInfoSB> = [];
  @Output() changeFolderEvent: EventEmitter<FolderSB> = new EventEmitter<FolderSB>();
  @Input() selectedFolder: FolderSB  | null = null;
  @Input() filePermissions: FilePermissions | null = null;
  constructor(private aRoute: ActivatedRoute) { }
  ngOnChanges(changes: SimpleChanges): void {    
    const {folders} = changes;
    if(folders){
      if(folders.currentValue){
        if(folders.currentValue && folders.firstChange){
          this.addAllFolder();
        }
      }
    }
  }

  ngOnInit(): void {
  
  }

  addAllFolder(){
    var allFolder: FolderSB = {
      DateCreated: new Date(),
      Description: "All Files",
      Name: "All Files",
      FolderId: "All",
      Files: []
    }
    allFolder.Files = this.files.map(result => result.FileId);
    this.folders.splice(0,0, allFolder);    
    this.changeFolder(allFolder);
  }

  filterFiles(folder:FolderSB){

  }

  changeFolder(folder: FolderSB){
    this.selectedFolder = folder;
    this.changeFolderEvent.next(folder);
  }

  setFolderIndex(idx: number){
    this.selectedFolder = this.folders[idx];
    this.changeFolderEvent.next(this.selectedFolder);
  }

}
