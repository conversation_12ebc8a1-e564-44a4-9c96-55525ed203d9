<ul class="list-group d-none d-md-block">
	<li class="list-group-item d-flex align-items-center" *ngFor="let folder of folders"
		[ngClass]="{'active': selectedFolder?.FolderId === folder.FolderId}" style="cursor: pointer;"
		(click)="changeFolder(folder)">
		<div>
			<i class="fas fa-folder fa-lg me-2" style="color: #f1d592"></i>
		</div>
		<div>{{folder.Name}} <span class="badge bg-info">{{folder?.Files?.length}}</span></div>
	</li>
</ul>

<div class="dropdown d-md-none mb-3">
	<button class="btn btn-outline-dark dropdown-toggle w-100" type="button" id="dropdownMenuButton1"
		data-bs-toggle="dropdown" aria-expanded="false">
		Folders
	</button>
	<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
		<li *ngFor="let folder of folders">
			<a class="dropdown-item" href="javascript:void(0)" (click)="changeFolder(folder)">{{ folder.Name }}</a>
		</li>		
	</ul>
</div>