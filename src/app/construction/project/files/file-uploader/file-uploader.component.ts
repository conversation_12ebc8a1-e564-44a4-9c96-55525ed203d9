import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { forkJoin, Observable } from 'rxjs';
import { ConstructionFilesService } from 'src/app/construction/shared/data-access/files.service';
import { FileInfoSB, FolderSB } from 'src/app/construction/shared/interfaces/project-files';
import { FileSize } from 'src/app/construction/utils/files-pipe';



@Component({
    selector: 'app-file-uploader',
    imports: [CommonModule, FormsModule, FileSize],
    templateUrl: './file-uploader.component.html',
    styleUrls: ['./file-uploader.component.css']
})
export class FileUploaderComponent implements OnInit {
  @Input() files: Array<FileInfoSB> = [];
  @Input() folders: Array<FolderSB> = [];
  @Input() projectId: string = "";
  @Input() maxFiles: number = 5;
  @Input() currentFolder: FolderSB = {} as FolderSB;  
  @ViewChild("filesComponentFileUploader") filesComponentFileUploader: ElementRef = {} as ElementRef;
  @Output() filesAddedEvent: EventEmitter<Array<FileInfoSB>> = new EventEmitter<Array<FileInfoSB>>();
  @Output() changeFolderEvent: EventEmitter<FolderSB> = new EventEmitter<FolderSB>();   
  filesService = inject(ConstructionFilesService);
  filePermissions = this.filesService.filePermissions;
  isUploading: boolean = false;
  currentUploadFilesInfo: Array<ComponentFileInfo> = [];
  maxFilesError: boolean = false;
  totalFiles: number = 0;
  constructor( ) { }

  ngOnInit(): void {
  }

  cancelUpload() {
    if (this.filesComponentFileUploader) {
      this.currentUploadFilesInfo = [];
      this.filesComponentFileUploader.nativeElement.files = null;
      this.filesComponentFileUploader.nativeElement.value = null;
    }
  }

  changeFolder() {
    if (this.currentFolder) {
      this.changeFolderEvent.next(this.currentFolder);
    }
  }

  onFileChange(event: any, type: string) {
    const reader = new FileReader();
    this.maxFilesError = false;
    this.currentUploadFilesInfo = [];
    if (event?.target?.files && event?.target?.files?.length) {

      let currentFiles = event.target.files;
      this.totalFiles = currentFiles.length;
      if (currentFiles.length > this.maxFiles) {
        this.cancelUpload();
        this.maxFilesError = true;
      }

      for (let file of currentFiles) {
        this.currentUploadFilesInfo.push(new ComponentFileInfo(file, file.name, file.size));
      }

      const [file] = event.target.files;
      reader.readAsArrayBuffer(file);
      reader.onloadend = async () => {
        const File: File = file;
        const FileRes: string | ArrayBuffer | null = reader?.result;


        // need to run CD since file load runs outside of zone
        //this.cdr.markForCheck();
      };
    }
  }

  uploadFiles() {

    this.isUploading = true;
    var asyncFileInfoArray = new Array<Observable<FileInfoSB>>();

    for (var fileInfo of this.currentUploadFilesInfo) {
      asyncFileInfoArray.push(this.uploadFile(fileInfo));
    }

    forkJoin(asyncFileInfoArray).subscribe({
      next: (result) => {
        if (this.filesComponentFileUploader) {
          this.filesComponentFileUploader.nativeElement.files = null;
          this.filesComponentFileUploader.nativeElement.value = null;
          this.isUploading = false;
          this.currentUploadFilesInfo = [];

          for (let file of result) {
            this.files.push(file);
          }

          this.filesAddedEvent.next(result);
        }

      }, error: (err) => {
        console.log("Error Uploading", err);
        this.isUploading = false;
      }

    });

  }


  uploadFile(fileInfo: ComponentFileInfo): Observable<FileInfoSB> {
    return new Observable<any>(obs => {
      var key = `public/project/${this.projectId}/files/${fileInfo.file.name}`;

      if (this.projectId) {
        this.filesService.generateUploadURL(this.projectId, key, fileInfo.file.type).subscribe(result => {
          this.filesService.uploadFileWithSignedURL(result.PresignedUrl, fileInfo.file).subscribe(
            {
              next: (event) => {
                if (event && typeof event === 'number') {
                  fileInfo.isProgressing = true;
                  fileInfo.uploadProgress = +event;
                } else if (fileInfo.uploadProgress === 100 && !event) {
                  fileInfo.isProgressing = false
                  var addFile = {} as FileInfoSB;
                  addFile.FileName = fileInfo.file.name;
                  addFile.Extension = fileInfo.file.name.split('.').pop() as string;
                  addFile.OriginalFileName = fileInfo.file.name;
                  addFile.Key = key;
                  addFile.ApplicationType = fileInfo.file.type ? fileInfo.file.type : "application/octet-stream",
                    addFile.Size = fileInfo.file.size;

               
                    this.filesService.addFile(this.projectId, addFile).subscribe(result => {
                      console.log("Added File", result);
                      fileInfo.isComplete = true;

                      obs.next(result);
                      obs.complete()
                    });
                

                }
              }, error: (err) => {
                obs.error(err);
                fileInfo.isProgressing = false;
                fileInfo.isComplete = false;
                this.isUploading = false;
              }
            })
        });
      }

    });
  }

}


export class ComponentFileInfo {

  constructor(file: File, name: string, size: number) {
    this.file = file;
    this.name = name;
    this.size = size;
  }
  presignedUrl: string | null = null;
  name: string;
  uploadProgress: number = 0;
  file: File
  size: number;
  isComplete: boolean = false;
  isProgressing: boolean = false;
}