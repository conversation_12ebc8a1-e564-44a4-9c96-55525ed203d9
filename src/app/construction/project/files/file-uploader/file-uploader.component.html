@if(filePermissions()?.uploadFilePermission === "Allow"){
<!-- choose files -->
<div class="mb-3">
	<input #filesComponentFileUploader class="form-control" type="file" id="bidFile"
		(change)="onFileChange($event, 'bidDoc')" #bidDocFile multiple [disabled]="isUploading" />
</div>
<!-- choose folders -->
<div class="mb-3">
	<label for="floatingSelect" class="form-label">Which folder would you like to save these files in?</label>
	<select class="form-select" id="floatingSelect" aria-label="Floating label select example" (change)="changeFolder()"
		[(ngModel)]="currentFolder" [disabled]="isUploading">
		<option selected *ngFor="let folder of folders" [ngValue]="folder">
			@if(folder.FolderId === 'All'){
			None
			}@else {
			{{ folder.Name }}
			}
		</option>
	</select>
</div>
}
<!--warning - you selected too many files -->
@if(maxFilesError){
<div class="alert alert-danger">
	Oops! You selected too many files. You can upload {{maxFiles}} files at a time, but you selected {{totalFiles}}
	files.
</div>
}
<!-- selected files -->
<ul class="list-group mb-3">
	@for (item of currentUploadFilesInfo; track $index) {
	<li class="list-group-item">
		<div class="row align-items-center">
			<div class="col-9 mb-1 mb-md-0 col-md-5">
				{{item.name}}
			</div>
			<div class="col-3 mb-1 mb-md-0 col-md-2 text-end">
				{{item.size | filesize }}
			</div>
			<div class="col-9 mb-1 mb-md-0 col-md-4">
				<div class="progress" style="height:30px;">
					<div class="progress-bar bg-success" role="progressbar" [style.width]="item.uploadProgress + '%'"
						aria-valuemin="0" aria-valuemax="100"></div>
				</div>
			</div>
			<div class="col-3 mb-1 mb-md-0 col-md-1 text-end">
				@if (item.uploadProgress < 100) { {{ item.uploadProgress }}% }@else if(item.isComplete &&
					item.uploadProgress===100){ <i class="fas fa-check text-success" *ngIf="item.isComplete"></i>
					}@else if(!item.isComplete && isUploading && !item.isProgressing){
					<i class="fas fa-circle-notch fa-spin text-succeess fa-2x"></i>
					}
			</div>
		</div>
	</li>
	}@empty {
	<li class="list-group-item">
		No Files Selected
	</li>
	}
</ul>
<!-- upload button -->
@if(filePermissions()?.uploadFilePermission === 'Allow'){
<div class="d-flex justify-content-end">
	<button type="button" class="btn btn-primary me-1" (click)="uploadFiles()"
		[disabled]="isUploading || currentUploadFilesInfo.length <= 0">
		Upload
	</button>
	@if(currentUploadFilesInfo.length > 0 && !isUploading){
	<button type="button" class="btn btn-outline-secondary" (click)="cancelUpload()">Cancel</button>
	}
</div>
}