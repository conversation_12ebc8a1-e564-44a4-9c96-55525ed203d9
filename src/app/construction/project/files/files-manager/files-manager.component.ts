import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { FileInfoSB } from 'src/app/construction/shared/interfaces/project-files';
import { ProjectFilesService } from 'src/app/modules/project-files/services/project-files.service';


@Component({
    selector: 'app-files-manager',
    imports: [CommonModule],
    templateUrl: './files-manager.component.html',
    styleUrls: ['./files-manager.component.css']
})
export class FilesManagerComponent implements OnInit {

  @Input() projectId: string | null = null;
  @Input() file: FileInfoSB = {} as FileInfoSB;
  @Output() cancelEditEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() saveEvent: EventEmitter<FileInfoSB> = new EventEmitter<FileInfoSB>();

  isSaving: boolean = false;
  constructor(private filesService: ProjectFilesService, private toastrService: ToastrService) { }

  ngOnInit(): void {
  }
  cancel(){
   this.cancelEditEvent.next(null);
  }

  save(){   
    if(this.projectId && this.file){
      this.isSaving = true;

      this.filesService.updateFile(this.projectId, this.file).subscribe({
        next: (result) => {     
          if(this.file){
            this.toastrService.success("File updated");
            this.saveEvent.next(this.file);
          }

          this.isSaving = false;
        },
        error: (err) => {
          this.isSaving = false;
        }
      })

    }    



  }
}
