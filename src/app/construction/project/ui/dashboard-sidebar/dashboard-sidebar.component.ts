import { Component, inject, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ConstructionProject } from 'src/app/construction/shared/interfaces/construction-project';
import { UserProjectComponent } from 'src/app/construction/shared/interfaces/dashboard';


@Component({
    selector: 'app-dashboard-sidebar',
    imports: [CommonModule, RouterLink],
    templateUrl: './dashboard-sidebar.component.html',
    styleUrl: './dashboard-sidebar.component.css'
})
export class DashboardSidebarComponent implements OnChanges {
  @Input() project: ConstructionProject | null = null;
  @Input() userProjectComponents: Array<UserProjectComponent> | null = null;

  
  ngOnChanges(changes: SimpleChanges): void {
    if(changes['userProjectComponents']?.currentValue !== changes['userProjectComponents']?.previousValue){
      if(changes['userProjectComponents'].currentValue){
        this.userProjectComponents = (changes['userProjectComponents'].currentValue as Array<UserProjectComponent>).filter(x => x.IsActive);
      }      
    }
  }
}