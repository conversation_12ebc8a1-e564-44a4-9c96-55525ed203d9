import { Component, Inject, Input, OnChanges, SimpleChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionProjectService } from 'src/app/construction/shared/data-access/project.service';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';
import { RouterLink } from '@angular/router';
import { ConstructionProject } from 'src/app/construction/shared/interfaces/construction-project';
import { UserProjectComponent } from 'src/app/construction/shared/interfaces/dashboard';


@Component({
    selector: 'app-dashboard-sidebar-menu',
    imports: [CommonModule, RouterLink],
    templateUrl: './dashboard-sidebar-menu.component.html',
    styleUrl: './dashboard-sidebar-menu.component.css'
})
export class DashboardSidebarMenuComponent implements OnChanges {
  @Input() project: ConstructionProject | null = null;
  @Input() userProjectComponents: Array<UserProjectComponent> | null = null;

  constructionProjectService = inject(ConstructionProjectService);  
  userProjectComponentService = inject(UserProjectComponentsService);
  projectLoading: any;
  sideBarLoading: any;  

  ngOnChanges(changes: SimpleChanges): void {
    if(changes['userProjectComponents']?.currentValue !== changes['userProjectComponents']?.previousValue){
      if(changes['userProjectComponents'].currentValue){
        this.userProjectComponents = (changes['userProjectComponents'].currentValue as Array<UserProjectComponent>).filter(x => x.IsActive);
      }      
    }
  }
}
