<div class="dropdown">
	<a class="btn btn-outline-secondary text-dark dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
	  data-bs-toggle="dropdown" aria-expanded="false">
	  <i class="fas fa-bars"></i>
	</a>

	 <ul class="dropdown-menu dropdown-menu-end"
		aria-labelledby="dropdownMenuButton1">
		@for(component of userProjectComponents; track $index){
				<li> 
				<a class="dropdown-item text-dark" routerLink="./{{ component.UrlLink }}" >{{ component.Name }}</a>
				</li>
		}@empty{
			<li> 
				<div>No Components. You can add components from the project settings menu</div>
			</li>
			
		}
	  </ul>
	

  </div>