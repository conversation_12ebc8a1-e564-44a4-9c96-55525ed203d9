<!-- alerts boxes -->
@if (dailyLogService.approvalSignal()?.CurrentApprover && !dailyLogService.approvalSignal()?.FinalApprovalDate) {
<div class="alert alert-info" role="alert">
  <i class="fa fa-lock me-2"></i>
  This diary is currently going through the approval process.
</div>
}
@if (dailyLogService.approvalSignal()?.CurrentApprover && dailyLogService.approvalSignal()?.FinalApprovalDate) {
<div class="alert alert-info" role="alert">
  <i class="fa fa-check me-2"></i>
  This diary has been approved.
</div>
}

<!-- Anchor for scrolling with offset -->
<div [id]="'anchor-' + projectId" class="scroll-anchor"></div>

<!-- Page header -->
<header class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between">
      <!-- Only show project title when NOT in single project mode -->
      @if (!diaryProjectsSelectorService.isSingleProjectModeSignal()) {
      <h1 class="page-title fs-6 d-flex align-items-center">
        {{ projectId ? dailyLogService.diaryService.getProjectName(projectId) : '' }}
      </h1>
      } @else {
      <div></div> <!-- Empty div to maintain flex layout -->
      }
      @if (dailyLogService.approvalSignal() && !dailyLogService.isLoadingSignal()) {
      <div class="d-flex flex-column align-items-end">
        @if (!dailyLogService.approvalSignal()?.CurrentApprover) {
        <button type="button" class="btn btn-danger" (click)="dailyLogService.unSubmit()"
          [disabled]="dailyLogService.isPersistingSignal()">Undo Submission</button>
        }
        <div class="text-secondary small">
          Submitted on {{ dailyLogService.approvalSignal()?.CreateDate | date: 'shortDate' }} at {{
          dailyLogService.approvalSignal()?.CreateDate | date: 'shortTime' }}.
        </div>
      </div>
      }
    </div>
  </div>
</header>

<!-- No Work Day toggle with dirty indicators -->
<div class="row mt-3 mb-4">
  <div class="col-12">
    <div class="d-flex align-items-center">
      @if (dailyLogService.accessSignal() === 'Allow' && !dailyLogService.approvalSignal()) {
      @if (dailyLogService.dailyLogProjectUserComponentSignal()?.NoWork) {
      <button class="btn btn-danger" (click)="dailyLogService.setNoWork(false)"
        [disabled]="dailyLogService.isLoadingSignal()">
        <i class="fa fa-check"></i>
        No Work Day
      </button>
      } @else {
      <button class="btn btn-outline-dark" (click)="dailyLogService.setNoWork(true)"
        [disabled]="dailyLogService.isLoadingSignal()">
        No Work Day
      </button>
      }
      }

      <!-- Dirty state indicator - moved to right of No Work Day button -->
      @if (checkDirtyState()) {
      <div class="ms-2">
        <small>
          <span class="text-danger" style="cursor: pointer;" (click)="saveDailyLog()"
            [ngbTooltip]="'Unsaved changes detected. Click to save.'">
            <i class="fa fa-save"></i>
          </span>
          <span class="ms-2" style="cursor: pointer;" (click)="resetChanges()" [ngbTooltip]="'Click to revert changes'">
            <i class="fa fa-undo text-primary"></i>
          </span>
        </small>
      </div>
      } @else {
      <div class="ms-2">
        <small>
          <span class="text-success">
            <i class="fa fa-check-circle"></i>
          </span>
        </small>
      </div>
      }
    </div>
  </div>
</div>

<!-- Main content with loading state -->
@if (dailyLogService.isLoadingSignal() || dailyLogService.isLoadingApprovalSignal() || this.isViewLoading()) {
<div class="card mb-4 placeholder-glow">
  <div class="card-header" style="height: 50px;">
    <span class="placeholder col-3"></span>
  </div>
  <div class="card-body" style="height: 100px">
    <span class="placeholder col-12" style="height: 100%;"></span>
  </div>
</div>
} @else {
<!-- Show "No work day" message if applicable -->
@if (dailyLogService.dailyLogProjectUserComponentSignal()?.NoWork) {
<div class="alert alert-info" role="alert">
  No work day.
</div>
} @else {
<!-- Child components -->
<div>
  @for (component of dailyLogService.dailyLogProjectUserComponentSignal()?.Components; track $index) {
  @if (dailyLogService.isActive(component.ComponentIdentifier)) {
  @if (dailyLogService.hasTimeSheetData(component) || dailyLogService.isSectionVisible('Time Sheet')()) {
  @if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.TIMECARD) {
  <app-daily-log-time-sheet></app-daily-log-time-sheet>
  }
  }
  @if (dailyLogService.hasNotesData(component) || dailyLogService.isSectionVisible('Notes')()) {
  @if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.NOTES) {
  <app-daily-log-notes></app-daily-log-notes>
  }
  }
  @if (dailyLogService.hasPhotosData(component) || dailyLogService.isSectionVisible('Photos')()) {
  @if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.PHOTOS) {
  <app-daily-log-photos></app-daily-log-photos>
  }
  }
  @if (dailyLogService.hasSiteConditionsData(component) || dailyLogService.isSectionVisible('Site Conditions')()) {
  @if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.SITE_CONDITIONS) {
  <app-daily-log-site-conditions></app-daily-log-site-conditions>
  }
  }
  @if (dailyLogService.hasWeatherData(component) || dailyLogService.isSectionVisible('Weather')()) {
  @if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.WEATHER) {
  <app-daily-log-weather></app-daily-log-weather>
  }
  }
  }
  } @empty {
  <div class="alert alert-info" role="alert">
    No components. You must setup your default component in user settings.
  </div>
  }
</div>
}
<!-- alert message -->
@if (dailyLogService.alertMessageSignal()) {
<div class="alert alert-primary" role="alert">
  {{ dailyLogService.alertMessageSignal() }}
</div>
}
}
<!-- action buttons -->
@if (dailyLogService.accessSignal() === 'Allow' && !dailyLogService.approvalSignal()) {
<app-daily-log-buttons></app-daily-log-buttons>
}