import { Injectable, Signal, computed, inject, signal } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { PhotosComponentProjectUser } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';
import { GalleryImage, Photo, PhotoInfo, PhotoProcessorInfo, PhotoProcessorStatus } from 'src/app/construction/shared/interfaces/photos';
import { v4 } from 'uuid';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs';
import { IComponentService } from 'src/app/construction/shared/interfaces/component-service.interface';
import { DailyLogDirtyService } from './daily-log-dirty.service';

export enum PhotoViews {
  Gallery = 'gallery',
  Upload = 'upload'
}

@Injectable()
export class DailyLogPhotosService implements IComponentService<PhotosComponentProjectUser> {
  // Injected services
  private constructionPhotosService = inject(ConstructionPhotosService);
  private sanitizer = inject(DomSanitizer);
  private dirtyService = inject(DailyLogDirtyService);
  
  // Public signals
  component = signal<PhotosComponentProjectUser | null>(null);
  view = signal<ComponentView>(ComponentView.ReadOnly);
  access = signal<string>('Allow');
  mainView = signal<string>(PhotoViews.Gallery);
  galleryImages = signal<Array<GalleryImage>>([]);
  isLoading = signal<boolean>(false);
  projectId = signal<string>('');
  
  // Implement isDirty signal required by IComponentService
  private readonly _isDirty = signal(false);
  readonly isDirty: Signal<boolean> = this._isDirty.asReadonly();
  
  // Additional signals for managing state - project-specific

  constructor() {
    // Subscribe to completion of photo uploads
    // const completeCheck = toSignal(toObservable(this.uploadComplete).pipe(
    //   tap((complete) => {
    //     if (complete) {
    //       const currentProjectId = this.projectId();
    //       if (!currentProjectId) return;
          
    //       // Get project-specific photos
    //       const projectPhotos = new Array<PhotoProcessorInfo>();//this.constructionPhotosService.getProjectPhotos(currentProjectId)();
          
    //       // Update gallery images for uploaded photos
    //       for (let photo of projectPhotos) { 
    //         this.galleryImages.update((images) => {
    //           const image = images.find((img) => img.Id === photo.Id);
    //           if (image) {
    //             image.UploadDate = new Date();
    //             image.LargeUrl = `https://${photo.LargePhotoInfo.Bucket}.s3.amazonaws.com/${photo.LargePhotoInfo.Key}`;
    //             image.ThumbUrl = `https://${photo.ThumbPhotoInfo.Bucket}.s3.amazonaws.com/${photo.ThumbPhotoInfo.Key}`;
    //             image.ThumbSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`https://${photo.ThumbPhotoInfo.Bucket}.s3.amazonaws.com/${photo.ThumbPhotoInfo.Key}`);
    //             image.LargeSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`https://${photo.LargePhotoInfo.Bucket}.s3.amazonaws.com/${photo.LargePhotoInfo.Key}`);
    //             image.IsUploaded = true;
    //           }
    //           return images;
    //         });
    //       }

    //       // Remove any images marked for deletion
    //       this.galleryImages.update((images) => 
    //         images.filter((image) => image.MarkForDeletion !== true)
    //       );
    //     }
    //   })
    // ));
  }

  setInitialData(photosComponent: PhotosComponentProjectUser | null, view: ComponentView, access: string): void {
    this.component.set(photosComponent);
    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
    this.mainView.set(PhotoViews.Gallery);
    
    // Reset dirty state
    this._isDirty.set(false);
    
    // Initialize gallery images from component photos
    if (photosComponent?.Photos?.length) {
      const images = photosComponent.Photos.map(photo => {
        const image = {} as GalleryImage;
        image.Id = photo.PhotoId as string;
        image.Description = photo.Description as string;
        image.Title = photo.Title as string;
        image.LargeUrl = `${photo.LargeStorage.Location}`;
        image.ThumbUrl = `${photo.ThumbnailStorage.Location}`;
        image.ThumbSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`${photo.ThumbnailStorage.Location}`);
        image.LargeSafeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`${photo.LargeStorage.Location}`);
        image.IsEditing = false;
        image.IsUploaded = true;
        image.IsProcessing = false;
        image.UploadDate = photo.UploadDate as Date;
        return image;
      });
      this.galleryImages.set(images);
    } else {
      this.galleryImages.set([]);
    }
  }

  getUpdatedComponent(): PhotosComponentProjectUser {
    return {
      _t: ['PhotosComponent'],
      ComponentIdentifier: DailyLogComponentIdentifiers.PHOTOS,
      IsActive: true,
      IsDeleted: false,
      Name: 'Photos',
      Photos: this.component()?.Photos || [],
      SelectedPhotos: this.component()?.SelectedPhotos || []
    };
  }

  setView(view: PhotoViews): void {
    this.mainView.set(view);
  }

  initializeFiles(files: FileList): void {
    Array.from(files).forEach((file) => {
      const id = v4();
      const fileUrl = URL.createObjectURL(file);
      const sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileUrl);

      // Create gallery image
      const image = {
        Id: id,
        ThumbSafeUrl: sanitizedUrl,
        LargeSafeUrl: sanitizedUrl,
        LargeUrl: fileUrl,
        ThumbUrl: fileUrl,
        IsUploaded: false, // Set to false for non-uploaded photos
        IsProcessing: false,
        IsEditing: false,
      } as GalleryImage;

      // Add to gallery images
      this.galleryImages.update((images) => [...images, image]);
      
      // Create processor info
      const photo = {
        Id: id,
        FileUrl: fileUrl,
        Status: PhotoProcessorStatus.Created,
        Name: file.name,
        ContentType: file.type,
        LargePhotoInfo: { Progress: 0 } as PhotoInfo,
        ThumbPhotoInfo: { Progress: 0 } as PhotoInfo,
        IsUploading: false
      } as PhotoProcessorInfo;

      // Get current project ID
      const currentProjectId = this.projectId();
      
      // Add to photos to upload with project ID
      this.constructionPhotosService.addPhotosToUpload(photo, currentProjectId);
      
      // Set dirty state
      this._isDirty.set(true);
      
      // Update dirty state in the DirtyService if we have a project ID
      if (currentProjectId) {
        this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.PHOTOS, true);
      }
    });  
  }

  markPhotoForDeletion(image: GalleryImage, mark: boolean): void {
    if (image.IsUploaded) {
      image.MarkForDeletion = mark;
      
      // Get current project ID
      const currentProjectId = this.projectId();
      
      if (mark) {
        // Add to photos to remove with project ID
        this.constructionPhotosService.addPhotoToRemove(image.Id, currentProjectId);
      } else {
        // Remove from photos to remove with project ID
        this.constructionPhotosService.removePhotoFromRemove(image.Id, currentProjectId);
      }
      
      // Set dirty state
      this._isDirty.set(true);
      
      // Update dirty state in the DirtyService if we have a project ID
      if (currentProjectId) {
        this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.PHOTOS, true);
      }
    } else {
      // For non-uploaded photos:
      
      // For non-uploaded photos, we need to remove them from the service
      // Since we can't directly access the private map, we'll clear and re-add the photos
      const currentProjectId = this.projectId();
      if (currentProjectId) {
        // Get current photos for this project
        //const projectPhotos = this.constructionPhotosService.getProjectPhotos(currentProjectId)();
        
        // Filter out the photo to remove
        //const updatedPhotos = projectPhotos.filter(photo => photo.Id !== image.Id);
        
        // Clear all photos for this project
        // this.constructionPhotosService.clear(currentProjectId);
        
        // // Re-add the filtered photos
        // for (const photo of updatedPhotos) {
        //   this.constructionPhotosService.addPhotosToUpload(photo, currentProjectId);
        // }
      }

      this.constructionPhotosService.projectPhotosToAdd.update(photos => photos.filter(photo => photo.Id !== image.Id));
      
      // 2. Remove immediately from gallery
      this.galleryImages.update((images) => 
        images.filter(img => img.Id !== image.Id)
      );
      
      // Set dirty state if needed
      if (this.galleryImages().length === 0) {
        this._isDirty.set(false);
        
        // Update dirty state in the DirtyService
        if (currentProjectId) {
          this.dirtyService.clearDirty(currentProjectId, DailyLogComponentIdentifiers.PHOTOS);
        }
      }
    }
  }

  cancelUpload(): void {
    // Get current project ID
    const currentProjectId = this.projectId();
    
    // Clear photos for this project
    if (currentProjectId) {
      this.constructionPhotosService.clear();
    } else {
      this.constructionPhotosService.clear();
    }
    
    // Reset the internal dirty state
    this._isDirty.set(false);
    
    // Also clear the dirty state in the DirtyService if we have a project ID
    if (currentProjectId) {
      this.dirtyService.clearDirty(currentProjectId, DailyLogComponentIdentifiers.PHOTOS);
    }
  }
}
