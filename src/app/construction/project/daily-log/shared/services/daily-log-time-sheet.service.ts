import { Injectable, Signal, computed, effect, inject, signal } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { ConstructionCostCodesService } from 'src/app/construction/shared/data-access/cost-codes.service';
import { DailyLogProjectUserService } from 'src/app/construction/shared/data-access/daily-log-project-user.service';
import { LatestDailyLogReponse, TimeCardComponentProjectUser } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { TimeCard, TimeCardCostCode, TimeCardEmployee } from 'src/app/construction/shared/interfaces/timecard';
import { CostCodeInfoComponent } from 'src/app/construction/shared/interfaces/cost-codes';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { Equipment, EquipmentStore } from 'src/app/construction/shared/interfaces/equipment';
import { rxResource } from '@angular/core/rxjs-interop';
import { EmployeeService, EmployeeStoreStatus } from 'src/app/construction/shared/data-access/employee.service';
import { EquipmentService, EquipmentStoreStatus } from 'src/app/construction/shared/data-access/equipment.service';
import { IComponentService } from 'src/app/construction/shared/interfaces/component-service.interface';
import { DailyLogDirtyService } from './daily-log-dirty.service';
import { of } from 'rxjs';

@Injectable()
export class DailyLogTimeSheetService implements IComponentService<TimeCardComponentProjectUser> {
  private toastrService = inject(ToastrService);
  private dailyLogProjectUserService = inject(DailyLogProjectUserService);
  private costCodeService = inject(ConstructionCostCodesService);
  private employeeService = inject(EmployeeService);
  private equipmentService = inject(EquipmentService);
  private dirtyService = inject(DailyLogDirtyService);

  component = signal<TimeCardComponentProjectUser | null>(null);
  view = signal<ComponentView>(ComponentView.ReadOnly);
  access = signal<string>('Allow');
  isLoading = signal<boolean>(false);
  currentDate = signal<NgbDate | null>(null);
  projectId = signal<string | null>(null);
  isImporting = computed(() => this.importTimeCardResource.isLoading());
  activeEmployees = computed(() => this.employeeService.employeeStore()?.Employees || []);
  activeEquipments = signal<Equipment[]>([]);
  latestTimeCard = computed(() => this.importTimeCardResource.value());
  
  // Store original time card data for dirty checking
  private originalTimeCards = signal<string>('');
  
  // Implement isDirty signal required by IComponentService
  private readonly _isDirty = signal(false);
  readonly isDirty: Signal<boolean> = this._isDirty.asReadonly();
  
  private loadEquipment = signal<boolean>(true);
  private loadLatestTImeCard = signal<boolean>(false);

  employeesStore = this.employeeService.employeeStore;
  importTimeCardResource = rxResource({
    request: () => ({
      projectId: this.projectId(),
      currentDate: this.currentDate(),
      loadLatest: this.loadLatestTImeCard()
    }),
    loader: (request) => {
      if(request.request.projectId && request.request.loadLatest) {
        return  this.dailyLogProjectUserService.getLastDailyLog(this.projectId() || '');
      }

      return of(null);
    }
  });

  // Use the equipment service's built-in rxResource
  equipmentGrid = this.equipmentService.equipmentGrid;
  //isEquipmentLoading = this.equipmentService.isEquipmentLoading;

  constructor() {
    this.employeeService.executeEmployeeStore.set(true);
    this.employeeService.status.set(EmployeeStoreStatus.Active);

    // Initialize equipment service
    this.equipmentService.status.set(EquipmentStoreStatus.Active);
    this.equipmentService.loadEquipment();

    effect(() => {
      const equipmentStore = this.equipmentGrid();
      if (equipmentStore?.Equipments?.length) {
        this.activeEquipments.set(
          equipmentStore.Equipments.map((info) => ({
            EquipmentInternalId: info.InternalId,
            Make: info.Make,
            Model: info.Model,
            Number: info.Number
          }))
        );
      }
    });

    effect(() => {
      if(this.latestTimeCard()){
          this.initLatestTimeCard(this.latestTimeCard() as LatestDailyLogReponse, this.currentDate() as NgbDate);
          this.loadLatestTImeCard.set(false); //TODO: this is necessary for this build, fix this in the future
      }
    });

    effect(() => {
      if(this.currentDate()){
        this.loadLatestTImeCard.set(false);
      }
    });
    
    // Effect to track changes to time card data and update dirty state
    effect(() => {
      const currentComponent = this.component();
      const currentProjectId = this.projectId();
      const originalTimeCardsJson = this.originalTimeCards();
      
      if (currentComponent && currentProjectId) {
        // Serialize current time cards to JSON for comparison
        const currentTimeCardsJson = JSON.stringify(currentComponent.TimeCards || []);
        
        // Check if time cards have changed
        const isDirty = currentTimeCardsJson !== originalTimeCardsJson;
        
        // Update dirty state
        this._isDirty.set(isDirty);
        
        // Update dirty state in the DirtyService
        this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.TIMECARD, isDirty);
        
        if (isDirty) {
          // console.log(`Time Sheet dirty state set to true for project ${currentProjectId}`);
        }
      }
    });
  }

  initLatestTimeCard(lastDailyLogResponse: LatestDailyLogReponse | null, currentDate: NgbDate): void {
        // Initialize the new timeCard structure
        const timeCard: TimeCard = {
          Id: '', // This is for the *new* day's log, so no existing TimeCard Id
          CostCodes: [],
          Employees: [],
          EquipmentTrackers: [] // Initialize even if null in source
        };

        let employeesAdded = false;

        // First, get employees from LatestDailyLogOnAccount
        if (lastDailyLogResponse?.LatestDailyLogOnAccount) {
          const accountDailyLogTimeCard = lastDailyLogResponse.LatestDailyLogOnAccount.Components.find(
            x => x.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD
          ) as TimeCardComponentProjectUser | undefined;

          if (accountDailyLogTimeCard?.TimeCards?.length) {
            const accountTimeCard = accountDailyLogTimeCard.TimeCards[0];
            
            // Copy employees from account's latest daily log
            if (accountTimeCard.Employees?.length) {
              timeCard.Employees = [...accountTimeCard.Employees];
              // Reset each employee's properties
              for (let emp of timeCard.Employees) {
                emp.CostCodes = [];
                emp.TotalHours = null;
                emp.TimeReason = null;
              }
              employeesAdded = true;
            }
          }
        }

        // Then, get cost codes from LatestDailyLogOnProject
        if (lastDailyLogResponse?.LatestDailyLogOnProject) {
          const projectDailyLog = lastDailyLogResponse.LatestDailyLogOnProject;
          
          // If the latest log found IS for the current date the user is viewing, there's nothing "last" to copy.
          if (projectDailyLog.Year === currentDate.year &&
              projectDailyLog.Month === currentDate.month &&
              projectDailyLog.Day === currentDate.day) {
            this.toastrService.info('The latest log is for the current date. Cannot copy last entry.');            
            return;
          }

          const projectDailyLogTimeCard = projectDailyLog.Components.find(
            x => x.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD
          ) as TimeCardComponentProjectUser | undefined;


          if (projectDailyLogTimeCard && projectDailyLogTimeCard?.TimeCards?.length) {
            const projectTimeCard = projectDailyLogTimeCard.TimeCards[0];
            
            // Get cost codes from project's latest daily log
            if (projectTimeCard.CostCodes?.length) {
              const costCodes = [...projectTimeCard.CostCodes];
              
              // Reset QtyCompl for each cost code
              for (let cost of costCodes) {
                cost.QtyCompl = null;
              }
              
              // Set the cost codes on the time card
              timeCard.CostCodes = costCodes;
              
              // Add cost codes to each employee
              for (let emp of timeCard.Employees) {
                for (let costCode of costCodes) {
                  emp.CostCodes.push(Object.assign({}, costCode as TimeCardCostCode));
                }
              }
              
              // Initialize equipment trackers if not already present
              if (!timeCard.EquipmentTrackers) {
                timeCard.EquipmentTrackers = [];
              }
            }
          } else if (!projectDailyLogTimeCard?.TimeCards?.length) {
            this.toastrService.warning('The previous daily log does not contain time card data to copy.');
          }

          // Check if we have any data to add
          if (timeCard.Employees.length === 0 && timeCard.CostCodes.length === 0) {
            this.toastrService.info('No employees or cost codes found to copy. Please add them manually.');
            return;
          }

          // Update the component signal with the new TimeCard structure
          this.component.update((currentComponent) => {
            if (!currentComponent) return null;
            // Replace the entire TimeCards array. Assuming only one TimeCard object per daily log.
            currentComponent.TimeCards = [timeCard];
            // Return a new object reference to potentially help with change detection if needed
            return { ...currentComponent };
          });

          // Explicitly set the dirty state after importing
          // This indicates that the copied data needs to be saved
          const currentProjectId = this.projectId();
          if (currentProjectId) {
            this._isDirty.set(true);
            this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.TIMECARD, true);
          }
        } else {
          // No LatestDailyLogOnProject, but we might still have employees from LatestDailyLogOnAccount
          // or from the global employee store
          
          // Check if we have any data to add
          if (timeCard.Employees.length === 0) {
            // No employees found from any source
            this.toastrService.info('No employees found to copy. Please add them manually.');

            return;
          }
          
          // We have employees but no cost codes
          // Update the component signal with the new TimeCard structure
          this.component.update((currentComponent) => {
            if (!currentComponent) return null;
            // Replace the entire TimeCards array. Assuming only one TimeCard object per daily log.
            currentComponent.TimeCards = [timeCard];
            // Return a new object reference to potentially help with change detection if needed
            return { ...currentComponent };
          });

          // Explicitly set the dirty state after importing
          // This indicates that the copied data needs to be saved
          const currentProjectId = this.projectId();
          if (currentProjectId) {
            this._isDirty.set(true);
            this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.TIMECARD, true);
          }
        }
  }

  setInitialData(
    data: TimeCardComponentProjectUser | null,
    view: ComponentView,
    access: string
  ): void {
    this.component.set(data);
    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
    
    // Store original time cards for dirty checking
    this.originalTimeCards.set(JSON.stringify(data?.TimeCards || []));
    
    // Reset dirty state
    this._isDirty.set(false);
    
    // Clear dirty state in the DirtyService if we have a project ID
    const currentProjectId = this.projectId();
    if (currentProjectId) {
      this.dirtyService.clearDirty(currentProjectId, DailyLogComponentIdentifiers.TIMECARD);
    }
  }

  getUpdatedComponent(): TimeCardComponentProjectUser {
    // Get current TimeCards and clean them
    const currentTimeCards = this.component()?.TimeCards || [];
    const cleanedTimeCards = currentTimeCards.map(timeCard => {
      const cleanedTimeCard = { ...timeCard };
      if (cleanedTimeCard.Employees) {
        cleanedTimeCard.Employees = cleanedTimeCard.Employees.map(employee => {
          // Create a new employee object with only the required properties
          return {
            FirstName: employee.FirstName,
            LastName: employee.LastName,
            EmployeeId: employee.EmployeeId,
            Classification: employee.Classification,
            // CustomId: employee.CustomId,
            CostCodes: employee.CostCodes,
            TimeReason: employee.TimeReason,
            TotalHours: employee.TotalHours
          };
        });
      }
      return cleanedTimeCard;
    });

    const component = {
      _t: ['TimeCardComponent'],
      ComponentIdentifier: DailyLogComponentIdentifiers.TIMECARD,
      IsActive: true,
      IsDeleted: false,
      Name: 'Time Sheet',
      TimeCards: cleanedTimeCards
    };
    
    // Update the original time cards to match the current value
    // This will be done when the component is saved
    this.originalTimeCards.set(JSON.stringify(component.TimeCards || []));
    
    // Clear the dirty state
    const currentProjectId = this.projectId();
    if (currentProjectId) {
      this._isDirty.set(false);
      this.dirtyService.clearDirty(currentProjectId, DailyLogComponentIdentifiers.TIMECARD);
    }
    
    return component;
  }

  //TODO: this is functioning halfway.  This needs full proper signal implementation in the future.
  importRecentTimeCard(): void {    
    const projectId = this.projectId();
    const currentDate = this.currentDate(); 

    if (!projectId || !currentDate) {
      this.toastrService.error('Project ID or current date is missing.');
      return;
    }

    this.currentDate.set(currentDate);
    this.loadLatestTImeCard.set(true);    

    // this.isImporting.set(true);
    // this.dailyLogProjectUserService
    //   .getLastDailyLog(projectId)
    //   .subscribe({
    //     next: (lastDailyLogResponse) => {
    //       this.initLatestTimeCard(lastDailyLogResponse, currentDate);
    //     },
    //     error: (err: any) => {
    //       this.handleError(err, 'getting last daily log');
    //       this.isImporting.set(false);
    //     }
    //   });
  }

  validate(): boolean {
    const timeCardComponent = this.component();
    if (!timeCardComponent?.TimeCards) return true;

    for (const timeCard of timeCardComponent.TimeCards) {
      if (timeCard.Employees?.length) {
        for (const employee of timeCard.Employees) {
          const hasHours = employee.CostCodes?.some((cc) => cc.UserValue != null && cc.UserValue > 0);
          if (hasHours && (!employee.TotalHours || employee.TotalHours <= 0)) {
            this.toastrService.error(
              'Invalid time sheet: Employees with assigned hours must have a total hours value.'
            );
            return false;
          }
        }
      }
    }
    return true;
  }

  private handleError(err: any, context: string): void {
    const errorMessage = err instanceof HttpErrorResponse
      ? `Error ${err.status}: ${err.message}`
      : 'An unknown error occurred.';
    console.error(`Error fetching ${context}:`, errorMessage);
    this.toastrService.error(`Error fetching ${context}`);
  }
}
