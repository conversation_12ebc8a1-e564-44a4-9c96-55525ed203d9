import { Injectable, Signal, inject, signal, effect } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { SiteConditionsComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { SiteConditionsComponentProjectUser } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { rxResource } from '@angular/core/rxjs-interop';
import { ToastrService } from 'ngx-toastr';
import { IComponentService } from 'src/app/construction/shared/interfaces/component-service.interface';
import { DailyLogDirtyService } from './daily-log-dirty.service';

@Injectable()
export class DailyLogSiteConditionsService implements IComponentService<SiteConditionsComponentProjectUser> {
  private dailyLogComponentService = inject(ConstructionDailyLogService);
  private toastrService = inject(ToastrService);
  private dirtyService = inject(DailyLogDirtyService);
  
  // Signals for reactive state management
  siteCondition = signal<string>('');
  conditions = signal<string[]>([]);
  view = signal<ComponentView>(ComponentView.ReadOnly);
  access = signal<string>('Allow');
  isLoading = signal<boolean>(false);
  projectId = signal<string>('');
  
  // Track original site condition value to detect changes
  private originalSiteCondition = signal<string>('');
  
  // Implement isDirty signal required by IComponentService
  private readonly _isDirty = signal(false);
  readonly isDirty: Signal<boolean> = this._isDirty.asReadonly();
  
  // rxResource for fetching site conditions
  private loadConditions = signal<boolean>(false);
  siteConditionsResource = rxResource({
    request: () => this.loadConditions(),
    loader: () => this.dailyLogComponentService.getSubComponent(
      DailyLogComponentIdentifiers.SITE_CONDITIONS
    )
  });

  constructor() {
    // Initialize the resource
    this.loadConditions.set(true);
    
    // Use effect to handle errors and update conditions
    effect(() => {
      const error = this.siteConditionsResource.error();
      if (error) {
        this.handleError(error);
      }
    });
    
    effect(() => {
      const value = this.siteConditionsResource.value();
      if (value && value.ComponentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS) {
        const siteConditionsComponent = value as SiteConditionsComponent;
        this.conditions.set(siteConditionsComponent.Conditions || []);
      }
    });
    
    // Effect to track changes to site condition and update dirty state
    effect(() => {
      const currentSiteCondition = this.siteCondition();
      const originalSiteCondition = this.originalSiteCondition();
      const currentProjectId = this.projectId();
      
      // Only update dirty state if we have a valid project ID
      if (currentProjectId) {
        const isDirty = currentSiteCondition !== originalSiteCondition;
        this._isDirty.set(isDirty);
        this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.SITE_CONDITIONS, isDirty);
      }
    });
  }

  setInitialData(siteConditionsComponent: SiteConditionsComponentProjectUser | null, view: ComponentView, access: string): void {
    const siteConditionValue = siteConditionsComponent?.SiteCondition || '';
    
    // Set site condition to empty string if not provided
    this.siteCondition.set(siteConditionValue);
    this.originalSiteCondition.set(siteConditionValue); // Store original value for comparison
    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
    
    // Reset dirty state
    this._isDirty.set(false);

    // Load conditions from the resource if needed
    if (this.conditions().length === 0) {
      this.loadConditions.set(true);
    }
  }

  getUpdatedComponent(): SiteConditionsComponentProjectUser {
    const component = {
      _t: ['SiteConditionComponent'],
      ComponentIdentifier: DailyLogComponentIdentifiers.SITE_CONDITIONS,
      IsActive: true,
      IsDeleted: false,
      Name: 'Site Conditions',
      SiteCondition: this.siteCondition()
    };
    
    // Update the original site condition to match the current value
    // This will be done when the component is saved
    this.originalSiteCondition.set(this.siteCondition());
    
    // Clear the dirty state
    if (this.projectId()) {
      this._isDirty.set(false);
      this.dirtyService.clearDirty(this.projectId(), DailyLogComponentIdentifiers.SITE_CONDITIONS);
    }
    
    return component;
  }

  updateSiteCondition(newCondition: string): void {
    this.siteCondition.set(newCondition);
  }

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred.';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    this.toastrService.error(errorMessage, 'Error');
    return throwError(() => new Error(errorMessage));
  }
}
