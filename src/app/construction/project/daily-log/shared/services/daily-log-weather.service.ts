import { Injectable, Signal, inject, signal, effect } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { WeatherComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { WeatherComponentProjectUser } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { rxResource } from '@angular/core/rxjs-interop';
import { ToastrService } from 'ngx-toastr';
import { IComponentService } from 'src/app/construction/shared/interfaces/component-service.interface';
import { DailyLogDirtyService } from './daily-log-dirty.service';

@Injectable()
export class DailyLogWeatherService implements IComponentService<WeatherComponentProjectUser> {
  private dailyLogComponentService = inject(ConstructionDailyLogService);
  private toastrService = inject(ToastrService);
  private dirtyService = inject(DailyLogDirtyService);
  
  // Signals for reactive state management
  temperature = signal<string>('');
  selectedSkys = signal<string[]>([]);
  availableTemperatures = signal<string[]>([]);
  availableSkys = signal<{ ItemId: string; ItemText: string }[]>([]);
  view = signal<ComponentView>(ComponentView.ReadOnly);
  access = signal<string>('Allow');
  isLoading = signal<boolean>(false);
  projectId = signal<string>('');
  
  // Track original values for dirty checking
  private originalTemperature = signal<string>('');
  private originalSelectedSkys = signal<string[]>([]);
  
  // Implement isDirty signal required by IComponentService
  private readonly _isDirty = signal(false);
  readonly isDirty: Signal<boolean> = this._isDirty.asReadonly();
  
  // rxResource for fetching weather data
  private loadWeather = signal<boolean>(false);
  weatherResource = rxResource({
    request: () => this.loadWeather(),
    loader: () => this.dailyLogComponentService.getSubComponent(
      DailyLogComponentIdentifiers.WEATHER
    )
  });

  constructor() {
    // Initialize the resource
    this.loadWeather.set(true);
    
    // Use effect to handle errors and update weather data
    effect(() => {
      const error = this.weatherResource.error();
      if (error) {
        this.handleError(error);
      }
    });
    
    effect(() => {
      const value = this.weatherResource.value();
      if (value && value.ComponentIdentifier === DailyLogComponentIdentifiers.WEATHER) {
        const weatherComponent = value as WeatherComponent;
        this.availableTemperatures.set(weatherComponent.Temperatures || []);
        this.availableSkys.set(
          (weatherComponent.Skys || []).map((sky) => ({
            ItemId: sky,
            ItemText: sky,
          }))
        );
      }
    });
    
    // Effect to track changes to weather data and update dirty state
    effect(() => {
      const currentTemperature = this.temperature();
      const currentSelectedSkys = this.selectedSkys();
      const originalTemperature = this.originalTemperature();
      const originalSelectedSkys = this.originalSelectedSkys();
      const currentProjectId = this.projectId();
      
      if (currentProjectId) {
        // Check if temperature or selected skys have changed
        const temperatureChanged = currentTemperature !== originalTemperature;
        
        // Compare arrays for content equality, ignoring order
        const skysChanged = !this.arraysEqual(currentSelectedSkys, originalSelectedSkys);
        
        const isDirty = temperatureChanged || skysChanged;
        
        // Update dirty state
        this._isDirty.set(isDirty);
        
        // Update dirty state in the DirtyService
        this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.WEATHER, isDirty);
      }
    });
  }

  // Helper method to compare arrays for equality regardless of order
  private arraysEqual(arr1: string[], arr2: string[]): boolean {
    if (arr1.length !== arr2.length) return false;
    const sortedArr1 = [...arr1].sort();
    const sortedArr2 = [...arr2].sort();
    return sortedArr1.every((value, index) => value === sortedArr2[index]);
  }

  setInitialData(weatherComponent: WeatherComponentProjectUser | null, view: ComponentView, access: string): void {    
    const temperatureValue = weatherComponent?.Temperature || '';
    const selectedSkysValue = weatherComponent?.SelectedSkys || [];
    
    // Set current values
    this.temperature.set(temperatureValue);
    this.selectedSkys.set(selectedSkysValue);
    
    // Store original values for dirty checking
    this.originalTemperature.set(temperatureValue);
    this.originalSelectedSkys.set([...selectedSkysValue]); // Create a copy of the array
    
    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
    
    // Reset dirty state
    this._isDirty.set(false);
    
    // Clear dirty state in the DirtyService if we have a project ID
    const currentProjectId = this.projectId();
    if (currentProjectId) {
      this.dirtyService.clearDirty(currentProjectId, DailyLogComponentIdentifiers.WEATHER);
    }
    
    // Load weather data from the resource if needed
    if (this.availableTemperatures().length === 0 || this.availableSkys().length === 0) {
      this.loadWeather.set(true);
    }
  }

  getUpdatedComponent(): WeatherComponentProjectUser {
    const component = {
      _t: ['WeatherComponent'],
      ComponentIdentifier: DailyLogComponentIdentifiers.WEATHER,
      IsActive: true,
      IsDeleted: false,
      Name: 'Weather',
      Temperature: this.temperature(),
      SelectedSkys: this.selectedSkys()
    };
    
    // Update the original values to match the current values
    // This will be done when the component is saved
    this.originalTemperature.set(this.temperature());
    this.originalSelectedSkys.set([...this.selectedSkys()]); // Create a copy of the array
    
    // Clear the dirty state
    const currentProjectId = this.projectId();
    if (currentProjectId) {
      this._isDirty.set(false);
      this.dirtyService.clearDirty(currentProjectId, DailyLogComponentIdentifiers.WEATHER);
    }
    
    return component;
  }

  updateTemperature(newTemperature: string): void {
    this.temperature.set(newTemperature);
  }

  addSky(sky: string): void {
    if (!this.selectedSkys().includes(sky)) {
      this.selectedSkys.update((skys) => [...skys, sky]);
    }
  }

  removeSky(sky: string): void {
    this.selectedSkys.update((skys) => skys.filter((s) => s !== sky));
  }

  updateSkys(skys: string[]): void {
    this.selectedSkys.set(skys);
  }

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred.';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    this.toastrService.error(errorMessage, 'Error');
    return throwError(() => new Error(errorMessage));
  }
}
