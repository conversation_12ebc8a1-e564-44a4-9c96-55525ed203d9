import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DailyLogProjectUserComponent } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { DailyLogService } from './daily-log.service';

@Injectable()
export class DailyLogButtonsService {
  private dailyLogService = inject(DailyLogService);
  private toastrService = inject(ToastrService);

  isEnabled = computed(() => !this.dailyLogService.isPersistingSignal() && !this.dailyLogService.isLoadingSignal());
  canDelete = computed(() => !!this.dailyLogService.dailyLogProjectUserComponentSignal()?.Id);
  canSubmit = computed(() => !this.dailyLogService.approvalSignal());
  canUnSubmit = computed(() => !!this.dailyLogService.approvalSignal() && !this.dailyLogService.approvalSignal()?.FinalApprovalDate);
  noWork = computed(() => this.dailyLogService.dailyLogProjectUserComponentSignal()?.NoWork ?? false);
  hasAccess = computed(() => this.dailyLogService.accessSignal() === 'Allow');
  isSubmitting = this.dailyLogService.isSubmitting;
  isApproving = this.dailyLogService.isApproving;
  isSaving = this.dailyLogService.isPersistingSignal;

  constructor() {
    // effect(() => {
    //   console.log('Button states updated:', {
    //     isEnabled: this.isEnabled(),
    //     canDelete: this.canDelete(),
    //     canSubmit: this.canSubmit(),
    //     canUnSubmit: this.canUnSubmit(),
    //     noWork: this.noWork(),
    //     hasAccess: this.hasAccess()
    //   });
    // });
  }

  clear(): void {
    this.dailyLogService.clear();
  }

  remove(): void {
    this.dailyLogService.remove();
  }

  save(): void {
    this.dailyLogService.save().subscribe({
      next: (result: DailyLogProjectUserComponent) => this.toastrService.success('Log has been saved'),
      error: (err: unknown) => this.toastrService.error('There was an error saving the log')
    });
  }

  submit(): void {
    this.dailyLogService.submit();
  }

  unSubmit(): void {
    this.dailyLogService.unSubmit();
  }
}