import { Injectable, Signal, effect, inject, signal } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { NotesComponentProjectUser } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { IComponentService } from 'src/app/construction/shared/interfaces/component-service.interface';
import { DailyLogDirtyService } from './daily-log-dirty.service';

@Injectable()
export class DailyLogNotesService implements IComponentService<NotesComponentProjectUser> {
  private toastrService = inject(ToastrService);
  private dirtyService = inject(DailyLogDirtyService);
  
  notes = signal<string>('');
  view = signal<ComponentView>(ComponentView.ReadOnly);
  access = signal<string>('Allow');
  isLoading = signal<boolean>(false);
  projectId = signal<string>('');
  
  // Track original note value to detect changes
  private originalNote = signal<string>('');
  
  // Implement isDirty signal required by IComponentService
  private readonly _isDirty = signal(false);
  readonly isDirty: Signal<boolean> = this._isDirty.asReadonly();
  
  constructor() {
    // Effect to track changes to notes and update dirty state
    effect(() => {
      const currentNotes = this.notes();
      const originalNotes = this.originalNote();
      const currentProjectId = this.projectId();
      
      // Only update dirty state if we have a valid project ID
      if (currentProjectId) {
        const isDirty = currentNotes !== originalNotes;
        this._isDirty.set(isDirty);
        this.dirtyService.setDirty(currentProjectId, DailyLogComponentIdentifiers.NOTES, isDirty);
      }
    });
  }

  setInitialData(notesComponent: NotesComponentProjectUser | null, view: ComponentView, access: string): void {
    const noteValue = notesComponent?.Note || '';
    this.notes.set(noteValue);
    this.originalNote.set(noteValue); // Store original value for comparison
    this.view.set(view);
    this.access.set(access);
    this.isLoading.set(false);
    
    // Note: ProjectId is set by the DailyLogService when it calls updateNotesService
    // We don't need to set it here as it's injected from the parent component
  }

  getUpdatedComponent(): NotesComponentProjectUser {
    // When getting the updated component, also reset the dirty state if the component is saved
    const component = {
      _t: ['NotesComponent'],
      ComponentIdentifier: DailyLogComponentIdentifiers.NOTES,
      IsActive: true,
      IsDeleted: false,
      Name: 'Notes',
      Note: this.notes()
    };
    
    // Update the original note to match the current value
    // This will be done when the component is saved
    this.originalNote.set(this.notes());
    
    // Clear the dirty state
    if (this.projectId()) {
      this._isDirty.set(false);
      this.dirtyService.clearDirty(this.projectId(), DailyLogComponentIdentifiers.NOTES);
    }
    
    return component;
  }
}
