import { Injectable, inject, signal, computed, effect, Signal } from '@angular/core';
import { EMPTY, Observable, from, of } from 'rxjs';
import { switchMap, catchError, tap, finalize } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { DiaryService } from '../../../../diary/diary.service';
import { ApprovalService } from '../../../../shared/data-access/approval.service';
import { DailyLogProjectUserService } from '../../../../shared/data-access/daily-log-project-user.service';
import { ConstructionPhotosService } from '../../../../shared/data-access/photos.service';
import { DailyLogDirtyService } from './daily-log-dirty.service';
import { RoleSettingsDiaryService } from 'src/app/account/shared/data-access/role-settings-diary-service';
import { DiarySectionType, VisibilityType } from 'src/app/account/user/shared/enums/diary-section.enum';
import {
  DailyLogProjectUserComponent,
  PhotosComponentProjectUser,
  TimeCardComponentProjectUser,
  SiteConditionsComponentProjectUser,
  WeatherComponentProjectUser
} from '../../../../shared/interfaces/daily-log-project-user';
import {
  DailyLogComponentIdentifiers,
  DailyLogInfoComponentSerializerInfo
} from '../../../../shared/interfaces/daily-log-shared';
import { Approval } from '../../../../shared/interfaces/approval';
import { Photo, PhotoProcessorInfo } from '../../../../shared/interfaces/photos';
import { DailyLogNotesService } from './daily-log-notes.service';
import { DailyLogTimeSheetService } from './daily-log-time-sheet.service';
import { DailyLogSiteConditionsService } from './daily-log-site-conditions.service';
import { DailyLogWeatherService } from './daily-log-weather.service';
import { DailyLogPhotosService } from './daily-log-photos.service';
import { ComponentView } from '../../../../shared/interfaces/project-components';
import { DiaryPageService } from 'src/app/construction/shared/data-access/diary-page.service';

/**
 * Type definitions
 */
interface ApprovalInput {
  DailyLogId: string;
  ProjectId: string;
  UserId: string;
  DailyLogTimeStamp: Date;
}

type ComponentServiceMap = {
  [key: string]: {
    service: any;
    getUpdatedComponent?: () => any;
  };
};

/**
 * Utility: Recursively remove specified fields from all objects/arrays in the payload
 */
function deepStripFields(obj: any, fields: string[] = []): any {
  if (Array.isArray(obj)) {
    return obj.map(item => deepStripFields(item, fields));
  } else if (obj && typeof obj === 'object') {
    const rest = { ...obj };
    for (const field of fields) {
      delete rest[field];
    }
    for (const key in rest) {
      if (rest.hasOwnProperty(key)) {
        rest[key] = deepStripFields(rest[key], fields);
      }
    }
    return rest;
  }
  return obj;
}

/**
 * Service responsible for managing daily log operations including CRUD operations,
 * component state management, and interactions with related services.
 */
@Injectable()
export class DailyLogService {
  // Injected services
  public readonly diaryService = inject(DiaryService);
  private readonly authService = inject(AuthService);
  private readonly approvalService = inject(ApprovalService);
  private readonly dailyLogProjectUserService = inject(DailyLogProjectUserService);
  private readonly photosService = inject(ConstructionPhotosService);
  public readonly toastrService = inject(ToastrService);
  private readonly confirmService = inject(ConfirmService);
  private readonly notesService = inject(DailyLogNotesService);
  private readonly timeSheetService = inject(DailyLogTimeSheetService);
  private readonly siteConditionsService = inject(DailyLogSiteConditionsService);
  private readonly weatherService = inject(DailyLogWeatherService);
  private readonly photosComponentService = inject(DailyLogPhotosService);
  private readonly dirtyService = inject(DailyLogDirtyService);
  private readonly roleSettingsDiaryService = inject(RoleSettingsDiaryService);

  // State management with signals
  private readonly projectId = signal<string>('');
  private readonly dailyLogProjectUserComponent = signal<DailyLogProjectUserComponent | null>(null);
  readonly approval = signal<Approval | null>(null);
  private readonly isLoading = signal<boolean>(false);  
  private readonly isLoadingApproval = signal<boolean>(false);
  private readonly isPersisting = signal<boolean>(false);
  private alertMessage = signal<string>('');
  isSubmitting = signal<boolean>(false);    
  isSettingNoWork = signal<boolean>(false);  
  isAddingApproval = signal<boolean>(false);  
  isApproving = signal<boolean>(false);
  private readonly access = signal<string>('Allow');
  private readonly diarySections = signal<{ Component: string, SettingValue: string }[]>([]);
  
  // Flag to prevent the effect from setting isLoading to false during submit/unsubmit
  private inSubmitOrUnsubmit = false;
  

  // Component service mapping for DRY operations
  private readonly componentServices: ComponentServiceMap = {
    [DailyLogComponentIdentifiers.NOTES]: {
      service: this.notesService,
      getUpdatedComponent: () => this.notesService.getUpdatedComponent()
    },
    [DailyLogComponentIdentifiers.TIMECARD]: {
      service: this.timeSheetService,
      getUpdatedComponent: () => this.timeSheetService.getUpdatedComponent()
    },
    [DailyLogComponentIdentifiers.SITE_CONDITIONS]: {
      service: this.siteConditionsService,
      getUpdatedComponent: () => this.siteConditionsService.getUpdatedComponent()
    },
    [DailyLogComponentIdentifiers.WEATHER]: {
      service: this.weatherService,
      getUpdatedComponent: () => this.weatherService.getUpdatedComponent()
    },
    [DailyLogComponentIdentifiers.PHOTOS]: {
      service: this.photosComponentService
    }
  };

  // Public read-only signals
  public readonly projectIdSignal = this.projectId.asReadonly();
  public readonly dailyLogProjectUserComponentSignal = this.dailyLogProjectUserComponent.asReadonly();
  public readonly approvalSignal = this.approval.asReadonly();
  public readonly isLoadingSignal = this.isLoading.asReadonly();
  public readonly isLoadingApprovalSignal = this.isLoadingApproval.asReadonly();
  public readonly isPersistingSignal = this.isPersisting.asReadonly();
  public readonly alertMessageSignal = this.alertMessage.asReadonly();
  public readonly accessSignal = this.access.asReadonly();
  diaryPageService = inject(DiaryPageService);
  public readonly currentDateSignal = computed(() =>
    new NgbDate(
      this.diaryService.selectedYear(),
      this.diaryService.selectedMonth(),
      this.diaryService.selectedDay()
    )
  );


  // Computed properties
  private readonly currentLog = computed(() => {
    const projectId = this.projectId();
    return projectId ? this.diaryService.getDailyLogByProjectId(projectId) : undefined;
  });

  constructor() {
    // Initialize role settings when service is initialized
    this.initializeRoleSettings();

    // Register this service with the DailyLogDirtyService
    effect(() => {
      const projectId = this.projectId();
      if (projectId) {
        this.dirtyService.registerService(projectId, this);
        this.diaryPageService.registerService(projectId, this);
      }
    });

    // Effect to update component state when the current log changes
    effect(() => {
      const log = this.currentLog();      // Only set isLoading to true if we're not in the middle of a submit/unsubmit operation
      
      
      if (!this.inSubmitOrUnsubmit) {
        this.isLoading.set(true);
      }
      this.dailyLogProjectUserComponent.set(log ?? null);
      this.approval.set(log ? this.diaryService.getApproval(log.Id) : null);

      // Clear photos to upload when changing logs to prevent photos from one day
      // being uploaded and linked to another day's log      
      this.photosService.clear();
      
      // Also clear the dirty state for the photos component to ensure the new day's log
      // doesn't incorrectly show as dirty
      if (this.projectId()) {
        this.dirtyService.clearDirty(this.projectId(), DailyLogComponentIdentifiers.PHOTOS);
      }

      // Only set isLoading to false if we're not in the middle of a submit/unsubmit operation
      if (!this.inSubmitOrUnsubmit) {
        this.isLoading.set(false);
      }
      if (log) {
        this.updateAllComponentServices();
      }
    });
  }

  /**
   * Lifecycle hook that is called when the component is destroyed
   * Unregisters this service from the DailyLogDirtyService
   */
  ngOnDestroy(): void {
    const projectId = this.projectId();
    if (projectId) {
      this.dirtyService.unregisterService(projectId);
    }
  }

  /**
   * Initializes role settings by setting the role ID to null
   * This is called once in the constructor to load default role settings
   */
  private initializeRoleSettings(): void {
    // Set the role ID to null to use the base URL
    // This will trigger an API call only if the role ID has changed
    this.roleSettingsDiaryService.setRoleId(null);
  }

  /**
   * Sets default section visibility settings
   */
  private setDefaultSettings(): void {
    const defaultSettings = [
      { Component: DiarySectionType.NOTES, SettingValue: VisibilityType.SHOW },
      { Component: DiarySectionType.SITE_CONDITIONS, SettingValue: VisibilityType.SHOW },
      { Component: DiarySectionType.PHOTOS, SettingValue: VisibilityType.SHOW },
      { Component: DiarySectionType.TIME_SHEET, SettingValue: VisibilityType.SHOW },
      { Component: DiarySectionType.WEATHER, SettingValue: VisibilityType.SHOW }
    ];
    this.diarySections.set(defaultSettings);
  }

  /**
   * Checks if a section is visible based on user role settings
   * @param sectionName The name of the section to check
   * @returns A Signal<boolean> indicating if the section is visible
   */
  isSectionVisible(sectionName: string): Signal<boolean> {
    return computed(() => {
      // Directly use the roleSettingsDiaryService.diarySections() signal
      // This ensures reactivity when the settings change
      const diarySettings = this.roleSettingsDiaryService.diarySections();

      // If we have settings, check if the section is visible
      if (diarySettings && diarySettings.length > 0) {
        const section = diarySettings.find(s => s.Component === sectionName);
        return section ? section.SettingValue === VisibilityType.SHOW : true;
      }

      // If no settings are available, default to showing the section
      return true;
    });
  }

  /**
   * Sets the current project ID and resets state
   */
  setProjectId(projectId: string): void {
    if (this.projectId() !== projectId) {
      this.projectId.set(projectId);
      this.resetState();
    }
  }

  /**
   * Checks if a component is active in the current daily log
   */
  isActive(componentIdentifier: string): boolean {
    return this.dailyLogProjectUserComponent()?.Components
      .find(c => c.ComponentIdentifier === componentIdentifier)?.IsActive ?? true;
  }

  /**
   * Calculates total hours from time cards for both employees and equipment
   */
  calculateTotalHours(): number {
    const timeCardComponent = this.findComponentByIdentifier(
      this.dailyLogProjectUserComponent(),
      DailyLogComponentIdentifiers.TIMECARD
    ) as TimeCardComponentProjectUser | null;

    if (!timeCardComponent?.TimeCards) return 0;

    return timeCardComponent.TimeCards.reduce((total, timeCard) => {
      const employeeHours = (timeCard.Employees ?? []).reduce((sum, emp) => sum + (emp.TotalHours ?? 0), 0);
      const equipmentHours = (timeCard.EquipmentTrackers ?? []).reduce((sum, eq) => sum + (eq.TotalHours ?? 0), 0);
      return total + employeeHours + equipmentHours;
    }, 0);
  }

  /**
   * Clears the current log after confirmation
   */
  clear(): void {
    this.confirmAction('Are you sure you want to clear this log?', () =>
      this.dailyLogProjectUserService.createDailyLog().pipe(
        tap(result => {
          if (!result) {
            throw new Error('Failed to create a new daily log');
          }
          const newLog = this.createNewLogFromTemplate(result, this.projectId());
          this.dailyLogProjectUserComponent.set(newLog);
          this.diaryService.updateLog(newLog);
        })
      )
    );
  }

  /**
   * Removes the current log after confirmation
   */
  remove(): void {
    this.confirmAction('Are you sure you want to delete this log?', () => {
      const component = this.dailyLogProjectUserComponent();
      const projectId = this.projectId();

      if (!component?.Id || !projectId) {
        // If no Id, just reset to a new blank log locally
        return this.dailyLogProjectUserService.createDailyLog().pipe(
          tap(result => {
            if (!result) {
              throw new Error('Failed to create a new daily log');
            }
            const newLog = this.createNewLogFromTemplate(result, projectId);
            this.dailyLogProjectUserComponent.set(newLog);
            this.diaryService.updateLog(newLog);
            this.toastrService.success('Log has been cleared');
          })
        );
      }

      return this.dailyLogProjectUserService.deleteDailyLog(projectId, component.Id).pipe(
        switchMap(() => this.dailyLogProjectUserService.createDailyLog()),
        tap(result => {
          if (!result) {
            throw new Error('Failed to create a new daily log');
          }
          const newLog = this.createNewLogFromTemplate(result, projectId);
          this.dailyLogProjectUserComponent.set(newLog);
          this.diaryService.updateLog(newLog);
          this.toastrService.success('Log has been deleted');
        })
      );
    });
  }

  /**
   * Validates prerequisites before saving a daily log
   */
  private validateSavePrerequisites(component: DailyLogProjectUserComponent | null, projectId: string): boolean {
    if (!component || !projectId || !component.Components) {
      this.toastrService.error('Missing data. Could not save');
      return false;
    }

    // Validate time sheet
    const timeCardComponent = this.findComponentByIdentifier(
      component,
      DailyLogComponentIdentifiers.TIMECARD
    ) as TimeCardComponentProjectUser | null;

    if (!timeCardComponent?.TimeCards) {
      return true; // No time cards to validate
    }

    for (const timeCard of timeCardComponent.TimeCards) {
      if (!timeCard.Employees?.length) continue;

      for (const employee of timeCard.Employees) {
        const hasCostCodes = employee.CostCodes && employee.CostCodes.length > 0;
        const hasHours = employee.CostCodes?.some(cc => cc.UserValue != null && cc.UserValue > 0);
        const isWorkDay = employee.TimeReason === 'Work Day';

        // If the employee has hours, TotalHours must be set and greater than 0
        if (hasHours && (employee.TotalHours == null || employee.TotalHours <= 0)) {
          this.toastrService.error('Invalid time sheet: Employees with assigned hours must have a total hours value.');
          return false;
        }

        // If TotalHours is greater than 0, TimeReason must be "Work Day"
        if (employee.TotalHours != null && employee.TotalHours > 0 && !isWorkDay) {
          this.toastrService.error('Invalid time sheet: Employees with hours must have a "Work Day" reason.');
          return false;
        }

        // If the employee has cost codes and is marked as "Work Day", they must have hours
        if (hasCostCodes && isWorkDay && !hasHours) {
          this.toastrService.error('Invalid time sheet: Employees marked as "Work Day" with assigned cost codes must have hours specified.');
          return false;
        }

        // If the employee has cost codes but no TimeReason, show an error
        if (hasCostCodes && !employee.TimeReason) {
          this.toastrService.error(`Invalid time sheet: Employee ${employee.FirstName} ${employee.LastName} has cost codes but no reason selected. Please select a reason.`);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Saves the current daily log
   */
  public save(): Observable<DailyLogProjectUserComponent> {
    const component = this.dailyLogProjectUserComponent();
    const projectId = this.projectId();

    if(this.approval())
    {
      return EMPTY;
    }

    if (!this.validateSavePrerequisites(component, projectId)) {
      return EMPTY;
    }

    // Create a clean copy of the component with proper structure for saving
    const cleanedComponent = this.prepareComponentForSaving(component!);

    return this.prepareAndSaveLog(cleanedComponent, projectId).pipe(
      tap(savedLog => {
        this.dailyLogProjectUserComponent.set(savedLog);
        this.diaryService.updateLog(savedLog);
      })
    );
  }

  /**
   * Submits the current log for approval
   */
  public submit(): void {   
  if (this.isPersisting() || this.isLoading()) return;

  if(this.approval())
    return;
    // First validate the log before setting loading states
    const component = this.dailyLogProjectUserComponent();
    const projectId = this.projectId();
    this.isSubmitting.set(true);

    // Perform the same validation as in save() method
    if (!this.validateSavePrerequisites(component, projectId)) {
      // If validation fails, don't proceed with submission
      this.isSubmitting.set(false);
      return;
    }

        
    this.inSubmitOrUnsubmit = true;
    this.isSubmitting.set(true);    // Keep loading state active throughout the entire process
    //this.setLoadingStates(true);

    // Step 1: Save the daily log
    this.save().pipe(
      // Step 2: Add approval
      switchMap(() => this.addApproval()),
      // Step 3: Show success message and update UI
      tap(approval => {
        if (!approval) {
          throw new Error('Failed to add approval');
        }

        // Set approval state
        this.approval.set(approval);           
        this.isApproving.set(false);
        // Show success message
        this.toastrService.success('Log has been submitted');
        // Force a delay to ensure all state updates are processed
        // This gives time for the approval state to be properly reflected in the UI

          // Update view states in the diary service
          // This will trigger the third API call to get approvals
          this.diaryService.updateViewStates(this.diaryService.fetchedLogs());                    
                    // Clear the flag before releasing the loading state
          this.inSubmitOrUnsubmit = false;

          // Finally release the loading state
          //this.setLoadingStates(false);
          this.isSubmitting.set(false);
   
      }),
      catchError(error => {
        this.toastrService.error('There was an issue submitting the log');
             this.isApproving.set(false);
        // Clear the flag in case of error
        this.inSubmitOrUnsubmit = false;

        this.setLoadingStates(false);

        this.isSubmitting.set(false);
        
        return of(null);
      })
    ).subscribe();
  }

  /**
   * Removes approval from the current log
   */
  unSubmit(): void {
    const approvalId = this.approval()?.Id;
    if (!approvalId) return;

    // Set the flag to prevent the effect from setting isLoading to false
    this.isSubmitting.set(true);        
    this.inSubmitOrUnsubmit = true;
    this.setLoadingStates(true);    

    this.approvalService.UnSubmit(approvalId).pipe(
      tap(() => {
        // Set approval state immediately to prevent UI flicker
        this.approval.set(null);

        // Show success message
        this.toastrService.success('Log has been unsubmitted');

        // Force a delay to ensure all state updates are processed
        // This gives time for the approval state to be properly reflected in the UI
        setTimeout(() => {
          // Update view states in the diary service
          // This will trigger the third API call to get approvals
          this.diaryService.updateViewStates(this.diaryService.fetchedLogs());

          this.isSubmitting.set(false);
        }, 500);
      }),
      catchError(error => {
        this.toastrService.error('There was an issue unsubmitting the log');
        
        this.isSubmitting.set(false);
        
        return of(null);
      })
    ).subscribe();
  }

  setNoWork(isNotWork: boolean): void {
    
    this.isLoading.set(true);

    let component = this.dailyLogProjectUserComponent();
    const projectId = this.projectId();

    const operation = component
      ? component.Id
        ? this.dailyLogProjectUserService.setNoWork(projectId!, component.Id, isNotWork).pipe(
          tap(() => {
            const updatedComponent = { ...component, NoWork: isNotWork };
            this.dailyLogProjectUserComponent.set(updatedComponent);
            this.diaryService.updateLog(updatedComponent);
          })
        )
        : this.save().pipe(
          switchMap((result) => {
            if (!result) {
              throw new Error('Failed to save new daily log');
            }
            return this.dailyLogProjectUserService.setNoWork(projectId!, result.Id, isNotWork).pipe(
              tap(() => {
                const updatedComponent = { ...result, NoWork: isNotWork };
                this.dailyLogProjectUserComponent.set(updatedComponent);
                this.diaryService.updateLog(updatedComponent);
              })
            );
          })
        )
      : this.dailyLogProjectUserService.createDailyLog().pipe(
        switchMap((newComponent) => {
          if (!newComponent) {
            this.toastrService.error('Failed to create new daily log');
            return of(null);
          }
          const initializedComponent = {
            ...newComponent,
            ProjectId: projectId!,
            NoWork: isNotWork,
            Year: this.diaryService.selectedYear(),
            Month: this.diaryService.selectedMonth(),
            Day: this.diaryService.selectedDay()
          };
          this.dailyLogProjectUserComponent.set(initializedComponent);
          return this.save().pipe(
            switchMap((savedResult) => {
              if (!savedResult) {
                throw new Error('Failed to save new daily log');
              }
              return this.dailyLogProjectUserService.setNoWork(projectId!, savedResult.Id, isNotWork).pipe(
                tap(() => {
                  const updatedComponent = { ...savedResult, NoWork: isNotWork };
                  this.dailyLogProjectUserComponent.set(updatedComponent);
                  this.diaryService.updateLog(updatedComponent);
                })
              );
            })
          );
        })
      );

    operation.pipe(
      catchError((err) => {
        this.toastrService.error(component?.Id ? 'Failed to update No Work status' : 'Failed to save new No Work status');
        return of(null);
      }),
      finalize(() => this.isLoading.set(false))
    ).subscribe();
  }

  public updateDailyLog(updater: (dailyLog: DailyLogProjectUserComponent | null) => DailyLogProjectUserComponent | null): void {
    this.dailyLogProjectUserComponent.update(updater);
  }

  private resetState(): void {
    this.dailyLogProjectUserComponent.set(null);
    this.approval.set(null);
    this.isLoading.set(false);
    this.isLoadingApproval.set(false);
    this.isPersisting.set(false);
    this.alertMessage.set('');
  }

  private confirmAction(message: string, action: () => Observable<any>): void {
    this.confirmService.open(message).result.then(result => {
      if (result !== 'yes') return;

      this.isPersisting.set(true);
      action().pipe(
        catchError(err => {
          this.toastrService.error('Operation failed');
          return of(null);
        }),
        finalize(() => this.isPersisting.set(false))
      ).subscribe();
    });
  }

  /**
   * Creates a new log from a template with default values
   */
  private createNewLogFromTemplate(template: DailyLogProjectUserComponent, projectId: string): DailyLogProjectUserComponent {
    return {
      ...template,
      Id: "",
      NoWork: false,
      OwnerAccountId: "",
      ProjectId: projectId,
      UserId: template.UserId || "", // Ensure UserId is a string
      Year: this.diaryService.selectedYear(),
      Month: this.diaryService.selectedMonth(),
      Day: this.diaryService.selectedDay()
    };
  }

  /**
   * Prepares a component for saving by cleaning up data structures
   */
  private prepareComponentForSaving(component: DailyLogProjectUserComponent): DailyLogProjectUserComponent {
    return {
      ...component,
      Components: component.Components.map(comp => {
        if (comp.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD) {
          const timeCardComp = comp as TimeCardComponentProjectUser;
          return {
            ...timeCardComp,
            TimeCards: timeCardComp.TimeCards.map((tc: any) => {
              // Remove the Id property from TimeCard
              const { Id, ...timeCardRest } = tc;

              // Process employees to remove properties not in the server model
              if (timeCardRest.Employees && timeCardRest.Employees.length > 0) {
                timeCardRest.Employees = timeCardRest.Employees.map((emp: any) => {
                  // Remove properties that aren't in the server's TimeCardEmployee model
                  const {
                    InternalId, // Remove InternalId which causes serialization errors
                    IsActive,   // Remove IsActive which isn't in the server model
                    CustomId,   // CustomId is redundant with EmployeeId
                    _t,         // Remove type information
                    ...employeeRest
                  } = emp;

                  return employeeRest;
                });
              }

              // Process equipment trackers if present
              if (timeCardRest.EquipmentTrackers && timeCardRest.EquipmentTrackers.length > 0) {
                timeCardRest.EquipmentTrackers = timeCardRest.EquipmentTrackers.map((eq: any) => {
                  // Remove any properties that might cause serialization issues
                  const { _t, ...equipmentRest } = eq;
                  return equipmentRest;
                });
              }

              return timeCardRest;
            })
          };
        }
        return comp;
      })
    };
  }

  /**
   * Updates all component services with current data
   */
  private updateAllComponentServices(): void {
    this.updateNotesService();
    this.updateTimeSheetService();
    this.updateSiteConditionsService();
    this.updateWeatherService();
    this.updatePhotosService();
  }

  /**
   * Prepares and saves the log with all component updates
   */
  private prepareAndSaveLog(component: DailyLogProjectUserComponent, projectId: string): Observable<DailyLogProjectUserComponent> {
    this.isPersisting.set(true);
    this.updateComponentMetadata(component, projectId);

    // Update all component data from their respective services
    Object.entries(this.componentServices).forEach(([identifier, serviceInfo]) => {
      if (!serviceInfo.getUpdatedComponent) return;

      const index = component.Components.findIndex(c => c.ComponentIdentifier === identifier);
      if (index !== -1) {
        let updatedComponent = serviceInfo.getUpdatedComponent();

        // Special handling for TimeCard component
        if (identifier === DailyLogComponentIdentifiers.TIMECARD) {
          updatedComponent.TimeCards = updatedComponent.TimeCards ?? [];

          // Remove Id property from each TimeCard to match backend expectations
          updatedComponent.TimeCards = updatedComponent.TimeCards.map((tc: any) => {
            const { Id, ...rest } = tc; // Remove the Id property
            return rest as any; // Cast to any to avoid TypeScript errors
          });
        }

        component.Components[index] = updatedComponent;
      }
    });

    /**
     * BULLETPROOF: Deep strip forbidden fields from the entire payload before saving
     * 
     * Core Issue Addressed:
     * Extra fields like InternalId and CustomId may be copied into employees (e.g., via "Copy Last Entry"),
     * but the backend rejects any fields it doesn't recognize. We must strip these before saving.
    */
    const cleanedComponent = deepStripFields(component, ['InternalId', 'CustomId']);

    const validation = this.dailyLogProjectUserService.validateDailyLog(cleanedComponent);
    if (!validation.isValid) {
      validation.messages.forEach(msg => this.toastrService.warning(msg));
      this.isPersisting.set(false);
      throw new Error('Invalid Daily Log');
    }

    const saveOperation = cleanedComponent.Id
      ? this.processPhotosAndSave(cleanedComponent)
      : this.dailyLogProjectUserService.saveDailyLog(projectId, cleanedComponent).pipe(
        tap(dLog => {       
          this.dailyLogProjectUserComponent.update(c => {
            if (c) c.Id = dLog.Id;
            return c;
          });
          cleanedComponent.Id = dLog.Id;
        }),
        switchMap(() => this.processPhotosAndSave(cleanedComponent))
      );

    return saveOperation.pipe(
      catchError(err => {
        this.toastrService.error('There was an error saving the log');
        throw err;
      }),
      finalize(() => this.isPersisting.set(false))
    );
  }

  private updateComponentMetadata(component: DailyLogProjectUserComponent, projectId: string): void {
    component.Month = this.diaryService.selectedMonth();
    component.Year = this.diaryService.selectedYear();
    component.Day = this.diaryService.selectedDay();
    component.ProjectId = projectId;
    component._t = ['BaseEntity', 'ProjectUserComponent', 'DailyLogUserComponent'];

    component.Components.forEach(c => {
      c._t = DailyLogInfoComponentSerializerInfo.getInstance()
        .getSerializerInfo(c.ComponentIdentifier);
    });
  }

  private processPhotosAndSave(component: DailyLogProjectUserComponent): Observable<DailyLogProjectUserComponent> {
    const projectId = this.projectId();
    const photosToUpload = this.photosService.projectPhotosToAdd(); //this.photosService.photosToUpload();
    const photosToRemove = this.photosService.projectPhotosToRemove(); //this.photosService.photosToRemove();

    const hasPhotoChanges = photosToRemove.length > 0 || photosToUpload.length > 0;

    if (hasPhotoChanges) {
      this.alertMessage.set('Saving Photos...');
    }

    // Process photos to remove regardless of whether there are photos to upload
    if (photosToRemove.length > 0) {
      // Update the photos component to remove marked photos
      this.updatePhotosComponent(component, []);
    }

    // Process photos to upload if there are any
    if (photosToUpload.length > 0) {
      return this.photosService.processPhotos(component.Id).pipe(
        tap(photos => this.updatePhotosComponent(component, photos)),
        switchMap(() => {
          // this.alertMessage.set('Saving Log...');
          return this.dailyLogProjectUserService.saveDailyLog(projectId, component);
        }),
        tap(() => {
          // Clear all dirty states for this project
          this.dirtyService.clearAllDirty(projectId);

          this.photosService.clear();
          this.alertMessage.set('');
        }),
        catchError(err => {
          this.alertMessage.set('');
          this.toastrService.error('There was an error saving the log');
          throw err;
        })
      );
    } else {
      // If there are no photos to upload, just save the log
      // this.alertMessage.set('Saving Log...');
      return this.dailyLogProjectUserService.saveDailyLog(projectId, component).pipe(
        tap(() => {
          // Clear all dirty states for this project
          this.dirtyService.clearAllDirty(projectId);

          this.photosService.clear();
          this.alertMessage.set('');
        }),
        catchError(err => {
          this.alertMessage.set('');
          this.toastrService.error('There was an error saving the log');
          throw err;
        })
      );
    }
  }

  private updatePhotosComponent(component: DailyLogProjectUserComponent, photos: Photo[]): void {
    const photosComponent = component.Components.find(
      x => x.ComponentIdentifier === DailyLogComponentIdentifiers.PHOTOS
    ) as PhotosComponentProjectUser;

    if (!photosComponent.Photos) photosComponent.Photos = [];
    if (photos?.length) photosComponent.Photos.push(...photos);

    // Process photos marked for deletion
    // This removes them from the Photos array, which signals to the backend
    // that they should be deleted from S3 and the database
    const photosToRemove = this.photosService.projectPhotosToRemove();
    if (photosToRemove.length > 0) {
      photosToRemove.forEach(photoId => {
        const index = photosComponent.Photos.findIndex(p => p.PhotoId === photoId);
        if (index > -1) {
          // Remove the photo from the array
          photosComponent.Photos.splice(index, 1);
        }
      });
    }
  }

  /**
  * Adds approval to the current log
  */
  private addApproval(): Observable<Approval> {       
    this.isApproving.set(true);
    this.isAddingApproval.set(true);

    const component = this.dailyLogProjectUserComponent();
    const projectId = this.projectId();

    if (!component?.Id || !projectId) {
      throw new Error('Missing required data for approval');
    }

    return from(this.authService.getSubId()).pipe(
      switchMap(userId => {
        // Create approval info with minimal properties like in the original code
        // Don't explicitly set Id to allow backend to handle existing approvals
        const approvalInfo = {} as Approval;
        approvalInfo.DailyLogId = component.Id;
        approvalInfo.ProjectId = projectId;
        approvalInfo.UserId = userId;
        approvalInfo.DailyLogTimeStamp = new Date(
          this.diaryService.selectedYear(),
          this.diaryService.selectedMonth() - 1,
          this.diaryService.selectedDay()
        );

        // Let the service add approval levels
        return this.approvalService.AddApproval(projectId, approvalInfo);
      }),
      tap(approval => this.approval.set(approval)),
      catchError(err => {
        throw err;
      })
    );
  }

  /**
  * Sets loading states for both isPersisting and isLoading
  */
  private setLoadingStates(active: boolean): void {
    this.isPersisting.set(active);
    this.isLoading.set(active);
  }

  /**
   * Finds a component by its identifier in the daily log
   */
  private findComponentByIdentifier(dailyLog: DailyLogProjectUserComponent | null, identifier: string): any {
    const component = dailyLog?.Components?.find(c => c.ComponentIdentifier === identifier) || null;
    return component;
  }

  /**
   * Updates the notes service with current data
   */
  private updateNotesService(): void {
    const dailyLog = this.dailyLogProjectUserComponent();
    if (!dailyLog) return;

    const notesComponent = this.findComponentByIdentifier(
      dailyLog,
      DailyLogComponentIdentifiers.NOTES
    );

    // Set the project ID in the notes service
    this.notesService.projectId.set(this.projectId());

    // Update the notes service with the current state
    this.notesService.setInitialData(
      notesComponent,
      this.diaryService.getComponentView(this.projectId()),
      this.access()
    );
  }

  /**
   * Updates the time sheet service with current data
   */
  private updateTimeSheetService(): void {
    const dailyLog = this.dailyLogProjectUserComponentSignal();
    if (!dailyLog) {
      return;
    }

    const timeCardComponent = this.findComponentByIdentifier(
      dailyLog,
      DailyLogComponentIdentifiers.TIMECARD
    ) as TimeCardComponentProjectUser | null;

    this.timeSheetService.setInitialData(
      timeCardComponent,
      this.diaryService.getComponentView(this.projectId()),
      this.access()
    );
    this.timeSheetService.currentDate.set(this.currentDateSignal());
    this.timeSheetService.projectId.set(this.projectId());
  }

  /**
   * Updates the site conditions service with current data
   */
  private updateSiteConditionsService(): void {
    const dailyLog = this.dailyLogProjectUserComponent();
    if (!dailyLog) return;

    const siteConditionComponent = this.findComponentByIdentifier(
      dailyLog,
      DailyLogComponentIdentifiers.SITE_CONDITIONS
    ) as SiteConditionsComponentProjectUser | null;

    // Set the project ID in the site conditions service
    this.siteConditionsService.projectId.set(this.projectId());

    // Use approvalSignal to determine view, consistent with notes and time-sheet
    const view = this.approvalSignal() ? ComponentView.ReadOnly : ComponentView.Edit;
    const access = this.access();

    this.siteConditionsService.setInitialData(
      siteConditionComponent,
      view,
      access
    );
  }

  /**
   * Updates the weather service with current data
   */
  private updateWeatherService(): void {
    const dailyLog = this.dailyLogProjectUserComponent();
    if (!dailyLog) return;

    const weatherComponent = this.findComponentByIdentifier(
      dailyLog,
      DailyLogComponentIdentifiers.WEATHER
    ) as WeatherComponentProjectUser | null;

    // Set the project ID in the weather service
    this.weatherService.projectId.set(this.projectId());

    // Use approvalSignal to determine view, consistent with notes and time-sheet
    const view = this.approvalSignal() ? ComponentView.ReadOnly : ComponentView.Edit;
    const access = this.access();

    this.weatherService.setInitialData(
      weatherComponent,
      view,
      access
    );
  }

  /**
   * Updates the photos service with current data
   */
  private updatePhotosService(): void {
    const dailyLog = this.dailyLogProjectUserComponent();
    if (!dailyLog) return;

    const photosComponent = this.findComponentByIdentifier(
      dailyLog,
      DailyLogComponentIdentifiers.PHOTOS
    ) as PhotosComponentProjectUser | null;

    // Set the project ID in the photos service
    this.photosComponentService.projectId.set(this.projectId());

    const view = this.approvalSignal() ? ComponentView.ReadOnly : ComponentView.Edit;
    const access = this.access();

    this.photosComponentService.setInitialData(
      photosComponent,
      view,
      access
    );
  }

  /**
   * Checks if a component has time sheet data
   */
  hasTimeSheetData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD) {
      const timeCards = component.TimeCards;
      // Check if there are employees, cost codes, or equipment trackers with data
      return !!(
        (timeCards?.Employees && timeCards.Employees.length > 0) ||
        (timeCards?.CostCodes && timeCards.CostCodes.length > 0) ||
        (timeCards?.EquipmentTrackers && timeCards.EquipmentTrackers.length > 0)
      );
    }
    return false;
  }

  /**
   * Checks if a component has notes data
   */
  hasNotesData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.NOTES) {
      return !!component.Note;
    }
    return false;
  }

  /**
   * Checks if a component has photos data
   */
  hasPhotosData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.PHOTOS) {
      return !!(component.Photos && component.Photos.length > 0);
    }
    return false;
  }

  /**
   * Checks if a component has site conditions data
   */
  hasSiteConditionsData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS) {
      return !!component.SiteCondition;
    }
    return false;
  }

  /**
   * Checks if a component has weather data
   */
  hasWeatherData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.WEATHER) {
      return !!(
        component.Temperature ||
        (component.SelectedSkys && component.SelectedSkys.length > 0)
      );
    }
    return false;
  }

  /**
 * Resets all component states to their original values by reloading the daily log data
 */
  reset(): void {
    this.confirmAction('Abort all unsaved changes?', () => {
      const projectId = this.projectId();
      if (!projectId) return of(null);

      // Set loading state
      this.isLoading.set(true);

      // Get current date
      const year = this.diaryService.selectedYear();
      const month = this.diaryService.selectedMonth();
      const day = this.diaryService.selectedDay();

      // First, explicitly clear all dirty states to ensure clean state
      this.dirtyService.clearAllDirty(projectId);

      // Reload the daily log data
      return this.dailyLogProjectUserService.getDailyLogs([projectId], month, day, year).pipe(
        tap(logs => {
          const dailyLog = logs.find(log => log.ProjectId === projectId);
          if (dailyLog) {
            // Update the daily log component with fresh data
            this.dailyLogProjectUserComponent.set(dailyLog);
            this.diaryService.updateLog(dailyLog);

            // Update all component services with the fresh data
            this.updateAllComponentServices();
          } else {
            // If no daily log found (API returned empty array), create a new one
            this.dailyLogProjectUserService.createDailyLog().subscribe(newLog => {
              if (newLog) {
                // Initialize the new log with project and date info
                newLog.ProjectId = projectId;
                newLog.Year = year;
                newLog.Month = month;
                newLog.Day = day;

                // Update the daily log component with the new log
                this.dailyLogProjectUserComponent.set(newLog);
                this.diaryService.updateLog(newLog);

                // Update all component services with the fresh data
                this.updateAllComponentServices();
              }
            });
          }

          // Clear dirty state again after all updates to ensure it's properly reset
          setTimeout(() => {
            this.dirtyService.clearAllDirty(projectId);
          }, 0);
        }),
        catchError(err => {
          this.toastrService.error('Failed to reset changes');
          return of(null);
        }),
        finalize(() => {
          // Final check to ensure dirty state is cleared
          setTimeout(() => {
            if (projectId) {
              this.dirtyService.clearAllDirty(projectId);
            }
            this.isLoading.set(false);
          }, 0);
        })
      );
    });
  }
}
