import { Injectable, Signal, computed, signal } from '@angular/core';

// Forward declaration to avoid circular dependency
// The actual type will be resolved at runtime
interface DailyLogServiceInterface {
  isPersistingSignal(): boolean;
}

/**
 * Service responsible for tracking and exposing dirty state for any DailyLogComponent
 * per projectId and per child component.
 */
@Injectable({
  providedIn: 'root'
})
export class DailyLogDirtyService {
  /**
   * Map to track dirty state
   * First key = projectId
   * Second key = component ID like 'notes', 'timesheet', etc.
   * Made public for direct access in components
   */
  public readonly dirtyMap = signal<Map<string, Map<string, boolean>>>(new Map());
  
  // Signal to force recomputation of global dirty state
  private readonly _globalDirtyStateChanged = signal<number>(0);

  /**
   * Registry to track DailyLogService instances by projectId
   * Used to check persisting state across all logs
   */
  private readonly serviceRegistry = signal<Map<string, DailyLogServiceInterface>>(new Map());

  constructor() {
  }

  /**
   * Registers a DailyLogService instance for a specific project
   * @param projectId The project ID
   * @param service The DailyLogService instance
   */
  registerService(projectId: string, service: DailyLogServiceInterface): void {
    this.serviceRegistry.update(registry => {
      const newRegistry = new Map(registry);
      newRegistry.set(projectId, service);
      return newRegistry;
    });
  }

  /**
   * Unregisters a DailyLogService instance for a specific project
   * @param projectId The project ID
   */
  unregisterService(projectId: string): void {
    this.serviceRegistry.update(registry => {
      const newRegistry = new Map(registry);
      newRegistry.delete(projectId);
      return newRegistry;
    });
  }

  /**
   * Returns a signal indicating whether any registered DailyLogService is persisting
   */
  anyPersisting(): Signal<boolean> {
    return computed(() => {
      const registry = this.serviceRegistry();
      for (const service of registry.values()) {
        if (service.isPersistingSignal()) {
          return true;
        }
      }
      return false;
    });
  }

  /**
   * Sets the dirty state for a specific component in a project
   * @param projectId The project ID
   * @param componentId The component ID (e.g., 'notes', 'timesheet')
   * @param isDirty Whether the component is dirty
   */
  setDirty(projectId: string, componentId: string, isDirty: boolean): void {
    this.dirtyMap.update(map => {
      // Create a new Map to ensure reactivity
      const newMap = new Map(map);
      
      // Get or create the project map
      let projectMap = newMap.get(projectId);
      if (!projectMap) {
        projectMap = new Map<string, boolean>();
        newMap.set(projectId, projectMap);
      } else {
        // Create a new Map for the project to ensure reactivity
        projectMap = new Map(projectMap);
        newMap.set(projectId, projectMap);
      }
      
      // Set the dirty state for the component
      projectMap.set(componentId, isDirty);
      
      // Trigger recomputation of global dirty state
      this._globalDirtyStateChanged.update(n => {
        return n + 1;
      });
      
      return newMap;
    });
  }

  // Private signals for tracking dirty state
  private readonly _projectDirtySignals = new Map<string, Signal<boolean>>();
  private readonly _componentDirtySignals = new Map<string, Signal<boolean>>();
  private readonly _globalDirtySignal = computed(() => {
    // Include the _globalDirtyStateChanged signal as a dependency
    const _ = this._globalDirtyStateChanged();
    
    const map = this.dirtyMap();
    
    // Check if any project has a dirty component
    for (const [, projectMap] of map.entries()) {
      for (const [, isDirty] of projectMap.entries()) {
        if (isDirty) {
          return true;
        }
      }
    }
    
    return false;
  });

  /**
   * Returns a signal indicating whether a specific component in a project is dirty
   * @param projectId The project ID
   * @param componentId The component ID (e.g., 'notes', 'timesheet')
   * @returns A signal that is true if the component is dirty, false otherwise
   */
  isComponentDirty(projectId: string, componentId: string): Signal<boolean> {
    const key = `${projectId}:${componentId}`;
    
    if (!this._componentDirtySignals.has(key)) {
      const componentDirtySignal = computed(() => {
        const map = this.dirtyMap();
        const projectMap = map.get(projectId);
        return projectMap ? projectMap.get(componentId) || false : false;
      });
      
      this._componentDirtySignals.set(key, componentDirtySignal);
    }
    
    return this._componentDirtySignals.get(key)!;
  }

  /**
   * Returns a signal indicating whether any component in a project is dirty
   * @param projectId The project ID
   * @returns A signal that is true if any component in the project is dirty, false otherwise
   */
  anyDirty(projectId: string): Signal<boolean> {
    if (!this._projectDirtySignals.has(projectId)) {
      const projectDirtySignal = computed(() => {
        const map = this.dirtyMap();
        const projectMap = map.get(projectId);
        if (!projectMap) {
          return false;
        }
        
        // Check if any component is dirty
        for (const [, isDirty] of projectMap.entries()) {
          if (isDirty) {
            return true;
          }
        }
        
        return false;
      });
      
      this._projectDirtySignals.set(projectId, projectDirtySignal);
    }
    
    return this._projectDirtySignals.get(projectId)!;
  }

  /**
   * Returns a signal indicating whether any component in any project is dirty
   * @returns A signal that is true if any component in any project is dirty, false otherwise
   */
  anyDirtyGlobally(): Signal<boolean> {
    return this._globalDirtySignal;
  }

  /**
   * Clears the dirty state for a specific component in a project
   * @param projectId The project ID
   * @param componentId The component ID (e.g., 'notes', 'timesheet')
   */
  clearDirty(projectId: string, componentId: string): void {
    this.dirtyMap.update(map => {
      // Create a new Map to ensure reactivity
      const newMap = new Map(map);
      
      const projectMap = newMap.get(projectId);
      if (projectMap) {
        // Create a new Map for the project to ensure reactivity
        const newProjectMap = new Map(projectMap);
        newProjectMap.set(componentId, false);
        newMap.set(projectId, newProjectMap);
      }
      
      return newMap;
    });
  }

  /**
   * Clears the dirty state for all components in a project
   * @param projectId The project ID
   */
  clearAllDirty(projectId: string): void {
    this.dirtyMap.update(map => {
      // Create a new Map to ensure reactivity
      const newMap = new Map(map);
      
      const projectMap = newMap.get(projectId);
      if (projectMap) {
        // Create a new Map for the project to ensure reactivity
        const newProjectMap = new Map(projectMap);
        
        // Set all components to not dirty
        for (const componentId of newProjectMap.keys()) {
          newProjectMap.set(componentId, false);
        }
        
        newMap.set(projectId, newProjectMap);
      }
      
      return newMap;
    });
  }

  /**
   * Clears all dirty states for all projects and components
   */
  clearAllDirtyGlobally(): void {
    this.dirtyMap.update(map => {
      // Create a new Map to ensure reactivity
      const newMap = new Map();
      
      // Clear all dirty states for all projects
      for (const [projectId, projectMap] of map.entries()) {
        const newProjectMap = new Map();
        
        for (const componentId of projectMap.keys()) {
          newProjectMap.set(componentId, false);
        }
        
        newMap.set(projectId, newProjectMap);
      }
      
      return newMap;
    });
  }
}
