/* Styles for the daily log component */

/* Scroll anchor positioning */
.scroll-anchor {
  display: block;
  position: relative;
  top: -50px; /* Increased negative margin to scroll further down */
  visibility: hidden;
  height: 0;
  margin: 0;
  padding: 0;
}

/* Highlight effect for selected project */
.highlight-project {
  animation: highlight-fade 2s ease-in-out;
}

@keyframes highlight-fade {
  0% { 
    background-color: rgba(255, 193, 7, 0.5); 
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);
  }
  50% { 
    background-color: rgba(255, 193, 7, 0.3); 
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
  }
  100% { 
    background-color: transparent; 
    box-shadow: none;
  }
}
