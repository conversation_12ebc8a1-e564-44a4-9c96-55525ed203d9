import { Component, inject, Input, effect, computed, signal, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { DailyLogNotesComponent } from './ui/daily-log-notes/daily-log-notes.component';
import { DailyLogPhotosComponent } from './ui/daily-log-photos/daily-log-photos.component';
import { DailyLogSiteConditionsComponent } from './ui/daily-log-site-conditions/daily-log-site-conditions.component';
import { DailyLogTimeSheetComponent } from './ui/daily-log-time-sheet/daily-log-time-sheet.component';
import { DailyLogWeatherComponent } from './ui/daily-log-weather/daily-log-weather.component';
import { FormsModule } from '@angular/forms';
import { DailyLogService } from './shared/services/daily-log.service';
import { DailyLogNotesService } from './shared/services/daily-log-notes.service';
import { DailyLogComponentIdentifiers, DailyLogViews } from '../../shared/interfaces/daily-log-shared';
import { DailyLogButtonsComponent } from './ui/daily-log-buttons/daily-log-buttons.component';
import { DailyLogTimeSheetService } from './shared/services/daily-log-time-sheet.service';
import { DailyLogSiteConditionsService } from './shared/services/daily-log-site-conditions.service';
import { DailyLogWeatherService } from './shared/services/daily-log-weather.service';
import { DailyLogPhotosService } from './shared/services/daily-log-photos.service';
import { DailyLogDirtyService } from './shared/services/daily-log-dirty.service';
import { DiaryProjectsSelectorService } from '../../shared/data-access/diary-projects-selector.service';
import { ConstructionPhotosService } from '../../shared/data-access/photos.service';
import { DiaryPageService } from '../../shared/data-access/diary-page.service';

@Component({
  selector: 'app-daily-log',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgbDatepickerModule,
    DailyLogNotesComponent,
    DailyLogPhotosComponent,
    DailyLogSiteConditionsComponent,
    DailyLogTimeSheetComponent,
    DailyLogWeatherComponent,
    DailyLogButtonsComponent 
  ],
  templateUrl: './daily-log.component.html',
  styleUrls: ['./daily-log.component.css'],
  providers: [DailyLogService, DailyLogNotesService, DailyLogTimeSheetService, DailyLogSiteConditionsService, DailyLogWeatherService, DailyLogPhotosService, ConstructionPhotosService]
})
export class DailyLogComponent implements OnDestroy {
  // Service injections - use readonly for immutability but keep public for template access
  public readonly dailyLogService = inject(DailyLogService);
  public readonly timeSheetService = inject(DailyLogTimeSheetService);
  private readonly dirtyService = inject(DailyLogDirtyService);
  public readonly diaryPageService = inject(DiaryPageService);
  public readonly diaryProjectsSelectorService = inject(DiaryProjectsSelectorService);
  
  // Signal to check if the current project has any dirty components
  private _isDirty = signal<boolean>(false);
  public readonly isDirty = this._isDirty.asReadonly();
  
  // Constants
  public readonly DAILY_LOG_COMPONENT_IDENTIFIERS: typeof DailyLogComponentIdentifiers = DailyLogComponentIdentifiers;
  public readonly DAILY_LOG_VIEWS: typeof DailyLogViews = DailyLogViews;
  
  private _projectId: string = '';

  isViewLoading = this.dailyLogService?.diaryService?.isViewLoading;
  
  // Effect to update the isDirty signal when the dirtyService signal changes
  constructor() {
    // Create an effect in the constructor context
    effect(() => {
      if (this._projectId) {
        const dirtySignal = this.dirtyService.anyDirty(this._projectId);
        const isDirty = dirtySignal();
        this._isDirty.set(isDirty);
      }
    });




  }

  @Input({ required: true }) set projectId(value: string) {
    if (value) {
      this._projectId = value;
      this.dailyLogService.setProjectId(value);
    }
  }
  
  get projectId(): string {
    return this._projectId;
  }

  // Expose service signals directly
  public readonly approval = this.dailyLogService.approvalSignal;
  public readonly isLoading = this.dailyLogService.isLoadingSignal;
  public readonly isLoadingApproval = this.dailyLogService.isLoadingApprovalSignal;
  public readonly access = this.dailyLogService.accessSignal;
  public readonly dailyLogProjectUserComponent = this.dailyLogService.dailyLogProjectUserComponentSignal;
  public readonly isPersisting = this.dailyLogService.isPersistingSignal;
  public readonly alertMessage = this.dailyLogService.alertMessageSignal;
  public readonly currentDate = this.dailyLogService.currentDateSignal;

  // Helper methods
  onImportRecentTimeCard(): void {
    this.timeSheetService.importRecentTimeCard();
  }
  
  // Method to directly check if any component is dirty for this project
  checkDirtyState(): boolean {
    if (!this._projectId) return false;
    
    // Get the project map from the dirtyService
    const dirtyMap = this.dirtyService.dirtyMap();
    const projectMap = dirtyMap.get(this._projectId);
    
    if (!projectMap) return false;
    
    // Check if any component is dirty
    for (const [componentId, isDirty] of projectMap.entries()) {
      if (isDirty) return true;
    }
    
    return false;
  }
  
  /**
   * Save the daily log when clicking on the dirty icon
   * This method is called from the template when the user clicks on the dirty icon
   */
  saveDailyLog(): void {
    if (this.dailyLogService.isPersistingSignal()) return;
    
    this.dailyLogService.save().subscribe({
      next: () => {
        this.dailyLogService.toastrService.success('Log has been saved');
      },
      error: (err) => {
        console.error('Error saving daily log:', err);
        this.dailyLogService.toastrService.error('There was an error saving the log');
      }
    });
  }

  /**
   * Reset all changes to their original state
   */
  resetChanges(): void {
    this.dailyLogService.reset();
  }
  
  /**
   * Lifecycle hook that is called when the component is destroyed
   * Calls the DailyLogService's ngOnDestroy method
   */
  ngOnDestroy(): void {
    // Call the DailyLogService's ngOnDestroy method
    if (this.dailyLogService['ngOnDestroy']) {
      this.dailyLogService['ngOnDestroy']();
    }
  }
}
