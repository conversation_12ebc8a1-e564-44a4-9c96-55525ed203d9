<div class="d-flex flex-row justify-content-end" *ngIf="hasAccess()">
	@if (!noWork()) {
	<div class="btn-group me-2" role="group" aria-label="Basic example">
		<button class="btn btn-outline-dark" (click)="dailyLogButtonsService.clear()" [disabled]="!isEnabled()">
			Clear
		</button>
		@if (canDelete()) {
		<button class="btn btn-outline-danger" (click)="dailyLogButtonsService.remove()" [disabled]="!isEnabled()">
			Delete
		</button>
		}
	</div>
	}
	<div class="btn-group" role="group" aria-label="Basic example">
		@if (!noWork()) {
		<button type="button" class="btn btn-outline-primary" (click)="dailyLogButtonsService.save()"
			[disabled]="!isEnabled()">
			@if(isSaving()) {
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}
			Save
		</button>
		}
		@if (canSubmit()) {
		<button type="button" class="btn btn-primary" (click)="dailyLogButtonsService.submit()"
			[disabled]="isSubmitting() || !isEnabled() || isApproving()">
			@if(isSubmitting() || isApproving()) {
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}
			Submit
		</button>
		}
		@if (canUnSubmit()) {
		<button type="button" class="btn btn-outline-danger" (click)="dailyLogButtonsService.unSubmit()"
			[disabled]="!isEnabled()">
			Undo Submission
		</button>
		}
	</div>
</div>