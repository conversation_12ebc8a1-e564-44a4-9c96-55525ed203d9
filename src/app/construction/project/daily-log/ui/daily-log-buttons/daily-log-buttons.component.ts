import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DailyLogButtonsService } from '../../shared/services/daily-log-buttons.service';

@Component({
  selector: 'app-daily-log-buttons',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './daily-log-buttons.component.html',
  styleUrls: ['./daily-log-buttons.component.css'],
  providers: [DailyLogButtonsService]
})
export class DailyLogButtonsComponent {
  dailyLogButtonsService = inject(DailyLogButtonsService);

  isEnabled = this.dailyLogButtonsService.isEnabled;
  canDelete = this.dailyLogButtonsService.canDelete;
  canSubmit = this.dailyLogButtonsService.canSubmit;
  canUnSubmit = this.dailyLogButtonsService.canUnSubmit;
  noWork = this.dailyLogButtonsService.noWork;
  hasAccess = this.dailyLogButtonsService.hasAccess;
  isSubmitting = this.dailyLogButtonsService.isSubmitting;
  isApproving = this.dailyLogButtonsService.isApproving;
  isSaving = this.dailyLogButtonsService.isSaving;
}