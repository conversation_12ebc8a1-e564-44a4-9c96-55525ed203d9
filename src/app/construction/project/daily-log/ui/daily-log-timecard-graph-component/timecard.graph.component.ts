﻿﻿import { Component, OnInit, Input, inject, signal, OnChanges, SimpleChanges, effect } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { TimeCard, TimeCardApprovalPermissionSystem, TimeCardCostCode, TimeCardEmployee, TimeInfo, EquipmentTracker } from 'src/app/construction/shared/interfaces/timecard';
import { CommonModule } from '@angular/common';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { EquipmentInfo } from 'src/app/construction/shared/interfaces/equipment';
import { SelectedData } from 'src/app/construction/shared/interfaces/selected-data';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Employee } from 'src/app/construction/shared/interfaces/employee';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { switchMap, tap } from 'rxjs';
import { ConstructionCostCodesService } from 'src/app/construction/shared/data-access/cost-codes.service';
import { CostCode, CostCodeInfoComponent } from 'src/app/construction/shared/interfaces/cost-codes';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { ApprovalService } from 'src/app/construction/shared/data-access/approval.service';
import { DiaryService } from 'src/app/construction/diary/diary.service';
import { DailyLogProjectUserComponent } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { DailyLogTimeSheetService } from '../../shared/services/daily-log-time-sheet.service';
import { DailyLogDirtyService } from '../../shared/services/daily-log-dirty.service';
import { EquipmentDropdownComponent } from 'src/app/construction/ui/equipment-dropdown/equipment-dropdown.component';


@Component({
  selector: 'timecard-graph',
  imports: [CommonModule, NgMultiSelectDropDownModule, FormsModule, ReactiveFormsModule, NgbTooltipModule, EquipmentDropdownComponent],
  templateUrl: './timecard.graph.component.html',
  styleUrls: ['./timecard.graph.component.css']
})
export class TimeCardGraphComponent implements OnInit, OnChanges {
  costCodeService = inject(ConstructionCostCodesService);
  constructionDailyLogService = inject(ConstructionDailyLogService);
  approvalService = inject(ApprovalService);
  diaryService = inject(DiaryService);
  timeSheetService = inject(DailyLogTimeSheetService);
  dirtyService = inject(DailyLogDirtyService);

  @Input({ required: true }) projectId: string = "";
  @Input() timeCard: TimeCard = {} as TimeCard;
  @Input() layoutType: TimeCardLayout = TimeCardLayout.Card;
  @Input() view: ComponentView = ComponentView.ReadOnly;
  @Input() timeCardApprovalSystem: TimeCardApprovalPermissionSystem = {} as TimeCardApprovalPermissionSystem;

  public readonly COMPONENT_VIEWS: typeof ComponentView = ComponentView;
  approvalPermissions = this.approvalService.approvalAccess;
  projectCostCodes = signal<Array<CostCode>>([]);
  equipments = signal<Array<EquipmentInfo>>([]);
  isLoading = signal<boolean>(false);
  selectedCostCode: TimeCardCostCode | null = null;
  selectedEmployee: Employee | null = null;
  employeeFullName: string = "";
  costCodes: Array<SelectedData> = [];
  employees: Array<SelectedData> = [];
  equipment: Array<SelectedData> = [];
  timeDescriptions: Array<string> = [];
  equipmentTimeDescriptions: Array<string> = [];
  selectedCostCodes: SelectedData[] = [];
  selectedEmployees: SelectedData[] = [];
  selectedEquipment: EquipmentInfo[] = [];
  timeInfo: Array<TimeInfo> = [];
  multiSelectSettings: any = {
    singleSelection: false,
    idField: 'ItemId',
    textField: 'ItemText',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 0,
    allowSearchFilter: true
  };
  timeCardComponentInfo = this.constructionDailyLogService.timeCardComponent;
  timeCardComponentIdentifier = signal<DailyLogComponentIdentifiers>(DailyLogComponentIdentifiers.TIMECARD);
  readOnlyUserProjectService = toSignal(toObservable(this.timeCardComponentIdentifier).pipe(
    tap((componentId) => this.isLoading.set(true)),
    switchMap((componentId) => this.constructionDailyLogService.getSubComponent(componentId)),
    tap((result) => {
      this.isLoading.set(false);
    })
  ));
  allEmployees = this.timeSheetService.activeEmployees;
  allEquipments = this.timeSheetService.activeEquipments;

  readonlyCostCodes = toSignal(toObservable(this.projectCostCodes).pipe(
    tap((result) => {
      this.costCodes = result.map((x) => {
        return {
          ItemId: x.CostCodeId,
          ItemText: `${x.Phase} - ${x.Description}`
        } as SelectedData;
      });
    }
    )
  ));

  constructor(private toastr: ToastrService) {
    this.timeInfo = this.GetTimeInfo();

    // console.log('TimeCardGraphComponent Inputs:', this.projectId, this.timeCard, this.layoutType, this.view, this.timeCardApprovalSystem);

    effect(() => {
      this.employees = this.allEmployees().map(x => ({
        ItemId: x.CustomId,
        ItemText: `${x.FirstName} ${x.LastName} - ${x.CustomId}`
      } as SelectedData));
    });

    effect(() => {
      this.equipment = this.allEquipments().map(x => ({
        ItemId: x.EquipmentInternalId,
        ItemText: `${x.Make} ${x.Model}`
      } as SelectedData));
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['projectId'] && changes['projectId'].currentValue) {
      this.costCodeService.getCostCodeComponent(changes['projectId'].currentValue).subscribe({
        next: (result: CostCodeInfoComponent) => {
          if (result?.CostCodes) {
            this.projectCostCodes.set(result.CostCodes);
          }
        },
        error: (err: any) => {
          console.log(err);
        }
      });
    }

    if (changes['timeCard'] && changes['timeCard'].currentValue) {
      if (this.timeCard.Employees) {
        for (let emp of this.timeCard.Employees) {
          if (emp.TimeReason === '' || emp.TimeReason === null) {
            emp.TimeReason = this.timeDescriptions[0];
          }
        }
      }
    }
  }


  ngOnInit() {
    // Store original values for comparison when checking if form is dirty
    this.storeOriginalValues();
  }

  /**
   * Stores the original values of the timecard for later comparison
   * to determine if the form is dirty
   */
  // Use a WeakMap to store original values without modifying the objects
  private originalValues = new WeakMap<object, any>();

  private storeOriginalValues() {
    if (this.timeCard) {
      // Store original QtyCompl values
      if (this.timeCard.CostCodes) {
        this.timeCard.CostCodes.forEach(costCode => {
          this.originalValues.set(costCode, { 
            QtyCompl: costCode.QtyCompl 
          });
        });
      }

      // Store original TimeReason values for employees and their cost code UserValues
      if (this.timeCard.Employees) {
        this.timeCard.Employees.forEach(employee => {
          this.originalValues.set(employee, { 
            TimeReason: employee.TimeReason 
          });
          
          // Store original UserValue for each cost code
          if (employee.CostCodes) {
            employee.CostCodes.forEach(costCode => {
              this.originalValues.set(costCode, { 
                UserValue: costCode.UserValue 
              });
            });
          }
        });
      }

      // Store original TimeReason values for equipment and their cost code UserValues
      if (this.timeCard.EquipmentTrackers) {
        this.timeCard.EquipmentTrackers.forEach(equipment => {
          this.originalValues.set(equipment, { 
            TimeReason: equipment.TimeReason 
          });
          
          // Store original UserValue for each cost code
          if (equipment.CostCodes) {
            equipment.CostCodes.forEach(costCode => {
              this.originalValues.set(costCode, { 
                UserValue: costCode.UserValue 
              });
            });
          }
        });
      }
    }
  }

  /**
   * Updates the original value in the WeakMap when a new value is set
   * This is used when we want to consider the current value as the "original"
   * @param obj The object to update
   * @param property The property name to update
   * @param value The new value to set
   */
  private updateOriginalValue(obj: object, property: string, value: any): void {
    const original = this.originalValues.get(obj);
    if (original) {
      original[property] = value;
    } else {
      const newOriginal: any = {};
      newOriginal[property] = value;
      this.originalValues.set(obj, newOriginal);
    }
  }

  /**
   * Handles changes to the QtyCompl input field
   * Always sets the dirty state to true when the QtyCompl value is changed
   */
  onQtyComplChange() {
    const currentLog = this.getCurrentLog();
    if (currentLog) {
      this.diaryService.updateLog(currentLog);
      
      // Always set dirty state to true when QtyCompl is changed
      // This ensures the form stays dirty until explicitly saved
      this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
    }
  }

  /**
   * Handles changes to the TimeReason dropdown
   * Always sets the dirty state to true when the reason is changed
   */
  onTimeReasonChange() {
    const currentLog = this.getCurrentLog();
    if (currentLog) {
      this.diaryService.updateLog(currentLog);
      
      // Always set dirty state to true when reason is changed
      // This ensures the form stays dirty until explicitly saved
      this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
    }
  }

  /**
   * Updates the dirty state based on a comprehensive check of all fields
   */
  private updateDirtyState() {
    const currentLog = this.getCurrentLog();
    if (currentLog && this.timeCard) {
      const isDirty = this.checkIfAnyFieldIsDirty();
      
      // Set dirty state
      this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, isDirty);
      
      // Update the log
      this.diaryService.updateLog(currentLog);
    }
  }

  /**
   * Checks if any field in the timecard is dirty by comparing current values with original values
   * @returns true if any field is dirty, false otherwise
   */
  private checkIfAnyFieldIsDirty(): boolean {
    // Check QtyCompl values
    if (this.timeCard.CostCodes) {
      for (const costCode of this.timeCard.CostCodes) {
        const original = this.originalValues.get(costCode);
        if (original && original.QtyCompl !== costCode.QtyCompl) {
          return true;
        }
      }
    }

    // Check employee TimeReason values and their cost code UserValues
    if (this.timeCard.Employees) {
      for (const employee of this.timeCard.Employees) {
        // Check TimeReason
        const empOriginal = this.originalValues.get(employee);
        if (empOriginal && empOriginal.TimeReason !== employee.TimeReason) {
          return true;
        }
        
        // Check UserValue for each cost code
        if (employee.CostCodes) {
          for (const costCode of employee.CostCodes) {
            const ccOriginal = this.originalValues.get(costCode);
            if (!ccOriginal) continue;
            
            // Use strict equality and handle null/undefined cases
            const originalValue = ccOriginal.UserValue;
            const currentValue = costCode.UserValue;
            
            // Special handling for floating point values
            if (typeof originalValue === 'number' && typeof currentValue === 'number') {
              // Use a small epsilon for floating point comparison
              if (Math.abs(originalValue - currentValue) > 0.0001) {
                return true;
              }
            } else if (originalValue !== currentValue) {
              return true;
            }
          }
        }
      }
    }
    
    // Check equipment TimeReason values and their cost code UserValues
    if (this.timeCard.EquipmentTrackers) {
      for (const equipment of this.timeCard.EquipmentTrackers) {
        // Check TimeReason
        const eqOriginal = this.originalValues.get(equipment);
        if (eqOriginal && eqOriginal.TimeReason !== equipment.TimeReason) {
          return true;
        }
        
        // Check UserValue for each cost code
        if (equipment.CostCodes) {
          for (const costCode of equipment.CostCodes) {
            const ccOriginal = this.originalValues.get(costCode);
            if (!ccOriginal) continue;
            
            // Use strict equality and handle null/undefined cases
            const originalValue = ccOriginal.UserValue;
            const currentValue = costCode.UserValue;
            
            // Special handling for floating point values
            if (typeof originalValue === 'number' && typeof currentValue === 'number') {
              // Use a small epsilon for floating point comparison
              if (Math.abs(originalValue - currentValue) > 0.0001) {
                return true;
              }
            } else if (originalValue !== currentValue) {
              return true;
            }
          }
        }
      }
    }
    
    // If we get here, nothing is dirty
    return false;
  }

  setEmployeeFullNameTip(employee: TimeCardEmployee) {
    this.employeeFullName += employee.FirstName + ' ' + employee.LastName;
  }


  checkTimeDescValue(tcEmployee: TimeCardEmployee) {
    if (tcEmployee.TotalHours) {
      if (tcEmployee.TotalHours > 0) {
        tcEmployee.TimeReason = 'Work Day';
        
        const currentLog = this.getCurrentLog();
        if (currentLog) {
          this.diaryService.updateLog(currentLog);
          
          // Set dirty state directly in the DailyLogDirtyService
          this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
        }
      }
    }
  }

  changeCostCode(newC: CostCode, idx: number) {
    var costCode = this.projectCostCodes().find(x => x.Phase === newC.Phase);
    if (costCode && this.timeCard) {
      if (costCode) {
        for (let emp of this.timeCard.Employees) {
          var n = Object.assign({}, costCode as TimeCardCostCode);
          var old = Object.assign({}, emp.CostCodes[idx]);
          n.UserValue = old.UserValue;
          emp.CostCodes.splice(idx, 1, n);
          emp.CostCodes[idx].UserValue = old.UserValue;
        }

        if (this.timeCard.EquipmentTrackers) {
          for (let equipment of this.timeCard.EquipmentTrackers) {
            var n = Object.assign({}, costCode as TimeCardCostCode);
            var oldd = Object.assign({}, equipment.CostCodes[idx] as TimeCardCostCode);
            n.UserValue = oldd.UserValue;
            equipment.CostCodes.splice(idx, 1, n);
          }
        }

        var cc = Object.assign({}, costCode);
        this.timeCard.CostCodes.splice(idx, 1, cc);
        
        const currentLog = this.getCurrentLog();
        if (currentLog) {
          this.diaryService.updateLog(currentLog);
          
          // Set dirty state directly in the DailyLogDirtyService
          this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
        }
      }
    }
  }

  addCostCode() {
    if (this.timeCard) {
      let costCodeAdded = false;
      
      for (let c of this.selectedCostCodes) {
        var costCode = this.projectCostCodes().find(x => x.CostCodeId === c.ItemId);

        if (costCode) {
          if (!this.timeCard.CostCodes) {
            this.timeCard.CostCodes = [];
          }
          var hasCostCode = this.timeCard.CostCodes.find(x => x.CostCodeId === c.ItemId);

          if (!hasCostCode) {
            costCodeAdded = true;

            if (!costCode.Units) {
              costCode.Units = 0;
            }

            if (!costCode.Hours) {
              costCode.Hours = 0;
            }

            this.timeCard.CostCodes.push(Object.assign({}, costCode));

            if (this.timeCard.Employees) {
              for (var x = 0; x < this.timeCard.Employees.length; x++) {
                //costCode.UserValue = null;
                if (!this.timeCard.Employees[x].CostCodes) {
                  this.timeCard.Employees[x].CostCodes = [];
                }
                this.timeCard.Employees[x].CostCodes.push(Object.assign({}, costCode as TimeCardCostCode));
              }
            }


            if (this.timeCard.EquipmentTrackers) {
              for (var x = 0; x < this.timeCard.EquipmentTrackers.length; x++) {
                // costCode[0].UserValue = null;
                if (!this.timeCard.EquipmentTrackers[x].CostCodes) {
                  this.timeCard.EquipmentTrackers[x].CostCodes = [];
                }

                this.timeCard.EquipmentTrackers[x].CostCodes.push(Object.assign({}, costCode as TimeCardCostCode));
              }
            }

          } else {
            this.toastr.error('Cost code ' + costCode.Phase + ' already exists.');
          }
        }
      }

      this.selectedCostCodes = [];
      
      if (costCodeAdded) {
        const currentLog = this.getCurrentLog();
        if (currentLog) {
          this.diaryService.updateLog(currentLog);
          
          // Set dirty state directly in the DailyLogDirtyService
          this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
        }
      }
    }
  }

  deleteEmployee(idx: number) {
    if (this.timeCard) {
        this.timeCard.Employees.splice(idx, 1);

        for (let emp of this.timeCard.Employees) {
            this.calculateTotalTimeEmployee(emp);
        }

        const currentLog = this.getCurrentLog();
        if (currentLog) {
            this.diaryService.updateLog(currentLog);
            
            // Set dirty state directly in the DailyLogDirtyService
            this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
        }
    }
}

  deleteEquipment(idx: number) {
    if (this.timeCard) {
      this.timeCard.EquipmentTrackers.splice(idx, 1);
      
      const currentLog = this.getCurrentLog();
      if (currentLog) {
        this.diaryService.updateLog(currentLog);
        
        // Set dirty state directly in the DailyLogDirtyService
        this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
      }
    }
  }

  deleteCostCode(idx: number) {
    if (this.timeCard) {
      this.timeCard.CostCodes.splice(idx, 1);

      // Check if Employees exists and is an array before iterating
      if (this.timeCard.Employees && Array.isArray(this.timeCard.Employees)) {
        for (let emp of this.timeCard.Employees) {
          emp.CostCodes.splice(idx, 1);
          this.calculateTotalTimeEmployee(emp);
        }
      }

      // Check if EquipmentTrackers exists and is an array before iterating
      if (this.timeCard.EquipmentTrackers && Array.isArray(this.timeCard.EquipmentTrackers)) {
        for (let eq of this.timeCard.EquipmentTrackers) {
          eq.CostCodes.splice(idx, 1);
          this.calculateTotalTimeEquipment(eq);
        }
      }

      const currentLog = this.getCurrentLog();
      if (currentLog) {
        this.diaryService.updateLog(currentLog);
        
        // Set dirty state directly in the DailyLogDirtyService
        this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
      }
    }
  }

  addEquipment() {
    if (this.timeCard) {
      if (!this.timeCard.EquipmentTrackers) {
        this.timeCard.EquipmentTrackers = [];
      }

      for (let equipment of this.selectedEquipment) {
        var hasEquipment = this.timeCard.EquipmentTrackers.some(
          x => x.EquipmentInternalId === equipment.InternalId
        );

        if (!hasEquipment) {
          var equipmentTracker = {} as EquipmentTracker;
          equipmentTracker.EquipmentInternalId = equipment.InternalId;
          equipmentTracker.Make = equipment.Make;
          equipmentTracker.Model = equipment.Model;
          equipmentTracker.Number = equipment.Number;
          equipmentTracker.CostCodes = [];

          if (this.timeCard.CostCodes) {
            for (let cCode of this.timeCard.CostCodes) {
              equipmentTracker.CostCodes.push(Object.assign({}, cCode as TimeCardCostCode));
            }
          }

          this.timeCard.EquipmentTrackers.push(equipmentTracker);
        } else {
          this.toastr.error('Equipment already exists in the time sheet.');
        }
      }

      this.selectedEquipment = [];
      
      const currentLog = this.getCurrentLog();
      if (currentLog) {
        this.diaryService.updateLog(currentLog);
        
        // Set dirty state directly in the DailyLogDirtyService
        this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
      }
    }
  }

  private getCurrentLog(): DailyLogProjectUserComponent | null {
    return this.diaryService.getDailyLogByProjectId(this.projectId);
}

  addEmployee() {
    if (this.timeCard) {
      for (let c of this.selectedEmployees) {
        var employee = this.allEmployees().find(x => x.CustomId === c.ItemId);
        if (employee) {
          var emp = employee as Employee;
          if (!this.timeCard.Employees) {
            this.timeCard.Employees = [];
          }
          var hasEmployee = this.timeCard.Employees.find(x => x.EmployeeId === emp.CustomId);
          if (!hasEmployee) {
            // Create TimeCardEmployee with only the required properties
            var timeCardEmployee: TimeCardEmployee = {
              FirstName: emp.FirstName || '',
              LastName: emp.LastName,
              EmployeeId: emp.CustomId,
              Classification: emp.Classification,              
              CostCodes: [],
              TimeReason: this.timeDescriptions[0],
              TotalHours: null
            };

            if (this.timeCard.CostCodes) {
              for (let cCode of this.timeCard.CostCodes) {
                let cc = cCode as TimeCardCostCode;
                cc.UserValue = null;
                timeCardEmployee.CostCodes.push(Object.assign({}, cc));
              }
            }
            
            this.timeCard.Employees.push(timeCardEmployee);
          } else {
            this.toastr.error(`Employee ${hasEmployee.FirstName} ${hasEmployee.LastName} already exists in the time sheet`);
          }
        }
      }
      this.selectedEmployees = [];
      const currentLog = this.getCurrentLog();
      if (currentLog) {
        this.diaryService.updateLog(currentLog);
        
        // Set dirty state directly in the DailyLogDirtyService
        this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
      }
    }
  }

  calculateTotalTimeEmployee(tcEmployee: TimeCardEmployee) {
    tcEmployee.TotalHours = 0;
    for (var x = 0; x < tcEmployee.CostCodes.length; x++) {
      if (tcEmployee.CostCodes[x].UserValue) {
        tcEmployee.TotalHours += tcEmployee.CostCodes[x].UserValue!;
      }
    }
    const currentLog = this.getCurrentLog();
    if (currentLog) {
      this.diaryService.updateLog(currentLog);
      
      // Always set dirty state to true when hours are calculated
      // This ensures the form stays dirty until explicitly saved
      this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
    }
  }

  calculateTotalTimeEquipment(tcEquipment: EquipmentTracker) {
    tcEquipment.TotalHours = 0;
    for (var x = 0; x < tcEquipment.CostCodes.length; x++) {
      if (tcEquipment.CostCodes[x].UserValue) {
        tcEquipment.TotalHours += tcEquipment.CostCodes[x].UserValue!;
      }
    }
    
    const currentLog = this.getCurrentLog();
    if (currentLog) {
      this.diaryService.updateLog(currentLog);
      
      // Always set dirty state to true when hours are calculated
      // This ensures the form stays dirty until explicitly saved
      this.dirtyService.setDirty(this.projectId, DailyLogComponentIdentifiers.TIMECARD, true);
    }
  }

  public GetTimeInfo(): Array<TimeInfo> {
    if (this.timeInfo.length <= 0) {
      this.timeInfo = new Array<TimeInfo>();

      for (let x = 0; x <= 24; x++) {
        const d = {} as TimeInfo;
        d.Display = `${x}`;
        d.Value = x;

        this.timeInfo.push(d);

        if (x !== 24) {
          const d5 = {} as TimeInfo;
          d5.Display = `${x}.5`;
          d5.Value = x + 0.5;
          this.timeInfo.push(d5);
        }
      }
    }

    return this.timeInfo;
  }
}



export enum TimeCardLayout {
  None = "none",
  Card = "card"
}
