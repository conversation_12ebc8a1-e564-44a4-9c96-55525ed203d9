﻿﻿<!-- *ngIf="access === 'Allow'"-->

@if(view === COMPONENT_VIEWS.Edit){
<div class="row mb-3">
  <!--add-costcode-->
  <div class="col-12 col-lg-4 mb-1 mb-lg-0">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 pe-1">
        <ng-multiselect-dropdown id="costCodeSelect" [settings]="multiSelectSettings"
          [placeholder]="'Select Cost Codes'" [data]="costCodes" [(ngModel)]="selectedCostCodes" style="width: 70%;">
        </ng-multiselect-dropdown>
      </div>
      <div>
        <button class="btn btn-outline-dark" type="button" (click)="addCostCode()">
          Add
        </button>
      </div>
    </div>
  </div>
  <!--add-employee-->
  <!-- *ngIf="access === 'Allow'"-->
  <div class="col-12 col-lg-4 mb-1 mb-lg-0">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 pe-1">
        <ng-multiselect-dropdown id="employeeSelect" [settings]="multiSelectSettings" [placeholder]="'Select Employees'"
          [data]="employees" [(ngModel)]="selectedEmployees" style="width: 100%;">
        </ng-multiselect-dropdown>
      </div>
      <div>
        <button class="btn btn-outline-dark" type="button" (click)="addEmployee()">
          Add
        </button>
      </div>
    </div>
  </div>
      <!--add-Equipment-->
     <!-- *ngIf="access === 'Allow' && view !== 2""-->
  
    <div class="col-12 col-lg-4 mb-1 mb-lg-0">
    <div class="d-flex align-items-center">
      <div class="flex-grow-1 pe-1">
        <app-equipment-dropdown [(ngModel)]="selectedEquipment" style="width: 100%;"></app-equipment-dropdown>
      </div>
      <div>
        <button class="btn btn-outline-dark" type="button" (click)="addEquipment()">
          Add
        </button>
      </div>
    </div>
  </div>
</div>
}

<!--table-->
<div class="table-responsive">
  <table class="table table-bordered small">
    <tbody>
      <!-- cost codes -->
      <tr>
        <td></td>
        @for (costCode of timeCard?.CostCodes; track $index) {
        <td class="cost-code-column">
          @if(view === COMPONENT_VIEWS.Edit){
          <select class="form-select" [(ngModel)]="costCode.Phase" (change)="changeCostCode(costCode, $index)">
            @for (c of projectCostCodes(); track $index) {
            <option [ngValue]="c.Phase"> {{ c.Phase }} - {{ c.Description }}
            </option>
            }
          </select>
          }@else {
          <div class="small mx-2">{{ costCode?.Phase }}</div>
          <div class="d-inline-block small mx-2" placement="bottom">
            <!--[ngbTooltip]="costCode.Description"-->
            {{ costCode?.Description }}
          </div>
          }
        </td>
        }
        <td class="align-middle text-center reason fw-bold">Reason</td>
        <td class="align-middle text-center total fw-bold">Total</td>
        <td class="delete-employee"></td>
      </tr>
      <!-- qty -->
      <tr>
        <td></td>
        @for (costCode of timeCard?.CostCodes; track $index) {
        <td>
          <div class="d-flex align-items-end">
            @if(view === COMPONENT_VIEWS.ReadOnly){
            <span>
              {{ costCode.QtyCompl || 0 }}
              <span class="badge badge-primary">{{ costCode.UnitOfMeasure }}</span>
            </span>
            }
            @if(view === COMPONENT_VIEWS.Edit || view === COMPONENT_VIEWS.EditorQtyNotesOnly){
            <div class="input-group">
              <input class="form-control" type="number" [(ngModel)]="costCode.QtyCompl"
                (ngModelChange)="onQtyComplChange()" placeholder="Qty" />
              <span class="input-group-text">{{ costCode.UnitOfMeasure }}</span>
            </div>
            }
          </div>
        </td>
        }
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <!-- employees -->
      @if(timeCard?.Employees?.length > 0){
      <tr>
        <td class="bg-light" [attr.colspan]="4 + timeCard?.CostCodes?.length">
          <span class="fw-bold">Employees</span>
        </td>
      </tr>
      @for (tcEmployee of timeCard?.Employees; track $index) {
      <tr>
        <td>
          <!-- <pre>
                    {{ tcEmployee | json }}
                  </pre> -->
          <div class="d-block text-truncate" [ngbTooltip]="employeeFullName"
            (mouseenter)="setEmployeeFullNameTip(tcEmployee)">
            {{ tcEmployee.FirstName || '' }} {{ tcEmployee.LastName || '' }} - {{ tcEmployee.EmployeeId}}
          </div>
          <div class="text-secondary">{{ tcEmployee.CustomId }}</div>
        </td>
        @for (costCode of tcEmployee.CostCodes; track $index) {
        <td class="align-middle text-end">
          @if(view === COMPONENT_VIEWS.Edit){
          <div class="input-group">
            <select class="form-select" [(ngModel)]="costCode.UserValue" class="form-select"
              (ngModelChange)="calculateTotalTimeEmployee(tcEmployee); checkTimeDescValue(tcEmployee)">
              @for (time of timeInfo; track $index) {
              <option [ngValue]="time.Value" [selected]="time.Value === costCode.UserValue">{{ time.Display }}
              </option>
              }
            </select>
            <label class="input-group-text" for="inputGroupSelect02">Hrs</label>
          </div>
          }@else if(costCode.UserValue){
          <span>
            {{ costCode.UserValue }} Hrs.
          </span>
          }
        </td>
        }
        @if(view === COMPONENT_VIEWS.Edit){
        <td class="align-middle text-end">
          <select [(ngModel)]="tcEmployee.TimeReason" class="form-select" [disabled]="tcEmployee?.TotalHours! > 0"
            (ngModelChange)="onTimeReasonChange()">
            @for (td of timeCardComponentInfo()?.WorkDayOptions; track $index) {
            <option [ngValue]="td" [selected]="td === tcEmployee.TimeReason">
              {{ td }}
            </option>
            }
          </select>
        </td>
        }@else {
          <td class="align-middle text-end">
            {{tcEmployee.TimeReason}}
          </td>
          }
          <td class="align-middle text-end" [style.color]="tcEmployee?.TotalHours! > 24 ? 'red' : 'black'"
            [style.font-weight]="tcEmployee?.TotalHours! > 24 ? 'bold' : 'normal'">
            {{ tcEmployee.TotalHours }} Hrs.
          </td>
          <td>
            @if(view === COMPONENT_VIEWS.Edit){
              <button class="btn btn-outline-danger w-100" (click)="deleteEmployee($index)">
                <i class="fa fa-trash"></i>
              </button>
            }
          </td>
      </tr>
      }
      }

      <!-- equipment -->
      @if(timeCard?.EquipmentTrackers && timeCard?.EquipmentTrackers?.length > 0){
        <tr class="bg-light">
          <td [attr.colspan]="4 + timeCard?.CostCodes?.length">
            <span class="fw-bold">Equipment</span>
          </td>
        </tr>
        @for (tcEquipment of timeCard?.EquipmentTrackers; track $index) {
          <tr>
            <td>
              <div class="d-block">{{ tcEquipment.Make }} {{ tcEquipment.Model }} </div>
              <div class="text-secondary">{{ tcEquipment.Number}}</div>
            </td>
            @for (costCode of tcEquipment.CostCodes; track $index) {
              <td class="align-middle text-end">
            
                @if(view === COMPONENT_VIEWS.Edit){
                  <div class="form-select" class="input-group">
                    <select [(ngModel)]="costCode.UserValue" class="form-select" (change)="calculateTotalTimeEquipment(tcEquipment);">
                      @for (time of timeInfo; track $index) {
                        <option [ngValue]="time.Value"[selected]="time.Value === costCode.UserValue">
                          {{ time.Display }}
                        </option>
                      }
                
                    </select>
                    <label class="input-group-text" for="inputGroupSelect02">Hrs.</label>
                  </div>
                }@else if(costCode.UserValue){
                  <span>{{ costCode.UserValue }} Hrs.</span>                      
                }
              
              </td>
            }
    
            @if(view === COMPONENT_VIEWS.Edit){
              <td class="align-middle text-end">
                <select [(ngModel)]="tcEquipment.TimeReason" class="form-select">
                  @for (td of timeCardComponentInfo()?.EquipmentDayOptions; track $index) {
                    <option [ngValue]="td" [selected]="td === tcEquipment.TimeReason">
                      {{ td }}
                    </option>
                  }
                
                </select>
              </td>
            }@else {
              <td class="align-middle text-end">
                {{tcEquipment.TimeReason}}
              </td>
            }         
        
            <td class="align-middle text-end" [style.color]="tcEquipment?.TotalHours! > 24 ? 'red' : 'black'"
              [style.font-weight]="tcEquipment?.TotalHours! > 24 ? 'bold' : 'normal'">
              {{ tcEquipment.TotalHours }} Hrs.
            </td>
            <td>
              @if (view === COMPONENT_VIEWS.Edit) {
                <button class="btn btn-outline-danger w-100" (click)="deleteEquipment($index)">
                  <i class="fa fa-trash"></i>
                </button>
              }
          
            </td>
          </tr>
        }
      }        

      <!-- delete row -->
      @if(timeCard?.CostCodes?.length > 0){
      <tr>
        <td></td>
        @for (costCode of timeCard?.CostCodes; track $index) {
        <td>
          @if(view === COMPONENT_VIEWS.Edit){
          <button class="btn btn-outline-danger w-100" (click)="deleteCostCode($index)">
            <i class="fa fa-trash"></i></button>
          }
        </td>
        }
        <td></td>
        <td></td>
        <td></td>
      </tr>
      }
    </tbody>
  </table>
</div>
<!--no-employees or cost codes.-->
<div class="alert alert-info mt-1" role="alert" *ngIf="timeCard?.CostCodes?.length <= 0">
  You have not added any cost codes to the time sheet.
</div>

<script>
  $(document).ready(function () {
    $('#select2-search__field').removeAttr('style');
  });
</script>