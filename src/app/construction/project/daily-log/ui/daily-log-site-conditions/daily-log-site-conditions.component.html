<div class="border p-3 mb-4">
	<h2 class="page-title fs-6 mb-3">Site Conditions</h2>
	<!-- <pre>DEBUG:</pre>
	<pre>service.isLoading(): {{ service.isLoading() }}</pre>
	<pre>service.access(): {{ service.access() }}</pre>
	<pre>service.view(): {{ service.view() }}</pre>
	<pre>service.access(): {{ service.access() }}</pre>
	<pre>service.siteCondition(): {{ service.siteCondition() }}</pre> -->
	<div>
	  @if (service.isLoading()) {
		<div class="placeholder-glow">
		  <div class="placeholder col-12" style="height: 50px;"></div>
		</div>
	  } @else {
		<div>
		  @if (service.access() === 'Allow' && service.view() === COMPONENT_VIEW.Edit) {
			<select
			  class="form-select"
			  [formControl]="siteConditionControl"
			  [disabled]="service.isLoading()"
			>
			  <option value="">Select a site condition</option>
			  @for (c of service.conditions(); track $index) {
				<option [value]="c">{{ c }}</option>
			  }
			</select>
		  } @else {
			<div>{{ service.siteCondition() || 'No site condition selected' }}</div>
		  }
		</div>
	  }
	</div>
  </div>
