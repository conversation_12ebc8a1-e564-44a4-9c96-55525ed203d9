import { Component, OnInit, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { DailyLogSiteConditionsService } from '../../shared/services/daily-log-site-conditions.service';

@Component({
  selector: 'app-daily-log-site-conditions',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './daily-log-site-conditions.component.html',
  styleUrls: ['./daily-log-site-conditions.component.css'],
})
export class DailyLogSiteConditionsComponent implements OnInit {
  service = inject(DailyLogSiteConditionsService);
  readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;
  
  // Reactive form control for site condition
  siteConditionControl = new FormControl<string>('');
  
  constructor() {
    // Initialize form control with blank value if no data is loaded
    this.siteConditionControl.setValue('');

    // Subscribe to form control changes to update service
    this.siteConditionControl.valueChanges.subscribe(value => {
      if (value !== null) {
        this.service.updateSiteCondition(value);
      }
    });

    // Update form control when service value changes
    effect(() => {
      const siteCondition = this.service.siteCondition();
      if (siteCondition !== this.siteConditionControl.value) {
        this.siteConditionControl.setValue(siteCondition || '', { emitEvent: false });
      }
    });
  }
  
  ngOnInit(): void {
  }
}
