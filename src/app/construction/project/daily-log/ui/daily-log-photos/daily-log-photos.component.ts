import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DailyLogPhotoUploaderComponent } from './ui/daily-log-photo-uploader/daily-log-photo-uploader.component';
import { DailyLogPhotoGalleryComponent } from './ui/daily-log-photo-gallery/daily-log-photo-gallery.component';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { PhotoUploadViewerComponent } from './ui/photo-upload-viewer/photo-upload-viewer.component';
import { DailyLogPhotosService, PhotoViews } from '../../shared/services/daily-log-photos.service';
import { GalleryImage } from 'src/app/construction/shared/interfaces/photos';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';

@Component({
    selector: 'app-daily-log-photos',
    standalone: true,
    imports: [
      CommonModule, 
      DailyLogPhotoUploaderComponent, 
      DailyLogPhotoGalleryComponent, 
      PhotoUploadViewerComponent
    ],
    templateUrl: './daily-log-photos.component.html',
    styleUrl: './daily-log-photos.component.css'
})
export class DailyLogPhotosComponent {
  // Inject the service
  public readonly service = inject(DailyLogPhotosService);    
  private constructionPhotosService = inject(ConstructionPhotosService);
  // Expose enums for the template
  public readonly PHOTO_VIEWS: typeof PhotoViews = PhotoViews;
  public readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;

  photosProjectId = this.service.projectId;
  isUploading =  this.constructionPhotosService.isUploading;
  photosToUpload = this.constructionPhotosService.projectPhotosToAdd;
  
  // Delegate event handlers to service methods
  initializeFiles(files: FileList): void {
    this.service.initializeFiles(files);
  }
  
  setView(view: PhotoViews): void {
    this.service.setView(view);
  }
  
  markPhotoForDeletion(image: GalleryImage, mark: boolean): void {
    this.service.markPhotoForDeletion(image, mark);
  }
  
  cancelUpload(): void {
    this.service.cancelUpload();
  }
}
