<div class="border p-3 mb-4">
	<div class="d-flex align-items-center align-items-center justify-content-between mb-3">
		<h2 class="fs-6 mb-0">Photos</h2>
		@if(service.access() === 'Allow' && service.view() === COMPONENT_VIEW.Edit){
		<button type="button" class="btn btn-outline-dark" (click)="setView(PHOTO_VIEWS.Upload)">Add Photos</button>
	
		}
	</div>
	<div>
		@if((service.isLoading())){
		<div class="col-12 placeholder-glow">
			<div class="col-12 mb-5">
				<div class="d-flex flex-column">
					<div class="fs-5 fw-bold border-bottom p-2 mb-4">
						<span class="placeholder col-4"></span>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="row">
					<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2,3,4]; let i = index">
						<div class="row">
							<div class="mb-4">
								<div class="mb-4">
									<span class="placeholder" style="width: 75px;"></span>
								</div>
								<div>
									<span class="placeholder" style="height: 100px; width: 100%;"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2]; let i = index">
						<div class="row">
							<div class="mb-4">
								<div class="mb-4">
									<span class="placeholder" style="width: 75px;"></span>
								</div>
								<div>
									<span class="placeholder" style="height: 100px; width: 100%;"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2,3]; let i = index">
						<div class="row">
							<div class="mb-4">
								<div class="mb-4">
									<span class="placeholder" style="width: 75px;"></span>
								</div>
								<div>
									<span class="placeholder" style="height: 100px; width: 100%;"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-12 col-sm-6 col-lg-2 pb-3" *ngFor="let skel of [0,1,2,3,4]; let i = index">
						<div class="row">
							<div class="mb-4">
								<div class="mb-4">
									<span class="placeholder" style="width: 75px;"></span>
								</div>
								<div>
									<span class="placeholder" style="height: 100px; width: 100%;"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		}@else {
		@if(service.mainView() === PHOTO_VIEWS.Upload && service.view() === COMPONENT_VIEW.Edit){
		<div class="alert alert-info d-flex align-items-center justify-content-between mb-3 mt-3" role="alert">
			<span>Files will not be uploaded until you save.</span>
			<button class="btn btn-outline-secondary" (click)="setView(PHOTO_VIEWS.Gallery)">
				Close
			</button>
		</div>
		<div class="mb-3">
		<app-daily-log-photo-uploader [maxFiles]="10"
			(filesAddedEvent)="initializeFiles($event)"></app-daily-log-photo-uploader>
		</div>
		}
		@if(isUploading()){
		<app-photo-upload-viewer></app-photo-upload-viewer>
		}@else{
		<div>
			<app-daily-log-photo-gallery [galleryImages]="service.galleryImages()"
				(photoMarkForDeletionEvent)="markPhotoForDeletion($event.image, $event.mark)"
				[componentView]="service.view()"></app-daily-log-photo-gallery>
		</div>
		}
		}
	</div>
</div>
