
<!-- photo gallery -->
<div class="d-flex flex-wrap">
	@for (image of galleryImages; track $index) {
	<div class="border p-2 m-2" (mouseenter)="showImage(image)" (mouseleave)="hideImage(image)"
		style="cursor: pointer;">
		<img [src]="image.ThumbSafeUrl" class="image-style mb-2" (click)="viewImage(image)"
			[ngClass]="{ notUploaded: !image.UploadDate, '': image.UploadDate, redImage: image.MarkForDeletion }" />
		@if(componentView === COMPONENT_VIEWS.Edit){
		<div class="d-flex justify-content-center">
			@if(image?.MarkForDeletion){
			<div>
				<i class="fas fa-check clickable text-muted" (click)="markForDeletion(image, false)"></i>
			</div>
			}@else {
			<div>
				<i class="fas fa-trash clickable text-muted" (click)="markForDeletion(image, true)"></i>
			</div>
			}
			<!--
			<a [href]="selectedImage()?.LargeUrl" target="_blank" (click)="$event.preventDefault()">
				<i class="fas fa-eye clickable text-muted" (click)="viewImage(image)"></i>
			</a>
			-->
		</div>
		}
	</div>
	}@empty {
	<div class="alert alert-info w-100 mb-0" role="alert">
		No photos yet.
	</div>
	}
</div>
<!-- photo viewer -->
@if (selectedImage() && selectedImage().LargeUrl) {
	<div class="imageviewercontainer bg-light p-3">
		<!-- instructions -->
		<div class="d-flex justify-content-between align-items-center mb-2">
			<span class="d-none d-md-block">Use your arrow keys to move left and right and Esc to close.</span>
			<span class="d-md-none">On mobile, swipe left and right.</span>
			@if(componentView === COMPONENT_VIEWS.Edit){
			<button class="btn btn-outline-dark">
				<i class="far fa-window-close"></i>
			</button>
			}
		</div>
		<!-- image -->
		<div id="hammerelement" class="d-flex justify-content-center mb-2 bg-dark">
			<div>
			<img [src]="selectedImage()?.LargeSafeUrl" class="img-fluid civimage" />
		</div>
		</div>
		<!-- title and description -->
		<div class="border bg-white p-2 mb-2">
			<div class="mb-1">Name: {{ selectedImage()?.Title }}</div>
			<div>Desc.: {{ selectedImage()?.Description }}</div>
		</div>
		<!-- input fields and close button -->
		@if(componentView === COMPONENT_VIEWS.Edit){
		<div class="mb-2">
			<div class="mb-1">
				<input type="text" class="form-control" [(ngModel)]="selectedImage().Title" placeholder="Title" />
			</div>
			<div class="mb-1">
				<textarea class="form-control" [(ngModel)]="selectedImage().Description"
					placeholder="Description"></textarea>
			</div>
		</div>
		}
		<!-- left and right controls -->
		<div class="d-flex justify-content-center align-items-center">
			<div class="mx-3" (click)="previousImage($event)" (mouseover)="isCaretMouseOver.set('left')"
				(mouseleave)="isCaretMouseOver.set('')">
				<i class="fas fa-caret-right fa-3x fa-flip-horizontal clickable"
					[ngClass]="{'text-muted': isCaretMouseOver() === 'left'}"></i>
			</div>
			<div class="mx-3" (click)="nextImage($event)" (mouseover)="isCaretMouseOver.set('right')"
				(mouseleave)="isCaretMouseOver.set('')">
				<i class="fas fa-caret-right fa-3x clickable"
					[ngClass]="{'text-muted': isCaretMouseOver() === 'right'}"></i>
			</div>
			<div class="mx-3">
				<i class="fas fa-pencil fa-2x" (click)="mainView = 'edit'" *ngIf="photoView() === ''"></i>
			</div>
			<a class="text-decoration-none" [href]="selectedImage()?.LargeUrl" target="_blank"
				(mouseover)="isCaretMouseOver.set('eye')" (mouseleave)="isCaretMouseOver.set('')">
				Open full size
			</a>
		</div>
	</div>
	}
