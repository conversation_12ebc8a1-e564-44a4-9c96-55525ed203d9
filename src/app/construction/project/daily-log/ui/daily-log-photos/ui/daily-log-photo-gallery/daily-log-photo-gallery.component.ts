import { Component, EventEmitter, HostListener, Input, OnChanges, Output, SimpleChanges, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { GalleryImage, Photo } from 'src/app/construction/shared/interfaces/photos';
import { FormsModule } from '@angular/forms';
import { sign } from 'crypto';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { DailyLogPhotosService } from '../../../../shared/services/daily-log-photos.service';

@Component({
    selector: 'app-daily-log-photo-gallery',
    imports: [CommonModule, FormsModule],
    templateUrl: './daily-log-photo-gallery.component.html',
    styleUrl: './daily-log-photo-gallery.component.css'
})
export class DailyLogPhotoGalleryComponent implements OnChanges {

  @Input() galleryImages: Array<GalleryImage> = [];  
  @Input() componentView: ComponentView = ComponentView.ReadOnly;
  // photosService = inject(ConstructionPhotosService);
  dailyLogPhotosService = inject(DailyLogPhotosService);
  @Output() photoMarkForDeletionEvent = new EventEmitter<{image: GalleryImage, mark: boolean}>();
  sanitizer = inject(DomSanitizer);
  selectedImage = signal<GalleryImage>({} as GalleryImage);  
  photoView = signal<string>('gallery');
  isCaretMouseOver = signal<string>('');
  public readonly COMPONENT_VIEWS: typeof ComponentView = ComponentView;
  

  @HostListener('window:keyup', ['$event'])
  keyUpPressed(evt: any){
    if(evt.key === 'ArrowLeft'){
      this.previousImage(evt);
    }else if(evt.key === 'ArrowRight'){
      this.nextImage(evt);
    }else if(evt.key === 'Escape'){
      this.selectedImage.set({} as GalleryImage);
    }
  }
  

  setGroup(group:string){    

  }

  ngOnChanges(changes: SimpleChanges): void {
    // if(!this.selectedImage().LargeUrl){
    //   if(changes['galleryImages'] && changes['galleryImages'].currentValue !== changes['galleryImages'].previousValue){
    //     if(this.galleryImages.length > 0){
    //       this.selectedImage.set(this.galleryImages[0]);
    //     }
    //   }
    // }

  }
  setCaretsMouseOver(caret: string){
    this.isCaretMouseOver.set(caret);
  }

  previousImage(evt: any){
    const idx = this.galleryImages.indexOf(this.selectedImage());
    if(idx > 0){
      this.selectedImage.set(this.galleryImages[idx - 1]);
    }else{
      this.selectedImage.set(this.galleryImages[this.galleryImages.length - 1]);
    }
  }

  nextImage(evt:any){
    const idx = this.galleryImages.indexOf(this.selectedImage());
    if(idx >= 0 && idx < this.galleryImages.length - 1){
      this.selectedImage.set(this.galleryImages[idx + 1]);
    }else{
      this.selectedImage.set(this.galleryImages[0]);
    }
  }

  showImage(image: GalleryImage){
    image.ShowOverlay = true;
  }

  hideImage(image: GalleryImage){
    image.ShowOverlay = false;
  }
  viewImage(image: GalleryImage){
    this.selectedImage.set(image);
  }

  markForDeletion(image: GalleryImage, mark: boolean){
    // Emit the event for the parent component to handle
    this.photoMarkForDeletionEvent.emit({image, mark});
    
    // Also use the DailyLogPhotosService directly for immediate UI update
    this.dailyLogPhotosService.markPhotoForDeletion(image, mark);
  }

}
