import { Component, EventEmitter, Input, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Photo } from 'src/app/construction/shared/interfaces/photos';

@Component({
    selector: 'app-daily-log-photo-uploader',
    imports: [CommonModule],
    templateUrl: './daily-log-photo-uploader.component.html',
    styleUrl: './daily-log-photo-uploader.component.css'
})
export class DailyLogPhotoUploaderComponent {
  @Input() maxFiles: number = 2;
  @Output() filesAddedEvent = new EventEmitter<FileList>();
  
  
  onDrop(event: DragEvent) {
    this.preventEvent(event);

    if (!event.dataTransfer || !event.dataTransfer.files) {
      return;
    }

    this.runFiles(event.dataTransfer.files);
  }

  acceptfiles: string = "image/png,image/jpeg,image/gif";

  onDragEnter(event: Event) {
    this.preventEvent(event);
  }

  onDragOver(event: any) {
    this.preventEvent(event);
  }

  runFiles(files: FileList| null) {
    if(files){
      this.filesAddedEvent.next(files);
    //   let files: FileList = target.files;

    //   if (this.maxFiles > 0) {
    //     if (files.length > this.maxFiles) {
    //       this.uploadError.next(new MaxFileError('Too many files. You are allowed to upload ' + this.maxFiles + ' at a time'));
    //       return;
    //     }
    //   }
  
    //   this.runFiles(files);
    //   target.value = '';
     }

    // this.executeFinishButton();
  }

  onChange(event: any) {
    let eventObj: any = event;
    let target: HTMLInputElement = <HTMLInputElement>eventObj.target;
    this.runFiles(target.files);


  }

  ngOnDestroy(): void {
    // this.unsubscribe.next();
    // this.unsubscribe.complete();
  }

  executeFinishButton() {
    //this.finishUploadButtonEvent.next(null);
  }

  // showOverlay(imageInfo: ImageInfo) {
  //   imageInfo.ShowOverlay = true;
  // }

  // hideOverlay(imageInfo: ImageInfo) {
  //   imageInfo.ShowOverlay = false;
  // }

  private preventEvent(event: any): void {
    event.stopPropagation();
    event.preventDefault();
  }
}