<div class="d-flex flex-wrap">
	@for (photo of photos(); track $index) {
		<div class="border p-2 m-2">
			<div class="row">
				<div class="col-12">
					<img src="{{photo.FileUrl}}" alt="{{photo.Name}}" class="image-style mb-2" />
				</div>
				<div class="col-12">

					<div class="progress" style="height: 25px;">
						<div class="progress-bar" role="progressbar" [ngStyle]="{'width': (photo.ThumbPhotoInfo.Progress + photo.LargePhotoInfo.Progress) / 2 + '%'}" [attr.aria-valuenow]="(photo.ThumbPhotoInfo.Progress + photo.LargePhotoInfo.Progress) / 2" aria-valuemin="0" aria-valuemax="100">{{(photo.ThumbPhotoInfo.Progress + photo.LargePhotoInfo.Progress) / 2}}%</div>
					  </div>

					<!-- Progress: {{(photo.ThumbPhotoInfo.Progress + photo.LargePhotoInfo.Progress) / 2}} -->

				</div>
			
				
			
			</div>
	
		
	  </div>
	}
</div>
<div class="mt-4">
	<div class="progress" style="height: 25px;">
		<div class="progress-bar progress-bar-striped bg-success" role="progressbar" [ngStyle]="{'width': totalProgress() + '%'}" [attr.aria-valuenow]="totalProgress()" aria-valuemin="0" aria-valuemax="100">{{totalProgress()}}%</div>
	  </div>
</div>
