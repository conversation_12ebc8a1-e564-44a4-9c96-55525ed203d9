.image-style {
	display: block;
	max-width: 150px;
	max-height: 90px;
	width: auto;
	height: auto;
}

.civoverlay {
	position: absolute;
	bottom: 0;
	right: 0;
	background-color: #008cba;
	overflow: hidden;
	width: 0;
	height: 100%;
	transition: 0.5s ease;
}

.notUploaded {
	border: 2px solid red;
}

.redImage {
	-webkit-filter: invert(40%) grayscale(100%) brightness(40%) sepia(100%) hue-rotate(-50deg) saturate(400%) contrast(2);
	filter: grayscale(100%) brightness(40%) sepia(100%) hue-rotate(-50deg) saturate(600%) contrast(0.8);
}

.imageviewercontainer {
	width: 100%;
	position: relative;
	margin: 0 auto;
}

.civimage {
	max-height: 800px;
	width: auto;
}

.imageviewercontainer .contentright {
	position: absolute;
	bottom: 0;
	right: 12px;
	background: rgba(29, 28, 28, 0.75);
	/* Black background with transparency */
	color: #f1f1f1;
	width: 75px;
	height: 100%;
	padding: 2px;
	cursor: pointer;
}

.imageviewercontainer .ivicon {
	bottom: 50%;
	position: absolute;
}

.imageviewercontainer .contenttop {
	position: absolute;
	color: #f1f1f1;
	width: 100%;
	height: 50px;
	padding: 2px;
	top: 0;
	position: absolute;
}

.imageviewercontainer .contentbottom {
	position: absolute;
	z-index: 1000;
	color: #f1f1f1;
	width: 30%;
	height: 50px;
	padding: 20px;
	bottom: 0;
	left: calc(50% - (30% / 2));
	/* Firefox */
	left: -moz-calc(50% - (30% / 2));
	/* WebKit */
	left: -webkit-calc(50% - (30% / 2));
	/* Opera */
	left: -o-calc(50% - (30% / 2));
	background: rgba(29, 28, 28, 0.75);
}

.imageviewercontainer .contenttop .iconcontainer {
	position: absolute;
	left: calc((50% - 150px) + (150px / 2));
	/* Firefox */
	left: -moz-calc((50% - 150px) + (150px / 2));
	/* WebKit */
	left: -webkit-calc((50% - 150px) + (150px / 2));
	/* Opera */
	left: -o-calc((50% - 150px) + (150px / 2));
	padding: 5px;
	width: 150px;
	background: rgba(29, 28, 28, 0.75);
	/* Black background with transparency */
}

.imageviewercontainer .contenttop .iconcontainer .ivtoolsicon {
	color: white;
	padding: 10px;
	cursor: pointer;
}

.imageviewercontainer .contentleft {
	position: absolute;
	bottom: 0;
	left: 12px;
	background: rgba(29, 28, 28, 0.75);
	/* Black background with transparency */
	color: #f1f1f1;
	width: 75px;
	height: 100%;
	padding: 2px;
	cursor: pointer;
}

.civ-process-image {
	background-color: lightgray;
	display: block;
	width: 460px;
	height: 190px;
	margin-right: 10px;
	margin-top: 15px;
}