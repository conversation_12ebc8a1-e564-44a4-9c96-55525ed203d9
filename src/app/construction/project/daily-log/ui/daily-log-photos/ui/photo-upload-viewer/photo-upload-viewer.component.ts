import { Component, inject, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';
import { DailyLogService } from '../../../../shared/services/daily-log.service';

@Component({
    selector: 'app-photo-upload-viewer',
    imports: [CommonModule],
    templateUrl: './photo-upload-viewer.component.html',
    styleUrl: './photo-upload-viewer.component.css'
})
export class PhotoUploadViewerComponent {
  photoConstructionService = inject(ConstructionPhotosService);
  dailyLogService = inject(DailyLogService);
  

  // Get project-specific photos
  // photos = computed(() => {
  //   const projectId = this.dailyLogService.projectIdSignal();
  //   return projectId ? this.photoConstructionService.getProjectPhotos(projectId)() : [];
  // });
  
  photos = this.photoConstructionService.projectPhotosToAdd;

  // Get project-specific progress
  // totalProgress = computed(() => {
  //   const projectId = this.dailyLogService.projectIdSignal();
  //   return projectId ? this.photoConstructionService.getProjectProgress(projectId)() : 0;
  // });

  totalProgress = this.photoConstructionService.totalProgress; ///TODO: FIX
  
  constructor() { }

  getRandomColor() {
    var color = '#'; // <-----------
    var letters = '0123456789ABCDEF';
    for (var i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }

    return color;
}
}
