<div class="border p-3 mb-4">
	<h2 class="page-title fs-6 mb-3">Weather</h2>
	<div>
	  @if (service.isLoading()) {
		<div class="placeholder-glow">
		  <div class="placeholder col-12" style="height: 50px;"></div>
		</div>
	  } @else {
		<div class="row">
			<div class="col-12 col-sm-6 mb-3 mb-sm-0">
			  <label for="temperatureSelect" class="form-label">Temp.</label>
			  @if (service.access() === 'Allow' && service.view() === COMPONENT_VIEW.Edit) {
				<select
				  id="temperatureSelect"
				  class="form-select"
				  [formControl]="temperatureControl"
				  [disabled]="service.isLoading()"
				>
				  <option value="">Select a temperature</option>
				  @for (temp of service.availableTemperatures(); track $index) {
					<option [value]="temp">{{ temp }}</option>
				  }
				  @empty {
					<option value="">No temperatures</option>
				  }
				</select>
			  } @else {
				<div>{{ service.temperature() || 'No Temperature Selected' }}</div>
			  }
			</div>
			<div class="col-12 col-sm-6">
			  <label for="skySelect" class="form-label">Sky</label>
			  <div class="d-block">
				@if (service.access() === 'Allow' && service.view() === COMPONENT_VIEW.Edit) {
				  <ng-multiselect-dropdown
					[placeholder]="'Select sky options'"
					[settings]="skyDropDownSettings"
					[data]="service.availableSkys()"
					[(ngModel)]="selectedItems"
					(onSelect)="onItemSelect($event)"
					(onSelectAll)="onSelectAll($event)"
					(onDeSelect)="onDeSelect($event)"
					(onDeSelectAll)="onDeSelectAll()"
				  >
				  </ng-multiselect-dropdown>
				} @else {
				  @for (sky of service.selectedSkys(); track $index) {
					<span>{{ sky }}{{ $index < service.selectedSkys().length - 1 ? ', ' : '' }}</span>
				  }
				  @if (!service.selectedSkys() || service.selectedSkys().length <= 0) {
					<span class="font-italic">Nothing Selected</span>
				  }
				}
			  </div>
			</div>
		</div>
	  }
	</div>
</div>
