import { Component, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { NgMultiSelectDropDownModule, IDropdownSettings } from 'ng-multiselect-dropdown';
import { DailyLogWeatherService } from '../../shared/services/daily-log-weather.service';

@Component({
  selector: 'app-daily-log-weather',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, NgMultiSelectDropDownModule],
  templateUrl: './daily-log-weather.component.html',
  styleUrls: ['./daily-log-weather.component.css']
})
export class DailyLogWeatherComponent {
  service = inject(DailyLogWeatherService);
  readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;

  temperatureControl = new FormControl<string>('');
  selectedItems: Array<any> = [];
  
  skyDropDownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'ItemId',
    textField: 'ItemText',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 5,
    allowSearchFilter: true,
    enableCheckAll: true,
    defaultOpen: false,
    closeDropDownOnSelection: false
  };

  constructor() {
    // Initialize temperature control with blank value if no data is loaded
    this.temperatureControl.setValue('');

    // Subscribe to form control changes to update service
    this.temperatureControl.valueChanges.subscribe(value => {
      if (value !== null) {
        this.service.updateTemperature(value);
      }
    });

    // Update form control when service value changes
    effect(() => {
      const temperature = this.service.temperature();
      if (temperature !== this.temperatureControl.value) {
        this.temperatureControl.setValue(temperature || '', { emitEvent: false });
      }
    });

    // Initialize selected items from service
    effect(() => {
      const selectedSkys = this.service.selectedSkys();
      this.selectedItems = selectedSkys.map(sky => ({
        ItemId: sky,
        ItemText: sky
      }));
    });
  }

  onItemSelect(evt: any): void {
    if (evt && evt.ItemId) {
      this.service.addSky(evt.ItemId);
    }
  }

  onSelectAll(evt: any): void {
    if (evt && Array.isArray(evt)) {
      const skys = evt.map((item: any) => item.ItemId);
      this.service.updateSkys(skys);
    }
  }

  onDeSelect(evt: any): void {
    if (evt && evt.ItemId) {
      this.service.removeSky(evt.ItemId);
    }
  }

  onDeSelectAll(): void {
    this.service.updateSkys([]);
  }
}
