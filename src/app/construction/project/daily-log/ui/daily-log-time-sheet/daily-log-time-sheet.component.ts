import { Component, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TimeCardGraphComponent } from '../daily-log-timecard-graph-component/timecard.graph.component';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { DailyLogTimeSheetService } from '../../shared/services/daily-log-time-sheet.service';

@Component({
  selector: 'app-daily-log-time-sheet',
  standalone: true,
  imports: [CommonModule, TimeCardGraphComponent],
  templateUrl: './daily-log-time-sheet.component.html',
  styleUrls: ['./daily-log-time-sheet.component.css'],
  // providers: [DailyLogTimeSheetService] // Provide service at component level
})
export class DailyLogTimeSheetComponent {
  public readonly service = inject(DailyLogTimeSheetService);
  public readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;

  layoutType = 'card'; // Hardcoded as in original, adjust if needed
  timeCardApprovalSystem = {}; // Placeholder, adjust if needed
  isImporting = this.service.isImporting;

  constructor() { }
}
