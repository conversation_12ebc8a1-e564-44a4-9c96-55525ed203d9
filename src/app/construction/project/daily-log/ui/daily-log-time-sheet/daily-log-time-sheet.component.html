<div class="border p-3 mb-4">
  <div class="d-flex align-items-center justify-content-between mb-3">
    <h2 class="page-title fs-6 mb-0">Time Sheet</h2>
    @if (service.access() === 'Allow' && service.currentDate() && service.view() === COMPONENT_VIEW.Edit) {
    <button type="button" class="btn btn-outline-dark ms-2" (click)="service.importRecentTimeCard()"
      [disabled]="service.isLoading() || isImporting()">
      @if(isImporting()) {
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}
      Copy Last Entry
    </button>
    }
  </div>
  <div style="min-height: 100px;">
    @if (service.component()?.TimeCards?.length > 0) {
    @for (tc of service.component()?.TimeCards; track $index) {
    <timecard-graph [projectId]="service.projectId()" [timeCard]="tc" [layoutType]="layoutType" [view]="service.view()"
      [timeCardApprovalSystem]="timeCardApprovalSystem">
    </timecard-graph>
    }
    } @else {
    <div class="alert alert-info" role="alert">
      No time sheet data available.
    </div>
    }
  </div>
</div> 