<div class="border p-3 mb-4">
    <h2 class="page-title fs-6 mb-3">Notes</h2>
    <div>
        @if (service.isLoading()) {
            <div class="placeholder-glow">
                <div class="placeholder col-12" style="height: 50px;"></div>
            </div>
        } @else {
            @if ((service.access() === 'Allow' && service.view() === COMPONENT_VIEW.Edit) || service.view() === COMPONENT_VIEW.EditorQtyNotesOnly) {
                <textarea
                    class="form-control"
                    rows="5"
                    [formControl]="notesControl"
                    [disabled]="service.isLoading()"
                    placeholder="Enter notes..."></textarea>
            } @else {
                <div>{{ service.notes() || 'No notes available' }}</div>
            }
        }
    </div>
</div>
