import { Component, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { DailyLogNotesService } from '../../shared/services/daily-log-notes.service';

@Component({
  selector: 'app-daily-log-notes',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './daily-log-notes.component.html',
  styleUrls: ['./daily-log-notes.component.css']
})
export class DailyLogNotesComponent {
  service = inject(DailyLogNotesService);
  readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;
  
  // Reactive form control for notes
  notesControl = new FormControl('');
  
  constructor() {
    // Initialize form control with current notes value
    this.notesControl.setValue(this.service.notes());
    
    // Subscribe to form control changes to update service
    this.notesControl.valueChanges.subscribe(value => {
      this.service.notes.set(value || '');
    });
    
    // Update form control when service value changes
    effect(() => {
      const notes = this.service.notes();
      if (notes !== this.notesControl.value) {
        this.notesControl.setValue(notes, { emitEvent: false });
      }
    });
  }
}
