<section class="p-3 p-lg-4">
	<div class="container">
		<!-- page title -->
		<h2 class="page-title text-muted fs-6">New Project</h2>
		@if(projectPermissions() && projectPermissions()?.addProject !== 'Allow'){
		<div class="alert alert-danger">You do not have access to add a project</div>
		}
		<!-- form -->
		<div>
			<form #projectForm="ngForm" (ngSubmit)="addProject()">
				<div>
					<!-- id -->
					<div class="form-floating mb-3">
						<input class="form-control" name="internalId" type="text" [(ngModel)]="project.InternalId"
							required [disabled]="projectPermissions()?.addProject !== 'Allow' || isSaving " />
						<label for="floatingInput">ID</label>
					</div>
					<!-- warning -->
					<div class="alert alert-warning" role="alert" *ngIf="warningMessage">
						{{ warningMessage }}
					</div>
					<!-- name -->
					<div class="form-floating mb-3">
						<input class="form-control" name="title" type="text" [(ngModel)]="project.Title" required
							[disabled]="projectPermissions()?.addProject !== 'Allow' || isSaving" />
						<label for="floatingInput">Name</label>
					</div>
				</div>
				@if(initializationStatus()){
				<div class="alert alert-info">
					{{ initializationStatus()}}
				</div>
				}
				<!-- save button -->
				<div class="d-flex justify-content-end">
					<button class="btn btn-primary" type="submit"
						[disabled]="!projectForm.form.valid || isSaving || projectPermissions()?.addProject !== 'Allow'">
						@if(isSaving){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						Add Project</button>
				</div>
			</form>
		</div>
	</div>
</section>