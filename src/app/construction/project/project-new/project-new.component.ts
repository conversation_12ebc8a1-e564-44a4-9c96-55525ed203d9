import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ConstructionProject } from '../../shared/interfaces/construction-project';
import { ConstructionProjectService } from '../../shared/data-access/project.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ConstructionProjectInitializationService } from '../../shared/data-access/project-initialization.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { delay } from 'rxjs';


@Component({
    selector: 'app-project-new',
    templateUrl: './project-new.component.html',
    styleUrls: ['./project-new.component.css'],
    imports: [CommonModule, FormsModule]
})
export class ProjectNewComponent implements OnInit {

  project: ConstructionProject = {} as ConstructionProject;
  projectInitializeService = inject(ConstructionProjectInitializationService);
  accessService = inject(AccessService);
  projectService = inject(ConstructionProjectService);  
  toastrService = inject(ToastrService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  isNewProjectLoading: boolean = true;
  isSaving: boolean = false;
  projectPermissions = this.projectService.projectPermissions;
  initializationStatus = this.projectInitializeService.status;
  warningMessage: string | null = null;
  addProjectAccess: string | null = null;
  ngOnInit() {
    this.projectService.getProjectPermissions().subscribe();    
  }

  addProject() {
    this.isSaving = true;
  
    this.projectService.addProject(this.project, this.aRoute.parent);
    
  }
}