import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { ConstructionProjectService } from '../../shared/data-access/project.service';
import { ToastrService } from 'ngx-toastr';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { switchMap, of, tap } from 'rxjs';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { ConstructionRecentProjectsService } from '../../shared/data-access/recent-projects.service';
import { ConstructionProjectsService } from '../../shared/data-access/projects.service';
import { ConstructionProject } from '../../shared/interfaces/construction-project';


@Component({
  selector: 'app-settings',
  imports: [CommonModule],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.css'
})
export class ProjectSettingsComponent {
  projectService = inject(ConstructionProjectService);  
  recentProjectService = inject(ConstructionRecentProjectsService);
  toastrService = inject(ToastrService);
  confirmService = inject(ConfirmService);
  aRouter = inject(ActivatedRoute);
  project = this.projectService.project;
  loading: boolean = false;
  isDeactivatingProject = this.projectService.isSaving;
  projectPermissions = this.projectService.projectPermissions;
  projectIdInfo = signal<string>("");
  projectId = signal<string>("");

  getProject = toSignal(toObservable(this.projectIdInfo).pipe(
    tap(() => this.loading = true),
    switchMap(projectId => projectId ? this.projectService.getProject(projectId) : of(null)),
    tap((project) => {
      this.loading = false;
    })
  ));

  constructor() {
    this.projectService.getProjectPermissions().subscribe();

    const project = this.project() as ConstructionProject;
    if (!project) {
      let projectId = this.aRouter.parent?.snapshot.params['projectId'];
      if (projectId) {
        this.projectIdInfo.set(projectId);
        this.projectId.set(projectId);
      }
    } else {
      this.projectId.set(project.Id);
    }
  }


  deactiveProject(status: boolean) {
    var message = (status) ? "close" : "open";

    this.confirmService.open(`Are you sure you want to ${message} the project?`).result.then(async value => {
      if (value === 'yes') {        
        if (this.projectId()) {
          this.projectService.setProjectStatus(this.projectId(), status);
        }
      }
    });

  }
}
