<!--page-title-->
<h2 class="page-title fs-6">Project Settings</h2>
<div>
	<button class="btn mt-3" (click)="deactiveProject(!project()?.Deactivated)"
		[ngClass]="{'btn-outline-dark': project()?.Deactivated, 'btn-outline-danger': !project()?.Deactivated}"
		[disabled]="projectPermissions()?.closeProject !== 'Allow'">
		@if(isDeactivatingProject()){
		<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
		}
		@if(!project()?.Deactivated){
		Close Project
		}@else {
		Reopen Open Project
		}
	</button>
</div>
@if(projectPermissions() && projectPermissions()?.closeProject !== 'Allow'){
<div calss="alert alert-info">Not allowed to close a project.</div>
}