import { Route } from '@angular/router';
import { ProjectComponent } from './project.component';
import { ConstructionProjectAccessGuard } from '../utils/project-access-guard';
import { ProjectUnauthorizedComponentComponent } from './project-unauthorized-component/project-unauthorized-component.component';

export const PROJECT_ROUTES: Route[] = [
	{
		path: '',
		component: ProjectComponent,
		children: [			
			{
				path: 'cost-codes',	
				canActivate: [ConstructionProjectAccessGuard],	
				data: { accessRequest: { action: "access-project-costcodes", resource: "route-access" } },		
				loadChildren: () => import('../project/cost-codes/cost-codes.routes').then(c => c.PROJECT_COSTCODES_ROUTES)
			},
			{
				path: 'daily-log',
				canActivate: [ConstructionProjectAccessGuard],    
				data: { accessRequest: { action: "access-project-dailylog", resource: "route-access" } },
				loadComponent: () => import('../diary/diary.component').then(c => c.DiaryComponent)
			  }, 		
			{
				path: 'files',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-files", resource: "route-access" } },		
				loadChildren: () => import('../project/files/files.routes').then(c => c.FILES_ROUTES)
			},
			{
				path: 'info',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-info", resource: "route-access" } },
				loadComponent: () => import('../project/info/info.component').then(c => c.ConstructionProjectInfoComponent)
			},
			{
				path: 'photos',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-photos", resource: "route-access" } },
				loadComponent: () => import('../project/photos/photos.component').then(c => c.PhotosComponent)
			},
			{
				path: 'punch-list',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-punch-list", resource: "route-access" } },
				loadChildren: () => import('../project/punch-lists/punch-lists.routes').then(c => c.PUNCH_LIST_ROUTES)
			},
			{
				path: 'reports',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-reports", resource: "route-access" } },
				loadComponent: () => import('../project/reports/reports.component').then(c => c.ReportsComponent)
			},
			{
				path: 'team',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-team", resource: "route-access" } },
				loadComponent: () => import('../project/team/team.component').then(c => c.TeamComponent)
			},
			{
				path: 'settings',
				canActivate: [ConstructionProjectAccessGuard],
				data: { accessRequest: { action: "access-project-settings", resource: "route-access" } },
				loadComponent: () => import('./settings/settings.component').then(c => c.ProjectSettingsComponent)
			},
			{
				path: 'unauthorized',
				component: ProjectUnauthorizedComponentComponent
			}
		]
	}
];