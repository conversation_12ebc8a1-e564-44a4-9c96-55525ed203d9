<!-- page header -->
<header class="bg-light p-4">
	<div class="container">
		<div class="row align-items-center">
			<!-- page title -->
			<div class="col-10">
				<h1 class="fs-6">{{ project()?.Title }}</h1>
			</div>
			<!-- menu -->
			<div class="col-2 d-flex justify-content-end">
				<div>
					@if(sideBarLoading()){
					<i class="fas fa-circle-notch fa-spin fa-2x"></i>
					}@else{
					<app-dashboard-sidebar-menu [project]="project()"
						[userProjectComponents]="userProjectComponents()"></app-dashboard-sidebar-menu>
					}
				</div>
			</div>
		</div>
	</div>
</header>
<!--project pages -->
<section class="p-4">
	<div class="container">
		@if(projectLoading()){
		<div class="placeholder-glow">
			<span class="placeholder col-4"></span>
		</div>
		}@else{
		<div>
			<router-outlet></router-outlet>
		</div>
		}
	</div>
</section>