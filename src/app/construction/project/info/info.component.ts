import { Component, Input, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionProjectInfoViewerComponent } from './ui/construction-project-info-viewer/construction-project-info-viewer.component';
import { ConstructionProjectService } from '../../shared/data-access/project.service';
import { ActivatedRoute } from '@angular/router';
import { ConstructionInfoService } from '../../shared/data-access/info.service';
import { ConstructionProjectInfoEditorComponent } from './ui/construction-project-info-editor/construction-project-info-editor.component';
import { map, of, switchMap, tap } from 'rxjs';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { ProjectInfoComponent } from '../../shared/interfaces/info';
import { ConstructionProject, ProjectUpdateOptions } from '../../shared/interfaces/construction-project';

@Component({
    selector: 'app-construction-project-info',
    imports: [CommonModule, ConstructionProjectInfoViewerComponent, ConstructionProjectInfoEditorComponent],
    templateUrl: './info.component.html',
    styleUrl: './info.component.css'
})
export class ConstructionProjectInfoComponent {

  aRoute = inject(ActivatedRoute);
  projectService = inject(ConstructionProjectService);
  infoComponentService = inject(ConstructionInfoService);
  infoPermissions = this.infoComponentService.infoPermissions;
  project = this.projectService.project;
  isLoading = signal<boolean>(false);
  mode = signal<InfoModes>(InfoModes.View);
  editInfoAccess = signal(""); //TODO: fix
  public readonly INFO_MODES: typeof InfoModes = InfoModes;    
  isGettingComponent = signal<boolean>(false);
  isUpdatingComponent = signal<boolean>(false);
  projectId = signal<string>('');  
  infoComponent = signal<ProjectInfoComponent | null>(null);
  newInternalId: string | null = null;
  newProjectTitle: string | null = null;
  private readonlyInfoComponent = toSignal(toObservable(this.projectId).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((projectId) => projectId ? this.infoComponentService.getOrCreateInfoComponent(projectId): of(null)),
    tap((result: ProjectInfoComponent | null) => {
      if(result){
        this.infoComponent.set(result);
        this.isLoading.set(false)
      }else{
        this.infoComponentService.getUserStoreInfoComponent().subscribe({
          next: (infoComponent: ProjectInfoComponent | null) => {
            if(infoComponent){
              this.infoComponent.set(infoComponent);
            }else{
              this.infoComponent.set(null);
            }
            
            this.isLoading.set(false);
          },error: (err) => {
            this.isLoading.set(false);
          },
        });
      }
  
    })
  ));

  constructor(){
    this.infoComponentService.getInfoPermissions().subscribe();

    const params = this.aRoute.parent?.snapshot.paramMap;  
    if(params){
      var projectId = params.get("projectId");
      if(projectId){
        this.projectId.set(projectId);      
      }  
    }
    this.editInfoAccess.set("Allow"); //TODO: fix
  }

  saveComponent(){
   of(true).pipe(
      tap(() => this.isUpdatingComponent.set(true)),      
      switchMap(() => this.infoComponent() ? this.infoComponentService.updateInfoComponent(this.projectId(), this.infoComponent() as ProjectInfoComponent) : of(null)),
      tap((infoComponent) => {
        this.isUpdatingComponent.set(false);
        this.infoComponent.set(infoComponent);
        this.mode.set(InfoModes.View);
      })).subscribe();

      if(this.project() && ((this.newInternalId && this.project()?.InternalId !== this.newInternalId) || (this.newProjectTitle && this.project()?.Title !== this.newProjectTitle))){

        var updateOptions = {} as ProjectUpdateOptions;
        if(this.newInternalId){
          updateOptions.internalId = this.newInternalId;
        }else{
          updateOptions.internalId = this.project()?.InternalId ?? '';
        }
        
        if(this.newProjectTitle){
          updateOptions.title = this.newProjectTitle;
        }else{
          updateOptions.title = this.project()?.Title ?? '';
        }

        this.projectService.updateProject(this.projectId(), updateOptions).subscribe({
          next: (comp) => {    
            //this.toastrService.success("Project Saved");
            this.project.update((project) => {
              if(project && this.newInternalId){
                project.InternalId = this.newInternalId;
              }
              
              if(project && this.newProjectTitle){
                project.Title = this.newProjectTitle;
              }
              

              return project;
            });
          },
          error: (err) => {
            console.log(err);
          }
        });
        
       
      }
   }

   changeMode(mode: InfoModes){
    this.mode.set(mode);   
   }

   changeInternalId(event: string){
    this.newInternalId = event;
   }

   changeProjectTitle(event: string){
    this.newProjectTitle = event;
   }
}

export enum InfoModes{
  View = "view",
  Edit = "edit"
}