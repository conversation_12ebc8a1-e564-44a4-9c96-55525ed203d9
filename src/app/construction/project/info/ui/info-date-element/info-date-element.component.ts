import { AfterViewInit, Component, Input, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbDate, NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { InfoItem, InfoValueTypes } from 'src/app/construction/shared/interfaces/info';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-info-date-element',
    imports: [CommonModule, NgbDatepickerModule, FormsModule],
    templateUrl: './info-date-element.component.html',
    styleUrl: './info-date-element.component.css'
})
export class InfoDateElementComponent implements AfterViewInit {
  @Input() infoItem: InfoItem | null = null;  
  selectedDate = signal<NgbDate | null>(null);
  formatter = inject(MomentDateFormatterService);


  ngAfterViewInit(): void {
    if (this.infoItem && this.infoItem.Value !== null && this.infoItem.Value !== undefined) {
      var dd = new Date(this.infoItem.Value.toString());
      var dateInfo = new NgbDate(dd.getFullYear(),dd.getMonth() + 1,dd.getDate());
      this.selectedDate.set(dateInfo);
    }    
  }

  dateChanged(evt: NgbDate) {

      if(!this.infoItem)
      {
        this.infoItem = {} as InfoItem;
      }

      const year = evt.year;
      const month = evt.month;
      const day = evt.day;

      if(year && month && day){
        this.infoItem.Value = new Date(year, month - 1, day).toISOString();
        this.selectedDate.set(evt);
      }        
  }
}
