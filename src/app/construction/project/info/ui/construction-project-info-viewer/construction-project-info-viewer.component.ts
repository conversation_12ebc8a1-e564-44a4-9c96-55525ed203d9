import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ProjectInfoComponent, InfoValueTypes } from 'src/app/construction/shared/interfaces/info';

@Component({
    selector: 'app-construction-project-info-viewer',
    imports: [CommonModule],
    templateUrl: './construction-project-info-viewer.component.html',
    styleUrl: './construction-project-info-viewer.component.css'
})
export class ConstructionProjectInfoViewerComponent {
  @Input({required: true}) projectInternalId: string = '';
  @Input({required: true}) infoComponent: ProjectInfoComponent = {} as ProjectInfoComponent;
  public readonly INFO_VALUE_TYPES: typeof InfoValueTypes = InfoValueTypes;
}
