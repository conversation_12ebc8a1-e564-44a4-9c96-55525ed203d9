<div class="row">
	<div class="col-12 col-sm-6 col-lg-4 pb-3 d-flex flex-column">
		<span class="pe-1 text-secondary font-size-sm">ID</span>
		<span>{{ projectInternalId }}</span>
	</div>
	@for (prop of infoComponent?.Properties; track $index) {
	@if(!prop.IsHidden){
	<div class="col-12 col-sm-6 col-lg-4 pb-3 d-flex flex-column">
		<span class="pe-1 text-secondary font-size-sm">{{ prop.Name }}</span>
		@switch (prop.ValueType) {
		@case (INFO_VALUE_TYPES.String)
		{
		{{prop.Value}}
		}
		@case(INFO_VALUE_TYPES.Decimal){
		{{prop.Value }}
		}
		@case(INFO_VALUE_TYPES.Boolean){
		{{prop.Value}}
		}
		@case(INFO_VALUE_TYPES.Integer){
		{{prop.Value}}
		}
		@case(INFO_VALUE_TYPES.Date){
		{{prop.Value | date: 'M/dd/yyyy'}}
		}
		@case(INFO_VALUE_TYPES.Currency){
		{{prop.Value | currency:'USD':'symbol':'1.2-2'}}
		}@default {
		}
		}
	</div>
	}
	}@empty{
	<div class="alert alert-info" role="alert">
		No properties found.
	</div>
	}
</div>