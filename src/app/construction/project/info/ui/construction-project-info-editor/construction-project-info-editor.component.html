<div class="row">
	<div class="col-12 col-sm-6 col-lg-6 pb-3 d-flex flex-column">
		<span class="pe-1 text-secondary font-size-sm">ID</span>
		<input class="form-control" id="projectInternalId" type="text" [(ngModel)]="projectInternalId"
			(ngModelChange)="changeInternalId($event)" />
	</div>
	<div class="col-12 col-sm-6 col-lg-6 pb-3 d-flex flex-column">
		<span class="pe-1 text-secondary font-size-sm">Title</span>
		<input class="form-control" id="projectTitle" type="text" [(ngModel)]="projectTitle"
			(ngModelChange)="changeTitle($event)" />
	</div>
</div>
<div class="row">
	@for (prop of infoComponent?.Properties; track $index) {
	@if(!prop.IsHidden){
	<div class="col-12 col-sm-6 col-lg-4 pb-3 d-flex flex-column">
		<span class="pe-1 text-secondary font-size-sm">{{ prop.Name }}</span>
		@switch (prop.ValueType) {
		@case (INFO_VALUE_TYPES.String)
		{
		<input class="form-control" [id]="prop.PropertyId" type="text" [(ngModel)]="prop.Value" />
		}
		@case(INFO_VALUE_TYPES.Decimal){
		<input class="form-control" [id]="prop.PropertyId" type="number" class="form-control"
			[(ngModel)]="prop.Value" />
		}
		@case(INFO_VALUE_TYPES.Boolean){
		<div class="form-check">
			<input class="form-check-input" [id]="prop.PropertyId" type="checkbox" [(ngModel)]="prop.Value" />
			<label class="form-check-label" for="flexCheckDefault"></label>
		</div>
		}
		@case(INFO_VALUE_TYPES.Integer){
		<input class="form-control" [id]="prop.PropertyId" type="number" class="form-control"
			[(ngModel)]="prop.Value" />
		}
		@case(INFO_VALUE_TYPES.Date){
		<app-info-date-element [infoItem]="prop"></app-info-date-element>
		}
		@case(INFO_VALUE_TYPES.Currency){
		<input class="form-control" class="form-control" currencyMask [(ngModel)]="prop.Value" />
		}@default {

		{{prop.Value}}
		}
		}
	</div>
	}
	}@empty{
	<div class="alert alert-info" role="alert">
		No custom properties found.
	</div>
	}
</div>