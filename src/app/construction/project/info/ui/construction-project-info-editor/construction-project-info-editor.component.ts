import { Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionProject } from 'src/app/construction/shared/interfaces/construction-project';
import { InfoValueTypes, ProjectInfoComponent } from 'src/app/construction/shared/interfaces/info';
import { FormsModule } from '@angular/forms';
import { InfoDateElementComponent } from '../info-date-element/info-date-element.component';
import { CurrencyMaskModule } from 'ng2-currency-mask';


@Component({
    selector: 'app-construction-project-info-editor',
    imports: [CommonModule, FormsModule, InfoDateElementComponent, CurrencyMaskModule],
    templateUrl: './construction-project-info-editor.component.html',
    styleUrl: './construction-project-info-editor.component.css'
})
export class ConstructionProjectInfoEditorComponent {

  @Input({required: true}) projectInternalId: string = '';
  @Input({required: true}) projectTitle: string = '';
  @Input({required: true}) infoComponent: ProjectInfoComponent = {} as ProjectInfoComponent;
  @Output() projectInternalIdChange = new EventEmitter<string>();
  @Output() projectTitleChange = new EventEmitter<string>();
  public readonly INFO_VALUE_TYPES: typeof InfoValueTypes = InfoValueTypes;



  ngOnInit(): void {}

  changeInternalId(event: any){
    this.projectInternalIdChange.emit(this.projectInternalId);
  }

  changeTitle(event: any){
    this.projectTitleChange.emit(this.projectTitle);
  }
}
