@if(isLoading() || isUpdatingComponent()){
<div class="placeholder-glow">
	<h3 class="page-title fs-6">Project Info</h3>
	<div class="row">
		<div class="col-12 col-sm-6 col-lg-4 pb-3"
			*ngFor="let skel of  [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]; let i = index">
			<div class="row">
				<div class="col-12">
					<div class="placeholder col-5"></div>
				</div>
				<div class="col-12">
					<div class="placeholder col-2"></div>
				</div>
			</div>
		</div>
	</div>
</div>
}@else {
<div class="d-flex justify-content-between align-items-center">
	<h3 class="page-title fs-6 mb-0">Project Info</h3>
	<div class="btn-group" role="group" aria-label="Basic example">
		@if (infoPermissions()?.editInfoAccess === "Allow" && mode() === INFO_MODES.Edit) {
		<button class="btn btn-outline-dark" (click)="saveComponent()" placement="bottom" ngbTooltip="Save">
			Save
		</button>
		<button class="btn btn-outline-dark" (click)="changeMode(INFO_MODES.View)">
			Close
		</button>
		}@else if(mode() === INFO_MODES.View){
		@if(infoPermissions()?.editInfoAccess === "Allow"){
		<button class="btn btn-outline-dark" (click)="changeMode(INFO_MODES.Edit)">
			Edit
		</button>
		}
		}
	</div>
</div>
@switch (mode()) {
@case(INFO_MODES.Edit){
<app-construction-project-info-editor [infoComponent]="infoComponent()" [projectInternalId]="project()?.InternalId"
	[projectTitle]="project()?.Title" (projectTitleChange)="changeProjectTitle($event)"
	(projectInternalIdChange)="changeInternalId($event)"></app-construction-project-info-editor>
}
@case(INFO_MODES.View){
<app-construction-project-info-viewer [infoComponent]="infoComponent()"
	[projectInternalId]="project()?.InternalId"></app-construction-project-info-viewer>
}
@default {
<div>Mode {{ mode }} not supported</div>
}
}
}