<!--page-title-->
<header class="d-flex justify-content-between align-items-center mb-3">
	<h3 class="page-title fs-6 mb-0">Punch Lists</h3>
	<div class="btn-group" role="group" aria-label="Basic example"
		*ngIf="punchListPermissions()?.addPunchList === 'Allow'">
		<a class="btn btn-outline-dark" routerLink="add" placement="bottom" ngbTooltip="Save">New Punch List</a>
	</div>
</header>
<!--punch lists-->
<ul class="list-group mb-3">
	@if(isLoading()){
	<li class="list-group-item placeholder-glow" *ngFor="let item of [1,2,3]">
		<div>
			<span class="placeholder w-100"></span>
		</div>
		<div>
			<span class="placeholder w-100"></span>
		</div>
	</li>
	}@else{
	@for (punchlist of punchListComponentInfo()?.PunchList; track $index) {
	<li class="list-group-item">
		<a class="text-decoration-none" [routerLink]="[punchlist.PunchListId]">
			<div>{{punchlist.Description}}</div>
			<div class="text-muted">Created by {{ punchlist?.CreatedByName}}.</div>
		</a>
	</li>
	}@empty {
	<li class="list-group-item">
		You don't have any punch lists yet.
	</li>
	}
	}
</ul>