import { Component, Input, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PunchListComponentInfo, PunchListPermissions } from '../../shared/interfaces/punch-lists';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';

import { of, switchMap, tap } from 'rxjs';
import { ConstructionProjectService } from '../../shared/data-access/project.service';
import { ConstructionPunchListsService } from '../../shared/data-access/punch-lists.service';



@Component({
    selector: 'app-punch-lists',
    imports: [CommonModule, RouterLink],
    templateUrl: './punch-lists.component.html',
    styleUrl: './punch-lists.component.css'
})
export class PunchListsComponent {
  aRoute = inject(ActivatedRoute);
  punchListService = inject(ConstructionPunchListsService);
  isLoading = signal<boolean>(false);
  punchListPermissions = this.punchListService.punchListPermissions;
  punchListComponentInfo = signal<PunchListComponentInfo>({} as PunchListComponentInfo);
  @Input() projectId = "";
  projectIdInfo = signal<string>("");
  getPunchLists = toSignal(toObservable(this.projectIdInfo).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap(projectId => projectId ? this.punchListService.getOrCreatePunchListComponent(projectId) : of(null)),
    tap((punchListComponent) => {
      if(punchListComponent){       
        this.punchListComponentInfo.set(punchListComponent as PunchListComponentInfo);        
      }
      this.isLoading.set(false);
    })

  ));
  constructor() { 
    if(this.projectId){
      this.projectIdInfo.set(this.projectId);   
    }
    else{
      const params = this.aRoute.parent?.parent?.snapshot.paramMap;  
      if(params){
        var projectId = params.get("projectId");
        if(projectId){
          this.projectIdInfo.set(projectId);      
        }  
      }
    }

    
    this.punchListService.getPunchListPermissions().subscribe();
  }
}
