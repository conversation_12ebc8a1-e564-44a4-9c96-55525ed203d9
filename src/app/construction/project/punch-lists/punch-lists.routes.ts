import { Route } from '@angular/router';
import { PunchListsComponent } from './punch-lists.component';

export const PUNCH_LIST_ROUTES: Route[] = [
	{
		path: '',
		component: PunchListsComponent
	},
	{
		path: 'add',
		loadComponent: () => import('./punch-list-manager/punch-list-manager.component').then(c => c.PunchListManagerComponent),
		data: { mode: 'add'}
	},
	{
		path: ':punchListId',
		loadComponent: () => import('./ui/punch-list-view/punch-list-view.component').then(c => c.PunchListViewComponent),
		data: { mode: 'edit'}
	},
	{
		path: ':punchListId/edit',
		loadComponent: () => import('./punch-list-manager/punch-list-manager.component').then(c => c.PunchListManagerComponent),
		data: { mode: 'edit'}
	},
	{
		path: ':punchListId/task/edit/:taskId',
		loadComponent: () => import('./punch-list-task-manager/punch-list-task-manager.component').then(c => c.PunchListTaskManagerComponent),
		data: { mode: 'edit'}
	},
	{
		path: ':punchListId/task/add',
		loadComponent: () => import('./punch-list-task-manager/punch-list-task-manager.component').then(c => c.PunchListTaskManagerComponent),
		data: { mode: 'add'}
	}
];