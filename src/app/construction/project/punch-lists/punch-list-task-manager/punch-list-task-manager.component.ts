import { CommonModule } from "@angular/common";
import { Component, Input, OnInit, inject } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { ConstructionPunchListsService } from "src/app/construction/shared/data-access/punch-lists.service";
import { PunchListTask } from "src/app/construction/shared/interfaces/punch-lists";

import { v4 } from "uuid";


@Component({
    selector: 'app-punch-list-task-manager',
    imports: [CommonModule, FormsModule, ReactiveFormsModule],
    templateUrl: './punch-list-task-manager.component.html',
    styleUrls: ['./punch-list-task-manager.component.css']
})
export class PunchListTaskManagerComponent implements OnInit {
  @Input() projectId: string | null = null;
  @Input() punchListId: string | null = null;
  @Input() mode: string = "";
  @Input() punchListTask: PunchListTask | null = null;
  @Input() punchListTaskId: string | null = null;
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  toastrService = inject(ToastrService);
  punchListService = inject(ConstructionPunchListsService);
  formBuilder = inject(FormBuilder);
  isLoading: boolean = false;
  isSaving: boolean = false;
  pageTitle: string = "";

  taskFormGroup: FormGroup = new FormGroup({
    description: new FormControl('', [Validators.required])
  });

  constructor(
  ) {
    const { mode } = this.aRoute.snapshot.data;

    if (mode) {
      this.mode = mode;
    }

    let params = this.aRoute.parent?.parent?.snapshot.params;
    if (params) {
      const { id } = params;
      this.projectId = id;
    }

    let punchListParams = this.aRoute.snapshot.params;

    if (punchListParams) {
      const { punchListId, taskId } = punchListParams;
      this.punchListId = punchListId;
      this.punchListTaskId = taskId;
    }

  }

  ngOnInit(): void {
    if (this.mode === "edit") {
      this.pageTitle = "Edit Punch List Task";
      this.isLoading = true;
      if (this.projectId) {
        this.punchListService.getPunchLists(this.projectId).subscribe({
          next: (lists) => {
            var punchList = lists.filter(x => x.PunchListId == this.punchListId)[0];
            this.punchListTask = punchList.PunchListTasks.filter(x => x.PunchListTaskId === this.punchListTaskId)[0];
            this.isLoading = false;
          }, error: (err) => {
            this.toastrService.error("Go not find cost code to edit");
            this.isLoading = false;
          }
        });
      } else {
        this.isLoading = false;
      }
    } else if (this.mode === "add") {
      this.pageTitle = "Add Punch List Task";
      this.punchListTask = {} as PunchListTask;
    } else {
      this.pageTitle = "Punch List Task Unknown State"
    }
  }

  saveTask() {

    if (this.mode === "add") {
      if (this.projectId && this.punchListTask && this.punchListId) {
        this.isSaving = true;
        this.punchListTask.PunchListTaskId = v4();

        this.punchListService.addPunchListTask(this.projectId, this.punchListId, this.punchListTask).subscribe({
          next: (result) => {
            this.router.navigate(['punch-list', this.punchListId], { relativeTo: this.aRoute.parent?.parent });
          },
          error: (err) => {
            console.log(err);
          }
        });
      }




    } else if (this.mode === "edit") {
      if (this.projectId && this.punchListTask && this.punchListId) {
        this.isSaving = true;
        this.punchListService.editPunchListTask(this.projectId, this.punchListId, this.punchListTask).subscribe({
          next: (result) => {
            this.router.navigate(['punch-list', this.punchListId], { relativeTo: this.aRoute.parent?.parent });
            this.isSaving = false;
          },
          error: (err) => {
            console.log(err);
            this.isSaving = false;
          }
        });
      }
    }
  }
}