<h3 class="page-title fs-6 mb-0">{{ pageTitle }}</h3>
@if(punchListTask){
<div class="placeholder-glow">
	<span class="placeholder col-12" style="height:50px"></span>
</div>
}@else{
<form #taskItemForm="ngForm" (ngSubmit)="saveTask()">
	<label>Description</label>
	<input type="text" class="form-control" name="description" formControlName="description" autocomplete="off"
		[disabled]="isSaving" required />

	<button class="btn btn-success mt-2" type="submit" [disabled]="!taskItemForm.form.valid || isSaving">
		@if(isSaving){
		<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
		}

		@switch(mode){
		@case('add'){
		Add
		}
		@case('edit'){
		Update
		}
		@default{
		Unknown
		}
		}
	</button>
</form>
}