import { CommonModule } from "@angular/common";
import { Component, OnInit, OnDestroy, Input, inject } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from "ngx-toastr";
import { fork<PERSON>oin } from "rxjs";
import { ConstructionPunchListsService } from "src/app/construction/shared/data-access/punch-lists.service";
import { PunchList, PunchListPermissions, PunchListTask } from "src/app/construction/shared/interfaces/punch-lists";
import { ConfirmService } from "src/app/shared/data-access/confirm.service";

@Component({
    selector: 'app-punch-list-view',
    templateUrl: './punch-list-view.component.html',
    imports: [CommonModule],
    styleUrls: ['./punch-list-view.component.css']
})
export class PunchListViewComponent implements OnInit {  
  @Input() punchListItem: PunchList = {} as PunchList;
  @Input() projectId: string = '';
  @Input() punchListId: string = '';
  isLoading: boolean = false;
  private punchListService = inject(ConstructionPunchListsService);
  private toastrService = inject(ToastrService);
  private router = inject(Router);
  private aRoute = inject(ActivatedRoute);
  private confirmService = inject(ConfirmService);
  punchListPermissions = this.punchListService.punchListPermissions;
  
  ngOnInit(): void {    
    this.punchListService.getPunchListPermissions().subscribe();

    let projectIdInfo = '';
    let punchListIdInfo = '';
    let projectIdParams = this.aRoute.parent?.parent?.snapshot.params;
    let punchListParams = this.aRoute.snapshot.params;

    if (projectIdParams) {
      const { projectId } = projectIdParams;
      projectIdInfo = projectId;     
    }   

    if (punchListParams) {
      const { punchListId } = punchListParams;
      punchListIdInfo = punchListId;     
    }

    if (punchListIdInfo && projectIdInfo) {
      this.projectId = projectIdInfo;
      this.isLoading = true;
      this.punchListService.getOrCreatePunchListComponent(this.projectId).subscribe({
        next: (result) => {
          this.punchListItem = result.PunchList.find(x => x.PunchListId == punchListIdInfo) as PunchList;          
          this.isLoading = false;
        },
        error: (err) => {
          this.isLoading = false;
        }
      });
    }

  }

  editPunchList() {
    this.router.navigate(['edit'], { relativeTo: this.aRoute });
  }

  addTask() {
    this.router.navigate(['task', 'add'], { relativeTo: this.aRoute });
  }

  editTask(task: PunchListTask) {
    this.router.navigate(['task', 'edit', task.PunchListTaskId], { relativeTo: this.aRoute });
  }

  deleteTask(task: PunchListTask) {
    this.confirmService.open("Are you so you wan to delete this task?").result.then(value => {   
  
      if(this.projectId && this.punchListItem){
        this.punchListService.deletePunchListTask(this.projectId, this.punchListItem.PunchListId, task.PunchListTaskId).subscribe({
          next: (result) => {
            if(this.punchListItem)
            {
              var idx = this.punchListItem.PunchListTasks.indexOf(task);
              this.punchListItem.PunchListTasks.splice(idx, 1);
              this.toastrService.success(`Task ${task.Description} has been removed`);
            }
          },
          error: (err) => {
            console.log(err);
          }
        });
      }
    });
  }

  markTask(task: PunchListTask) {
    
    var completed = (task.DateCompleted) ? false : true;

    if(this.projectId && this.punchListItem){
      this.punchListService.markTask(this.projectId, this.punchListItem.PunchListId, task.PunchListTaskId, completed).subscribe({
        next: (nTask) => {
          if(this.punchListItem){
            var idx = this.punchListItem.PunchListTasks.indexOf(this.punchListItem.PunchListTasks.filter(x => x.PunchListTaskId === task.PunchListTaskId)[0]);
            this.punchListItem.PunchListTasks.splice(idx, 1, nTask);        
        
            if(nTask.DateCompleted){
              this.toastrService.success(`Task ${task.Description} has been completed`)
            }else{
              this.toastrService.success(`Task ${task.Description} has been removed from completed`)
            }
          }
        },
        error: (err) => {
          console.log(err);
        }
      });
    }
  }
}
