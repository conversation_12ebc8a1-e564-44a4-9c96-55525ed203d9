<!-- <cm-loading [show]="isLoading"></cm-loading> -->
<header class="d-flex justify-content-between align-items-center mb-3">
	<h3 class="page-title fs-6 mb-0">{{ punchListItem?.Description }}</h3>
	<div class="btn-group" role="group" aria-label="Basic example"
		*ngIf="punchListPermissions()?.updatePunchlist === 'Allow'">
		<button class="btn btn-outline-dark" (click)="editPunchList()" placement="bottom"
			ngbTooltip="Save">Edit</button>
	</div>
</header>
<ul class="list-group my-2">
	@if(isLoading){
	<li class="list-group-item placeholder-glow" *ngFor="let item of [1,2,3]">
		<div>
			<span class="placeholder w-100"></span>
		</div>
	</li>
	}@else {
	@for (task of punchListItem?.PunchListTasks; track $index) {
	<li class="list-group-item">
		<div class="row align-items-center">
			<div class="col-12 col-lg-9 mb-1 mb-lg-0 d-flex align-items-center order-2 order-lg-1">
				<!-- <i class="fas fa-circle-notch fa-spin fa-1x me-3"></i>  -->
				@if(punchListPermissions()?.markTask === 'Allow'){
				<div class="form-check">
					<input class="form-check-input me-3" type="checkbox" [checked]="task.DateCompleted"
						(change)="markTask(task)" />
				</div>
				}
				<div>
					@if(task.DateCompleted){
					<s>{{task.Description}}</s>
				<!-- 	<div class="d-lg-none text-secondary small">{{task.DateCompleted | date: 'M/dd/yyyy h:mm:ss a'}}
					</div> -->
					}@else {
					{{task.Description}}
					}
				</div>
			</div>
			<div class="col-12 col-lg-3 text-muted small text-lg-end order-1 order-lg-2">
				{{task.DateCompleted | date: 'M/dd/yyyy H:mm:ss a'}}
			</div>
		</div>
	</li>
	}@empty {
	<li class="list-group-item" *ngIf="punchListItem?.PunchListTasks?.length">
		No tasks yet.
	</li>
	}
	}
</ul>

<!-- 	<div class="d-flex justify-content-end mb-1">
		<button class="btn btn-danger" (click)="deletePunchlist()" placement="bottom" ngbTooltip="Save" [disabled]="isDeleting">
			<i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isDeleting"></i> 
			Delete Punchlist
		</button>	
	</div> -->