import { Component, Input, OnInit, ViewChild, inject, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { v4 } from 'uuid';
import { FormsModule, NgForm } from '@angular/forms';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { PunchList, PunchListTask } from 'src/app/construction/shared/interfaces/punch-lists';
import { CommonModule } from '@angular/common';
import { ConstructionPunchListsService } from 'src/app/construction/shared/data-access/punch-lists.service';

@Component({
    selector: 'app-punch-list-manager',
    imports: [CommonModule, FormsModule],
    templateUrl: './punch-list-manager.component.html',
    styleUrls: ['./punch-list-manager.component.css']
})
export class PunchListManagerComponent implements OnInit {

  @ViewChild("addPunchListForm") addPunchListForm: NgForm = {} as NgForm;
  @Input() projectId: string = "";
  accessService = inject(AccessService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  punchListService = inject(ConstructionPunchListsService);
  confirmService = inject(ConfirmService);
  toastrService = inject(ToastrService);
  punchListPermissions = this.punchListService.punchListPermissions;
  punchListItem = signal<PunchList>({} as PunchList);
  isLoading = signal<boolean>(false);
  isSaving = signal<boolean>(false);
  isDeleting = signal<boolean>(false);
  punchListId: string = "";
  mode: string = "";
  pageTitle: string = "";

  constructor() {
    this.mode = this.aRoute.snapshot.data["mode"];
    this.projectId = this.aRoute.snapshot.parent?.parent?.params["projectId"];
    this.punchListId = this.aRoute.snapshot.params["punchListId"];
  }

  ngOnInit(): void {
    this.punchListService.getPunchListPermissions().subscribe();

    if (this.mode === "edit") {
      this.pageTitle = "Edit Punch List";

      if (this.projectId && this.punchListId) {
        this.isLoading.set(true);
        this.punchListService.getPunchLists(this.projectId).subscribe({
          next: (lists) => {
            this.punchListItem.set(lists.find(x => x.PunchListId == this.punchListId) as PunchList);
            this.isLoading.set(false);
          }, 
          error: (err) => {
            this.toastrService.error("Go not find cost code to edit");
            this.isLoading.set(false);
          }
        });
      }     
    } else if (this.mode === "add") {
      this.pageTitle = "New Punch List";

      var task: PunchListTask = {
        CompletedByUserId: null,
        DateCompleted: null,
        Description: null,
        PunchListTaskId: v4()
      }

      this.punchListItem.update((item) => {
        item.PunchListTasks = [task];
        return item;
      });
    } else {
      this.pageTitle = "Punch List Unknown State"
    }
  }

  addNewTask() {
    let punchListItem = this.punchListItem() as PunchList;

    if (punchListItem && punchListItem.PunchListTasks == null) {
      punchListItem.PunchListTasks = [];
    }

    if (punchListItem) {
      var task: PunchListTask = {
        CompletedByUserId: null,
        DateCompleted: null,
        Description: null,
        PunchListTaskId: v4()
      }

      punchListItem.PunchListTasks.push(task);
      this.focusInput(task.PunchListTaskId);
    }
  }

  focusInput(id: any) {
    setTimeout(() => {
      var element = document.getElementById(id) as HTMLInputElement;
      element.focus();
      element.select();
    }, 0);

  }

  savePunchList() {
    if (this.addPunchListForm) {
      if (!this.addPunchListForm.valid) {
        this.toastrService.error("Form is not valid");
        return;
      }

      let punchListItem = this.punchListItem() as PunchList;

      this.isSaving.set(true);
      this.removeEmptyValueOnEnd();

      if (this.projectId && punchListItem) {
        if (this.mode === "add") {
          this.punchListService.addPunchListItem(this.projectId, punchListItem).subscribe({
            next: (nPunchList) => {
              this.toastrService.success("Punch List Added");
              this.router.navigate([nPunchList.PunchListId], { relativeTo: this.aRoute.parent });
              this.isSaving.set(false);
            },
            error: (err: any) => {
              this.toastrService.error(err.message);
              this.isSaving.set(false);
            }
          });
        } else if (this.mode === "edit") {
          this.punchListService.updatePunchList(this.projectId, punchListItem).subscribe({
            next: (result: string) => {
              this.toastrService.success("Punch List Updated");
              this.isSaving.set(false);
              this.router.navigate([punchListItem.PunchListId], { relativeTo: this.aRoute.parent });
            },
            error: (err) => {
              this.toastrService.error(err.message);
              this.isSaving.set(false);
            }
          });     
        }
      } else {
        this.isSaving.set(false);
      }
    }

  }

  removeEmptyValueOnEnd() {
    let punchListItem = this.punchListItem() as PunchList;

    var idx = punchListItem.PunchListTasks.length - 1;
    var lastItem = punchListItem.PunchListTasks[idx];
    if (lastItem) {
      if (!lastItem.Description) {
        punchListItem.PunchListTasks.splice(idx, 1);
      }
    }


  }

  deletePunchlist() {
    this.confirmService.open("Are you so you wan to delete this punch list?").result.then(async value => {
      let punchListItem = this.punchListItem() as PunchList;
      if (this.projectId && punchListItem) {
        this.isDeleting.set(true);
        this.punchListService.deletePunchList(this.projectId, punchListItem.PunchListId).subscribe(result => {
          this.toastrService.success(`Punch List `)
          this.isDeleting.set(false);
          this.router.navigate(['punch-list'], { relativeTo: this.aRoute.parent?.parent });
        }, err => {
          this.isDeleting.set(false);
        });
      }
    });
  }

  removeTask(task: PunchListTask) {
    let punchListItem = this.punchListItem() as PunchList;
    var idx = punchListItem.PunchListTasks.indexOf(task);
    punchListItem.PunchListTasks.splice(idx, 1);
  }
}