<!-- place holder -->
@if(isLoading()){
<div class="placeholder-glow">
	<div class="mb-3" *ngFor="let item of [1,2,3,4, 5]">
		<span class="placeholder col-12" style="height:60px;"></span>
	</div>
</div>
}@else {
<h3 class="page-title fs-6">{{ pageTitle }}</h3>
<!-- punch list -->
<form #addPunchListForm="ngForm" (ngSubmit)="savePunchList()">
	<!-- name punch list -->
	<div class="mb-3">
		<label for="example-text-input" class="form-label">Punch List Name</label>
		<input class="form-control" type="text" name="ccPhase" placeholder="Punch List Name" autofocus
			[(ngModel)]="punchListItem().Description" required />
	</div>
	<!-- add tasks -->
	<!-- <div class="mb-1">Add tasks to your punch list. You can always do this later if you want.</div> -->
	<div class="card">
		<div class="card-body">
			<header class="d-flex justify-content-between align-items-center mb-3">
				<h4 class="page-title fs-6 mb-0">{{punchListItem()?.Description}}</h4>
				<button type="button" class="btn btn-outline-dark" (click)="addNewTask()">
					Add Task
				</button>
			</header>
			<ul class="list-group mb-3">
				@for (task of punchListItem().PunchListTasks; track $index) {
				<li class="list-group-item">
					<div class="row">
						<div class="col-8 col-md-11">
							<input id="{{task.PunchListTaskId}}" class="form-control" type="text"
								name="taskDescription{{$index}}" (keydown.enter)="addNewTask()"
								placeholder="Type a task here." [(ngModel)]="task.Description"
								[required]="$index < punchListItem().PunchListTasks.length-1" />
						</div>
						<div class="col-4 col-md-1 text-end">
							<button type="button" class="btn btn-outline-danger" (click)="removeTask(task)">
								Delete
							</button>
						</div>
					</div>
				</li>
				}@empty {
				<li class="list-group-item">
					No tasks yet.
				</li>
				}
			</ul>
			<div class="d-flex justify-content-end mt-2">
				@if(mode==='edit' && punchListPermissions()?.deletePunchList === 'Allow'){
				<button class="btn btn-outline-danger me-1" (click)="deletePunchlist()" placement="bottom"
					ngbTooltip="Delete" [disabled]="isDeleting()" type="button">
					@if(isDeleting()){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isDeleting"></i>
					}
					Delete Punch List
				</button>
				}

				@if(punchListPermissions()?.updatePunchlist === 'Allow' || punchListPermissions()?.addPunchList ===
				'Allow'){
				<!-- create punch list -->
				<button class="btn btn-primary" type="button" (click)="savePunchList()"
					[disabled]="isSaving() || addPunchListForm.invalid">
					<!-- [disabled]="addPunchListForm.invalid || isSaving" -->
					@if(isSaving()){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}

					@switch (mode) {
					@case('add'){
					Create
					}
					@case('edit'){
					Save
					}
					@default{
					Unknown
					}
					}
				</button>
				}

			</div>
		</div>
	</div>
</form>
}


<!-- add place holder -->