import { Component, EventEmitter, Input, Output, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CostCode } from 'src/app/construction/shared/interfaces/cost-codes';
import { ConstructionCostCodesService } from 'src/app/construction/shared/data-access/cost-codes.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';

@Component({
    selector: 'app-cost-codes-list',
    imports: [CommonModule, RouterLink, FormsModule],
    templateUrl: './cost-codes-list.component.html',
    styleUrl: './cost-codes-list.component.css'
})
export class CostCodesListComponent {
  @Input() costCodes: Array<CostCode> = [];
  @Input() totalHours: number = 0  
  @Output() deleteCostCode = new EventEmitter<string>();
  access: string = "Allow";
  searchText: string = "";

  private searchText$ = new Subject<string>();

  costCodeComponentService = inject(ConstructionCostCodesService);
  
  
  constructor(){


  }

  deleteCostCodeEvent(costCode: CostCode){
    this.deleteCostCode.emit(costCode.CostCodeId);
  }


}
