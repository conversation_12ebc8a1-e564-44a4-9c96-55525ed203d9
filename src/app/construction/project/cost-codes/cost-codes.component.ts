import { Component, OnInit, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ConstructionCostCodesService } from '../../shared/data-access/cost-codes.service';
import { CostCodesListComponent } from './ui/cost-codes-list/cost-codes-list.component';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { debounceTime, distinctUntilChanged, map, of, switchMap, tap } from 'rxjs';
import { CostCode, CostCodeInfoComponent } from '../../shared/interfaces/cost-codes';
import { environment } from 'src/environments/environment';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'app-cost-codes',
    imports: [CommonModule, CostCodesListComponent, RouterLink],
    templateUrl: './cost-codes.component.html',
    styleUrl: './cost-codes.component.css'
})
export class CostCodesComponent implements OnInit {
    
  costCodeComponentService = inject(ConstructionCostCodesService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);  
  toaster = inject(ToastrService);
  projectId = signal<string>('');
  isLoading = signal<boolean>(false);
  costCodeComponent = signal<CostCodeInfoComponent>({} as CostCodeInfoComponent);
  templateUrl: string =  environment.excel_template_locations.cost_code;
  searchText = signal<string>('');
  access = signal<string>("Allow");

  private readOnlySearchText = toSignal(toObservable(this.searchText).pipe(
    distinctUntilChanged(),
      debounceTime(500),
      tap((searchInfo) => {
        let s = null;
        if(searchInfo){
          s = searchInfo;
        }
  
        this.router.navigate(
          [], { queryParams: { search: s}, queryParamsHandling: 'merge' }); 
      })
  ));
  
  totalHours = computed<number>(() => {  
      return this.costCodeComponent()?.CostCodes?.reduce((previousValue, currentValue) => {
        return previousValue += (currentValue.Hours) ? currentValue.Hours : 0;
      }, 0);    
  });  

  private filteredStore: Array<CostCode> = []  
  private readonlyCostCodeComponent = toSignal<CostCodeInfoComponent>(toObservable(this.projectId).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap(projectId => this.costCodeComponentService.getCostCodeComponent(projectId)),
    tap(costcodeinfo => {
      if(costcodeinfo){
        this.costCodeComponent.set(costcodeinfo);
        if(costcodeinfo.CostCodes){
          this.filteredStore = [...costcodeinfo.CostCodes];
        }else{
          this.filteredStore = [];
        }
      }
   
      this.isLoading.set(false);
    })
    )
  );

  constructor(){
    const params = this.aRoute.parent?.parent?.snapshot.paramMap;  
    if(params){
      var projectId = params.get("projectId");
      if(projectId){
        this.projectId.set(projectId);      
      }  
    }

    this.aRoute.queryParamMap.pipe(
      map(params => {
      const search = params.get("search");
   
        if(search){            
          let costCodesFiltered = this.filteredStore.filter(x => x.Description.toLowerCase().indexOf(search.toLowerCase()) >= 0 || x.Phase.toLowerCase().indexOf(search.toLowerCase()) >= 0);
     
            this.costCodeComponent.update((component) => {
                component.CostCodes = [...costCodesFiltered];
                return component;
            });
        }else{
          this.costCodeComponent.update((component) => {
              component.CostCodes = [...this.filteredStore];
              return component;
          });
        }          
      }      
    )).subscribe();
  }

  ngOnInit(): void {


  }

  search(value: string){
    this.searchText.set(value);
  }

  deleteCostCode(costCodeId: string) {
    this.costCodeComponent.update((component) => {
      let costCode = component.CostCodes.find(x => x.CostCodeId === costCodeId);

      if(costCode){
        costCode.IsLoading = true;
      }
      return {...component};
    });    

    this.costCodeComponentService.deleteCostCode(this.projectId(), costCodeId).subscribe({
      next: (result) => {

        this.costCodeComponent.update((component) => {
          component.CostCodes = component.CostCodes.filter(x => x.CostCodeId != costCodeId);
          return {...component};
        });          
        
      },
      error: (err) => {
        console.log(err);
        this.toaster.error("Error deleting cost code");
        this.costCodeComponent.update((component) => {
          let costCode = component.CostCodes.find(x => x.CostCodeId === costCodeId);
    
          if(costCode){
            costCode.IsLoading = false;
          }
          return {...component};
        });    
      }    
    });
  }
}
