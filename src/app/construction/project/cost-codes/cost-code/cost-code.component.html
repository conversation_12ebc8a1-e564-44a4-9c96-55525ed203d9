@if(isLoading){
<div class="placeholder-glow" *ngIf="isLoading">
	<div class="mb-3" *ngFor="let item of [1,2,3,4, 5]">
		<span class="placeholder col-12" style="height:60px;"></span>
	</div>
</div>
}@else {
<h3 class="page-title fs-6">{{ pageTitle }}</h3>
<form [formGroup]="costCodeFormGroup" (ngSubmit)="saveCostCode()">
	<div class="row mb-3">
		<div class="col-12 col-md-2 mb-3 mb-md-0">
			<label for="ccPhase" class="form-label">Code</label>
			<input class="form-control" type="text" id="ccPhase" name="ccPhase" formControlName="Phase" />
		</div>
		<div class="col-12 col-md-4 mb-3 mb-md-0">
			<label for="ccDescription" class="form-label">Description</label>
			<input class="form-control" type="text" id="ccDescription" name="ccDescription"
				formControlName="Description" />
		</div>
		<div class="col-12 col-md-2 mb-3 mb-md-0">
			<label for="ccHours" class="form-label">Hours</label>
			<input class="form-control" type="number" id="ccHours" name="ccHours" formControlName="Hours" />
		</div>
		<div class="col-12 col-md-2 mb-3 mb-md-0">
			<label for="ccUnits" class="form-label">Units</label>
			<input class="form-control" type="number" id="ccUnits" name="ccUnits" formControlName="Units" />
		</div>
		<div class="col-12 col-md-2 mb-3 mb-md-0">
			<label for="ccUM" class="form-label">UM</label>
			<input class="form-control" type="text" name="ccUM" id="ccUM" formControlName="UnitOfMeasure" />
		</div>
	</div>
	<div class="d-flex justify-content-end">
		<button class="btn btn-primary" type="submit" [disabled]="!costCodeFormGroup.valid || isSaving">
			@if(isSaving){
			<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}
			<span>{{pageTitle}}</span>
		</button>
	</div>
</form>
}