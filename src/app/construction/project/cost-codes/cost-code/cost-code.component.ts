import { Component, Input, OnInit, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, RequiredValidator, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ConstructionCostCodesService } from 'src/app/construction/shared/data-access/cost-codes.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { switchMap, tap } from 'rxjs';
import { CostCode } from 'src/app/construction/shared/interfaces/cost-codes';

@Component({
    selector: 'app-cost-code',
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './cost-code.component.html',
    styleUrl: './cost-code.component.css'
})
export class CostCodeComponent implements OnInit {
  // costCodeInfo = signal<costCodeInfo>({} as costCodeInfo);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  mode: string = 'none';
  isLoading: boolean = false;
  isSaving: boolean = false;
  projectId: string | null | undefined = null;
  costCodeId: string | null = null;
  costCodeComponentService = inject(ConstructionCostCodesService);
  costCode = signal<CostCode>({} as CostCode);
  pageTitle: string = ""; 

  // private costCode$ = toObservable(this.costCodeInfo).pipe(
  //   switchMap(costCodeInfo => this.costCodeComponentService.getCostCode(costCodeInfo.projectId, costCodeInfo.costCodeId)),
  //   tap(costCode => this.costCode.set(costCode))
  // );
  // readonlyCostCode = toSignal(this.costCode$, {initialValue: {} as CostCode});

  costCodeFormGroup: FormGroup = new FormGroup({
    Phase: new FormControl('', [Validators.required]),
    Description: new FormControl(''),
    Hours: new FormControl(null),
    Units: new FormControl(null),
    UnitOfMeasure: new FormControl('')
  });

  constructor() {
    var snap = this.aRoute.snapshot;
    const { mode } = snap.data;
    this.mode = mode;

    if(this.mode === "edit"){
      this.pageTitle = "Edit Cost Code";
    }else if(this.mode === "add"){
      this.pageTitle = "Add Cost Code";
    }else{
      this.pageTitle = "Cost Code";
    }
    this.costCodeId = snap.paramMap.get("costcodeId");
    this.projectId = snap.parent?.parent?.paramMap.get("projectId");
  }

  ngOnInit(): void {

    if (this.costCodeId && this.projectId) {
      this.isLoading = true;
      this.costCodeComponentService.getCostCode(this.projectId, this.costCodeId).subscribe({
        next: (costCode) => {
          this.costCodeFormGroup.patchValue({
            Phase: costCode.Phase,
            Description: costCode.Description,
            Hours: costCode.Hours,
            Units: costCode.Units,
            UnitOfMeasure: costCode.UnitOfMeasure
          });

          this.isLoading = false;
        },
        error: (err) => {
          this.isLoading = false;
        }
      });
    }

  }

  saveCostCode() {
    console.log(this.costCodeFormGroup.value);
    this.isSaving = true;

    let cc = this.costCodeFormGroup.value as CostCode;

    if (this.mode === "edit") {
      if (this.projectId && this.costCodeId) {
        
        cc.CostCodeId = this.costCodeId;
        
        this.costCodeComponentService.updateCostCode(this.projectId, this.costCodeId, cc).subscribe({
          next: (result) => {
            this.isSaving = false; 
            this.router.navigate(['cost-codes'], { relativeTo: this.aRoute.parent?.parent });
          },
          error: (err) => {
            this.isSaving = false;
          }
        });
      }else{
        this.isSaving = false;
      }
    } else if (this.mode === "add") {
      if(this.projectId){
        this.costCodeComponentService.addCostCode(this.projectId, cc).subscribe({
          next: (result) => {
            this.isSaving = false;
            this.router.navigate(['cost-codes'], { relativeTo: this.aRoute.parent?.parent });
          },
          error: (err) => {
            this.isSaving = false;
          }
        });
      }
  
    }else{
      this.isSaving = false;
    }

  }

}

type costCodeInfo = { projectId: string, costCodeId: string };