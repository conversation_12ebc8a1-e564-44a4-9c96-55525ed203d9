import { Route } from '@angular/router';
import { CostCodesComponent } from './cost-codes.component';

export const PROJECT_COSTCODES_ROUTES: Route[] = [
	{
		path: '',
		component: CostCodesComponent
	},
	{
		path: 'edit/:costcodeId',
		loadComponent: () => import('./cost-code/cost-code.component').then(c => c.CostCodeComponent),
		data: {mode: 'edit'}
	},
	{
		path: 'add',
		loadComponent: () => import('./cost-code/cost-code.component').then(c => c.CostCodeComponent),
		data: {mode: 'add'}
	},
	{
		path: 'import',
		loadComponent: () => import('./cost-code-import/cost-code-import.component').then(c => c.CostCodeImportComponent),		
	}
];