<h3 class="page-title fs-6">Import Cost Codes</h3>
<div [ngSwitch]="costCodeWizard.CurrentStep">
	<div *ngSwitchCase="0">
		<div class="mb-3">
			<p>Choose a file to import cost codes. Make sure it follows our Excel template format.</p>
			<div class="mb-3">
				<civcast-aws-uploader [maxFiles]="1" (uploadError)="errorInfo($event)"
					(selectedFiles)="setSelectedFiles($event)" [allowDragDrop]="false" [allowFolderFiles]="false"
					[accept-info]="''" *ngIf="!isLoading"></civcast-aws-uploader>
			</div>
			<div class="progress" *ngIf="isLoading">
				<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
					[ngStyle]="{ 'width': '100%' }"></div>
				<!-- <div class="progress-bar" role="progressbar" [ngStyle]="{ 'width': uploadTotal + '%' }"></div> -->
			</div>
			<label class="alert alert-danger" *ngIf="isError">
				{{ error }}
			</label>
		</div>
	</div>
</div>