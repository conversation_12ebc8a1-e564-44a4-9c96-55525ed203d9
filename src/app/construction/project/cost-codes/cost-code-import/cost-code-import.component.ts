import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { environment } from 'src/environments/environment';
import { newGuid } from 'src/app/models/utilities';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { CommonModule } from '@angular/common';
import { CostCodeEnum, CostCodeInfoComponent, CostCodeWizard } from 'src/app/construction/shared/interfaces/cost-codes';
import { ConstructionCostCodesService } from 'src/app/construction/shared/data-access/cost-codes.service';
import { CivCastAWSUploader } from 'src/app/shared/ui/uploader/civcast.aws.uploader.component';

@Component({
    selector: 'app-cost-code-import',
    templateUrl: './cost-code-import.component.html',
    imports: [CommonModule, CivCastAWSUploader],
    styleUrls: ['./cost-code-import.component.css']
})
export class CostCodeImportComponent implements OnInit {
  closeResult: string | null = null;
  costCodesSection: CostCodeInfoComponent | null = null;
  error: string | null = null;
  isError = false;
  public letterArray: string[] = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
  costCodeWizard: CostCodeWizard;
  uploadTotal: number = 0;
  isLoading: boolean = false;

  @Input() projectId: string | null = null;
  constructor(
    private authService: AuthService,
    private costCodeService: ConstructionCostCodesService,
    private toastrService: ToastrService,
    private aRoute: ActivatedRoute,
    private lambdaAWSService: LambdaAWSService,
    private router: Router) {
    this.costCodeWizard = new CostCodeWizard();
    this.costCodeWizard.CurrentStep = CostCodeEnum.Import;

    if (!this.projectId && this.aRoute.snapshot.parent?.parent?.params) {
      this.projectId = this.aRoute.snapshot.parent?.parent?.params['projectId'];
    }
  }
  ngOnInit() {

  }

  setSelectedFiles(files: FileList) {
    let file = files[0];
    this.uploadCostCodeFile(file);
  }

  uploadCostCodeFile(file: File) {
    this.isLoading = true;

    var key = `cost-codes-imports/${newGuid()}`;

    this.lambdaAWSService.uploadFilePresignedUrl(key, file).subscribe({
      next: (result) => {
        this.costCodeService.importCostCode(encodeURIComponent(`${key}`), environment.CostCodes.Bucket).subscribe({
          next: (codes) => {
            if (codes.length > 0) {
              if (this.projectId) {
                this.costCodeService.addManyCostCode(this.projectId, codes).subscribe({
                  next: (result) => {
                    this.toastrService.success("Import successfull!");
                    this.router.navigate(['cost-codes'], { relativeTo: this.aRoute.parent?.parent });

                    this.isLoading = false;
                  },
                  error: (err) => {
                    this.isLoading = false;
                  }
                });
              }else{
                this.toastrService.error("No project id");
                this.isLoading = false;
              }
            } else {
              this.toastrService.warning("No cost codes to import");
              this.isLoading = false;
            }
          }
        });
      },
      error: (err) => {
        this.isLoading = false;
      }
    });
  }

  errorInfo(event: any) {
    console.log(event);
  }
}