import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { DashboardSidebarMenuComponent } from './ui/dashboard-sidebar-menu/dashboard-sidebar-menu.component';
import { ConstructionRecentProjectsService } from '../shared/data-access/recent-projects.service';
import { ConstructionProjectService } from '../shared/data-access/project.service';
import { ConstructionProject } from '../shared/interfaces/construction-project';
import { toSignal,toObservable } from '@angular/core/rxjs-interop';
import { switchMap, tap } from 'rxjs';
import { UserProjectComponentsService } from '../shared/data-access/user-project-components.service';

@Component({
    selector: 'app-project',
    imports: [CommonModule, RouterOutlet, DashboardSidebarMenuComponent],
    templateUrl: './project.component.html',
    styleUrl: './project.component.css'
})
export class ProjectComponent implements OnInit {

  recentProjectService = inject(ConstructionRecentProjectsService);
  projectService = inject(ConstructionProjectService);  
  userProjectComponentsService= inject(UserProjectComponentsService);
  aRoute = inject(ActivatedRoute);
  projectLoading = signal<boolean>(false);
  project = signal<ConstructionProject>({} as ConstructionProject);    
  projectId = signal<string>('');
  userProjectComponents = this.userProjectComponentsService.cachedUserAllComponents;  
  sideBarLoading = signal<boolean>(false);
  private readOnlyProject = toSignal(toObservable(this.projectId).pipe(
    tap(() => this.projectLoading.set(true)),
    switchMap(projectId => this.projectService.getProject(projectId)),
    tap(project => {  
      this.projectLoading.set(false);
      this.project.set(project);
      this.recentProjectService.addRecentProject(project);
    })
  ));
  
  private readOnlyProjectComponents = toSignal(toObservable(this.userProjectComponentsService.cachedUserAllComponents).pipe(
    tap(() => this.sideBarLoading.set(true)),
    switchMap(project => this.userProjectComponentsService.getUserComponentsAllCached()),
    tap(() => this.sideBarLoading.set(false))    
  ));
  
  constructor(){
   
    this.aRoute.paramMap.subscribe({
      next: (params) => {
        var projectId = params.get("projectId");
        if(projectId){
          this.projectId.set(projectId);          
        }
      }
    });

  }

  ngOnInit(): void {

  }
}
