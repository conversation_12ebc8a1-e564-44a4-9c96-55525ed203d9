import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { ConstructionNavBarComponent } from './ui/construction-nav-bar/construction-nav-bar.component';
import { ConstructionRecentProjectsService } from './shared/data-access/recent-projects.service';
import { filter } from 'rxjs/operators';


@Component({
    selector: 'app-construction',
    imports: [CommonModule, RouterOutlet],
    providers: [],
    templateUrl: './construction.component.html',
    styleUrl: './construction.component.css'
})
export class ConstructionComponent {

  recentProjectService = inject(ConstructionRecentProjectsService);
  showNavBar = true;
  private routerSub: any;

  constructor(private router: Router) {
    this.recentProjectService.load();
    // this.routerSub = this.router.events
    //   .pipe(filter(event => event instanceof NavigationEnd))
    //   .subscribe((event: NavigationEnd) => {
    //     const url = event.urlAfterRedirects;
    //     this.showNavBar = /^\/construction\/projects\/?$/.test(url);
    //   });
  }

  ngOnDestroy() {
    if (this.routerSub) {
      this.routerSub.unsubscribe();
    }
  }
  
}