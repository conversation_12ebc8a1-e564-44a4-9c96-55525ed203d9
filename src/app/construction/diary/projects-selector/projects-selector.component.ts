import { Component, OnInit, effect, inject, computed, AfterViewInit, signal, ViewChild } from '@angular/core';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { MultiSelect, MultiSelectModule } from 'primeng/multiselect';
import { DiaryProjectsSelectorService } from '../../shared/data-access/diary-projects-selector.service';
import { ConstructionProject } from '../../shared/interfaces/construction-project';
import { CommonModule } from '@angular/common';
import { ConstructionProjectsService } from '../../shared/data-access/projects.service';
import { DateSelectorComponent } from '../date-selector/date-selector.component';
import { toSignal } from '@angular/core/rxjs-interop';
import { ToastrService } from 'ngx-toastr';
import { DiaryService } from '../diary.service';
import { DailyLogDirtyService } from '../../project/daily-log/shared/services/daily-log-dirty.service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { ConfirmationMessages } from '../../shared/utils/confirmation-messages';
import { ProjectListDropdownService } from '../../shared/data-access/project-list-dropdown.service';
import { ProjectsListDropDownComponent } from '../../ui/projects-list-drop-down/projects-list-drop-down.component';

@Component({
  selector: 'app-projects-selector',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MultiSelectModule, DateSelectorComponent, ProjectsListDropDownComponent],
  templateUrl: './projects-selector.component.html',
  styleUrls: ['./projects-selector.component.css'] 
})
export class ProjectsSelectorComponent implements OnInit, AfterViewInit {
  private diaryProjectsSelectorService = inject(DiaryProjectsSelectorService);
  private projectListDropdownService  = inject(ProjectListDropdownService)  
  private toastrService = inject(ToastrService);
  private diaryService = inject(DiaryService);
  private dirtyService = inject(DailyLogDirtyService);
  private confirmService = inject(ConfirmService);

  @ViewChild('projectMultiSelect') projectMultiSelect: MultiSelect | undefined;
  
  diaryFormGroup = new FormGroup({
    selectedProjectControl: new FormControl<ConstructionProject[] | null>([])
  });

  private selectedProjectsSignal = toSignal(this.diaryFormGroup.controls.selectedProjectControl.valueChanges, {
    initialValue: this.diaryFormGroup.controls.selectedProjectControl.value
  });

  // Track the previous valid selection to revert if limit is exceeded
  private previousSelection = signal<ConstructionProject[]>([]);

  isLoading = this.projectListDropdownService.projectsSelectionLoading;
  projects = computed(() => {
    const projects = this.projectListDropdownService.selectedProjects() || [];
    return [...projects].sort((a, b) => a.Title.localeCompare(b.Title));
  });

  // Get mode from service
  isSingleProjectMode = this.diaryProjectsSelectorService.isSingleProjectModeSignal;
  singleProjectId = this.diaryProjectsSelectorService.singleProjectIdSignal;

  constructor() {
    // Initialize the form control to an empty array
    this.diaryFormGroup.controls.selectedProjectControl.setValue([], { emitEvent: false });    
    this.projectListDropdownService.selectedProjects.set([]);
    this.projectListDropdownService.selectionProjectType.set("default");
  
    // Create the signal without an initial value that might retain old state
    this.selectedProjectsSignal = toSignal(this.diaryFormGroup.controls.selectedProjectControl.valueChanges, {
      initialValue: []
    });
  
    // Sync form control to service
    effect(() => {
      const selectedProjects = this.selectedProjectsSignal();
      if (selectedProjects !== this.diaryProjectsSelectorService.selectedProjects()) {
        this.diaryProjectsSelectorService.setSelectedProjects(selectedProjects || []);
      }
    });
  
    // Sync service's selectedProjects with form only in single-project mode
    effect(() => {
      if (this.isSingleProjectMode()) {
        const serviceValue = this.diaryProjectsSelectorService.selectedProjects();
        const formValue = this.diaryFormGroup.controls.selectedProjectControl.value;
        if (serviceValue !== formValue) {
          this.diaryFormGroup.controls.selectedProjectControl.setValue(serviceValue, { emitEvent: false });
        }
      } else {
        // Ensure the form control is empty in multi-project mode
        if (this.diaryFormGroup.controls.selectedProjectControl.value?.length) {
          this.diaryFormGroup.controls.selectedProjectControl.setValue([], { emitEvent: false });
          this.diaryFormGroup.controls.selectedProjectControl.updateValueAndValidity();
        }
      }
    });
  
    // Reactively handle project pre-selection in single-project mode
    effect(() => {
      const projectsList = this.projects();
      if (this.isSingleProjectMode() && this.singleProjectId() && projectsList.length > 0) {
        const project = projectsList.find(p => p.Id === this.singleProjectId());
        if (project) {
          this.diaryFormGroup.controls.selectedProjectControl.setValue([project], { emitEvent: true });
          this.diaryProjectsSelectorService.setSelectedProjects([project]);
          this.previousSelection.set([project]);
          this.getDailyLogs(); // Auto-load logs
        } else {
          this.toastrService.error('Selected project not found');
        }
      }
    });
  }
  
  ngOnInit(): void {
    // Ensure the form control is in the correct state based on mode
    if (this.isSingleProjectMode()) {
      this.diaryFormGroup.controls.selectedProjectControl.disable();
    } else {
      this.diaryFormGroup.controls.selectedProjectControl.setValue([], { emitEvent: false });
      this.diaryFormGroup.controls.selectedProjectControl.enable();
      this.diaryFormGroup.controls.selectedProjectControl.updateValueAndValidity();
    }
  }
  
  ngAfterViewInit(): void {
  }

  getDailyLogs(): void {
    // Check if any log is dirty before loading new logs
    // Use checkGlobalDirtyState to check the dirtyMap directly
    const isDirty = this.checkGlobalDirtyState();
    
    if (isDirty) {
      // Show confirmation dialog
      this.confirmService.open(
        ConfirmationMessages.UNSAVED_CHANGES_RELOAD_PROJECTS,
        ConfirmationMessages.UNSAVED_CHANGES_RELOAD_PROJECTS_TITLE
      ).result.then(result => {
        if (result === 'yes') {
          // User confirmed, load the logs
          this.diaryProjectsSelectorService.loadLogs.set(true);
        }
      }).catch(() => {
        // Dialog dismissed, do nothing
      });
    } else {
      // No dirty state, load the logs directly
      this.diaryProjectsSelectorService.loadLogs.set(true);
    }
  }
  
  /**
   * Method to directly check if any project has dirty components
   * This is similar to DiaryComponent.checkGlobalDirtyState
   */
  private checkGlobalDirtyState(): boolean {
    // Get the dirtyMap from the dirtyService
    const dirtyMap = this.dirtyService.dirtyMap();
    
    // Check if any project has a dirty component
    let isDirty = false;
    for (const [, projectMap] of dirtyMap.entries()) {
      for (const [, isDirtyValue] of projectMap.entries()) {
        if (isDirtyValue) {
          isDirty = true;
          break;
        }
      }
      if (isDirty) break;
    }
    
    return isDirty;
  }

  /**
   * Checks if any log is currently persisting
   * Used to disable project controls during save operations
   */
  isAnyLogPersisting(): boolean {
    return this.diaryService.isAnyLogPersisting();
  }

  onProjectSelectionChange(event: any): void {
    // Only enforce limit in multi-project mode
    if (!this.isSingleProjectMode()) {
      const selectedProjects = event.value as ConstructionProject[];
      const maxProjects = 10;

      // If selection exceeds 10, revert to previous selection and show toast
      if (selectedProjects.length > maxProjects) {
        this.diaryFormGroup.controls.selectedProjectControl.setValue(this.previousSelection(), { emitEvent: false });
        this.diaryProjectsSelectorService.setSelectedProjects(this.previousSelection());
        this.toastrService.warning(`You cannot select more than ${maxProjects} projects.`, 'Selection Limit Reached');
        return;
      }
      
      // Find deselected projects
      const deselectedProjects = this.previousSelection().filter(
        current => !selectedProjects.some(newProj => newProj.Id === current.Id)
      );
      
      // Check for deselected projects and confirm if any have dirty logs
      this.diaryProjectsSelectorService.confirmProjectDeselection(
        this.previousSelection(),
        selectedProjects
      ).then(finalSelection => {
        // Check if the final selection is different from the selected projects
        const isSame = this.areSelectionsEqual(finalSelection, selectedProjects);
        
        if (!isSame) {
          // User canceled, revert to previous selection
          this.diaryFormGroup.controls.selectedProjectControl.setValue(finalSelection, { emitEvent: false });
        }
        
        // Update previous selection and service with the final selection
        this.previousSelection.set(finalSelection);
        this.diaryProjectsSelectorService.setSelectedProjects(finalSelection);
      });
    }
  }
  
  /**
   * Helper method to compare two arrays of ConstructionProject
   * @param selection1 First selection array
   * @param selection2 Second selection array
   * @returns True if the selections contain the same projects, false otherwise
   */
  private areSelectionsEqual(selection1: ConstructionProject[], selection2: ConstructionProject[]): boolean {
    if (selection1.length !== selection2.length) {
      return false;
    }
    
    // Check if every project in selection1 is in selection2
    return selection1.every(project1 => 
      selection2.some(project2 => project2.Id === project1.Id)
    );
  }

  /**
   * Determines the maximum number of selected labels to display before switching to "X projects selected"
   * Will return 0 if any of the currently selected projects have a long title
   * @returns The maximum number of labels to display before summarizing
   */
  getMaxSelectedLabels(): number {
    const selectedProjects = this.diaryFormGroup.controls.selectedProjectControl.value || [];
    
    // Check if any selected project title is longer than a threshold
    const longTitleThreshold = 15;
    const hasLongTitle = selectedProjects.some(project => project.Title.length > longTitleThreshold);
    
    // If any project has a long title, return 0 to always display the summary
    if (hasLongTitle) {
      return 0;
    }
    
    // Otherwise, use the default behavior (showing up to 3 items before summarizing)
    return 3;
  }
}
