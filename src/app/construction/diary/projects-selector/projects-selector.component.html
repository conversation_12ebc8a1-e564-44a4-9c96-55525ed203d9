<div class="col-12">
  <form [formGroup]="diaryFormGroup">
    <div class="d-flex flex-column flex-md-row align-items-center">
      <!-- Project Dropdown (hidden in single-project mode) -->
      @if (!isSingleProjectMode()) {
        <div class="col-12 col-md-6 d-flex align-items-center me-md-2 mb-2 mb-md-0">
          <div style="flex: 1 1 0; min-width: 0;">
            <app-projects-list-drop-down style="width: 100%; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;"
              formControlName="selectedProjectControl">
            </app-projects-list-drop-down>
          </div>
          <button type="button" class="btn btn-outline-dark ms-2" style="min-width: 120px;" (click)="getDailyLogs()"
            [disabled]="!(diaryFormGroup.get('selectedProjectControl')?.value?.length > 0) || isLoading() || isAnyLogPersisting()">
            Get Projects
          </button>
        </div>
      }
      <div class="col-12 col-md-6">
        <app-date-selector></app-date-selector>
      </div>
    </div>
  </form>
</div>
