<div class="container p-4 mb-4">
  <!-- header -->
  <h1 class="fs-5">Diary</h1>
  <!-- date pickers -->
  <section class="mb-3">
    @if (isSingleProjectMode()) {
    <app-date-selector></app-date-selector>
    }
    @else {
    <app-projects-selector></app-projects-selector>
    }
  </section>
  <!-- diaries -->
  <section class="mb-3">
    @if (isLoading() || isViewLoading()) {
    <div class="placeholder-glow">
      <div class="placeholder col-12" style="height:50px"></div>
    </div>
    } @else {
    @if (fetchedLogs()?.length > 0) {
    @for (dl of fetchedLogs(); track trackByProjectId($index, dl)) {
    <app-daily-log [projectId]="dl.ProjectId" [id]="'project-' + dl.ProjectId"></app-daily-log>
    @if ($index < fetchedLogs().length - 1) { <div>
      <div class="bg-primary my-3" style="height: 12px;">
      </div>
</div>
}
}
}
@if (fetchedLogs()?.length === 0 && !isLoading()) {
<div class="alert alert-info" role="alert">
  Select one or more projects.
</div>
}
}
</section>
<!-- total hours -->
@if (fetchedLogs()?.length > 1 && !isLoading()) {
<section class="d-flex justify-content-end mb-3">
  <h3 class="fs-6">Total Hours: {{ totalEmployeeHours() }}</h3>
</section>
}
<!-- save all/submit all buttons -->
@if (fetchedLogs()?.length > 1 && !isLoading()) {
<section class="d-flex justify-content-end mb-3">
  <div class="btn-group">
    <button class="btn btn-outline-success" (click)="saveAllDailyLogs()"
      [disabled]="isAnyLogPersisting() || totalDailyLogsNotSubmitted() === 0">
      Save All ({{ totalDailyLogsNotSubmitted() }})
    </button>
    <button class="btn btn-success" (click)="submitAllDailyLogs()"
      [disabled]="isAnyLogPersisting() || isSubmitting() || totalDailyLogsNotSubmitted() === 0">
      Submit All ({{ totalDailyLogsNotSubmitted() }})
    </button>
  </div>
</section>
}

<!-- footer -->
<!-- @if (fetchedLogs()?.length > 0 && !isLoading()) {
<footer class="card bg-light p-3 mb-3">
  <div class="row align-items-center">
    <div class="col-6 mb-2">
      @if (checkGlobalDirtyState()) {
      <span class="badge text-bg-warning">You have unsaved changes!</span>
      } @else {
      <span class="badge text-bg-success">Saved!</span>
      }
    </div>
    <div class="col-12 mb-1">
      @if (fetchedLogs()?.length > 1) {
      <app-project-navigator></app-project-navigator>
      }
    </div>
    <div class="col-12 col-md-6 order-2 order-md-1 mb-1">
      <i class="fa fa-chevron-left me-2"
        [ngClass]="{'cursor-pointer': !isAnyLogPersisting(), 'text-danger': checkGlobalDirtyState()}"
        style="cursor: pointer;" (click)="!isAnyLogPersisting() && navigateDay(-1)"></i>
      <span [ngClass]="{'text-danger': checkGlobalDirtyState()}">{{ getCurrentFormattedDate() }}</span>
      <i class="fa fa-chevron-right ms-2"
        [ngClass]="{'cursor-pointer': !isAnyLogPersisting(), 'text-danger': checkGlobalDirtyState()}"
        style="cursor: pointer;" (click)="!isAnyLogPersisting() && navigateDay(1)"></i>
    </div>
  </div>
</footer>
} -->
</div>