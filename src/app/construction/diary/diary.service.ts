import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DailyLogProjectUserComponent } from '../shared/interfaces/daily-log-project-user';
import { DiaryProjectsSelectorService } from '../shared/data-access/diary-projects-selector.service';
import { ConstructionProjectsService } from '../shared/data-access/projects.service';
import { ConstructionPhotosService } from '../shared/data-access/photos.service';
import { ComponentView } from '../shared/interfaces/project-components';
import { DailyLogComponentIdentifiers } from '../shared/interfaces/daily-log-shared';
import { ApprovalService } from '../shared/data-access/approval.service';
import { Approval, DailyLogApprovals } from '../shared/interfaces/approval';
import { DateSelectorService } from '../shared/data-access/date-selector.service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { DailyLogDirtyService } from '../project/daily-log/shared/services/daily-log-dirty.service';
import { ConfirmationMessages } from '../shared/utils/confirmation-messages';
import { ProjectListDropdownService } from '../shared/data-access/project-list-dropdown.service';

@Injectable({
    providedIn: 'root'
})
export class DiaryService {
    private projectsSelectorService = inject(DiaryProjectsSelectorService);
    private projectsService = inject(ConstructionProjectsService);
    // private photosService = inject(ConstructionPhotosService);
    private toastr = inject(ToastrService);
    private projectListDropdownService = inject(ProjectListDropdownService);
    private approvalService = inject(ApprovalService);
    private dateSelectorService = inject(DateSelectorService);
    private confirmService = inject(ConfirmService);
    private dirtyService = inject(DailyLogDirtyService);
    
    fetchedLogs = signal<DailyLogProjectUserComponent[]>([]);
    componentViews = signal<Map<string, ComponentView>>(new Map());
    isViewLoading = signal<boolean>(false);
    approvals = signal<DailyLogApprovals>({});
    selectedYear = signal<number>(this.projectsSelectorService.year());
    selectedMonth = signal<number>(this.projectsSelectorService.month());
    selectedDay = signal<number>(this.projectsSelectorService.day());
    isLoading = computed(() => this.projectsService.isLoading() || this.projectsSelectorService.selectedProjectsResource.isLoading());
    totalEmployeeHours = computed(() => this.calculateTotalHours());
    isAnyLogPersisting = computed(() => this.dirtyService.anyPersisting()());    
    teamProjects = computed(() => this.projectsService.projects());
    selectedProjects = this.projectsSelectorService.selectedProjects;

    constructor() {
        // Effect to update fetchedLogs based on selectedProjectsResource
        effect(() => {
            // Get the value directly from the resource
            const logs = this.projectsSelectorService.selectedProjectsResource.value();
            
            // Also check the currentLogsValue from the service as a fallback
            const currentLogsValue = this.projectsSelectorService.currentLogsValue;
            
            // Use logs if available, otherwise try currentLogsValue
            const logsToUse = logs || currentLogsValue;
            
            // Check if logs is an array and has items
            if (logsToUse && Array.isArray(logsToUse) && logsToUse.length > 0) {
                
                try {
                    const sortedLogs = [...logsToUse].sort((a, b) => {
                        const nameA = this.getProjectName(a.ProjectId).toLowerCase();
                        const nameB = this.getProjectName(b.ProjectId).toLowerCase();
                        return nameA.localeCompare(nameB);
                    });
                    this.fetchedLogs.set(sortedLogs);
                    this.updateViewStates(sortedLogs);
                } catch (error) {
                    console.error('DiaryService: Error sorting logs:', error);
                    this.fetchedLogs.set(logsToUse); // Set unsorted logs if sorting fails
                    this.updateViewStates(logsToUse);
                }
            } else {
                this.fetchedLogs.set([]);
            }
        });


        // Effect to handle errors from selectedProjectsResource
        effect(() => {
            const error = this.projectsSelectorService.selectedProjectsResource.error();
            if (error) {
                this.fetchedLogs.set([]);
                this.componentViews.set(new Map());
                this.isViewLoading.set(false);
                this.toastr.error('Error fetching daily logs');
            }
        });

        // Effect to sync date signals
        effect(() => {
            const newYear = this.projectsSelectorService.year();
            const newMonth = this.projectsSelectorService.month();
            const newDay = this.projectsSelectorService.day();
            
            // Check if date has changed
            const dateChanged = this.selectedYear() !== newYear || 
                               this.selectedMonth() !== newMonth || 
                               this.selectedDay() !== newDay;
            
            this.selectedYear.set(newYear);
            this.selectedMonth.set(newMonth);
            this.selectedDay.set(newDay);
            
            if (dateChanged) {
                this.fetchedLogs.set([]);
            }
        });
    }

    
    getDailyLogByProjectId(projectId: string): DailyLogProjectUserComponent | null {
        return this.fetchedLogs().find(log => log.ProjectId === projectId) || null;
    }

    //TODO: Not sure about this.
    getProjectName(projectId: string): string {
      const project = this.projectListDropdownService.projectsForSelection()?.find(p => p.Id === projectId);
        return `${project?.InternalId} - ${project?.Title || ''}`;
    }

    getComponentView(projectId: string): ComponentView {
        const view = this.componentViews().get(projectId) || ComponentView.Edit;
        return view;
    }

    handleLogDeleted(event: any, projectId: string): void {
        this.fetchedLogs.update(logs => logs.filter(log => log.ProjectId !== projectId));
        this.componentViews.update(views => {
            views.delete(projectId);
            return new Map(views);
        });
    }

    refreshTotalHours(): void {
        this.projectsSelectorService.loadLogs.set(true);
    }

    private calculateTotalHours(): number {
        return this.fetchedLogs().reduce((total, log) => total + this.calculateLogHours(log), 0);
    }

    private calculateLogHours(log: DailyLogProjectUserComponent): number {
        if (log.NoWork) return 0;
        
        let totalHours = 0;
        const timeCardComponent = log.Components.find(
            c => c.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD
        ) as any;

        if (timeCardComponent?.TimeCards) {
            for (const timeCard of timeCardComponent.TimeCards) {
                if (timeCard.Employees) {
                    for (const employee of timeCard.Employees) {
                        if (employee.TotalHours) totalHours += employee.TotalHours;
                    }
                }
            }
        }
        return totalHours;
    }

    public updateViewStates(logs: DailyLogProjectUserComponent[]) {
        this.isViewLoading.set(true);
        const dailyLogIds = logs.map(log => log.Id).filter(id => id) as string[];
    
        if (dailyLogIds.length === 0) {
            this.isViewLoading.set(false);
            this.componentViews.update(views => {
                logs.forEach(log => {
                    views.set(log.ProjectId, ComponentView.Edit);
                });
                return new Map(views);
            });
            return;
        }
    
        this.approvalService.GetApprovalsByDailyLogIds(dailyLogIds).subscribe({
            next: (approvalsDict) => {
                this.approvals.set(approvalsDict); // Store batch response
                this.componentViews.update(views => {
                    logs.forEach(log => {
                        const approval = approvalsDict[log.Id];
                        const view = approval ? ComponentView.ReadOnly : ComponentView.Edit;
                        views.set(log.ProjectId, view);
                    });

                    return new Map(views);
                });
                this.isViewLoading.set(false);
            },
            error: (err) => {
                console.log('Error fetching approvals:', err);
                this.isViewLoading.set(false);
                this.approvals.set({});
                this.componentViews.update(views => {
                    logs.forEach(log => {
                        views.set(log.ProjectId, ComponentView.Edit);
                    });
                    return new Map(views);
                });
            }
        });
    }

    getApproval(dailyLogId: string): Approval | null {
        return this.approvals()[dailyLogId] || null;
    }

    updateLog(updatedLog: DailyLogProjectUserComponent): void {
        this.fetchedLogs.update(logs => {
            const index = logs.findIndex(log => log.ProjectId === updatedLog.ProjectId);
            if (index !== -1) {
                const newLogs = [...logs];
                newLogs[index] = updatedLog;
                return newLogs;
            }
            return logs;
        });
    }

  /**
   * Requests a date change with dirty state confirmation
   * @param requestedDate The requested date (Date object) or day offset (number)
   * @returns A Promise that resolves to true if the date change was successful, false otherwise
   */
  requestDateChange(requestedDate: Date | number): Promise<boolean> {
    // Store the current date for potential reversion
    const currentDate = this.dateSelectorService.selectedDate();
    
    // If any log is currently persisting, don't allow date change
    if (this.isAnyLogPersisting()) {
      return Promise.resolve(false);
    }
    
    // Check if any log is dirty using the global dirty state
    const isDirty = this.checkGlobalDirtyState();
    
    // If dirty, show confirmation dialog
    if (isDirty) {
      return this.showDateChangeConfirmation(requestedDate, currentDate);
    } 
    
    // No dirty state, update the date directly
    this.updateDate(requestedDate);
    return Promise.resolve(true);
  }
  
  /**
   * Shows a confirmation dialog for date change when dirty state is detected
   * @param requestedDate The requested date (Date object) or day offset (number)
   * @param currentDate The current date to revert to if needed
   * @returns A Promise that resolves to true if the date change was confirmed, false otherwise
   */
  private showDateChangeConfirmation(requestedDate: Date | number, currentDate: Date): Promise<boolean> {
    return this.confirmService.open(
      ConfirmationMessages.UNSAVED_CHANGES_DATE_NAVIGATION,
      ConfirmationMessages.UNSAVED_CHANGES_DATE_NAVIGATION_TITLE
    ).result.then(result => {
      if (result === 'yes') {
        // User confirmed, update the date
        this.updateDate(requestedDate);
        
        // Clear all dirty states to prevent them from persisting
        this.clearAllDirtyStates();
        
        return true;
      } else {
        // User canceled, revert to previous date
        this.revertDate(currentDate);
        return false;
      }
    }).catch(() => {
      // Dialog dismissed, revert to previous date
      this.revertDate(currentDate);
      return false;
    });
  }
  
  /**
   * Clears all dirty states to prevent them from persisting when changing dates
   */
  private clearAllDirtyStates(): void {
    // Clear the photos to upload and photos to remove
    // this.photosService.clear();
    
    // Clear ALL dirty states for all projects
    const dirtyMap = this.dirtyService.dirtyMap();
    for (const [projectId] of dirtyMap.entries()) {
      this.dirtyService.clearAllDirty(projectId);
    }
  }
  
  /**
   * Updates the date based on the requested date type
   * @param dateOrOffset Either a Date object or a number representing day offset
   */
  private updateDate(dateOrOffset: Date | number): void {
    if (typeof dateOrOffset === 'number') {
      this.dateSelectorService.moveDate(dateOrOffset);
    } else {
      this.dateSelectorService.setDate(dateOrOffset);
    }
  }
  
  /**
   * Reverts the date to a previous value
   * @param date The date to revert to
   */
  private revertDate(date: Date): void {
    this.dateSelectorService.setDate(date);
  }
  
  /**
   * Checks if any project has dirty components
   * @returns True if any component in any project is dirty, false otherwise
   */
  private checkGlobalDirtyState(): boolean {
    // First try using the dirtyService.anyDirtyGlobally() method
    const isDirtyFromSignal = this.dirtyService.anyDirtyGlobally()();
    if (isDirtyFromSignal) {
      return true;
    }
    
    // Then check the dirtyMap directly as a fallback
    const dirtyMap = this.dirtyService.dirtyMap();
    
    // Check if any project has a dirty component
    for (const [, projectMap] of dirtyMap.entries()) {
      for (const [, isDirtyValue] of projectMap.entries()) {
        if (isDirtyValue) {
          return true;
        }
      }
    }
    
    return false;
  }

    /**
     * Clean up resources when the service is destroyed or when the component using it is destroyed
     * This ensures all state is properly reset to prevent stale data from being displayed
     */
    destroy(): void {
        this.fetchedLogs.set([]);
        this.componentViews.set(new Map());
        this.approvals.set({});
        
        // Clear all dirty states globally
        // This prevents the "unsaved changes" warning from appearing when returning to the diary page
        this.dirtyService.clearAllDirtyGlobally();
        
        // Clear photos and other resources
        this.clearAllDirtyStates();
        
        // Reset the cache in the DiaryProjectsSelectorService
        // This is critical to prevent stale data from being displayed when returning to the diary page
        this.projectsSelectorService.resetLogsCache();
    }
}
