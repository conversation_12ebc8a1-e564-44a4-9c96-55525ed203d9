.input-group {
    display: flex;
    align-items: center;
    --datepicker-height: 45px; /* Height for the date picker - doesnt seem to work */
    --navigator-height: 45px; /* Height for the navigator (left arrow, Today, right arrow) */
}

/* Apply consistent height and slight rounded corners to all buttons */
.input-group .btn {
    height: var(--navigator-height) !important;
    border-radius: 4px !important; /* Match PrimeNG's default roundness */
}

.input-group-append .input-group-prepend .btn:first-child {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
    border-top-right-radius: 0 !important; 
    border-bottom-right-radius: 0 !important; 
    border-right-width: 0 !important; 
}

.input-group-append .btn:last-child {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
    border-top-left-radius: 0 !important; 
    border-bottom-left-radius: 0 !important; 
    border-left-width: 0 !important; 
}

/* PrimeNG Datepicker Styling */
.custom-datepicker {
    height: var(--datepicker-height) !important;
    display: flex;
    align-items: center;
    margin-right: 10px !important; 
}

/* Ensure the entire p-datepicker wrapper uses the variable height */
.custom-datepicker p-datepicker {
    height: var(--datepicker-height) !important;
    min-height: var(--datepicker-height) !important; /* Override any PrimeNG default */
    display: flex;
    align-items: center;
}

/* Allow date picker to use PrimeNG's default rounded corners */
.custom-datepicker {}

/* Allow input field to use PrimeNG's default rounded corners */
.custom-datepicker .p-inputtext {}

/* Ensure input field is the same height as the buttons */
.custom-datepicker .p-inputtext {
    height: var(--datepicker-height) !important;
    min-height: var(--datepicker-height) !important;
    border: 1px solid #ced4da !important;
    padding: 0.375rem 2rem 0.375rem 0.75rem !important;
    font-size: 1rem !important;
    line-height: 1.3 !important;
}

/* PrimeNG Calendar Icon Button */
.custom-datepicker .p-datepicker-trigger {
    height: var(--datepicker-height) !important;
    min-height: var(--datepicker-height) !important;
    width: 38px !important; 
    border: 1px solid #ced4da !important;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem !important;
}

/* Ensure Today button has no rounded corners */
.input-group-append .btn-outline-secondary {
    height: var(--navigator-height) !important; 
    border-radius: 0 !important; 
    border-left-width: 0 !important; 
    border-right-width: 0 !important; 
}

/* Ensure button group (navigator) is seamless */
.input-group-append {
    display: flex;
    border-collapse: collapse;
}

.input-group-append > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: 0 !important;
}