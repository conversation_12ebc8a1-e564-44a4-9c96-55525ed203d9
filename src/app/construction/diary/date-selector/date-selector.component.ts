import { Component, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DateSelectorService } from '../../shared/data-access/date-selector.service';
import { DatePickerModule } from 'primeng/datepicker';
import { DiaryService } from '../diary.service';
import { DiaryPageService } from '../../shared/data-access/diary-page.service';

@Component({
  selector: 'app-date-selector',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, DatePickerModule],
  templateUrl: './date-selector.component.html',
  styleUrls: ['./date-selector.component.css']
})
export class DateSelectorComponent {
  private dateSelectorService = inject(DateSelectorService);
  private diaryService = inject(DiaryService);
  private diaryPageService = inject(DiaryPageService);

  formattedDate = this.dateSelectorService.formattedDate;

  dateFormGroup: FormGroup = new FormGroup({
    date: new FormControl(this.dateSelectorService.selectedDate())
  });
  
  // Flag to prevent duplicate date change requests
  private isHandlingDateChange = false;
  // Store the original date before a change attempt
  private originalDate: Date | null = null;

  constructor() {
    // Subscribe to form control value changes
    this.dateFormGroup.get('date')?.valueChanges.subscribe((value: Date | null) => {
      // Only process if not already handling a date change and value exists
      if (!this.isHandlingDateChange && value) {
        // Use the common date change handler
        this.handleDateChange(value);
      }
    });

    // Effect to synchronize the form control value with the service's selected date.
    // This ensures that any changes to the selected date in the service are reflected
    // in the form control without triggering additional value change events.
    effect(() => {
      const serviceDate = this.dateSelectorService.selectedDate();
      if (serviceDate !== this.dateFormGroup.get('date')?.value) {
        this.dateFormGroup.get('date')?.setValue(serviceDate, { emitEvent: false });
      }
    });
  }

  /**
   * Handles date change requests with proper locking and error handling
   * @param dateOrOffset Either a Date object or a number representing day offset
   */
  private handleDateChange(dateOrOffset: Date | number): void {
    if (!this.isHandlingDateChange) {
      this.isHandlingDateChange = true;
      this.diaryPageService.clear();
      // Store the original date before attempting to change
      this.originalDate = this.dateSelectorService.selectedDate();
      
      this.diaryService.requestDateChange(dateOrOffset)
        .then(success => {
          if (!success) {
            // If the date change was not successful (user canceled), revert the form control
            this.dateFormGroup.get('date')?.setValue(this.originalDate, { emitEvent: false });
          }
        })
        .finally(() => {
          this.isHandlingDateChange = false;
        });
    }
  }

  /**
   * Move the calendar by the specified number of days
   * @param dayOffset Number of days to move (positive or negative)
   */
  moveCalendar(dayOffset: number): void {
    this.handleDateChange(dayOffset);
  }

  /**
   * Set the calendar to today's date
   */
  selectToday(): void {
    this.handleDateChange(new Date());
  }

  /**
   * Handle date selection from the calendar component
   * @param date The selected date
   */
  dateChangedHandler(date: Date): void {
    // This is called from the calendar's onSelect event
    // Instead of updating the form control and triggering the valueChanges subscription,
    // we can directly use our common date change handler
    this.handleDateChange(date);
  }
  
  /**
   * Checks if any log is currently persisting
   * Used to disable date controls during save operations
   */
  isAnyLogPersisting(): boolean {
    return this.diaryService.isAnyLogPersisting();
  }
}
