import { Component, inject, effect, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { ProjectNavigatorService, ProjectOption } from './project-navigator.service';
import { Subscription } from 'rxjs';
import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-project-navigator',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, NgbPopoverModule],
  templateUrl: './project-navigator.component.html',
  styleUrls: ['./project-navigator.component.css'],
  providers: [ProjectNavigatorService]
})
export class ProjectNavigatorComponent implements OnInit, OnDestroy {
  // Inject service
  service = inject(ProjectNavigatorService);
  
  // Form control for project selector
  projectSelectorControl = new FormControl<string | null>(null);
  
  // Subscription for form control changes
  private valueChangesSubscription: Subscription | null = null;
  
  // Popover text for the legend with carriage returns
  popoverText = `🔴 Unsaved Changes

⏳ Submitted for Approval

✓ Approved

(none) Unsubmitted`;
  
  constructor() {
    // Initialize form control with current selected project ID
    this.projectSelectorControl.setValue(this.service.selectedProjectId());
    
    // Effect to update form control when selected project ID changes
    effect(() => {
      const selectedId = this.service.selectedProjectId();
      if (selectedId !== this.projectSelectorControl.value) {
        this.projectSelectorControl.setValue(selectedId, { emitEvent: false });
      }
    });
    
    // Effect to initialize selected project to the first project when logs change
    effect(() => {
      const logs = this.service.fetchedLogs();
      if (logs && logs.length > 0 && !this.service.selectedProjectId()) {
        this.service.setSelectedProjectId(logs[0].ProjectId);
      } else if (logs && logs.length === 0) {
        this.service.setSelectedProjectId(null);
      }
    });
  }
  
  ngOnInit(): void {
    // Subscribe to form control changes
    this.valueChangesSubscription = this.projectSelectorControl.valueChanges.subscribe(value => {
      if (value) {
        this.service.scrollToProject(value);
      }
    });
  }
  
  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    if (this.valueChangesSubscription) {
      this.valueChangesSubscription.unsubscribe();
      this.valueChangesSubscription = null;
    }
  }
  
  /**
   * Handle selection change event
   * This is a backup for the valueChanges subscription
   */
  onSelectionChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const value = select.value;
    if (value) {
      this.service.scrollToProject(value);
    }
  }
  
  /**
   * Track by function for ngFor
   */
  trackByProjectId(index: number, option: ProjectOption): string {
    return option.id;
  }
}
