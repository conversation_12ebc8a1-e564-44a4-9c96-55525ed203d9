import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { DiaryService } from '../diary.service';
import { DailyLogDirtyService } from '../../project/daily-log/shared/services/daily-log-dirty.service';
import { Approval } from '../../shared/interfaces/approval';

/**
 * Enum representing the status of a project
 */
export enum ProjectStatus {
  Unsubmitted = 'unsubmitted',
  Submitted = 'submitted',
  Approved = 'approved'
}

/**
 * Interface for project options in the dropdown
 */
export interface ProjectOption {
  id: string;
  name: string;
  isDirty: boolean;
  status: ProjectStatus;
}

/**
 * Service for the ProjectNavigator component
 * Handles project selection and navigation
 */
@Injectable()
export class ProjectNavigatorService {
  // Inject services
  private readonly diaryService = inject(DiaryService);
  private readonly dirtyService = inject(DailyLogDirtyService);
  
  // Selected project ID
  private _selectedProjectId = signal<string | null>(null);
  public readonly selectedProjectId = this._selectedProjectId.asReadonly();
  
  // Expose service signals for template use
  public readonly fetchedLogs = this.diaryService.fetchedLogs;
  
  // Signal to force recomputation when dirty state changes
  private _dirtyStateChanged = signal<number>(0);
  
  /**
   * Check if a project is dirty
   * @param projectId The project ID to check
   * @returns True if the project is dirty, false otherwise
   */
  private isProjectDirty(projectId: string): boolean {
    // Check the dirtyMap directly
    const dirtyMap = this.dirtyService.dirtyMap();
    
    if (dirtyMap && dirtyMap.has(projectId)) {
      const projectMap = dirtyMap.get(projectId);
      if (projectMap) {
        // Check each component's dirty state
        for (const [componentId, isDirty] of projectMap.entries()) {
          if (isDirty) {
            return true;
          }
        }
      }
    }
    
    // If we get here, the project is not dirty
    return false;
  }
  
  /**
   * Determine the status of a project based on its approval
   * @param dailyLogId The daily log ID
   * @returns The project status
   */
  private getProjectStatus(dailyLogId: string): ProjectStatus {
    const approval = this.diaryService.getApproval(dailyLogId);
    
    if (!approval) {
      return ProjectStatus.Unsubmitted;
    }
    
    if (approval.FinalApprovalDate) {
      return ProjectStatus.Approved;
    }
    
    return ProjectStatus.Submitted;
  }
  
  // Computed signal for project options
  public readonly projectOptions = computed<ProjectOption[]>(() => {
    // Include the dirtyStateChanged signal as a dependency to force recomputation
    const _ = this._dirtyStateChanged();
    
    const logs = this.fetchedLogs();
    if (!logs || logs.length === 0) return [];
    
    // Add "-- TOP --" option as the first item
    const options: ProjectOption[] = [
      { 
        id: 'top', 
        name: '-- TOP --', 
        isDirty: false,
        status: ProjectStatus.Unsubmitted
      }
    ];
    
    // Add project options with dirty state and status
    logs.forEach(log => {
      const isDirty = this.isProjectDirty(log.ProjectId);
      const projectName = this.diaryService.getProjectName(log.ProjectId);
      const status = this.getProjectStatus(log.Id);
      
      options.push({
        id: log.ProjectId,
        name: projectName,
        isDirty: isDirty,
        status: status
      });
    });
    
    return options;
  });
  
  constructor() {
    // Set up an effect to listen for changes to the dirtyMap
    effect(() => {
      // This will run whenever the dirtyMap changes
      const dirtyMap = this.dirtyService.dirtyMap();
      
      // Increment the counter to force recomputation of projectOptions
      this._dirtyStateChanged.update(count => {
        return count + 1;
      });
    });
    
    // Set up an effect to listen for changes to the approvals
    effect(() => {
      // This will run whenever the approvals change
      const approvals = this.diaryService.approvals();
      
      // Increment the counter to force recomputation of projectOptions
      this._dirtyStateChanged.update(count => {
        return count + 1;
      });
    });
  }
  
  /**
   * Set the selected project ID
   */
  setSelectedProjectId(projectId: string | null): void {
    this._selectedProjectId.set(projectId);
  }
  
  /**
   * Scroll to the selected project using anchor
   */
  scrollToProject(projectId: string): void {
    if (!projectId) return;
    
    this._selectedProjectId.set(projectId);
    
    // Special case for "top" option
    if (projectId === 'top') {
      this.scrollToTop();
      return;
    }
    
    // Use the anchor element for scrolling
    const anchorId = `anchor-${projectId}`;
    const anchor = document.getElementById(anchorId);
    
    if (anchor) {
      // Find the parent daily-log element to add highlight effect
      const dailyLog = document.getElementById(`project-${projectId}`);
      if (dailyLog) {
        dailyLog.classList.add('highlight-project');
        
        // Remove highlight after delay
        setTimeout(() => {
          dailyLog.classList.remove('highlight-project');
        }, 2000);
      }
      
      // Scroll to the anchor with smooth behavior
      window.location.hash = ''; // Clear any existing hash
      setTimeout(() => {
        window.location.hash = anchorId;
        
        // Remove the hash from the URL after scrolling
        setTimeout(() => {
          history.replaceState('', document.title, window.location.pathname + window.location.search);
        }, 100);
      }, 10);
    } else {
      console.error('Could not find anchor element for project ID:', projectId);
    }
  }
  
  /**
   * Scroll to the top of the page by scrolling to the first project's anchor
   */
  scrollToTop(): void {
    // Get the first project ID
    const logs = this.fetchedLogs();
    if (!logs || logs.length === 0) {
      // If no projects, just scroll to top
      window.scrollTo(0, 0);
      return;
    }
    
    // Get the first project ID
    const firstProjectId = logs[0].ProjectId;
    
    // Scroll to the first project's anchor
    // This will use the same logic as scrollToProject, which is known to work
    if (firstProjectId !== this.selectedProjectId()) {
      this.scrollToProject(firstProjectId);
    } else {
      // If already on first project, force a scroll to its anchor
      const anchorId = `anchor-${firstProjectId}`;
      const anchor = document.getElementById(anchorId);
      
      if (anchor) {
        // Find the parent daily-log element to add highlight effect
        const dailyLog = document.getElementById(`project-${firstProjectId}`);
        if (dailyLog) {
          dailyLog.classList.add('highlight-project');
          
          // Remove highlight after delay
          setTimeout(() => {
            dailyLog.classList.remove('highlight-project');
          }, 2000);
        }
        
        // Scroll to the anchor with smooth behavior
        window.location.hash = ''; // Clear any existing hash
        setTimeout(() => {
          window.location.hash = anchorId;
          
          // Remove the hash from the URL after scrolling
          setTimeout(() => {
            history.replaceState('', document.title, window.location.pathname + window.location.search);
          }, 100);
        }, 10);
      }
    }
  }
}
