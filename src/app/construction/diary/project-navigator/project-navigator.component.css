
.project-navigator select {
  width: auto;
  min-width: 150px;
  max-width: 250px;
}

/* Help icon styling */
.icon-help {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #007bff; /* Bootstrap primary blue */
  color: white;
  font-size: 0.7rem;
  transition: background-color 0.2s ease;
}

.icon-help:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

/* Legend popover styling */
.legend-popover {
  padding: 0.5rem;
}

.legend-popover div {
  margin-bottom: 0.5rem;
  white-space: nowrap;
}

.legend-popover div:last-child {
  margin-bottom: 0;
}

/* Highlight effect for selected project */
:host ::ng-deep .highlight-project {
  animation: highlight-fade 2s ease-in-out;
  position: relative;
  z-index: 1;
}

@keyframes highlight-fade {
  0% { 
    background-color: rgba(255, 193, 7, 0.5); 
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);
  }
  50% { 
    background-color: rgba(255, 193, 7, 0.3); 
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
  }
  100% { 
    background-color: transparent; 
    box-shadow: none;
  }
}

/* Media query for smaller screens */
@media (max-width: 768px) {
  .project-navigator {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .project-navigator select {
    margin-top: 0.5rem;
    width: 100%;
    max-width: 100%;
  }
}
