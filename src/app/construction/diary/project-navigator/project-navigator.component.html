@if (service.projectOptions().length > 1) {
<div class="project-navigator d-flex ">
  <!-- jump to drop down -->
  <div>
    <label for="projectSelector" class="form-label">Jump To</label>
    <select id="projectSelector" class="form-select" [formControl]="projectSelectorControl"
      (change)="onSelectionChange($event)">
      @for (option of service.projectOptions(); track trackByProjectId($index, option)) {
      <option [value]="option.id">
        @if (option.isDirty) {
        🔴 <!-- Red dot for dirty state -->
        } @else if (option.status === 'submitted') {
        ⏳ <!-- Hourglass for submitted/processing -->
        } @else if (option.status === 'approved') {
        ✓ <!-- Checkmark for approved -->
        }
        {{ option.name }}
      </option>
      }
    </select>
  </div>
  <!-- Question mark icon with popover -->
  <div>
    <span class="ms-2 icon-help" [ngbPopover]="popContent" popoverTitle="Project Status Icons" placement="top"
      [autoClose]="true" triggers="mouseenter:mouseleave">
      <i class="fa fa-question"></i>
    </span>
  </div>
  <!-- popover -->
  <ng-template #popContent>
    <div class="legend-popover">
      <div>🔴 Unsaved Changes</div>
      <div>⏳ Submitted for Approval</div>
      <div>✓ Approved</div>
      <div>(none) Unsubmitted</div>
    </div>
  </ng-template>
</div>
}