import { Component, inject, computed, signal, effect, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DailyLogComponent } from '../project/daily-log/daily-log.component';
import { ProjectsSelectorComponent } from './projects-selector/projects-selector.component';
import { DateSelectorComponent } from './date-selector/date-selector.component';
import { ProjectNavigatorComponent } from './project-navigator/project-navigator.component';
import { DiaryService } from './diary.service';
import { DailyLogProjectUserComponent } from '../shared/interfaces/daily-log-project-user';
import { DateSelectorService } from '../shared/data-access/date-selector.service';
import { DiaryProjectsSelectorService } from '../shared/data-access/diary-projects-selector.service';
import { DailyLogDirtyService } from '../project/daily-log/shared/services/daily-log-dirty.service';
import { DiaryPageService } from '../shared/data-access/diary-page.service';
import { ProjectListDropdownService } from '../shared/data-access/project-list-dropdown.service';

@Component({
  selector: 'app-diary',
  standalone: true,
  imports: [CommonModule, DailyLogComponent, ProjectsSelectorComponent, DateSelectorComponent],
  providers: [DiaryPageService],
  templateUrl: './diary.component.html',
  styleUrls: ['./diary.component.css']
})
export class DiaryComponent implements OnDestroy {
  // Inject services
  private readonly diaryService = inject(DiaryService);
  private readonly dateSelectorService = inject(DateSelectorService);
  private readonly diaryProjectsSelectorService = inject(DiaryProjectsSelectorService);
  private readonly dirtyService = inject(DailyLogDirtyService);
  private readonly diaryPageService = inject(DiaryPageService);
  
  // Signal to check if any project has dirty components
  private _anyDirty = signal<boolean>(false);
  public readonly anyDirty = this._anyDirty.asReadonly();
  
  // Expose service methods for template use
  public isLoading(): boolean {
    return this.diaryService.isLoading();
  }
  
  public isViewLoading(): boolean {
    return this.diaryService.isViewLoading();
  }
  public readonly fetchedLogs = this.diaryService.fetchedLogs;
  public readonly totalEmployeeHours = this.diaryService.totalEmployeeHours;
  public readonly isSingleProjectMode = this.diaryProjectsSelectorService.isSingleProjectModeSignal;
  public readonly singleProjectId = this.diaryProjectsSelectorService.singleProjectIdSignal;
  totalDailyLogsSubmitted = this.diaryPageService.totalDailyLogsSubmitted;
  totalDailyLogsNotSubmitted = this.diaryPageService.totalDailyLogsNotSubmitted;
  isSubmitting = this.diaryPageService.isSubmitting;
  // Expose service methods needed by template
  public getProjectName(projectId: string): string {
    return this.diaryService.getProjectName(projectId);
  }

  // Method to get the formatted date for the footer
  public getCurrentFormattedDate(): string {
    const date = this.dateSelectorService.selectedDate();
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayOfWeek = dayNames[date.getDay()];
    const month = date.getMonth() + 1; // getMonth() returns 0-11
    const day = date.getDate();
    const year = date.getFullYear().toString().slice(-2); // Get last 2 digits of year
    
    return `${dayOfWeek} ${month}/${day}/${year}`;
  }

  /**
   * Navigate to a different day by the specified offset
   * @param dayOffset Number of days to move (positive or negative)
   */
  public navigateDay(dayOffset: number): void {
    this.diaryService.requestDateChange(dayOffset);
  }
  
  /**
   * Navigate to the previous day
   */
  public goToPreviousDay(): void {
    this.navigateDay(-1);
  }

  /**
   * Navigate to the next day
   */
  public goToNextDay(): void {
    this.navigateDay(1);
  }

  constructor() {
    // Initialize date if in single project mode
    if (this.isSingleProjectMode()) {
      this.dateSelectorService.setToday();
    }
    
    // Create an effect to update our local anyDirty signal when the dirtyService signal changes
    effect(() => {
      const dirtySignal = this.dirtyService.anyDirtyGlobally();
      const isDirty = dirtySignal();
      this._anyDirty.set(isDirty);
    });
  }

  // Helper methods
  trackByProjectId(index: number, dl: DailyLogProjectUserComponent): string {
    return dl.ProjectId;
  }
  
  // Method to directly check if any project has dirty components
  checkGlobalDirtyState(): boolean {
    // Get the dirtyMap from the dirtyService
    const dirtyMap = this.dirtyService.dirtyMap();
    
    // Check if any project has a dirty component
    let isDirty = false;
    for (const [, projectMap] of dirtyMap.entries()) {
      for (const [, isDirtyValue] of projectMap.entries()) {
        if (isDirtyValue) {
          isDirty = true;
          break;
        }
      }
      if (isDirty) break;
    }
    
    return isDirty;
  }
  
  /**
   * Checks if any log is currently persisting
   * Used to disable date controls during save operations
   */
  isAnyLogPersisting(): boolean {
    return this.diaryService.isAnyLogPersisting();
  }
  
  /**
   * Scroll to a specific project by ID
   * This method is called from the ProjectNavigatorComponent
   */
  scrollToProject(projectId: string): void {
    // Find the element with the given project ID
    const elementId = `project-${projectId}`;
    const element = document.getElementById(elementId);
    
    if (element) {
      // Add highlight class
      element.classList.add('highlight-project');
      
      // Calculate position
      const rect = element.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const elementTop = rect.top + scrollTop;
      
      // Scroll to element with offset
      window.scrollTo({
        top: elementTop - 150,
        behavior: 'smooth'
      });
      
      // Remove highlight after delay
      setTimeout(() => {
        element.classList.remove('highlight-project');
      }, 2000);
    }
  }

  /**
   * Clean up resources when the component is destroyed
   * This follows Angular best practices for component lifecycle management
   */
  ngOnDestroy(): void {
    // Call the destroy method on the service to clean up resources
    this.diaryService.destroy();
  }

  saveAllDailyLogs(){
    // Call the save method on the diaryPageService to save all daily logs
    this.diaryPageService.save();
  }

  submitAllDailyLogs(){
    // Call the submit method on the diaryPageService to submit all daily logs
    this.diaryPageService.submit();
  }
}
