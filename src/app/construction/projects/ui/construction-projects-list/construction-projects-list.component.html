<!-- search -->
<div>
	<ng-content></ng-content>
</div>
<!-- all projects -->
<table class="table align-middle">
	<thead>
		<tr>
			<th scope="col"></th>
			<th scope="col">No.</th>
			<th scope="col">Name</th>
			<th scope="col"></th>
		</tr>
	</thead>
	<tbody>
		@if(isLoading()){
		<tr class="placeholder-glow" *ngFor="let item of [1,2,3,4,5,6,7]">
			<td><span class="placeholder w-100"></span></td>
			<td><span class="placeholder w-100"></span></td>
			<td><span class="placeholder w-100"></span></td>
			<td><span class="placeholder w-100"></span></td>
		</tr>
		}@else {
		@for (project of projects(); track $index) {
		<tr>
			<td>
				@if(showReopenButtonForDeactivatedProjects()){
				@if(project.Deactivated){
				<button class="btn btn-outline-dark" (click)="reopenProject(project)" [disabled]="projectsService.isProjectStatusChanging(project.Id)">
					@if(projectsService.isProjectStatusChanging(project.Id)){
						<i class="fas fa-circle-notch fa-spin fa-1x"></i>
					}
					Reopen
				</button>
				}@else {
				Is Active
				}
				}@else {
						
				
					@if(isFavoritesLoading()){
				<i 
						class="fas fa-star fa-lg" 
						aria-hidden="true" 
						style="cursor: pointer; color:rgb(234, 235, 236)"						
						[ngClass]="{ 'flashing': project?.IsFavorite, 'fal': !project?.IsFavorite }">
						</i>
					}@else {
						@if(isFavoritingProject(project)){
						<i class="fas fa-circle-notch fa-spin fa-2x"></i>
						}@else {
						<i class="fas fa-star fa-lg text-warning" aria-hidden="true" style="cursor: pointer;"
							(click)="favorite(project)"
							[ngClass]="{ fas: project?.IsFavorite, fal: !project?.IsFavorite }"></i>
						}
					}
				
				}
			</td>
			<td>
				<a [routerLink]="['../projects', project.Id]" class="custom-link">
					{{ project?.InternalId || 'No Id' }}</a>
			</td>
			<td>
				<a [routerLink]="['../projects', project.Id]" class="custom-link">
					{{ project?.Title || 'No Title' }}</a>
			</td>
			<td class="text-end">
				<div class="dropdown">
					<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
						data-bs-toggle="dropdown" aria-expanded="false">
						<i class="far fa-ellipsis-v fa-lg" aria-hidden="true" id="dropdownMenuButton"
							data-toggle="dropdown"></i>
					</button>
					<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
						@if(!userProjectComponents){
						<li class="text-center">
							<i class="fas fa-circle-notch fa-spin fa-2x"></i>
						</li>
						}@else {
						<li>
							@for(component of userProjectComponents; track $index){
							<a class="dropdown-item text-dark" [routerLink]="['../projects',project?.Id, component.UrlLink]">
								{{ component.Name }}
							</a>
							}@empty {
							<div>No Components.</div>
							<div><a [routerLink]="['./user', 'settings']">Click here to add Components to your
									Projects</a></div>
							}
						</li>
						}
					</ul>
				</div>
			</td>
		</tr>
		@if(currentView() === PROJECTS_VIEW.DETAILS){
		@for(component of project.Components; track $index){
		@switch (component.Name) {
		@case ("Info") {
		<tr>
			<td class="p-4" colspan="4">
				<app-construction-project-info-viewer [infoComponent]="component"
					[projectInternalId]="project.InternalId">
				</app-construction-project-info-viewer>
			</td>
		</tr>
		}@default {
		<tr>
			<td colspan="4">
				<div class="alert alert-info mb-0" role="alert">
					Component {{ component.Name }} not supported.
				</div>
			</td>
		</tr>
		}
		}
		}@empty {
		<tr>
			<td colspan="4">
				<div class="alert alert-info mb-0" role="alert">
					No information on this project.
				</div>
			</td>
		</tr>
		}
		}@else {
		}
		}@empty{
		<tr>
			<td colspan="4">
				<div class="alert alert-info mb-0" role="alert">
					No projects yet.
				</div>
			</td>
		</tr>
		}
		}
	</tbody>
</table>