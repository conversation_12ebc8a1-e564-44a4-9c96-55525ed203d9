import { Component, EventEmitter, Input, Output, computed, effect, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ConstructionProject, ProjectsView } from 'src/app/construction/shared/interfaces/construction-project';
import { ConstructionProjectsService } from 'src/app/construction/shared/data-access/projects.service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { ConstructionProjectInfoViewerComponent } from 'src/app/construction/project/info/ui/construction-project-info-viewer/construction-project-info-viewer.component';
import { ConstructionRecentProjectsService } from 'src/app/construction/shared/data-access/recent-projects.service';
import { UserProjectComponent } from 'src/app/construction/shared/interfaces/dashboard';
import { ConstructionFavoritesService } from 'src/app/construction/shared/data-access/favorites.service';

@Component({
    selector: 'app-construction-projects-list',
    imports: [CommonModule, RouterLink, ConstructionProjectInfoViewerComponent],
    templateUrl: './construction-projects-list.component.html',
    styleUrl: './construction-projects-list.component.css'
})
export class ConstructionProjectsListComponent {

  projectsService = inject(ConstructionProjectsService);
  confirmService = inject(ConfirmService);
  recentProjectService = inject(ConstructionRecentProjectsService);  
  favoritesService = inject(ConstructionFavoritesService);

  @Input() userProjectComponents: Array<UserProjectComponent> | null = null;
  showReopenButtonForDeactivatedProjects = this.projectsService.deactivated;
  currentView = this.projectsService.currentView;  
  isLoading = this.projectsService.isLoading;
  public readonly PROJECTS_VIEW: typeof ProjectsView = ProjectsView;   
  projects = this.projectsService.projects;  
  favorites = this.favoritesService.favorites;  
  isFavoritesLoading = this.favoritesService.isLoading;

  constructor() {
    this.favoritesService.initialize.set(true);

    effect(() => {
      if(this.projects() && this.projects().length > 0 && this.favorites()){
        for(let project of this.projects()){
          let favorite = this.favorites().find(x => x.ProjectId === project.Id);
          if(favorite){
            project.IsFavorite = true;
          }else{
            project.IsFavorite = false;
          }
        }
      }
    });
  }
  reopenProject(project: ConstructionProject) {
    if (project) {
      this.confirmService.open("Are you sure you want to open the project?").result.then(async result => {
        if(result === "yes"){
          this.projectsService.setProjectStatus(project.Id, false);
        }
      });
    }
  }

  isFavoritingProject(project: ConstructionProject) {
    if (project) {
     return this.favoritesService.isFavoritingProject(project.Id);
    }

    return false;
  }

  favorite(project: ConstructionProject) {
    if(this.isFavoritingProject(project)){
      return;
    }

    if(project.IsFavorite){
      this.favoritesService.removeFavorite(project.Id);
    }else{
      this.favoritesService.addFavorite(project.Id);
    }
    
  }


}
