import { Component, Input, effect, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionRecentProjectsService } from 'src/app/construction/shared/data-access/recent-projects.service';
import { RouterLink } from '@angular/router';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';
import { UserProjectComponent } from 'src/app/construction/shared/interfaces/dashboard';
import { ConstructionFavoritesService } from 'src/app/construction/shared/data-access/favorites.service';
import { ConstructionProject } from 'src/app/construction/shared/interfaces/construction-project';

@Component({
    selector: 'app-recent-projects',
    imports: [CommonModule, RouterLink],
    templateUrl: './recent-projects.component.html',
    styleUrl: './recent-projects.component.css'
})
export class RecentProjectsComponent {
  recentProjectsService = inject(ConstructionRecentProjectsService);
  userProjectComponentsService= inject(UserProjectComponentsService);
  recentProjects = this.recentProjectsService.recentProjectsList;    
  favoritesService = inject(ConstructionFavoritesService);  
  favorites = this.favoritesService.favorites;
  isFavoritesLoading = this.favoritesService.isLoading;

  constructor() {
    effect(() => {
      if(this.recentProjects() && this.recentProjects().length > 0 && this.favorites()){
        for(let project of this.recentProjects()){
          let favorite = this.favorites().find(x => x.ProjectId === project.Id);
          if(favorite){
            project.IsFavorite = true;
          }else{
            project.IsFavorite = false;
          }
        }
      }
    });
  }

  
    isFavoritingProject(project: ConstructionProject) {
      if (project) {
       return this.favoritesService.isFavoritingProject(project.Id);
      }
  
      return false;
    }
  
    favorite(project: ConstructionProject) {
      if(project.IsFavorite){
        this.favoritesService.removeFavorite(project.Id);
      }else{
        this.favoritesService.addFavorite(project.Id);
      }
      
    }
  
}
