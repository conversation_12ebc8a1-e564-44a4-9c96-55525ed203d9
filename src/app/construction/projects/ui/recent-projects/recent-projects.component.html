<table class="table align-middle">
	<tbody>
		@for (project of recentProjects(); track $index) {
		<tr>
			<td>
			@if(isFavoritesLoading()){
				<i 
						class="fas fa-star fa-lg" 
						aria-hidden="true" 
						style="cursor: pointer; color:rgb(234, 235, 236)"						
						[ngClass]="{ 'flashing': project?.IsFavorite, 'fal': !project?.IsFavorite }">
						</i>
					}@else {
						@if(isFavoritingProject(project)){
						<i class="fas fa-circle-notch fa-spin fa-2x"></i>
						}@else {
						<i class="fas fa-star fa-lg text-warning" aria-hidden="true" style="cursor: pointer;"
							(click)="favorite(project)"
							[ngClass]="{ fas: project?.IsFavorite, fal: !project?.IsFavorite }"></i>
						}
					}
			</td>
			<td>
				<a [routerLink]="[project?.Id]" class="custom-link">
					{{ project?.InternalId || 'No Id' }}</a>
			</td>
			<td>
				<a [routerLink]="[project?.Id]" class="custom-link">
					{{ project?.Title || 'No Title' }}</a>
			</td>
			<td class="text-end">
				<div class="dropdown">
					<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
						data-bs-toggle="dropdown" aria-expanded="false">
						<i class="far fa-ellipsis-v  fa-lg" aria-hidden="true" id="dropdownMenuButton"
							data-toggle="dropdown"></i>
					</button>
					<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
						@if(!userProjectComponents){
						<li class="text-center">
							<i class="fas fa-circle-notch fa-spin fa-2x"></i>
						</li>
						}@else {
						<li>
							@for(component of userProjectComponents; track $index){
							<a class="dropdown-item text-dark" [routerLink]="['../projects',project?.Id, component.UrlLink]">
								{{ component.Name }}
							</a>
							}@empty {
							<div>No Components.</div>
							<div><a [routerLink]="['./user', 'settings']">Click here to add Components to
									your
									Projects</a></div>
							}
						</li>
						}
					</ul>
				</div>
			</td>
		</tr>
		}@empty {
		<tr>
			<td colspan="4">
				<div class="alert alert-info mb-0" role="alert">
					No recent projects.
				</div>
			</td>
		</tr>
		}
	</tbody>
</table>