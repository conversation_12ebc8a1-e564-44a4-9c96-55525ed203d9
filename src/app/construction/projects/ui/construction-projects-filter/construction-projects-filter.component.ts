import { Component, inject, Output, EventEmitter, Input, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
import { ProjectsView } from 'src/app/construction/shared/interfaces/construction-project';
import { ConstructionProjectsService } from 'src/app/construction/shared/data-access/projects.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';


@Component({
    selector: 'app-construction-projects-filter',
    imports: [CommonModule, FormsModule, ReactiveFormsModule],
    templateUrl: './construction-projects-filter.component.html',
    styleUrl: './construction-projects-filter.component.css'
})
export class ConstructionProjectsFilterComponent {

  private constructionProjectsService = inject(ConstructionProjectsService);
  private router = inject(Router);
  private aRoute = inject(ActivatedRoute);
  currentView = this.constructionProjectsService.currentView;
  orderBy = this.constructionProjectsService.orderBy;
  searchText = this.constructionProjectsService.title;
  isReversed = this.constructionProjectsService.isReversed;
  isFavorites = this.constructionProjectsService.isFavorites;
  isLoading = this.constructionProjectsService.isLoading;
  searchControl = new UntypedFormControl();
  
  private searchText$ = new Subject<string>();    
  public readonly PROJECTS_VIEW: typeof ProjectsView = ProjectsView;

  constructor() {
    this.searchControl.valueChanges.pipe(
      debounceTime(400)
    ).subscribe(value => {
      let s = null;
      if (value) {
        s = value;
      }

      this.router.navigate([], { queryParams: { search: s }, queryParamsHandling: 'merge' });
    });

    this.aRoute.queryParams.subscribe(params => {
      var searchText = params["search"] ?? null;

      if (searchText) {
        this.searchControl.setValue(searchText, { emitEvent: false });
      } else {
        this.searchControl.setValue(null);
      }
    });

  }

  search(value: string) {    
    this.searchText$.next(value);
  }


  setFavorites() {  
    this.router.navigate([], {
      queryParams: { page: 1, isFavorites: !this.isFavorites() ? 'true' : null },
      queryParamsHandling: 'merge'
    });

  }
  setView(filter: ProjectsView) {
   if(filter === ProjectsView.DETAILS){  
      this.router.navigate(
        [], { queryParams: { view: filter, includeComponents: this.constructionProjectsService.allowedComponents}, queryParamsHandling: 'merge' });        
    }else{
      this.router.navigate(
        [], { queryParams: { view: filter,  includeComponents: null}, queryParamsHandling: 'merge' });        
    }    
  }

  setOrder(orderBy: string, isReversed: boolean) {    
    this.router.navigate([], {
      queryParams: { orderBy: orderBy, isReversed: isReversed },
      queryParamsHandling: 'merge'
    });
  
  }

}
