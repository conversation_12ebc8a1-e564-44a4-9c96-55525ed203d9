<div class="d-flex justify-content-between align-items-center">
	<div class="input-group me-2">
		<span class="input-group-text" id="basic-addon1">
			<i class="fa fa-search" aria-hidden="true"></i>
		</span>
		<input type="search" class="form-control" [formControl]="searchControl" />
	</div>
	<div class="btn-group" role="group" aria-label="Basic example">
		<button type="button" class="btn btn-outline-dark" (click)="setFavorites()" placement="bottom"
			[ngClass]="{'text-warning active': isFavorites()}" ngbTooltip="List View">
			<i class="fa fa-star" aria-hidden="true"></i>
		</button>
		<button type="button" class="btn btn-outline-dark" (click)="setView(PROJECTS_VIEW.LIST)" placement="bottom"
			[ngClass]="{'active': currentView() === PROJECTS_VIEW.LIST}" ngbTooltip="List View">
			<i class="fa fa-list" aria-hidden="true"></i>
		</button>
		<button type="button" class="btn btn-outline-dark" (click)="setView(PROJECTS_VIEW.DETAILS)"
			[ngClass]="{'active': currentView() === PROJECTS_VIEW.DETAILS}" placement="bottom"
			ngbTooltip="Details View">
			<i class="fa fa-th" aria-hidden="true"></i>
		</button>
		<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
			data-bs-toggle="dropdown" aria-expanded="false">
			<i class="fas fa-sort"></i>
		</button>
		<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
			<li><a class="dropdown-item" href="javascript:void(0)" (click)="setOrder('Title', false)"
					[ngClass]="{'active': orderBy() === 'Title' && isReversed() === false}">Sort by
					Name <i class="fa fa-caret-down me-1" aria-hidden="true"></i></a></li>
			<li>
				<a class="dropdown-item" href="javascript:void(0)" (click)="setOrder('Title', true)"
					[ngClass]="{'active': orderBy() === 'Title' && isReversed() === true}">
					Sort by Name <i class="fa fa-caret-up me-1" aria-hidden="true"></i>
				</a>
			</li>
			<li><a class="dropdown-item" href="javascript:void(0)" (click)="setOrder('InternalId', false)"
					[ngClass]="{'active': orderBy() === 'InternalId' && isReversed() === false}">Sort
					by ID
					<i class="fa fa-caret-down me-1" aria-hidden="true"></i></a></li>
			<li><a class="dropdown-item" href="javascript:void(0)" (click)="setOrder('InternalId', true)"
					[ngClass]="{'active': orderBy() === 'InternalId' && isReversed() === true}">Sort
					by ID
					<i class="fa fa-caret-up me-1" aria-hidden="true"></i></a></li>
		</ul>
	</div>
</div>