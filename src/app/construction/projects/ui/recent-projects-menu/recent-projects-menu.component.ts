import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ConstructionRecentProjectsService } from 'src/app/construction/shared/data-access/recent-projects.service';

@Component({
    selector: 'app-recent-projects-menu',
    imports: [CommonModule, RouterLink],
    templateUrl: './recent-projects-menu.component.html',
    styleUrl: './recent-projects-menu.component.css'
})
export class RecentProjectsMenuComponent {

  recentProjectsService = inject(ConstructionRecentProjectsService);
  recentProjects = this.recentProjectsService.recentProjectsList;
}
