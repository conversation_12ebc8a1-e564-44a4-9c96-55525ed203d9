<div class="dropdown">
	<a class="dropdown-toggle custom-link" href="#" role="button"
	  id="dropdownProjectsMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
	  <!-- <i class="fas fa-list d-lg-none"></i> -->
	  Projects
	</a>
	<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownProjectsMenuLink">
	  <li>
		<h5 class="dropdown-header">Recent</h5>
	  </li>
	  @for(project of recentProjects(); track $index){
		<li>
			<a class="dropdown-item" [routerLink]="['../projects', project.Id]">
			  {{project.InternalId || "No Id"}}
			</a>
		  </li>
	  }@empty {
		<li>
		  <a class="dropdown-item" href="#">
			No Recent Projects
		  </a>	
		</li>
	  }
	  <div class="dropdown-divider"></div>
	  <li><a class="dropdown-item" routerLink="../projects">View All Projects</a></li>
	  <li><a class="dropdown-item" [routerLink]="['../projects']"
		[queryParams]="{deactivated: true}">Closed
		Projects</a></li>
	</ul>
  </div>