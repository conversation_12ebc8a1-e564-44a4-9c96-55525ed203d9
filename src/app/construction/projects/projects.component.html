<app-construction-nav-bar>
	<app-construction-projects-filter></app-construction-projects-filter>
</app-construction-nav-bar>
<section class="container px-4">
	<!-- recent projects -->
	@if(!this.projectFilterOptions()?.deactivated){
	<section class="mb-3">
		<h2 class="fs-6">Recent</h2>
		<app-recent-projects [userProjectComponents]="userProjectComponents()"></app-recent-projects>
	</section>
	}
	<!-- all  projects -->
	<div class="mb-3">
		<!-- all projects-->
		<h2 class="page-title fs-6">All</h2>
		<app-construction-projects-list [userProjectComponents]="userProjectComponents()">
		</app-construction-projects-list>
	</div>
	<!-- footer  -->
	<footer class="d-flex justify-content-end mb-3">
		@if(total() > limit()){
		<ngb-pagination [page]="currentPage()" [maxSize]="5" [boundaryLinks]="true" [pageSize]="limit()"
			[collectionSize]="total()" (pageChange)="changePage($event)" [rotate]="true">
			<ng-template ngbPaginationFirst>First</ng-template>
			<ng-template ngbPaginationPrevious>Previous</ng-template>
			<ng-template ngbPaginationNext>Next</ng-template>
			<ng-template ngbPaginationLast>Last</ng-template>
		</ngb-pagination>
		}
	</footer>
	@if(projectsService.showHelpVideo()){
	<div class="mb-3">
		<h1 class="fs-4 mb-0">Get Started</h1>
	</div>
	<div class="ratio ratio-16x9">
		<iframe src="https://www.youtube.com/embed/WC9ak3vohuk?rel=0" title="YouTube video" allowfullscreen></iframe>
	</div>
	}
</section>