import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConstructionProjectsListComponent } from './ui/construction-projects-list/construction-projects-list.component';
import { ConstructionProjectsService } from '../shared/data-access/projects.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ConstructionProject, FilterOptions, ProjectsView } from '../shared/interfaces/construction-project';
import { UserProjectComponentsService } from '../shared/data-access/user-project-components.service';
import { ConstructionProjectsFilterComponent } from './ui/construction-projects-filter/construction-projects-filter.component';
import { NgbPagination, NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { UserProjectComponent } from '../shared/interfaces/dashboard';
import { RecentProjectsComponent } from './ui/recent-projects/recent-projects.component';
import { UserService } from 'src/app/shared/data-access/user.service';
import { ProjectComponentsService } from '../shared/data-access/project-components.service';
import { ConstructionNavBarComponent } from '../ui/construction-nav-bar/construction-nav-bar.component';
@Component({
    selector: 'app-projects',
    imports: [CommonModule, ConstructionProjectsListComponent, ConstructionProjectsFilterComponent, NgbPaginationModule, RecentProjectsComponent, ConstructionNavBarComponent],
    templateUrl: './projects.component.html',
    styleUrl: './projects.component.css'
})
export class ProjectsComponent {

  projectsService = inject(ConstructionProjectsService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  userProjectComponentsService= inject(UserProjectComponentsService);
  projectComponentService = inject(ProjectComponentsService);
  userService = inject(UserService);
  isLoading = signal<boolean>(false);
  projectFilterOptions = signal<FilterOptions>({} as FilterOptions);
  deactivated = this.projectsService.deactivated;
  title = this.projectsService.title;
  userProjectComponents = computed(() => this.userProjectComponentsService.cachedUserAllComponents()?.filter(x => x.IsActive));  
  isProjectLoading = this.projectsService.isLoading;
  projects = this.projectsService.projects;
  currentView = this.projectsService.currentView;
  total = this.projectsService.projectsTotal;
  limit = this.projectsService.limit;
  currentPage = this.projectsService.currentPage;
  projectsInfo: [] = [];
  allowedComponents = "info";

  // private readOnlyProjectComponents = toSignal(toObservable(this.userProjectComponentsService.cachedUserAllComponents).pipe(
  //   switchMap(project => this.userProjectComponentsService.getUserComponentsAllCached()),
  //   tap(components => this.userProjectComponents.set(components.filter(x => x.IsActive)))
  // ));

  constructor() {
    this.userProjectComponentsService.getUserComponentsAllCached().subscribe();
    this.aRoute.queryParamMap.subscribe(params => {    
      const searchText = params.get("search") ?? null;
      const orderBy = params.get("orderBy") ?? 'Title';
      const isReversed =  params.get("isReversed") === "true";
      const includeComponents = params.get("includeComponents") ?? null;
      const deactivated = (params.get("deactivated") === "true") ? true : false;
      const page = (params.get("page") ?? '1');      
      const view = params.get("view") ?? ProjectsView.LIST;

      if(params.get("isFavorites") === "true"){
        this.projectsService.isFavorites.set(true);
      }else{
        this.projectsService.isFavorites.set(false);
      }


      this.projectsService.deactivated.set(deactivated);
      this.projectsService.title.set(searchText);
      this.projectsService.internalId.set(searchText);  
      this.projectsService.orderBy.set(orderBy);
      this.projectsService.isReversed.set(isReversed);
      this.projectsService.includeComponents.set(includeComponents);
      this.projectsService.currentPage.set(parseInt(page));  
      this.projectsService.currentView.set(view as ProjectsView);

      if(view === ProjectsView.DETAILS){

        this.currentView.set(ProjectsView.DETAILS);
      }else{
        this.currentView.set(ProjectsView.LIST);
      }

      this.projectsService.initialize.set(true);
    });
  }

  changePage($event: any) {
    if($event){
      this.router.navigate(
        [], { queryParams: { page: $event }, queryParamsHandling: 'merge' });
    }
  }
}