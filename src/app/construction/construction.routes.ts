import { Route } from '@angular/router';
import { ConstructionComponent } from './construction.component';
import { UserAccessGuard } from '../shared/utils/guards/user-access-guard';
import { TeamUnauthorizedComponent } from './project/team-unauthorized/team.unauthorized.component';
import { TeamGuardService } from './utils/team-project-guard';

export const CONSTRUCTION_ROUTES: Route[] = [
	{path : '' , redirectTo : 'projects' , pathMatch : 'full'},
	{
		path: '',
		component: ConstructionComponent,
		children: [
			{
				path: 'projects',
				loadChildren: () => import('./projects/projects.routes').then(c => c.PROJECTS_ROUTES)
			},			
			{
				path: 'projects/:projectId',
				canActivate: [TeamGuardService],
				loadChildren: () => import('./project/project.routes').then(c => c.PROJECT_ROUTES)
			},
			{
				path: 'mechanics-diary',
				canActivate: [UserAccessGuard],
				data: { permissionGroup: 'global', resource: 'route-access', action: 'access-mechanics-diary' },
				loadComponent: () => import('./mechanics-diary/mechanics-diary.component').then(c => c.MechanicsDiaryComponent)
			},
			{
				path: 'mechanics-diary/:date',
				canActivate: [UserAccessGuard],
				data: { permissionGroup: 'global', resource: 'route-access', action: 'access-mechanics-diary' },
				loadComponent: () => import('./mechanics-diary/mechanics-diary.component').then(c => c.MechanicsDiaryComponent)
			},
			{
				path: 'diary',
				canActivate: [UserAccessGuard],
				data: { permissionGroup: 'global', resource: 'route-access', action: 'access-project-dailylog' },
				loadComponent: () => import('./diary/diary.component').then(c => c.DiaryComponent)
			},
			{
				path: 'team-unauthorized',
				component: TeamUnauthorizedComponent
			},
			{
				path: 'approvals',
				loadComponent: () => import('./approvals/approvals.component').then(c => c.ApprovalsComponent)
			},
			{
				path: 'reports',
				loadComponent: () => import('./reports/reports.component').then(c => c.ReportsComponent)
			}
		]
	},
	

	
];
