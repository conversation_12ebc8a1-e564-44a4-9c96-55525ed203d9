import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { ConstructionTeamsService } from '../shared/data-access/teams.service';
import { ConstructionProjectService } from '../shared/data-access/project.service';


@Injectable({  providedIn: 'root'})
export class TeamGuardService  {
  constructor(private teamService: ConstructionTeamsService, private projectService: ConstructionProjectService, private router: Router) {}
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return new Observable<boolean>(obs => {
      var projectId = route.params['projectId'];

      if (!projectId) {
        projectId = route.parent?.params['projectId'];
      }

      if(projectId){
        this.projectService.IsProjectOwner(projectId).subscribe({
          next: (isOwner) => {
            if (!isOwner) {
              this.teamService.IsTeamMember(projectId).subscribe({
                next: (isTeamMember) => {
                  console.log("TEAMGUARDSERVICE: isTeamMember: " + isTeamMember);
                  if (!isTeamMember) {
                    console.log("TEAMGUARDSERVICE: unauthorized");
                    this.router.navigate(['/construction', 'team-unauthorized']);
                    obs.next(false);
                  } else {
                    console.log("TEAMGUARDSERVICE: Authorized");
                    obs.next(true);
                  }
                },
                error: (err) => {
                  console.log(err);
                  console.log("TEAMGUARDSERVICE: ERROR");
                  obs.next(false);
                }
              });
            } else {
              console.log("TEAMGUARDSERVICE: isOwner: " + isOwner);
              obs.next(true);
            }
          },
          error: (err) => {
            console.log(err);
            console.log("TEAMGUARDSERVICE: ERROR");
            obs.next(false);
          }
        });  
      }else{
        console.log("TEAMGUARDSERVICE: projectId not found");
        this.router.navigate(['/construction', 'team-unauthorized']);
        obs.next(false);
      }

    });
  }
}
