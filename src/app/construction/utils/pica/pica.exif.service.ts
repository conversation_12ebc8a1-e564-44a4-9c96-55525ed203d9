﻿import { Injectable, OnDestroy } from '@angular/core';
import * as EXIF from 'exif-js';
import * as ExifReader from 'exifreader';
import { takeUntil, Observable, Subject, of } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class CivCastPicaExifService implements OnDestroy {
  private unsubscribe: Subject<void> = new Subject();

  public getExifOrientedImagePromise(tagInfo: ImageTagInfo): Observable<HTMLImageElement> {
    return new Observable<HTMLImageElement>(result => {
      this.getOrientationByFile(tagInfo.file).pipe(takeUntil(this.unsubscribe))
        .subscribe(
          orientation => {
            const exifOrientation = orientation;

            if (exifOrientation !== null && exifOrientation !== undefined) {
              const canvas: HTMLCanvasElement = document.createElement('canvas'),
                ctx = canvas.getContext('2d'),
                width = tagInfo.image.width,
                height = tagInfo.image.height;

              if ([5, 6, 7, 8].indexOf(exifOrientation) > -1) {
                canvas.width = height;
                canvas.height = width;
              } else {
                canvas.width = width;
                canvas.height = height;
              }

              if (ctx) {
                // transform context before drawing image
                switch (exifOrientation) {
                  case 2:
                    ctx.transform(-1, 0, 0, 1, width, 0);
                    break;
                  case 3:
                    ctx.transform(-1, 0, 0, -1, width, height);
                    break;
                  case 4:
                    ctx.transform(1, 0, 0, -1, 0, height);
                    break;
                  case 5:
                    ctx.transform(0, 1, 1, 0, 0, 0);
                    break;
                  case 6:
                    ctx.transform(0, 1, -1, 0, height, 0);
                    break;
                  case 7:
                    ctx.transform(0, -1, -1, 0, height, width);
                    break;
                  case 8:
                    ctx.transform(0, -1, 1, 0, 0, width);
                    break;
                  default:
                    ctx.transform(1, 0, 0, 1, 0, 0);
                }

                // Draw img into canvas
                ctx.drawImage(tagInfo.image, 0, 0, width, height);

                const img = new Image();

                if ([5, 6, 7, 8].indexOf(exifOrientation) > -1) {
                  img.width = height;
                  img.height = width;
                } else {
                  img.width = width;
                  img.height = height;
                }

                img.onload = () => {
                  result.next(img);
                  result.complete();
                };
                var quality = 0.7;
                img.src = canvas.toDataURL('image/jpeg', quality);
              }
            } else {
              result.next(tagInfo.image);
              result.complete();
            }
          },
          err => {
            result.error(tagInfo.image);
            result.complete();
          }
        );
    });
  }

  public getAllTags(image: HTMLImageElement): Observable<any> {
    var allTags = new Subject<number>();

    var tags = EXIF.getAllTags(image);

    allTags.next(tags);
    allTags.complete();

    return allTags.asObservable();
  }

  public getOrientation(image: HTMLImageElement): Observable<number> {
    var orientationTag = new Subject<number>();

    var o = EXIF.getTag(image, "Orientation");   
    orientationTag.next(o);

    return orientationTag.asObservable();
  }

  public getOrientationByFile(image: File): Observable<number> {
    var orientationTag = new Subject<number>();

    var tags = ExifReader.load(image).then((result) => {
      orientationTag.next(result.Orientation?.value as number ?? -1);      
      orientationTag.complete(); 
    });

    return orientationTag.asObservable();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}


export interface ImageTagInfo{
  image: HTMLImageElement, 
  file: File;
}