import { Injectable, inject, signal } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { AccessEffects, AccessResponse } from '../../shared/interfaces/access';
import { AccessService } from 'src/app/shared/data-access/access-service';

@Injectable({
    providedIn: 'root'
})
export class ConstructionProjectAccessGuard {
    router = inject(Router);
    aRoute = inject(ActivatedRoute)
    accessService = inject(AccessService);
    toastrService = inject(ToastrService);    
    storeAccessGuardChecks = signal<Array<AccessGuardCheck>>([])    
    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
        return new Observable<boolean>(obs => {
            var request = route.data['accessRequest'];


            let hasAccess = this.storeAccessGuardChecks().find(x => x.action === request.action && x.resource === request.resource);
            if(hasAccess){
                if(hasAccess.response.Access === AccessEffects.Allow){
                    obs.next(true);                      
                }else{
                    obs.next(false);                    
                }              

                obs.complete();
            }else{
                this.accessService.CheckAccessByRequest(request).subscribe({
                    next: (result) => {
                        if (result.Access === AccessEffects.Allow) {
                            this.storeAccessGuardChecks.set([...this.storeAccessGuardChecks(), {action: request.action, resource: request.resource, response: result}]);
                            this.accessService.LatestRouteAccess = result;
                            obs.next(true);
                            obs.complete();
                        } else {
                            try {
                                var id = route.parent?.params['projectId'];
                                if (!id) {
                                    id = route.parent?.parent?.params['projectId'];
                                    if (!id) {
                                        id = route.parent?.parent?.parent?.params['projectId'];
                                        if (!id) {
                                            id = route.parent?.parent?.parent?.parent?.params['projectId'];
                                            if (!id) {
                                                id = route.parent?.parent?.parent?.parent?.parent?.params['projectId'];
                                                if (!id) {
                                                    id = route.parent?.parent?.parent?.parent?.parent?.parent?.params['projectId'];
                                                }
                                            }
                                        }
                                    }
                                }
    
                                this.router.navigate(['construction', 'projects', id, 'unauthorized']);
    
                                obs.next(false);
                                obs.complete();
                            } catch (error) {
                                this.router.navigate(['/unauthorized']);
                                obs.next(false);
                                obs.complete();
                            }
                        }
                    },
                    error: (error) => {
                        obs.error(error);
                    }
                });
            }

   
        });
    }
}

export interface AccessGuardCheck{
    action: string;
    resource: string;
    response: AccessResponse;
}