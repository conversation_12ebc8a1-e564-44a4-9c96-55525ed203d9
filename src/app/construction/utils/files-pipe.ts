import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
	name: 'filesize',
	standalone: true,
	pure: false	
})
/**
 * Represents a pipe that transforms a file size into a human-readable format.
 */
export class FileSize implements PipeTransform {
	private cachedData: any = null;
	private cachedSize = 0;

	constructor() { }

	/**
	 * Transforms the given file size into a human-readable format.
	 * @param size The size of the file in bytes.
	 * @returns The file size in a human-readable format.
	 */
	transform(size: number): any {
		if (size !== this.cachedSize) {
			this.cachedData = null;
			this.cachedSize = size;
			this.cachedData = this.humanFileSize(size, true);
		}

		return this.cachedData;
	}

	/**
	 * Converts the given file size into a human-readable format.
	 * @param bytes The size of the file in bytes.
	 * @param si Specifies whether to use the SI (decimal) or binary system for units.
	 * @param dp The number of decimal places to round the file size to.
	 * @returns The file size in a human-readable format.
	 */
	humanFileSize(bytes: any, si = false, dp = 1) {
		const thresh = si ? 1000 : 1024;

		if (Math.abs(bytes) < thresh) {
			return bytes + ' B';
		}

		const units = si
			? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
			: ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
		let u = -1;
		const r = 10 ** dp;

		do {
			bytes /= thresh;
			++u;
		} while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);


		return bytes.toFixed(dp) + ' ' + units[u];
	}
}
  

