import { Component, OnInit, computed, effect, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MomentDateFormatterService } from 'src/app/shared/data-access/moment-date-formatter-service';
import { ActivatedRoute, Router } from '@angular/router';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { ConstructionProjectsService } from '../shared/data-access/projects.service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { ToastrService } from 'ngx-toastr';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { Approval, ApprovalInfo, DateFilterInfo } from '../shared/interfaces/approval';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { AccessEffects } from '../../shared/interfaces/access';
import { Observable, Subscription, debounceTime, forkJoin } from 'rxjs';
import { ApprovalService } from '../shared/data-access/approval.service';
import { ProjectComponentsService } from '../shared/data-access/project-components.service';
import { ApprovalComponent } from './ui/approval/approval.component';
import { DailyLogProjectUserService } from '../shared/data-access/daily-log-project-user.service';
import { OrderPipe } from 'src/app/shared/utils/pipes/order-by-pipe';
import { DatePickerModule } from 'primeng/datepicker';
import { MultiSelectModule } from 'primeng/multiselect';
import { ConstructionProject } from '../shared/interfaces/construction-project';
import { RolesDropDownComponent } from 'src/app/shared/ui/role-drop-down/roles-drop-down.component';
import { ProjectsListDropDownComponent } from '../ui/projects-list-drop-down/projects-list-drop-down.component';
import { ProjectListDropdownService } from '../shared/data-access/project-list-dropdown.service';


@Component({
  selector: 'app-approvals',
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RolesDropDownComponent,
    ProjectsListDropDownComponent,
    MultiSelectModule, DatePickerModule, OrderPipe, ApprovalComponent, NgbPaginationModule],
  standalone: true,
  templateUrl: './approvals.component.html',
  styleUrl: './approvals.component.css'
})
export class ApprovalsComponent implements OnInit {
  momentFormatter = inject(MomentDateFormatterService);
  approvalService = inject(ApprovalService);
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  accessService = inject(AccessService);
  projectsService = inject(ConstructionProjectsService);
  projectComponentService = inject(ProjectComponentsService);
  dailyLogProjectUserService = inject(DailyLogProjectUserService);
  toastrService = inject(ToastrService);
  confirmService = inject(ConfirmService);
  projectListDropdownService = inject(ProjectListDropdownService);
  hasTeamProjects = signal<boolean>(true);
  Approvals = signal<Array<ApprovalInfo>>([]);
  ApprovalLevels = this.approvalService.userApprovalLevels;
  selectedProjects: Array<ConstructionProject> = [];
  access: string = AccessEffects.ReadOnly;
  searchControl = new UntypedFormControl();
  payrollLevel: number = this.approvalService.payrollLevel;
  isApprovalsLoading = computed(() => this.approvalService.isLoading() || this.projectSelectionLoading());
  isApproving = signal<boolean>(false);
  sortDataItems: Array<any> = [
    {
      Name: 'Date',
      Sort: 0
    },
    {
      Name: 'Foreman',
      Sort: 0
    },
    {
      Name: 'ProjectId',
      Sort: 0
    },
    {
      Name: 'Project',
      Sort: 0
    }
  ];

  dateFilterInfo: DateFilterInfo = {} as DateFilterInfo;
  approvalGroups: Array<any> = new Array<any>();
  isPayroll = computed(() => { return this.approvalService.userApprovalLevel() === 999 });

  filterSubscription: Subscription | null = new Subscription();

  receivedApprovalLevels = false;
  sortName = this.approvalService.sortName;
  sortOrder = this.approvalService.sort;
  currentPage = this.approvalService.currentPage;
  singleDate = this.approvalService.singleDate;
  startDate = this.approvalService.startDate;
  endDate = this.approvalService.endDate;
  projectSelectionLoading = this.projectListDropdownService.projectsSelectionLoading;
  userApprovalLevel = this.approvalService.userApprovalLevel;
  selectedApprovalLevel = signal<string>('all');
  //currentSelectedLevel = this.currentUserLevel;
  currentType = signal<string>('unapproved');
  currentFilter = signal<string>('Unapproved');
  dateType = signal<string>('SingleDay');
  projectFilterType = signal<string>('TeamProjects');
  expandAllApprovals = signal<boolean>(false);
  rangeDatesInfo: Date[] = [];
  singleDateInfo: Date | undefined;
  total = this.approvalService.approvalsTotal;
  limit = this.approvalService.limit;
  hasSelectedApprovals = computed(() => {
    let selectedApprovals = this.Approvals().filter(x => x.IsSelected === true);
    return selectedApprovals.length > 0;
  });

  constructor() {
    this.approvalService.initializeApprovals.set(true);
    this.approvalService.initializeApprovalAccess();
    this.approvalService.initializeUserApprovalLevels();
    this.approvalService.initializeUserApprovalLevel.set(true);

    effect(() => {
      let approvalInfos = new Array<ApprovalInfo>();
      for (let approval of this.approvalService.approvals()) {
        let approvalInfo = {} as ApprovalInfo;
        approvalInfo.Approval = approval;
        approvalInfo.ProjectTitle = approval.ProjectTitle ?? '';
        approvalInfo.ProjectInternalId = approval.ProjectInternalId ?? '';
        approvalInfos.push(approvalInfo);
      }

      this.Approvals.set(approvalInfos);
    })

    this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(value => {
      let searchValue = value?.trim() ?? '';
      if (!searchValue) {
        searchValue = null;
      }

      this.router.navigate([], {
        queryParams: { search: searchValue, page: 1 },
        queryParamsHandling: 'merge'
      });
    });


    this.aRoute.queryParams.subscribe(params => {

      if (params["approval-level"]) {
        this.approvalService.approvalLevel.set(parseInt(params["approval-level"]));
      } else {
        this.approvalService.approvalLevel.set(this.userApprovalLevel() ?? -1);
      }

      if (params["approval-type"]) {
        this.approvalService.approvalType.set(params["approval-type"]);
      } else {
        this.approvalService.approvalType.set("unapproved");
      }

      if (params["approval-type"] && params["approval-level"]) {
        if (params["approval-type"] === "all") {
          this.selectedApprovalLevel.set(params["approval-type"]);
        } else {
          this.selectedApprovalLevel.set(params["approval-type"] + params["approval-level"]);
        }

      } else {
        if (this.userApprovalLevel()) {
          this.selectedApprovalLevel.set("unapproved" + this.userApprovalLevel());
        }
      }

      if (params["currentFilter"]) {
        this.currentFilter.set(params["currentFilter"]);
      } else {
        this.currentFilter.set("Unapproved");
      }

      if (params["sortOrder"]) {
        this.approvalService.sort.set(parseInt(params["sortOrder"]));
      } else {
        this.approvalService.sort.set(0);
      }

      if (params["sortName"]) {
        this.approvalService.sortName.set(params["sortName"]);
      } else {
        this.approvalService.sortName.set("ProjectId");
      }

      if (params["search"]) {
        this.approvalService.search.set(params["search"]);
        this.searchControl.setValue(params["search"], { emitEvent: false });
      } else {
        this.approvalService.search.set(null);
        this.searchControl.setValue(null, { emitEvent: false });
      }

      if (params["page"]) {
        this.approvalService.currentPage.set(parseInt(params["page"]));
      } else {
        this.approvalService.currentPage.set(1);
      }

      if (params["datetype"]) {
        this.dateType.set(params["datetype"]);
      } else {
        this.dateType.set("SingleDay");
      }

      if (params["role"]) {
        this.approvalService.roleId.set(params["role"]);
      } else {
        this.approvalService.roleId.set(null);
      }

      if (params["startDate"] && params["endDate"]) {

        this.approvalService.startDate.set(params["startDate"]);
        this.approvalService.endDate.set(params["endDate"]);

        if (params["datetype"] === "SingleDay") {
          this.singleDateInfo = new Date(params["startDate"]);
        } else if (params["datetype"] === "RangeOfDays") {
          this.rangeDatesInfo = [new Date(params["startDate"]), new Date(params["endDate"])];
        }

      } else {
        this.approvalService.startDate.set(null);
        this.approvalService.endDate.set(null);
      }

      if (params["projectFilterType"]) {
        this.projectFilterType.set(params["projectFilterType"]);

      } else {
        this.projectFilterType.set("TeamProjects");

      }

      // if (this.projectFilterType() === "TeamProjects") {
      //   this.projectListDropdownService.selectionProjectType.set("team");
      // } else if(this.projectFilterType() === "AllProjects") {
      //   this.projectListDropdownService.selectionProjectType.set("all");
      // }else{
      //   this.projectListDropdownService.selectionProjectType.set("team");
      // }

      this.projectListDropdownService.selectionProjectType.set("default");
      this.projectListDropdownService.deactivated.set(false);
    });

    effect(() => {
      if (this.projectListDropdownService.projectsForSelection()) {        
        this.projectListDropdownService.selectedProjects.set(this.projectListDropdownService.projectsForSelection() as Array<ConstructionProject>);       
      }
    });

    effect(() => {
      if(this.projectListDropdownService.selectedProjects()) {
        this.approvalService.selectedProjectsIds.set(this.projectListDropdownService.selectedProjects().map(x => x.Id));
      }
    });

    effect(() => {
      if (this.userApprovalLevel()) {
        this.setupApprovalLevels(this.userApprovalLevel() ?? -1);
        this.selectedApprovalLevel.set('unapproved' + this.userApprovalLevel());
        this.approvalService.approvalLevel.set(this.userApprovalLevel() ?? -1);
      }

    });


    effect(() => {
      if (this.expandAllApprovals()) {
        for (const item of this.Approvals()) {
          item.ShowTimeCard = true;
        }
      } else {
        for (const item of this.Approvals()) {
          item.ShowTimeCard = false;
        }
      }
    });
  }


  clearDateFilterRange() {
    if (this.dateFilterInfo) {
      this.dateFilterInfo.dateRangeStart = null;
      this.dateFilterInfo.dateRangeEnd = null;
    }

    this.router.navigate([], {
      queryParams: { startDate: null, endDate: null, page: 1 },
      queryParamsHandling: 'merge'
    });
  }

  ngOnInit() {

  }

  onDateInputChange(value: any) {
    if (this.isValidDate(value)) {
      this.onSingleDateSelection(new Date(value));
    }

  }

  // Utility function to validate the date
  isValidDate(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }


  onSingleDateSelection(date: Date) {
    this.router.navigate([], {
      queryParams: { page: 1, startDate: this.formatDate(date), endDate: this.formatDate(date) },
      queryParamsHandling: 'merge'
    });
  }

  onRangeDateSelection(date: Date) {


    if (this.rangeDatesInfo.length === 2) {
      if (this.rangeDatesInfo[0] && this.rangeDatesInfo[1]) {
        this.router.navigate([], {
          queryParams: { startDate: this.formatDate(this.rangeDatesInfo[0]), endDate: this.formatDate(this.rangeDatesInfo[1]), page: 1 },
          queryParamsHandling: 'merge'
        });
      }
    }
  }

  formatDate(date: Date): string {
    if (!date)
      return '';
    return `${date.getMonth() + 1}-${date.getDate()}-${date.getFullYear()}`;
  }

  setProjectFilter(filterType: any) {
    this.router.navigate([], {
      queryParams: { projectFilterType: filterType, page: 1 },
      queryParamsHandling: 'merge'
    });
  }

  filterApprovals(approvalLevelId: any) {
    var approvalInfo: [string, number] | null = null;
    for (let group of this.approvalGroups) {
      if (approvalInfo !== null) {
        break;
      }

      for (let item of group.items) {
        if (item.id === approvalLevelId) {

          this.router.navigate([], {
            queryParams: { "approval-type": group.groupId, "approval-level": item.level, page: 1 },
            queryParamsHandling: 'merge'
          });
          break;
        }
      }
    }
  }

  setupApprovalLevels(level: number) {
    this.approvalGroups = [
      {
        group: "Show me all timesheets...",
        groupId: "all",
        items: [
          {
            name: "All Timesheets",
            id: "all",
            level: 999
          }
        ]
      },
      {
        group: "Unapproved",
        groupId: "unapproved",
        items: [
          {
            name: "Unapproved at L1",
            id: "unapproved1",
            level: 1
          },
          {
            name: "Unapproved at L2",
            id: "unapproved2",
            level: 2
          },
          {
            name: "Unapproved at L3",
            id: "unapproved3",
            level: 3
          },
          {
            name: "Unapproved at L4",
            id: "unapproved4",
            level: 4
          },
          {
            name: "Unapproved at L5",
            id: "unapproved5",
            level: 5
          },
          {
            name: "Unapproved at Payroll",
            id: "unapproved999",
            level: 999
          }
        ]
      },
      {
        group: "Approved",
        groupId: "approved",
        items: [
          {
            name: "Approved at L1",
            id: "approved1",
            level: 1
          },
          {
            name: "Approved at L2",
            id: "approved2",
            level: 2
          },
          {
            name: "Approved at L3",
            id: "approved3",
            level: 3
          },
          {
            name: "Approved at L4",
            id: "approved4",
            level: 4
          },
          {
            name: "Approved at L5",
            id: "approved5",
            level: 5
          },
          {
            name: "Approved at Payroll",
            id: "approved999",
            level: 999
          }
        ]
      }
    ];

    for (let group of this.approvalGroups) {
      for (let item of group.items) {
        if (item.id != "all") {
          if (item.level === level) {
            item.name += " (My Level)";
          }
        }

      }
    }
  }


  setDateFilter(item: string) {
    this.rangeDatesInfo = [];
    this.singleDateInfo = undefined;
    this.router.navigate([], {
      queryParams: { datetype: item, page: 1, startDate: null, endDate: null, singleDate: null },
      queryParamsHandling: 'merge'
    });
  }

  onItemSelect(item: any) {
    this.router.navigate([], {
      queryParams: { page: 1 },
      queryParamsHandling: 'merge'
    });

    var ids = item.value.map((x: any) => x.Id) as Array<string>;

    this.approvalService.selectedProjectsIds.set(ids);


  }

  expand() {
    this.expandAllApprovals.set(!this.expandAllApprovals());
  }

  sortData(sort: any) {
    let sortName = sort.Name;
    let sortOrder = this.sortOrder();;


    if (sortName === this.sortName()) {
      if (sortOrder === 0) {
        sortOrder = 1;
      } else if (sortOrder === 1) {
        sortOrder = 0;
      }
    }

    this.router.navigate([], {
      queryParams: { sortOrder: sortOrder, sortName: sortName, page: 1 },
      queryParamsHandling: 'merge'
    });
  }


  selectAll() {
    this.Approvals.update(approvals => {
      for (const item of approvals) {
        if (item.Approval?.FinalApprovalDate === null) {
          item.IsSelected = true;
        }
      }
      return [...approvals];
    });
  }

  unSelectAll() {
    this.Approvals.update(approvals => {
      for (const item of approvals) {
        if (item.Approval?.FinalApprovalDate === null) {
          item.IsSelected = false;
        }
      }
      return [...approvals];
    });
  }

  selectApproval(approval: ApprovalInfo) {
    this.Approvals.update(approvals => {
      return approvals.map(a => {
        if (a.Approval?.Id === approval.Approval?.Id && a.Approval.FinalApprovalDate === null) {
          return { ...a, IsSelected: !a.IsSelected };
        }
        return a;
      });
    });
  }

  changeApprovalListPage(pageNum: any) {
    console.log(pageNum);

    this.router.navigate([], {
      queryParams: { page: pageNum },
      queryParamsHandling: 'merge'
    });

  }

  approveSelected() {
    if (!this.userApprovalLevel()) {
      this.toastrService.error("You do not have the correct level to approve time sheets.");
      return;
    }
    this.isApproving.set(true);
    const approvals = Array<Observable<any>>();

    var selectedApprovals = new Array<ApprovalInfo>();
    for (const a of this.Approvals()) {
      if (a.Approval) {
        if (a.IsSelected) {
          selectedApprovals.push(Object.assign({}, a));
          approvals.push(this.approvalService.Approve(a.Approval.Id, this.userApprovalLevel() ?? -1));
        }
      }
    }

    if (approvals.length > 0) {
      //this.fiilterLoaded = false;
      forkJoin(approvals).subscribe({
        next: (result) => {
          this.isApproving.set(false);
          for (let approval of selectedApprovals) {
            if (approval.Approval) {
              this.removeApproval(approval.Approval);
            }
          }
        },
        error: (err) => {
          console.log(err);
          this.toastrService.error("Error approving time sheets");
          this.isApproving.set(false);
        }
      });
    }
  }

  removeApproval(approval: Approval) {
    if (approval) {
      this.Approvals.update(approvals => approvals.filter(x => x.Approval?.Id !== approval.Id));
    }
  }

  updateApproval(approval: any) {

  }

  clearDate() {
    this.router.navigate([], {
      queryParams: { startDate: null, endDate: null, page: 1 },
      queryParamsHandling: 'merge'
    });
  }

  unapprove(approval: Approval) {
    if (!this.userApprovalLevel()) {
      this.toastrService.error("You do not have the correct level to unapprove time sheets.");
      return;
    }

    this.confirmService.open('Do you want to unpprove this time sheet?').result.then(item => {
      this.approvalService.Unapprove(approval.Id, this.userApprovalLevel() ?? -1);
    });
  }
}