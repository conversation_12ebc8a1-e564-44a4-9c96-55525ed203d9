import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, computed, inject, signal, Signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Approval, ApprovalEditMode } from 'src/app/construction/shared/interfaces/approval';
import { DailyLogTimeSheetComponent } from 'src/app/construction/project/daily-log/ui/daily-log-time-sheet/daily-log-time-sheet.component';
import { DailyLogProjectUserComponent, NotesComponentProjectUser, SiteConditionsComponentProjectUser, TimeCardComponentProjectUser, WeatherComponentProjectUser, PhotosComponentProjectUser } from 'src/app/construction/shared/interfaces/daily-log-project-user';
import { DailyLogProjectUserService } from 'src/app/construction/shared/data-access/daily-log-project-user.service';
import { ToastrService } from 'ngx-toastr';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { forkJoin, of } from 'rxjs';
import { switchMap, tap, catchError } from 'rxjs/operators';
import { ApprovalService } from 'src/app/construction/shared/data-access/approval.service';
import { NgbDate, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { DailyLogComponentIdentifiers, DailyLogInfoComponentSerializerInfo, DailyLogViews } from 'src/app/construction/shared/interfaces/daily-log-shared';
import { DailyLogNotesComponent } from 'src/app/construction/project/daily-log/ui/daily-log-notes/daily-log-notes.component';
import { DailyLogPhotosComponent } from 'src/app/construction/project/daily-log/ui/daily-log-photos/daily-log-photos.component';
import { DailyLogSiteConditionsComponent } from 'src/app/construction/project/daily-log/ui/daily-log-site-conditions/daily-log-site-conditions.component';
import { DailyLogWeatherComponent } from 'src/app/construction/project/daily-log/ui/daily-log-weather/daily-log-weather.component';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { ApprovalHistoryComponent } from '../approval-history/approval-history.component';
import { AccessEffects } from 'src/app/shared/interfaces/access';
import { clone } from 'src/app/construction/utils/clone';
import { DailyLogNotesService } from 'src/app/construction/project/daily-log/shared/services/daily-log-notes.service';
import { DailyLogTimeSheetService } from 'src/app/construction/project/daily-log/shared/services/daily-log-time-sheet.service';
import { DailyLogSiteConditionsService } from 'src/app/construction/project/daily-log/shared/services/daily-log-site-conditions.service';
import { DailyLogWeatherService } from 'src/app/construction/project/daily-log/shared/services/daily-log-weather.service';
import { DailyLogPhotosService } from 'src/app/construction/project/daily-log/shared/services/daily-log-photos.service';
import { DailyLogDirtyService } from 'src/app/construction/project/daily-log/shared/services/daily-log-dirty.service';
import { RoleSettingsDiaryService } from 'src/app/account/shared/data-access/role-settings-diary-service';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';
@Component({
    selector: 'app-approval',
    imports: [CommonModule, DailyLogTimeSheetComponent, NgbTooltip,
        ApprovalHistoryComponent,
        DailyLogNotesComponent,
        DailyLogPhotosComponent,
        DailyLogSiteConditionsComponent,
        DailyLogTimeSheetComponent,
        DailyLogWeatherComponent
      ],
    templateUrl: './approval.component.html',
    styleUrl: './approval.component.css',
  providers: [DailyLogPhotosService, DailyLogNotesService, DailyLogTimeSheetService, DailyLogSiteConditionsService, DailyLogWeatherService, ConstructionPhotosService] 
})
export class ApprovalComponent implements OnChanges {

  @Input() approval: Approval = {} as Approval;

  @Input() view: string = "view";
  @Output() remove = new EventEmitter<Approval>();

  private notesService = inject(DailyLogNotesService);
  private timeSheetService = inject(DailyLogTimeSheetService);
  private siteConditionsService = inject(DailyLogSiteConditionsService);
  private weatherService = inject(DailyLogWeatherService);
  private photosService = inject(DailyLogPhotosService);
  private dirtyService = inject(DailyLogDirtyService);
  private roleSettingsDiaryService = inject(RoleSettingsDiaryService);

  dailyLogProjectUserService = inject(DailyLogProjectUserService);
  toastrService = inject(ToastrService);
  confirmService = inject(ConfirmService);
  approvalService = inject(ApprovalService);
  userLevel = this.approvalService.approvalLevel;
  dailyLogUserComponent = signal<DailyLogProjectUserComponent>({} as DailyLogProjectUserComponent);
  storedDailyLogUserComponent: DailyLogProjectUserComponent | null = null;
  timeCardComponent = signal<TimeCardComponentProjectUser>({} as TimeCardComponentProjectUser);
  isRunning = signal<boolean>(false);
  isSaving = signal<boolean>(false);
  isApproving= signal<boolean>(false);
  isDeleting = signal<boolean>(false);
  isUnapproving = computed(() => this.approvalService.unApprovingIds()?.includes(this.approval.Id));
  noTimeCard = signal<boolean>(false);
  approvalAccess = this.approvalService.approvalAccess;
  approvalEditMode = signal<ApprovalEditMode>(ApprovalEditMode.NonEdit);
  timeSheetComponentView = signal<ComponentView>(ComponentView.ReadOnly);
  public readonly EDIT_MODES: typeof ApprovalEditMode = ApprovalEditMode;
  public readonly APPROVAL_VIEWS: typeof ApprovalViews = ApprovalViews;
  public readonly DAILY_LOG_VIEWS: typeof DailyLogViews = DailyLogViews;
  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;
  public readonly DAILY_LOG_COMPONENT_IDENTIFIERS: typeof DailyLogComponentIdentifiers = DailyLogComponentIdentifiers;
  payrollLevel: number = this.approvalService.payrollLevel;
  isEditQtyOnly = computed(() => {
    // If approvalAccess is undefined (no approvals), return false
    if (!this.approvalService.approvalAccess()) {
      return false;
    }
    
    if(!this.isPayroll() && this.approvalService.approvalAccess()?.EditQtyNotes ===  AccessEffects.Allow){
      return true;
    }

    return false;
  });
  isPayroll = computed(() => {
    return this.approvalService.approvalLevel() === this.payrollLevel;
  });

  approvalView = signal<ApprovalViews>(ApprovalViews.TimeSheet);
  public readonly COMPONENTS_VIEWS: typeof ComponentView = ComponentView;

  /**
   * Checks if a section is visible based on user role settings
   * @param sectionName The name of the section to check
   * @returns A Signal<boolean> indicating if the section is visible
   */
  isSectionVisible(sectionName: string): Signal<boolean> {
    return computed(() => {
      // Directly use the roleSettingsDiaryService.diarySections() signal
      // This ensures reactivity when the settings change
      const diarySettings = this.roleSettingsDiaryService.diarySections();
      
      // If we have settings, check if the section is visible
      if (diarySettings && diarySettings.length > 0) {
        const section = diarySettings.find(s => s.Component === sectionName);
        return section ? section.SettingValue === 'Show' : true;
      }
      
      // If no settings are available, default to showing the section
      return true;
    });
  }

  /**
   * Checks if a component has time sheet data
   */
  hasTimeSheetData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD) {
      const timeCards = component.TimeCards;
      // Check if there are employees, cost codes, or equipment trackers with data
      return !!(
        (timeCards?.Employees && timeCards.Employees.length > 0) || 
        (timeCards?.CostCodes && timeCards.CostCodes.length > 0) ||
        (timeCards?.EquipmentTrackers && timeCards.EquipmentTrackers.length > 0)
      );
    }
    return false;
  }

  /**
   * Checks if a component has notes data
   */
  hasNotesData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.NOTES) {
      return !!component.Note;
    }
    return false;
  }

  /**
   * Checks if a component has photos data
   */
  hasPhotosData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.PHOTOS) {
      return !!(component.Photos && component.Photos.length > 0);
    }
    return false;
  }

  /**
   * Checks if a component has site conditions data
   */
  hasSiteConditionsData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS) {
      return !!component.SiteCondition;
    }
    return false;
  }

  /**
   * Checks if a component has weather data
   */
  hasWeatherData(component: any): boolean {
    if (component?.ComponentIdentifier === DailyLogComponentIdentifiers.WEATHER) {
      return !!(
        component.Temperature || 
        (component.SelectedSkys && component.SelectedSkys.length > 0)
      );
    }
    return false;
  }
  
  /**
   * Checks if any component is dirty for the current project
   * @returns true if any component is dirty, false otherwise
   */
  checkDirtyState(): boolean {
    if (!this.approval?.ProjectId) return false;
    
    // Get the project map from the dirtyService
    const dirtyMap = this.dirtyService.dirtyMap();
    const projectMap = dirtyMap.get(this.approval.ProjectId);
    
    if (!projectMap) return false;
    
    // Check if any component is dirty
    for (const [, isDirty] of projectMap.entries()) {
      if (isDirty) return true;
    }
    
    return false;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["approval"] && changes["approval"].currentValue != changes["approval"].previousValue) {
      const dateParts = this.approval.DailyLogTimeStamp?.toString().split('-');
      const currentDate = dateParts ? new NgbDate(
        parseInt(dateParts[0]),
        parseInt(dateParts[1]),
        parseInt(dateParts[2].split('T')[0])
      ) : new NgbDate(0, 0, 0);
      if (this.approval.DailyLogId) {
        this.isRunning.set(true);
        this.dailyLogProjectUserService.getDailyLogById(this.approval.ProjectId, this.approval.DailyLogId).subscribe({
          next: (result) => {
            if (result) {
              this.dailyLogUserComponent.set(result);
              var tc = result.Components.find(c => c.ComponentIdentifier == DailyLogComponentIdentifiers.TIMECARD) as TimeCardComponentProjectUser;
              this.timeCardComponent.set(tc);

              const notesComponent = result.Components.find(
                (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.NOTES
              ) as NotesComponentProjectUser;

              this.notesService.setInitialData(
                notesComponent,
                this.timeSheetComponentView(),
                !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
              );

              const timeSheetComponent = result.Components.find(
                (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD
              ) as TimeCardComponentProjectUser;

              this.timeSheetService.setInitialData(
                timeSheetComponent,
                this.timeSheetComponentView(),
                !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
              );

              this.timeSheetService.currentDate.set(currentDate);
              this.timeSheetService.projectId.set(this.approval.ProjectId);
              
              // Initialize site conditions service
              const siteConditionsComponent = result.Components.find(
                (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS
              ) as SiteConditionsComponentProjectUser | null;
              
              this.siteConditionsService.setInitialData(
                siteConditionsComponent,
                this.timeSheetComponentView(),
                !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
              );
              
              // Initialize weather service
              const weatherComponent = result.Components.find(
                (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.WEATHER
              ) as WeatherComponentProjectUser | null;
              
              this.weatherService.setInitialData(
                weatherComponent,
                this.timeSheetComponentView(),
                !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
              );

              // Initialize photos service
              const photosComponent = result.Components.find(
                (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.PHOTOS
              ) as PhotosComponentProjectUser | null;
              
              this.photosService.setInitialData(
                photosComponent,
                this.timeSheetComponentView(),
                !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
              );
            } else {
              this.toastrService.error("Diary not found for this approval");
            }

            this.isRunning.set(false);
          },
          error: (error) => {
            this.isRunning.set(false);
            console.log(error);
          }
        });
      } else {
        this.toastrService.error("Diary not found for this approval");
      }
    }
  }

  setApprovalView(view: ApprovalViews){
    this.approvalView.set(view);
    
    // If we're not in edit mode, ensure all components are in read-only mode
    if (this.approvalEditMode() === ApprovalEditMode.NonEdit) {
      this.notesService.view.set(ComponentView.ReadOnly);
      this.timeSheetService.view.set(ComponentView.ReadOnly);
      this.siteConditionsService.view.set(ComponentView.ReadOnly);
      this.weatherService.view.set(ComponentView.ReadOnly);
      this.photosService.view.set(ComponentView.ReadOnly);
    }
  }
  
  /**
  * Saves the daily log by validating and updating its components, then persisting via DailyLogProjectUserService.
  * Returns of(null) on validation failure to signal to callers (e.g., approve) that the save did not proceed, ensuring proper chaining.
 */
  saveDailyLog() {
    let dluProjectUserComponent = this.dailyLogUserComponent() as DailyLogProjectUserComponent;
    this.isSaving.set(true);
    dluProjectUserComponent._t = ["BaseEntity", "ProjectUserComponent", "DailyLogUserComponent"];

    if(dluProjectUserComponent.Id != this.approval.DailyLogId){
      this.toastrService.error("Daily Log Id does not match approval");
      this.isSaving.set(false);
      return of(null);
    }

    if(dluProjectUserComponent.ProjectId != this.approval.ProjectId){
      this.toastrService.error("Project Id does not match approval");
      this.isSaving.set(false);
      return of(null);
    }

    var validate = this.dailyLogProjectUserService.validateDailyLog(dluProjectUserComponent);

    if(!validate.isValid){
      for(let message of validate.messages){
        this.toastrService.error(message);
      }
      this.isSaving.set(false);
      return of(null);
    }else{
      dluProjectUserComponent.Id = this.approval.DailyLogId;

      for (let component of dluProjectUserComponent.Components) {
        component._t = DailyLogInfoComponentSerializerInfo.getInstance().getSerializerInfo(component.ComponentIdentifier);
      }
  
      // Update notes component
      const notesComponent = dluProjectUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.NOTES
      ) as NotesComponentProjectUser;
  
      if (notesComponent) {
        const updatedNotes = this.notesService.getUpdatedComponent();
        notesComponent.Note = updatedNotes.Note;
      }
      
      // Update site conditions component
      const siteConditionsComponent = dluProjectUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS
      ) as SiteConditionsComponentProjectUser;
      
      if (siteConditionsComponent) {
        const updatedSiteConditions = this.siteConditionsService.getUpdatedComponent();
        siteConditionsComponent.SiteCondition = updatedSiteConditions.SiteCondition;
      }
      
      // Update weather component
      const weatherComponent = dluProjectUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.WEATHER
      ) as WeatherComponentProjectUser;
      
      if (weatherComponent) {
        const updatedWeather = this.weatherService.getUpdatedComponent();
        weatherComponent.Temperature = updatedWeather.Temperature;
        weatherComponent.SelectedSkys = updatedWeather.SelectedSkys;
      }
      
      // Update timecard component
      const timeCardComponent = dluProjectUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD
      ) as TimeCardComponentProjectUser;
      
      if (timeCardComponent) {
        const updatedTimeCard = this.timeSheetService.getUpdatedComponent();
        timeCardComponent.TimeCards = updatedTimeCard.TimeCards;
      }

      return this.dailyLogProjectUserService.saveDailyLogApprover(this.approval.ProjectId, dluProjectUserComponent).pipe(
        tap(result => {
          this.isSaving.set(false);
          this.dailyLogUserComponent.set(result);
          this.timeSheetComponentView.set(ComponentView.ReadOnly);
          this.approvalEditMode.set(ApprovalEditMode.NonEdit);
          
          // Reset all service views to read-only mode
          this.notesService.view.set(ComponentView.ReadOnly);
          this.timeSheetService.view.set(ComponentView.ReadOnly);
          this.siteConditionsService.view.set(ComponentView.ReadOnly);
          this.weatherService.view.set(ComponentView.ReadOnly);
          this.photosService.view.set(ComponentView.ReadOnly);
          
          // Clear all dirty states for this project
          this.dirtyService.clearAllDirty(this.approval.ProjectId);
          
          // Store the updated component as the new baseline
          try {
            this.storedDailyLogUserComponent = structuredClone(result);  
          } catch (error) {
            console.log(error);
            this.storedDailyLogUserComponent = clone(result);  
          }
        }),
        catchError(error => {
          console.log(error);
          this.isSaving.set(false);
          this.toastrService.error("Error saving daily log");
          return of(null);
        })
      );
    }
  }

  /**
  * Handles the "Save" button click by subscribing to the saveDailyLog observable.
  * This ensures the save operation executes when the user clicks "Save" in Edit mode.
  * Used by the template's "Save" button to persist changes to the daily log.
 */
  public onSaveDailyLog(): void {
    this.saveDailyLog().subscribe({
      next: () => {
        // No specific action needed here on success, 
        // as saveDailyLog handles state updates and toasts internally via tap.
      }
      // No error handler needed here, as saveDailyLog handles errors internally via catchError.
    });
  }

  edit(){    
    var isUserGreatherThanCurrentLevel = this.userLevel() >= ((this.approval && this.approval.CurrentApprover && this.approval.CurrentApprover.Level) ? this.approval.CurrentApprover.Level : 0);
    
    // Check if we're in a project with no approvals or if the user is at a higher level
    const hasNoApprovals = !this.approval?.CurrentApprover;
    
    // If there are no approvals or user is at higher level, set to Edit mode
    if (hasNoApprovals || isUserGreatherThanCurrentLevel) {
      this.timeSheetComponentView.set(ComponentView.Edit);
    } else if(this.isEditQtyOnly() && !this.isPayroll() && !isUserGreatherThanCurrentLevel){
      this.timeSheetComponentView.set(ComponentView.EditorQtyNotesOnly);
    } else if(this.isPayroll()){
      this.timeSheetComponentView.set(ComponentView.Edit);
    } else{
      this.timeSheetComponentView.set(ComponentView.ReadOnly);
    }
    
    this.approvalEditMode.set(ApprovalEditMode.Edit);
    this.approvalView.set(ApprovalViews.DailyLog);
    var component = this.dailyLogUserComponent() as DailyLogProjectUserComponent;

    // Set the project ID in all services
    if (this.approval?.ProjectId) {
      this.notesService.projectId.set(this.approval.ProjectId);
      this.timeSheetService.projectId.set(this.approval.ProjectId);
      this.siteConditionsService.projectId.set(this.approval.ProjectId);
      this.weatherService.projectId.set(this.approval.ProjectId);
      this.photosService.projectId.set(this.approval.ProjectId);
    }

    // If there are no approvals or user is at higher level, set all components to Edit mode and access to Allow
    if (!this.approval?.CurrentApprover || isUserGreatherThanCurrentLevel) {
      this.notesService.view.set(ComponentView.Edit);
      this.notesService.access.set('Allow');
      
      this.timeSheetService.view.set(ComponentView.Edit);
      this.timeSheetService.access.set('Allow');
      
      this.siteConditionsService.view.set(ComponentView.Edit);
      this.siteConditionsService.access.set('Allow');
      
      this.weatherService.view.set(ComponentView.Edit);
      this.weatherService.access.set('Allow');
    } else if (this.isEditQtyOnly() && !this.isPayroll() && !isUserGreatherThanCurrentLevel) {
      // In EditorQtyNotesOnly mode, only notes and timesheet (for qty) should be editable
      this.notesService.view.set(ComponentView.Edit);
      
      // Set timesheet to EditorQtyNotesOnly mode
      this.timeSheetService.view.set(ComponentView.EditorQtyNotesOnly);
      
      // Site conditions and weather should be read-only
      this.siteConditionsService.view.set(ComponentView.ReadOnly);
      
      this.weatherService.view.set(ComponentView.ReadOnly);
    } else {
      // For other cases, set all components to the appropriate view
      
      // Notes are always editable
      this.notesService.view.set(ComponentView.Edit);
      
      // Other components follow the timeSheetComponentView
      this.timeSheetService.view.set(this.timeSheetComponentView());
      this.siteConditionsService.view.set(this.timeSheetComponentView());
      this.weatherService.view.set(this.timeSheetComponentView());
    }

    try {
      this.storedDailyLogUserComponent = structuredClone(component);  
    } catch (error) {
      console.log(error);
      this.storedDailyLogUserComponent = clone(component);  
    }
    
  }

  cancelEdit(){
    this.setApprovalView(ApprovalViews.TimeSheet);
    this.approvalEditMode.set(ApprovalEditMode.NonEdit);
    this.timeSheetComponentView.set(ComponentView.ReadOnly);   

    // Restore the stored component data
    if (this.storedDailyLogUserComponent && this.storedDailyLogUserComponent.Components) {
      this.dailyLogUserComponent.set(this.storedDailyLogUserComponent);
      
      // Reset all services with the original data
      const notesComponent = this.storedDailyLogUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.NOTES
      ) as NotesComponentProjectUser;
      
      if (notesComponent) {
        this.notesService.setInitialData(
          notesComponent,
          ComponentView.ReadOnly,
          !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
        );
      }
      
      const timeSheetComponent = this.storedDailyLogUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.TIMECARD
      ) as TimeCardComponentProjectUser;
      
      if (timeSheetComponent) {
        this.timeSheetService.setInitialData(
          timeSheetComponent,
          ComponentView.ReadOnly,
          !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
        );
      }
      
      const siteConditionsComponent = this.storedDailyLogUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.SITE_CONDITIONS
      ) as SiteConditionsComponentProjectUser;
      
      if (siteConditionsComponent) {
        this.siteConditionsService.setInitialData(
          siteConditionsComponent,
          ComponentView.ReadOnly,
          !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
        );
      }
      
      const weatherComponent = this.storedDailyLogUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.WEATHER
      ) as WeatherComponentProjectUser;
      
      if (weatherComponent) {
        this.weatherService.setInitialData(
          weatherComponent,
          ComponentView.ReadOnly,
          !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
        );
      }
      
      const photosComponent = this.storedDailyLogUserComponent.Components.find(
        (c) => c.ComponentIdentifier === DailyLogComponentIdentifiers.PHOTOS
      ) as PhotosComponentProjectUser;
      
      if (photosComponent) {
        this.photosService.setInitialData(
          photosComponent,
          ComponentView.ReadOnly,
          !this.approvalService.approvalAccess() ? 'Allow' : (this.approvalAccess()?.EditQtyNotes === AccessEffects.Allow ? 'Allow' : 'Deny')
        );
      }
    }
    
    // Set all views to read-only
    this.notesService.view.set(ComponentView.ReadOnly);
    this.timeSheetService.view.set(ComponentView.ReadOnly);
    this.siteConditionsService.view.set(ComponentView.ReadOnly);
    this.weatherService.view.set(ComponentView.ReadOnly);
    this.photosService.view.set(ComponentView.ReadOnly);
  }

  deleteApproval(){
    this.confirmService.open('This cannot be undone. Are you sure you want to delete this diary entry?').result.then(item => {
      if (item === 'yes' && this.approval && this.dailyLogUserComponent() && this.dailyLogUserComponent().Id) {
        this.isDeleting.set(true);
        this.isApproving.set(true);

        forkJoin({ removeApproval: this.approvalService.RemoveApproval(this.approval.Id), deleteDailyLog: this.dailyLogProjectUserService.deleteDailyLog(this.dailyLogUserComponent().ProjectId, this.dailyLogUserComponent().Id) }).subscribe({
          next: () => {    
            this.remove.next(this.approval);
            this.isDeleting.set(false);
            this.isApproving.set(false);
          },
          error: (err) => {
            console.log(err);
            this.toastrService.error("Error deleting approval");
            this.isDeleting.set(false);
            this.isApproving.set(false);
          }
        });
      }
    });

  }
  unapprove(){
    this.confirmService.open('Do you want to unpprove this time sheet?').result.then(item => {      
      if (item === 'yes') {
        if(this.approval && this.userLevel() !== -1){
          this.approvalService.Unapprove(this.approval.Id, this.userLevel());
        }
      }
    });
  }
  approve(){
    this.confirmService.open('Do you want to approve this time sheet?').result.then(item => {
      if(item === 'yes' && this.approval){
        this.isApproving.set(true);

        if (this.approvalEditMode() === ApprovalEditMode.Edit) {
          // If in Edit mode, save the daily log first, then approve
          this.saveDailyLog().pipe(
            switchMap(result => {
              if (result === null) {
                this.isApproving.set(false);
                return of(null);
              }
              return this.approvalService.Approve(this.approval.Id, this.userLevel());
            }),
            tap(approval => {
              if (approval) {
                this.approval = approval;
                this.remove.next(this.approval);
                this.toastrService.success("Time sheet approved successfully");
              }
              this.isApproving.set(false);
            }),
            catchError(error => {
              console.log(error);
              this.toastrService.error("Error approving time sheet");
              this.isApproving.set(false);
              return of(null);
            })
          ).subscribe();
        } else {
          // If not in Edit mode, just approve
          this.approvalService.Approve(this.approval.Id, this.userLevel()).pipe(
            tap(approval => {
              this.approval = approval;
              this.remove.next(this.approval);
              this.toastrService.success("Time sheet approved successfully");
              this.isApproving.set(false);
            }),
            catchError(error => {
              console.log(error);
              this.toastrService.error("Error approving time sheet");
              this.isApproving.set(false);
              return of(null);
            })
          ).subscribe();
        }
      }
    });
  }
}

export enum ApprovalViews{
  TimeSheet = "TimeSheet",  
  DailyLog = "DailyLog",
  History = "History"
}
