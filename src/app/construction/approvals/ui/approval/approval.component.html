@if(isRunning()){
<div class="placeholder-glow">
	<div class="placeholder col-3 mb-2">
	</div>
	<div class="placeholder col-12" style="height: 50px;">
	</div>
</div>
}@else {
<div> <!--[hidden]="isRunning" -->
	<div class="d-flex align-items-center justify-content-between mb-3">
		<!-- header left -->
		<div>
			@if(approvalEditMode() === EDIT_MODES.Edit){
			<div class="d-flex align-items-center">
				<div class="btn-group" role="group" aria-label="Basic example">
					<button type="button" class="btn btn-outline-dark" placement="bottom" ngbTooltip="Cancel"
						(click)="cancelEdit()">
						Cancel
					</button>
					<button type="button" class="btn btn-primary" placement="bottom" ngbTooltip="Save"
						(click)="onSaveDailyLog()" [disabled]="isSaving()">
						@if(isSaving()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						Save
					</button>
				</div>
				@if(checkDirtyState() && !isSaving()){
				<span class="ms-2 text-danger">
					<i class="fa fa-exclamation-circle"></i>
					<span class="ms-1">Unsaved changes</span>
				</span>
				} @else {
				<span class="ms-2 text-primary">
					<i class="fa fa-check-circle"></i>
				</span>
				}
			</div>
			}@else if(approvalEditMode() === EDIT_MODES.NonEdit){
			<div class="btn-group me-2" role="group" aria-label="Basic example">
				@if((userLevel() >= ((approval && approval.CurrentApprover && approval.CurrentApprover.Level) ?
				approval.CurrentApprover.Level : 0) && !approval.FinalApprovalDate) || approvalAccess()?.EditQtyNotes
				=== ACCESS_EFFECTS.Allow){
				<button type="button" class="btn btn-outline-dark" placement="bottom" ngbTooltip="Edit"
					(click)="edit()" [disabled]="isApproving()">
					Edit
				</button>
				}
				<button type="button" class="btn btn-outline-dark" placement="bottom" ngbTooltip="Approval History"
					(click)="setApprovalView(APPROVAL_VIEWS.History)" [disabled]="isApproving()"
					[ngClass]="{'active': approvalView() === APPROVAL_VIEWS.History}">
					History
				</button>
				<button type="button" class="btn btn-outline-dark" placement="bottom" ngbTooltip="Diary"
					(click)="setApprovalView(APPROVAL_VIEWS.DailyLog)" [disabled]="isApproving()"
					[ngClass]="{'active': approvalView() === APPROVAL_VIEWS.DailyLog}">
					Open
				</button>
				@if(approval?.CurrentApprover && userLevel() >= ((approval?.CurrentApprover &&
				approval?.CurrentApprover.Level) ? approval?.CurrentApprover.Level : 0)){
				<button type="button" class="btn btn-outline-danger" placement="bottom" ngbTooltip="Unapprove"
					(click)="unapprove()" [disabled]="isApproving()">
					@if(isUnapproving()){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Unapprove
				</button>
				}
				@if(approvalAccess()?.DeleteApproval === ACCESS_EFFECTS.Allow && approval?.CurrentApprover?.Level !==
				payrollLevel){
				<button type="button" class="btn btn-outline-danger" placement="bottom" ngbTooltip="Delete Approval"
					(click)="deleteApproval()" [disabled]="isApproving() || isDeleting()">
					@if(isDeleting()){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					Delete
				</button>
				}
			</div>
			@if(approvalView() !== APPROVAL_VIEWS.TimeSheet){
			<button type="button" class="btn btn-outline-dark ms-2" placement="bottom" ngbTooltip="Time Sheet"
				(click)="setApprovalView(APPROVAL_VIEWS.TimeSheet)" [disabled]="isApproving()">
				Close
			</button>
			}
			}
		</div>
		<!-- header right -->
		<div>
			@if(!approval?.FinalApprovalDate && (userLevel() > ((approval?.CurrentApprover &&
			approval?.CurrentApprover.Level) ? approval?.CurrentApprover.Level : 0))){
			<button type="button" class="btn btn-primary" placement="bottom" (click)="approve()" ngbTooltip="Approve"
				[disabled]="isApproving()">
				@if(isApproving()){
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
				}
				Approve
			</button>
			}
			@if((userLevel() <= ((approval && approval.CurrentApprover && approval.CurrentApprover.Level) ?
				approval.CurrentApprover.Level : 0))){ <div class="d-flex align-items-center">
				<span class="badge bg-success me-2">Approved</span>
				<i class="far fa-check-circle text-success" aria-hidden="true" placement="bottom"
					ngbTooltip="Approved"></i>
		</div>
		}
	</div>
</div>
<div class="d-flex flex-column">
	@if(approval?.CurrentApprover){
	<div class="small text-muted">
		<span> Last approved by {{ approval?.CurrentApprover?.FirstName }} {{ approval?.CurrentApprover?.LastName }}
		</span>
		@if(isPayroll()){
		<span>(Level - Final Approver).</span>
		}@else {
		<span>(Level - {{ approval?.CurrentApprover?.Level }}).</span>
		}

	</div>
	}
	@switch (approvalView()) {
	@case (APPROVAL_VIEWS.DailyLog) {
	@if(dailyLogUserComponent().NoWork){
	<div class="alert alert-info" role="alert">
		The foreman selected "No Work Day" for this day.
	</div>
	}@else {
	@for (component of dailyLogUserComponent()?.Components; track $index) {
	<!-- Time Sheet Component -->
	@if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.TIMECARD) {
	@if (hasTimeSheetData(component) || isSectionVisible('Time Sheet')()) {
	<app-daily-log-time-sheet></app-daily-log-time-sheet>
	}
	}
	<!-- Notes Component -->
	@if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.NOTES) {
	@if (hasNotesData(component) || isSectionVisible('Notes')()) {
	<app-daily-log-notes></app-daily-log-notes>
	}
	}
	<!-- Photos Component -->
	@if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.PHOTOS) {
	@if (hasPhotosData(component) || isSectionVisible('Photos')()) {
	<app-daily-log-photos></app-daily-log-photos>
	}
	}
	<!-- Site Conditions Component -->
	@if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.SITE_CONDITIONS) {
	@if (hasSiteConditionsData(component) || isSectionVisible('Site Conditions')()) {
	<app-daily-log-site-conditions></app-daily-log-site-conditions>
	}
	}
	<!-- Weather Component -->
	@if (component.ComponentIdentifier === DAILY_LOG_COMPONENT_IDENTIFIERS.WEATHER) {
	@if (hasWeatherData(component) || isSectionVisible('Weather')()) {
	<app-daily-log-weather></app-daily-log-weather>
	}
	}
	}@empty {
	<div class="row">
		<div class="col-12">
			<div class="alert alert-info" role="alert">
				No components. You must setup your default component in user settings.
			</div>
		</div>
	</div>
	}
	}
	}
	@case (APPROVAL_VIEWS.History) {
	<app-approval-history [history]="approval.History"></app-approval-history>
	}
	@default {
	@if(dailyLogUserComponent().NoWork){
	<div class="alert alert-info" role="alert">
		The foreman selected "No Work Day" for this day.
	</div>
	}@else {
	@if(timeCardComponent().TimeCards?.length <= 0){ <div class="alert alert-info" role="alert">
		No time sheet found on this diary.
</div>
}@else {
<div>
	<app-daily-log-time-sheet></app-daily-log-time-sheet>
</div>
}
}
}
}
</div>
</div>

<!-- @if(isPayroll() || isEditQtyOnly()){
	}@else {
		<div class="alert alert-info">You do not have access to edit this timecard</div>
	} -->

}