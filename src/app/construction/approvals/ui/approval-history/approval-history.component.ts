import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ApprovalHistory } from 'src/app/construction/shared/interfaces/approval';

@Component({
    selector: 'app-approval-history',
    imports: [CommonModule],
    templateUrl: './approval-history.component.html',
    styleUrl: './approval-history.component.css'
})
export class ApprovalHistoryComponent {
  @Input() history: Array<ApprovalHistory> = [];
}
