<h5 class="page-title fs-6">Approval History</h5>
<ul class="list-group">
	<li class="list-group-item d-flex flex-column" *ngFor="let item of history" > <!--| orderBy: '-ApprovedDate'"-->
		<small class="text-muted">{{ item.ApprovedDate | date: 'M/dd/yyyy hh:mm a' }}</small>
		<div>
			<span *ngIf="item.HistoryType === 0">Submitted by </span>
			<span *ngIf="item.HistoryType === 1">Approved by </span>
			<span *ngIf="item.HistoryType === 2">Edited by </span>
			<span *ngIf="item.HistoryType === 3">Payroll approval by </span>
			<span *ngIf="item.HistoryType === 4">Unapproved by </span>
			<span *ngIf="item.HistoryType === 5">Unlocked by </span>
			<span>{{ item.User?.FirstName }} {{ item.User?.LastName }}</span>
			<span *ngIf="item.User?.Level !== 999 && item.HistoryType !== 0"> (Level {{ item.User?.Level }}) </span>
			<span *ngIf="item.User?.Level === 999 && item.HistoryType !== 0"> (Payroll) </span>
		</div>
	</li>
</ul>


  