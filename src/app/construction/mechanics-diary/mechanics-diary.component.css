/* Mechanics Diary Component Styles */

/* Ensure footer doesn't overlap content */
.container {
  margin-bottom: 80px;
}

/* Custom datepicker styling to match existing patterns */
.custom-datepicker {
  width: 100%;
}

/* Card styling for daily log */
.card {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

/* Button styling */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Badge styling for unsaved changes */
.badge {
  font-size: 0.75rem;
}

/* Footer styling */
footer {
  background-color: #f8f9fa !important;
  border-top: 1px solid #dee2e6 !important;
  z-index: 1000;
}

/* Cursor pointer for navigation arrows */
.cursor-pointer {
  cursor: pointer !important;
}

/* Alert styling for component placeholders */
.alert {
  border-radius: 0.375rem;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Form check styling */
.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* Section headers */
h6.border-bottom {
  border-bottom: 2px solid #dee2e6 !important;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

/* Empty state styling */
.text-center .fa-3x {
  font-size: 3rem;
}

/* Input group styling */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-prepend {
  margin-right: -1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  footer .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .card-header .d-flex {
    flex-direction: column;
    align-items: stretch !important;
  }
  
  .btn-sm {
    width: 100%;
    margin-bottom: 0.25rem;
  }
}

/* Loading placeholder animation */
.placeholder-glow .placeholder {
  animation: placeholder-glow 2s ease-in-out infinite alternate;
}

@keyframes placeholder-glow {
  50% {
    opacity: 0.5;
  }
}

/* Hover effects for interactive elements */
.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  transition: transform 0.2s ease-in-out;
}

.cursor-pointer:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease-in-out;
}

/* Focus states for accessibility */
.btn:focus,
.form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Text color utilities */
.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: #6c757d !important;
}

/* Gap utility for button groups */
.gap-2 > * + * {
  margin-left: 0.5rem;
}

.gap-1 > * + * {
  margin-left: 0.25rem;
}

/* Date navigation styling */
.custom-datepicker {
  min-width: 200px;
}

/* Ensure date navigation stays on one line */
.d-flex.align-items-center.gap-2 {
  flex-wrap: nowrap;
}

.d-flex.align-items-center.gap-1 {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* Button group styling for date navigation */
.d-flex.align-items-center.gap-1 .btn {
  flex-shrink: 0;
}
