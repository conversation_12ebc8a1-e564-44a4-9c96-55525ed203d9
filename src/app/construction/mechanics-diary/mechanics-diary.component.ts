import { Component, inject, computed, effect, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DatePickerModule } from 'primeng/datepicker';
import { ToastrService } from 'ngx-toastr';
import { Router, ActivatedRoute } from '@angular/router';
import { MechanicsDailyLogService } from '../shared/data-access/mechanics-daily-log.service';
import { UserDailyLogComponent, MechanicsTimeCardComponent as MechanicsTimeCardData } from '../shared/interfaces/mechanics';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { MechanicsTimeCardComponent } from './ui/mechanics-timecard/mechanics-timecard.component';
import { MechanicsNotesComponent } from './ui/mechanics-notes/mechanics-notes.component';
import { MechanicsPhotosComponent } from './ui/mechanics-photos/mechanics-photos.component';
import { MechanicsDirtyService } from '../shared/data-access/mechanics-dirty.service';
import { MechanicsNotesService } from '../shared/data-access/mechanics-notes.service';
import { MechanicsPhotosService } from '../shared/data-access/mechanics-photos.service';
import { ConstructionPhotosService } from '../shared/data-access/photos.service';
import { MechanicsComponentIdentifiers } from '../shared/interfaces/mechanics-shared';
import { MechanicsApprovalsService } from '../shared/data-access/mechanics-approvals.service';
import { MechanicsApproval, MechanicsApprovalStatus } from '../shared/interfaces/mechanics-approval';

@Component({
  selector: 'app-mechanics-diary',
  standalone: true,
  imports: [CommonModule, FormsModule, DatePickerModule, MechanicsTimeCardComponent, MechanicsNotesComponent, MechanicsPhotosComponent],
  templateUrl: './mechanics-diary.component.html',
  styleUrls: ['./mechanics-diary.component.css'],
  providers: [MechanicsDailyLogService, MechanicsNotesService, MechanicsPhotosService, ConstructionPhotosService]
})
export class MechanicsDiaryComponent {
  private mechanicsService = inject(MechanicsDailyLogService);
  private mechanicsApprovalsService = inject(MechanicsApprovalsService);
  private authService = inject(AuthService);
  private toastrService = inject(ToastrService);
  private dirtyService = inject(MechanicsDirtyService);
  private notesService = inject(MechanicsNotesService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  // Service signals
  currentDailyLog = this.mechanicsService.currentDailyLog;
  selectedDate = this.mechanicsService.selectedDate;
  isLoading = this.mechanicsService.isLoading;
  isDirty = this.mechanicsService.isDirty;

  // Button state management (following project daily log patterns)
  private _isSaving = signal(false);
  private _isDeleting = signal(false);

  // Computed button states following project daily log business rules
  isPersisting = computed(() => 
    this._isSaving() || this.mechanicsService.isSubmitting() || this._isDeleting() || this.isLoading()
  );

  isEnabled = computed(() => !this.isPersisting());

  canDelete = computed(() => {
    const currentLog = this.currentDailyLog();
    const logId = currentLog?._id || (currentLog as any)?.Id;
    return !!(logId && logId.trim().length > 0);
  });

  noWork = computed(() => {
    const currentLog = this.currentDailyLog();
    return !!(currentLog?.NoWork);
  });

  // Current approval state - now using service approval signal as primary source
  currentApproval = computed(() => {
    // First try to get approval from service (more reactive and immediate)
    const serviceApproval = this.mechanicsService.approval();
    if (serviceApproval) return serviceApproval;
    
    // Fallback to existing logic for compatibility
    const currentLog = this.currentDailyLog();
    const approvals = this.mechanicsApprovalsService.approvals();
    const isLoading = this.mechanicsApprovalsService.isLoading();
    
    // If still loading, return null
    if (isLoading) return null;
    
    // If no current log, return null
    if (!currentLog) return null;
    
    // Try to find by ApprovalId if it exists
    if (currentLog.ApprovalId) {
      const approval = approvals.find(approval => approval.Id === currentLog.ApprovalId);
      if (approval) return approval;
    }
    
    // Fallback: Find by daily log ID
    const dailyLogId = currentLog._id || (currentLog as any)?.Id;
    if (dailyLogId) {
      const approval = approvals.find(approval => approval.DailyLogId === dailyLogId);
      if (approval) return approval;
    }
    
    return null;
  });

  // Check if current daily log has been submitted (has approval) - matching project pattern
  isSubmitted = computed(() => {
    const currentLog = this.currentDailyLog();
    const approval = this.currentApproval();
    const hasApprovalId = !!currentLog?.ApprovalId;
    const hasSubmittedDate = !!currentLog?.SubmittedDate;
    
    // Primary check: if the daily log has been submitted, it should have either:
    // 1. An ApprovalId field populated
    // 2. A SubmittedDate field populated
    // 3. An approval object found in the approvals service
    return hasApprovalId || hasSubmittedDate || !!approval;
  });

  // Check if can unsubmit - matching project pattern
  canUnSubmit = computed(() => {
    const approval = this.currentApproval();
    return !!approval && !approval.FinalApprovalDate;
  });

  // User information from identity token (signal-based)
  currentUser = computed(() => {
    const token = this.authService.identityToken();
    if (token) {
      try {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        return {
          Id: tokenPayload.sub || '',
          FirstName: tokenPayload.given_name || tokenPayload.FirstName || '',
          LastName: tokenPayload.family_name || tokenPayload.LastName || '',
          OwnerAccountId: tokenPayload.custom?.OwnerAccountId || tokenPayload.OwnerAccountId || ''
        };
      } catch (error) {
        console.error('Error parsing identity token:', error);
        return null;
      }
    }
    return null;
  });

  constructor() {
    // Initialize date from URL parameter or today's date
    const dateParam = this.route.snapshot.paramMap.get('date');
    const initialDate = this.parseDateFromUrl(dateParam) || new Date();
    this.mechanicsService.setSelectedDate(initialDate);

    // Initialize mechanics approvals service for current user
    this.mechanicsApprovalsService.initializeUserApprovalLevels();
    this.mechanicsApprovalsService.initializeApprovals.set(true);
    this.mechanicsApprovalsService.approvalType.set('all');
    this.mechanicsApprovalsService.setSingleDate(initialDate);
    // Set a high limit to ensure we get all approvals for the current user
    this.mechanicsApprovalsService.limit.set(1000);

    // Update URL when date changes
    effect(() => {
      const currentDate = this.selectedDate();
      this.updateUrlWithDate(currentDate);
      // Update approvals service date filter when date changes
      this.mechanicsApprovalsService.setSingleDate(currentDate);
      // Force reload approvals when date changes to ensure we get the latest data
      this.mechanicsApprovalsService.reload();
    });

    // Register component services (following project daily log pattern)
    this.mechanicsService.registerComponentService(MechanicsComponentIdentifiers.NOTES, this.notesService);
    // Photos service is handled by its own isolated component
    // TimeCard data is handled via component output events
  }

  /**
   * Parse date from URL parameter (YYYY-MM-DD format)
   */
  private parseDateFromUrl(dateParam: string | null): Date | null {
    if (!dateParam) return null;
    
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateParam)) return null;
    
    const date = new Date(dateParam + 'T00:00:00');
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Update URL with current date (following existing app patterns)
   */
  private updateUrlWithDate(date: Date): void {
    const dateString = this.formatDateForUrl(date);
    const currentUrl = this.router.url;
    const basePath = '/construction/mechanics-diary';
    
    // Only navigate if the URL actually needs to change
    if (currentUrl !== `${basePath}/${dateString}`) {
      this.router.navigate([basePath, dateString], { replaceUrl: true });
    }
  }

  /**
   * Format date for URL (YYYY-MM-DD format)
   */
  private formatDateForUrl(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Handle date selection from calendar picker
   */
  onDateSelect(date: Date): void {
    if (date && !this.isLoading()) {
      this.mechanicsService.setSelectedDate(date);
    }
  }

  /**
   * Navigate to a different day by the specified offset
   */
  navigateDay(dayOffset: number): void {
    if (this.isLoading()) return;
    
    const currentDate = new Date(this.selectedDate());
    currentDate.setDate(currentDate.getDate() + dayOffset);
    this.mechanicsService.setSelectedDate(currentDate);
  }

  /**
   * Set the calendar to today's date
   */
  selectToday(): void {
    if (this.isLoading()) return;
    
    const today = new Date();
    const currentDate = this.selectedDate();
    
    // Check if we're already on today's date
    if (currentDate.toDateString() === today.toDateString()) {
      // Force reload the current date's data
      this.mechanicsService.reload();
    } else {
      // Navigate to today
      this.mechanicsService.setSelectedDate(today);
    }
  }

  /**
   * Get formatted date string for display
   */
  getCurrentFormattedDate(): string {
    const date = this.selectedDate();
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayOfWeek = dayNames[date.getDay()];
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const year = date.getFullYear().toString().slice(-2);
    
    return `${dayOfWeek} ${month}/${day}/${year}`;
  }

  /**
   * Create a new daily log for the selected date
   * Note: Service now auto-creates in-memory templates, so this just triggers reload
   */
  createNewDailyLog(): void {
    if (this.isLoading()) return;
    
    // Service will auto-create an in-memory template when no log exists
    this.mechanicsService.reload();
  }

  /**
   * Toggle the "No Work" status
   * Following the same pattern as project daily-log
   */
  toggleNoWork(event: any): void {
    const currentLog = this.currentDailyLog();
    
    // Service auto-creates logs, so currentLog should always exist
    if (!currentLog) {
      console.warn('No current daily log found - this should not happen');
      return;
    }
    
    // For button clicks, toggle the current NoWork state
    // For checkbox events, use event.target.checked
    const isNoWork = event.target && 'checked' in event.target 
      ? event.target.checked 
      : !currentLog.NoWork;
    
    // Use the new setNoWork service method (following project daily-log pattern)
    this.mechanicsService.setNoWork(isNoWork);
  }

  /**
   * Save the current daily log (following project daily log business rules)
   */
  async saveDailyLog(): Promise<void> {
    if (!this.isEnabled() || !this.isDirty()) return;

    const currentLog = this.currentDailyLog();
    if (!currentLog) return;

    this._isSaving.set(true);
    try {
      // Collect updates from all child component services
      this.mechanicsService.updateFromChildServices();
      
      // Get the updated daily log with all child component data
      const updatedLog = this.mechanicsService.currentDailyLog();
      if (!updatedLog) return;
      
      await this.mechanicsService.saveDailyLog(updatedLog);
      this.toastrService.success('Daily log saved successfully', 'Success');
      
      // Clear dirty state for this date
      const dateString = this.mechanicsService.currentDateString();
      this.dirtyService.clearDirty(dateString);
    } catch (error) {
      console.error('Error saving daily log:', error);
      this.toastrService.error('Failed to save daily log. Please try again.', 'Error');
    } finally {
      this._isSaving.set(false);
    }
  }

  /**
   * Import recent timecard data
   */
  importRecentTimeCard(): void {
    if (this.isLoading()) return;

    this.mechanicsService.importRecentTimeCard();
  }

  /**
   * Handle timecard data changes from the timecard component
   */
  onTimeCardChanged(timeCardData: MechanicsTimeCardData): void {
    const currentLog = this.currentDailyLog();
    const user = this.currentUser();

    if (!currentLog || !user) return;

    // First, collect current data from all component services to preserve their state
    this.mechanicsService.updateFromChildServices();

    // Get the updated daily log with all current component data
    const logWithAllComponents = this.mechanicsService.currentDailyLog();
    if (!logWithAllComponents) return;

    // Update or add the timecard component to the daily log
    const updatedComponents = logWithAllComponents.Components ? [...logWithAllComponents.Components] : [];
    const timeCardIndex = updatedComponents.findIndex(c => c.ComponentIdentifier === MechanicsComponentIdentifiers.TIMECARD);

    if (timeCardIndex >= 0) {
      // Update existing timecard
      updatedComponents[timeCardIndex] = timeCardData;
    } else {
      // Add new timecard
      updatedComponents.push(timeCardData);
    }

    const updatedLog: UserDailyLogComponent = {
      ...logWithAllComponents,
      Components: updatedComponents,
      ModifiedDate: new Date().toISOString(),
      ModifiedBy: user.Id
    };

    this.mechanicsService.setCurrentDailyLog(updatedLog);

    // Mark the daily log as dirty since timecard data has changed
    this.mechanicsService.markDirty();
  }

  /**
   * Clear the current daily log (following project daily log business rules)
   */
  clearDailyLog(): void {
    if (!this.isEnabled()) return;

    // Confirm with user (following project pattern)
    if (!confirm('Are you sure you want to clear this log?')) return;

    // Create new blank log template (following project pattern)
    const newLog = this.getDefaultDailyLog();
    this.mechanicsService.setCurrentDailyLog(newLog);
    
    // Clear all dirty states for this date
    const dateString = this.mechanicsService.currentDateString();
    this.dirtyService.clearDirty(dateString);
    
    this.toastrService.success('Daily log cleared', 'Success');
  }

  /**
   * Submit the daily log - now using service layer following exact project pattern
   */
  submitDailyLog(): void {
    if (!this.isEnabled()) return;

    const currentLog = this.currentDailyLog();
    if (!currentLog) return;

    // Collect updates from all child component services before submission
    this.mechanicsService.updateFromChildServices();
    
    // Use the service's submit method following exact project pattern
    this.mechanicsService.submit();
  }

  /**
   * Unsubmit daily log - now using service layer following exact project pattern
   */
  unSubmit(): void {
    // Use the service's unSubmit method following exact project pattern
    this.mechanicsService.unSubmit();
  }

  /**
   * Reset changes by reloading the current date (following project pattern)
   */
  resetChanges(): void {
    // Clear all dirty states for this date first
    const dateString = this.mechanicsService.currentDateString();
    this.dirtyService.clearDirty(dateString);
    
    // Reload fresh data from API
    this.mechanicsService.reload();
  }

  /**
   * Delete the current daily log (following project daily log business rules)
   */
  async deleteDailyLog(): Promise<void> {
    if (!this.isEnabled() || !this.canDelete()) return;

    const currentLog = this.currentDailyLog();
    const logId = currentLog?._id || (currentLog as any)?.Id;
    if (!logId) return;

    // Confirm with user (following project pattern)
    if (!confirm('Are you sure you want to delete this log?')) return;

    this._isDeleting.set(true);
    try {
      await this.mechanicsService.deleteDailyLog(logId);
      
      // Create new blank log template after deletion
      const newLog = this.getDefaultDailyLog();
      this.mechanicsService.setCurrentDailyLog(newLog);
      
      // Clear all dirty states for this date
      const dateString = this.mechanicsService.currentDateString();
      this.dirtyService.clearDirty(dateString);
      
      this.toastrService.success('Daily log deleted successfully', 'Success');
    } catch (error) {
      console.error('Error deleting daily log:', error);
      this.toastrService.error('Failed to delete daily log. Please try again.', 'Error');
    } finally {
      this._isDeleting.set(false);
    }
  }

  /**
   * Get default daily log structure for when no data exists
   */
  getDefaultDailyLog(): UserDailyLogComponent {
    const user = this.currentUser();
    const selectedDate = this.selectedDate();

    return {
      _t: ["UserDailyLogComponent"],
      _id: '',
      ComponentIdentifier: "MechanicsDailyLog" as const,
      UserId: user?.Id || '',
      OwnerAccountId: user?.OwnerAccountId || '',
      DateCreated: new Date().toISOString(),
      ModifiedDate: new Date().toISOString(),
      CreatedBy: user?.Id || '',
      ModifiedBy: user?.Id || '',
      IsActive: true,
      Name: `Mechanics Daily Log - ${selectedDate?.toLocaleDateString() || new Date().toLocaleDateString()}`,
      Year: selectedDate?.getFullYear() || new Date().getFullYear(),
      Month: selectedDate?.getMonth() + 1 || new Date().getMonth() + 1,
      Day: selectedDate?.getDate() || new Date().getDate(),
      NoWork: false,
      Components: []
    };
  }

}
