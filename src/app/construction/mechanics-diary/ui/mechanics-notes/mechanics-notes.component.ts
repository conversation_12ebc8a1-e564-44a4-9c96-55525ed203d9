import { Component, inject, effect, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { MechanicsNotesService } from 'src/app/construction/shared/data-access/mechanics-notes.service';

/**
 * Mechanics Notes Component
 * 
 * Wrapper component that adapts the existing daily log notes functionality
 * for the mechanics daily log system. Reuses the existing notes UI patterns
 * while integrating with the mechanics-specific data structures.
 * 
 * This component provides the same interface as DailyLogNotesComponent
 * but works with UserDailyLogComponent data instead of project-based data.
 */
@Component({
  selector: 'app-mechanics-notes',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './mechanics-notes.component.html',
  styleUrls: ['./mechanics-notes.component.css']
})
export class MechanicsNotesComponent {
  service = inject(MechanicsNotesService);
  readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;
  
  // Signal-based notes value for two-way binding
  notesValue = signal('');
  
  constructor() {
    // Initialize notes value from service
    effect(() => {
      const serviceNotes = this.service.notes();
      this.notesValue.set(serviceNotes);
    });
    
    // Update service when notes value changes
    effect(() => {
      const currentNotes = this.notesValue();
      this.service.updateNotes(currentNotes);
    });
  }
  
  /**
   * Handle input changes from template
   */
  onNotesChange(value: string): void {
    this.notesValue.set(value);
  }
}