/* Mechanics TimeCard Component Styles - Matching Project Timesheet */

/* Table styling to match project timesheet */
.table-responsive {
  border-radius: 0.375rem;
  overflow: hidden;
}

.table {
  margin-bottom: 0;
}

.table td {
  vertical-align: middle;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
}

.table th {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  font-weight: 600;
  text-align: center;
}

/* Cost code column styling */
.cost-code-column {
  min-width: 200px;
  max-width: 250px;
}

.cost-code-column .form-select {
  font-size: 0.875rem;
}

/* Reason and Total columns */
.reason {
  min-width: 120px;
  background-color: #f8f9fa;
}

.total {
  min-width: 80px;
  background-color: #f8f9fa;
}

/* Delete column */
.delete-employee {
  width: 50px;
  text-align: center;
}

/* Input group styling */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
}

/* Button styling */
.btn-outline-dark {
  color: #212529;
  border-color: #212529;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Input focus styles */
.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert styles */
.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
  border-radius: 0.375rem;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
}

/* Equipment row styling */
.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cost-code-column {
    min-width: 150px;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
  }
}