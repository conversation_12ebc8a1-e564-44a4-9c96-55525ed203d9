<div class="border p-3 mb-4">
  <div class="d-flex align-items-center justify-content-between mb-3">
    <h2 class="page-title fs-6 mb-0">Time Sheet</h2>
    @if (mechanicsService.selectedDate() && !mechanicsService.isLoading()) {
    <button type="button" class="btn btn-outline-dark ms-2" (click)="copyLastEntry()"
      [disabled]="mechanicsService.isLoading() || isImporting()">
      @if(isImporting()) {
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}
      Copy Last Entry
    </button>
    }
  </div>

  <!-- Always show the timesheet interface (matching project timesheet behavior) -->
  <div style="min-height: 100px;">
    <!-- Add Controls Row -->
    <div class="row mb-3">
      <!-- Add Mechanics Cost Code -->
      <div class="col-12 col-lg-4 mb-1 mb-lg-0">
        <div class="d-flex align-items-center">
          <div class="flex-grow-1 pe-1">
            <ng-multiselect-dropdown
              id="costCodeSelect"
              [settings]="multiSelectSettings"
              [placeholder]="'Select Mechanics Cost Codes'"
              [data]="costCodesForDropdown()"
              [(ngModel)]="selectedCostCodes"
              style="width: 70%;">
            </ng-multiselect-dropdown>
          </div>
          <div>
            <button class="btn btn-outline-dark" type="button" (click)="addCostCode()">
              Add
            </button>
          </div>
        </div>
      </div>

      <!-- Add Equipment -->
      <div class="col-12 col-lg-4 mb-1 mb-lg-0">
        <div class="d-flex align-items-center">
          <div class="flex-grow-1 pe-1">
            <app-equipment-dropdown
              [(ngModel)]="selectedEquipmentForDropdown"
              style="width: 100%;">
            </app-equipment-dropdown>
          </div>
          <div>
            <button class="btn btn-outline-dark" type="button" (click)="addEquipment()">
              Add
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Timesheet Table -->
    <div class="table-responsive">
      <table class="table table-bordered small">
        <tbody>
          <!-- Cost codes header row -->
          <tr>
            <td></td>
            @for (costCode of timeCardCostCodes(); track $index) {
            <td class="cost-code-column">
              <select class="form-select" [(ngModel)]="costCode.CostCodeId" (change)="changeCostCode(costCode, $index)">
                @for (cc of availableCostCodes(); track cc.value) {
                <option [ngValue]="cc.value">{{ cc.label }}</option>
                }
              </select>
            </td>
            }
            <td class="align-middle text-center reason fw-bold">Reason</td>
            <td class="align-middle text-center total fw-bold">Total</td>
            <td class="delete-employee"></td>
          </tr>

          <!-- Hours input row -->
          <tr>
            <td></td>
            @for (costCode of timeCardCostCodes(); track $index) {
            <td>
              <div class="d-flex align-items-end">
                <div class="input-group">
                  <input class="form-control" type="number" [(ngModel)]="costCode.Hours"
                    (ngModelChange)="onHoursChange()" placeholder="Hours" min="0" max="24" step="0.25" />
                  <span class="input-group-text">hrs</span>
                </div>
              </div>
            </td>
            }
            <td></td>
            <td class="align-middle text-center fw-bold">{{ getTotalHours() }}</td>
            <td></td>
          </tr>

          <!-- Equipment rows -->
          @for (equipment of timeCardEquipment(); track $index; let i = $index) {
          <tr>
            <td class="align-middle">
              <span class="me-2">{{ equipment.Number }} - {{ equipment.Make }} {{ equipment.Model }}</span>
            </td>
            @for (costCode of timeCardCostCodes(); track $index; let j = $index) {
            <td>
              <input class="form-control" type="number"
                [value]="equipment.CostCodes[j]?.QtyCompl || 0"
                (input)="onEquipmentHoursChange(i, j, $event)"
                placeholder="0" min="0" max="24" step="0.25" />
            </td>
            }
            <td>
              <select class="form-select" [(ngModel)]="equipment.TimeReason" (ngModelChange)="onTimeReasonChange()">
                @for (reason of availableTimeReasons(); track reason) {
                  <option [value]="reason">{{ reason }}</option>
                }
              </select>
            </td>
            <td class="align-middle text-center fw-bold">{{ getEquipmentTotalHours(equipment) }}</td>
            <td>
              <button class="btn btn-outline-danger w-100" type="button" (click)="removeEquipment(i)"
                title="Remove Equipment">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
          }

          <!-- Delete Cost Codes Row (Column-based delete buttons - matching project pattern) -->
          @if (timeCardCostCodes().length > 0) {
          <tr>
            <td></td>
            @for (costCode of timeCardCostCodes(); track $index; let i = $index) {
            <td>
              <button class="btn btn-outline-danger w-100" type="button" (click)="removeCostCode(i)"
                title="Remove Cost Code">
                <i class="fa fa-trash"></i>
              </button>
            </td>
            }
            <td></td>
            <td></td>
            <td></td>
          </tr>
          }
        </tbody>
      </table>
    </div>

    <!-- No cost codes message (matching project timesheet behavior) -->
    <div class="alert alert-info mt-1" role="alert" *ngIf="timeCardCostCodes().length <= 0">
      You have not added any cost codes to the time sheet.
    </div>
  </div>
</div>