import { Component, OnInit, input, output, signal, computed, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import {
  UserDailyLogComponent,
  MechanicsTimeCardComponent as MechanicsTimeCardData
} from '../../../shared/interfaces/mechanics';
import { EquipmentInfo } from '../../../shared/interfaces/equipment';
import { EquipmentDropdownService } from '../../../shared/data-access/equipment-dropdown.service';
import { EquipmentService } from '../../../shared/data-access/equipment.service';
import { MechanicsCostCodesService } from '../../../shared/data-access/mechanics-cost-codes.service';
import { MechanicsDailyLogService } from '../../../shared/data-access/mechanics-daily-log.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { EquipmentDropdownComponent } from 'src/app/construction/ui/equipment-dropdown/equipment-dropdown.component';
import { SelectedData } from 'src/app/construction/shared/interfaces/selected-data';

// Interfaces for timecard structure (matching project timesheet)
interface TimeCardCostCode {
  CostCodeId: string;
  Phase: string;
  Description: string;
  Hours: number;
  Units: number;
  UnitOfMeasure: string;
}

interface TimeCardEquipment {
  EquipmentInternalId: string;
  Number: string;
  Make: string;
  Model: string;
  TimeReason: string;
  TotalHours: string; // Changed to string to match EquipmentTracker interface
  CostCodes: EquipmentCostCode[];
}

interface EquipmentCostCode {
  CostCodeId: string;
  QtyCompl: number;
}

/**
 * Mechanics TimeCard Component - Redesigned to match project timesheet structure
 * 
 * Key features:
 * - Always shows interface (dropdowns, table) even when no data exists
 * - Uses table-based layout matching project timesheet exactly
 * - Mechanics cost codes instead of employee dropdown
 * - Equipment dropdown functionality identical to project timesheet
 */
@Component({
  selector: 'app-mechanics-timecard',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgMultiSelectDropDownModule,
    EquipmentDropdownComponent
  ],
  templateUrl: './mechanics-timecard.component.html',
  styleUrls: ['./mechanics-timecard.component.css']
})
export class MechanicsTimeCardComponent implements OnInit {
  // Inputs and outputs
  dailyLog = input.required<UserDailyLogComponent>();
  dataChanged = output<MechanicsTimeCardData>();
  
  // Injected services
  mechanicsService = inject(MechanicsDailyLogService);
  private costCodesService = inject(MechanicsCostCodesService);
  private equipmentDropdownService = inject(EquipmentDropdownService);
  private equipmentService = inject(EquipmentService);
  private authService = inject(AuthService);
  private toastr = inject(ToastrService);
  
  // Internal state signals for timecard structure (matching project timesheet)
  private _timeCardCostCodes = signal<TimeCardCostCode[]>([]);
  private _timeCardEquipment = signal<TimeCardEquipment[]>([]);
  private _isImporting = signal(false);
  
  // Selection state (matching project timesheet exactly)
  selectedCostCodes: SelectedData[] = [];
  selectedEquipmentForDropdown = signal<EquipmentInfo[]>([]);

  // Multi-select dropdown settings (matching project timesheet)
  multiSelectSettings = {
    singleSelection: false,
    idField: 'ItemId',
    textField: 'ItemText',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 3,
    allowSearchFilter: true,
    searchPlaceholderText: 'Search...',
    noDataAvailablePlaceholderText: 'No data available',
    enableCheckAll: true,
    limitSelection: -1
  };

  // Cost codes data for multi-select dropdown (matching project timesheet format)
  costCodes: Array<SelectedData> = [];

  // Public readonly signals
  timeCardCostCodes = this._timeCardCostCodes.asReadonly();
  timeCardEquipment = this._timeCardEquipment.asReadonly();
  isImporting = this._isImporting.asReadonly();

  // Computed signals for available data
  availableEquipment = computed(() => {
    const equipment = this.equipmentDropdownService.equipmentForSelection();
    return equipment?.filter((e: EquipmentInfo) => e.IsActive) || [];
  });
  
  availableCostCodes = computed(() => {
    const costCodes = this.costCodesService.costCodes();
    return costCodes?.map(cc => ({
      value: cc.CostCodeId,
      label: `${cc.Phase} - ${cc.Description}`
    })) || [];
  });

  // Separate computed signal for multi-select dropdown data
  costCodesForDropdown = computed(() => {
    const costCodes = this.costCodesService.costCodes();
    return costCodes?.map(cc => ({
      ItemId: cc.CostCodeId,
      ItemText: `${cc.Phase} - ${cc.Description}`
    } as SelectedData)) || [];
  });
  
  availableTimeReasons = computed(() => [
    'Work Day',
    'Too Wet to Work',
    'Called Off Rent',
    'Mob\'d In',
    'Mob\'d Out',
    'Maintenance',
    'Repair',
    'Idle'
  ]);

  constructor() {
    // Initialize from daily log data when component loads
    effect(() => {
      const dailyLog = this.dailyLog();
      if (dailyLog) {
        this.initializeFromDailyLog(dailyLog);
      }
    });
  }

  ngOnInit(): void {
    // Initialize services (ensuring proper initialization order)
    // 1. Initialize main equipment service first (this prevents the dropdown effect from setting initializeEquipmentData to false)
    this.equipmentService.initialized.set(true);
    
    // 2. Initialize cost codes service
    this.costCodesService.reload();
    
    // 3. Initialize equipment dropdown (now that main service is ready)
    this.equipmentDropdownService.initializeEquipmentData.set(true);
  }

  // Initialize from daily log data
  private initializeFromDailyLog(dailyLog: UserDailyLogComponent): void {
    const timeCardComponent = dailyLog.Components?.find(
      (c: any) => c.ComponentIdentifier === 'TimeCard'
    ) as MechanicsTimeCardData;

    if (timeCardComponent?.TimeCards?.length > 0) {
      // Convert from mechanics timecard format to table format
      this.convertFromMechanicsFormat(timeCardComponent);
    } else {
      // Initialize empty state (always show interface)
      this._timeCardCostCodes.set([]);
      this._timeCardEquipment.set([]);
    }
  }

  // Convert mechanics timecard data to table format (API format)
  private convertFromMechanicsFormat(timeCardComponent: MechanicsTimeCardData): void {
    // Extract unique cost codes from all timeCards
    const costCodeMap = new Map<string, TimeCardCostCode>();
    const equipmentList: TimeCardEquipment[] = [];

    timeCardComponent.TimeCards?.forEach(timeCard => {
      // Process cost codes - API format uses different property names
      timeCard.CostCodes?.forEach(cc => {
        if (!costCodeMap.has(cc.CostCodeId)) {
          costCodeMap.set(cc.CostCodeId, {
            CostCodeId: cc.CostCodeId,
            Phase: cc.Phase || '',
            Description: cc.Description || '',
            Hours: cc.Hours || 0,
            Units: cc.Units || 0,
            UnitOfMeasure: cc.UnitOfMeasure || 'hour' // API default
          });
        }
      });

      // Process equipment trackers - API format
      timeCard.EquipmentTrackers?.forEach(et => {
        equipmentList.push({
          EquipmentInternalId: et.EquipmentInternalId,
          Number: et.Number || '',
          Make: et.Make || '',
          Model: et.Model || '',
          TimeReason: et.TimeReason || 'Work Day', // API default
          TotalHours: et.TotalHours || '0', // API expects string
          CostCodes: et.CostCodes?.map(cc => ({
            CostCodeId: cc.CostCodeId,
            QtyCompl: cc.QtyCompl || 0 // API uses QtyCompl (number)
          })) || []
        });
      });
    });

    this._timeCardCostCodes.set(Array.from(costCodeMap.values()));
    this._timeCardEquipment.set(equipmentList);
  }

  // Add cost code to timecard (matching project timesheet behavior exactly)
  addCostCode(): void {
    if (!this.selectedCostCodes || this.selectedCostCodes.length === 0) {
      this.toastr.warning('Please select cost codes first');
      return;
    }

    let costCodeAdded = false;

    for (let c of this.selectedCostCodes) {
      const costCode = this.costCodesService.costCodes()?.find(cc => cc.CostCodeId === c.ItemId);

      if (costCode) {
        // Check if already added
        if (this._timeCardCostCodes().some(cc => cc.CostCodeId === c.ItemId)) {
          this.toastr.warning(`Cost code ${costCode.Phase} already added`);
          continue;
        }

        const newCostCode: TimeCardCostCode = {
          CostCodeId: costCode.CostCodeId,
          Phase: costCode.Phase,
          Description: costCode.Description,
          Hours: 0,
          Units: costCode.Units || 0,
          UnitOfMeasure: costCode.UnitOfMeasure || 'hour'
        };

        this._timeCardCostCodes.update(codes => [...codes, newCostCode]);

        // Add corresponding cost code entries to all equipment
        this._timeCardEquipment.update(equipment =>
          equipment.map(eq => ({
            ...eq,
            CostCodes: [...eq.CostCodes, { CostCodeId: c.ItemId || '', QtyCompl: 0 }]
          }))
        );

        costCodeAdded = true;
      }
    }

    if (costCodeAdded) {
      // Clear selections after adding (matching project timesheet behavior)
      this.selectedCostCodes = [];
      this.emitDataChanged();
    }
  }

  // Add equipment to timecard
  addEquipment(): void {
    const selected = this.selectedEquipmentForDropdown();
    if (selected.length === 0) {
      this.toastr.warning('Please select equipment first');
      return;
    }

    selected.forEach(equipment => {
      // Check if already added
      if (this._timeCardEquipment().some(eq => eq.EquipmentInternalId === equipment.InternalId)) {
        this.toastr.warning(`Equipment ${equipment.Number} already added`);
        return;
      }

      const newEquipment: TimeCardEquipment = {
        EquipmentInternalId: equipment.InternalId,
        Number: equipment.Number || '',
        Make: equipment.Make || '',
        Model: equipment.Model || '',
        TimeReason: 'Work Day',
        TotalHours: '0',
        CostCodes: this._timeCardCostCodes().map(cc => ({
          CostCodeId: cc.CostCodeId,
          QtyCompl: 0
        }))
      };

      this._timeCardEquipment.update(eq => [...eq, newEquipment]);
    });

    this.selectedEquipmentForDropdown.set([]);
    this.emitDataChanged();
  }

  // Remove equipment
  removeEquipment(index: number): void {
    this._timeCardEquipment.update(equipment => 
      equipment.filter((_, i) => i !== index)
    );
    this.emitDataChanged();
  }

  // Remove cost code (column-based delete - following project pattern)
  removeCostCode(index: number): void {
    // Remove cost code from main array
    this._timeCardCostCodes.update(costCodes => 
      costCodes.filter((_, i) => i !== index)
    );

    // Remove corresponding cost code from all equipment entries
    this._timeCardEquipment.update(equipment =>
      equipment.map(eq => ({
        ...eq,
        CostCodes: eq.CostCodes.filter((_, i) => i !== index)
      }))
    );

    this.emitDataChanged();
  }

  // Change cost code
  changeCostCode(costCode: TimeCardCostCode, index: number): void {
    // Find the full cost code data from the service
    const fullCostCode = this.costCodesService.costCodes()?.find(cc => cc.CostCodeId === costCode.CostCodeId);

    if (fullCostCode) {
      // Update cost code with full data
      this._timeCardCostCodes.update(codes =>
        codes.map((cc, i) => i === index ? {
          ...cc,
          CostCodeId: fullCostCode.CostCodeId,
          Phase: fullCostCode.Phase,
          Description: fullCostCode.Description,
          UnitOfMeasure: fullCostCode.UnitOfMeasure || 'hour'
        } : cc)
      );
      this.emitDataChanged();
    }
  }

  // Hours change handlers
  onHoursChange(): void {
    this.emitDataChanged();
  }

  onEquipmentHoursChange(equipmentIndex: number, costCodeIndex: number, event: Event): void {
    const target = event.target as HTMLInputElement;
    const hours = parseFloat(target.value) || 0;

    this._timeCardEquipment.update(equipment =>
      equipment.map((eq, i) => {
        if (i === equipmentIndex) {
          const updatedCostCodes = [...eq.CostCodes];
          if (updatedCostCodes[costCodeIndex]) {
            updatedCostCodes[costCodeIndex] = {
              ...updatedCostCodes[costCodeIndex],
              QtyCompl: hours
            };
          }
          return { ...eq, CostCodes: updatedCostCodes };
        }
        return eq;
      })
    );
    this.emitDataChanged();
  }

  // Time reason change handler
  onTimeReasonChange(): void {
    this.emitDataChanged();
  }

  // Calculate total hours
  getTotalHours(): number {
    return this._timeCardCostCodes().reduce((sum, cc) => sum + (cc.Hours || 0), 0);
  }

  // Calculate equipment total hours
  getEquipmentTotalHours(equipment: TimeCardEquipment): number {
    return equipment.CostCodes.reduce((sum, cc) => sum + (cc.QtyCompl || 0), 0);
  }

  // Copy last entry functionality
  copyLastEntry(): void {
    this._isImporting.set(true);
    try {
      // Call the mechanics service to import recent timecard data
      this.mechanicsService.importRecentTimeCard();
      this.toastr.info('Importing recent timecard data...');
    } catch (error) {
      this.toastr.error('Failed to import recent timecard data');
    } finally {
      this._isImporting.set(false);
    }
  }

  // Get current user info
  private getCurrentUser() {
    const token = this.authService.identityToken();
    if (token) {
      try {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        return {
          Id: tokenPayload.sub || '',
          FirstName: tokenPayload.given_name || tokenPayload.FirstName || '',
          LastName: tokenPayload.family_name || tokenPayload.LastName || '',
          OwnerAccountId: tokenPayload.custom?.OwnerAccountId || tokenPayload.OwnerAccountId || ''
        };
      } catch (error) {
        console.error('Error parsing identity token:', error);
        return null;
      }
    }
    return null;
  }

  // Emit data changed event - Format according to API documentation
  private emitDataChanged(): void {
    const user = this.getCurrentUser();
    const userId = user?.Id || '';

    const timeCardData: MechanicsTimeCardData = {
      _t: ["TimeCardComponent"],
      ComponentIdentifier: "TimeCard",
      TimeCards: [{
        CostCodes: this._timeCardCostCodes().map(cc => ({
          CostCodeId: cc.CostCodeId,
          CostCode: cc.Phase, // Use Phase as CostCode per API docs
          Phase: cc.Phase,
          Description: cc.Description,
          Hours: cc.Hours,
          Units: cc.Units,
          UnitOfMeasure: cc.UnitOfMeasure,
          IsActive: true,
          DateCreated: new Date().toISOString(),
          ModifiedDate: new Date().toISOString(),
          CreatedBy: userId,
          ModifiedBy: userId
        })),
        EquipmentTrackers: this._timeCardEquipment().map(eq => ({
          EquipmentInternalId: eq.EquipmentInternalId,
          Make: eq.Make,
          Model: eq.Model,
          Number: eq.Number,
          TimeReason: eq.TimeReason,
          TotalHours: eq.TotalHours, // String format as per API docs
          CostCodes: eq.CostCodes.map(cc => ({
            CostCodeId: cc.CostCodeId,
            QtyCompl: cc.QtyCompl // Maps to qtyCompl in API
          }))
        }))
      }]
    };

    this.dataChanged.emit(timeCardData);
  }
}
