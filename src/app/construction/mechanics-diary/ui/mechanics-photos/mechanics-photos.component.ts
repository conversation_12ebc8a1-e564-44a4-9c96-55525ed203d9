import { Component, computed, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DailyLogPhotoUploaderComponent } from 'src/app/construction/project/daily-log/ui/daily-log-photos/ui/daily-log-photo-uploader/daily-log-photo-uploader.component';
import { DailyLogPhotoGalleryComponent } from 'src/app/construction/project/daily-log/ui/daily-log-photos/ui/daily-log-photo-gallery/daily-log-photo-gallery.component';
import { DailyLogPhotosService } from 'src/app/construction/project/daily-log/shared/services/daily-log-photos.service';
import { ComponentView } from 'src/app/construction/shared/interfaces/project-components';
import { PhotoUploadViewerComponent } from 'src/app/construction/project/daily-log/ui/daily-log-photos/ui/photo-upload-viewer/photo-upload-viewer.component';
import { ConstructionPhotosService } from 'src/app/construction/shared/data-access/photos.service';
import { MechanicsDailyLogService } from 'src/app/construction/shared/data-access/mechanics-daily-log.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { PhotosComponent } from 'src/app/construction/shared/interfaces/mechanics';
import { MechanicsPhotosService } from 'src/app/construction/shared/data-access/mechanics-photos.service';
import { MechanicsComponentIdentifiers } from 'src/app/construction/shared/interfaces/mechanics-shared';

export enum PhotoViews {
  Gallery = 'gallery',
  Upload = 'upload'
}

/**
 * Mechanics Photos Component
 * 
 * Mirrors the DailyLogPhotosComponent but uses MechanicsPhotosService
 * for proper integration with the mechanics daily log system.
 */
@Component({
    selector: 'app-mechanics-photos',
    standalone: true,
    imports: [
      CommonModule,
      DailyLogPhotoUploaderComponent,
      DailyLogPhotoGalleryComponent,
      PhotoUploadViewerComponent
    ],
    providers: [
      // Provide DailyLogPhotosService as an alias to MechanicsPhotosService
      // This allows DailyLogPhotoGalleryComponent to work with mechanics data
      // Note: MechanicsPhotosService and ConstructionPhotosService are provided by parent component
      { provide: DailyLogPhotosService, useExisting: MechanicsPhotosService }
    ],
    templateUrl: './mechanics-photos.component.html',
    styleUrls: ['./mechanics-photos.component.css']
})
export class MechanicsPhotosComponent {
  // Inject services (following project patterns)
  public readonly service = inject(MechanicsPhotosService);
  private constructionPhotosService = inject(ConstructionPhotosService);
  private mechanicsService = inject(MechanicsDailyLogService);
  private authService = inject(AuthService);

  // Expose enums for the template
  public readonly PHOTO_VIEWS: typeof PhotoViews = PhotoViews;
  public readonly COMPONENT_VIEW: typeof ComponentView = ComponentView;

  // User information from identity token (following mechanics diary pattern)
  private currentUser = computed(() => {
    const token = this.authService.identityToken();
    if (token) {
      try {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));
        return {
          Id: tokenPayload.sub || '',
          FirstName: tokenPayload.given_name || tokenPayload.FirstName || '',
          LastName: tokenPayload.family_name || tokenPayload.LastName || '',
          OwnerAccountId: tokenPayload.custom?.OwnerAccountId || tokenPayload.OwnerAccountId || ''
        };
      } catch (error) {
        console.error('Error parsing identity token:', error);
        return null;
      }
    }
    return null;
  });

  photosProjectId = this.service.projectId;
  isUploading = this.constructionPhotosService.isUploading;
  photosToUpload = this.constructionPhotosService.projectPhotosToAdd;

  constructor() {
    // Register photos service with mechanics daily log service immediately
    this.mechanicsService.registerComponentService(MechanicsComponentIdentifiers.PHOTOS, this.service);
    
    // Initialize service with user and date isolation (following project patterns)
    effect(() => {
      const user = this.currentUser();
      const selectedDate = this.mechanicsService.selectedDate();

      if (user?.Id && selectedDate) {
        // Set user and date for complete isolation
        const dateString = selectedDate.toISOString().split('T')[0];
        this.service.setCurrentUser(user.Id);
        this.service.setCurrentDate(dateString);

        // Initialize with current daily log data
        const currentDailyLog = this.mechanicsService.currentDailyLog();
        const foundComponent = currentDailyLog?.Components?.find(
          (c: any) => c.ComponentIdentifier === MechanicsComponentIdentifiers.PHOTOS
        );

        // Type guard function to check if component is PhotosComponent
        const isPhotosComponent = (comp: any): comp is PhotosComponent => {
          return comp && comp.ComponentIdentifier === MechanicsComponentIdentifiers.PHOTOS && 'Photos' in comp;
        };

        // Use type guard for safe casting
        const photosComponent: PhotosComponent | null = isPhotosComponent(foundComponent)
          ? foundComponent
          : null;

        this.service.setInitialData(photosComponent, ComponentView.Edit, 'Allow');
      }
    });
  }
  
  // Delegate event handlers to service methods
  initializeFiles(files: FileList): void {
    this.service.initializeFiles(files);
  }
  
  setView(view: PhotoViews): void {
    this.service.setView(view);
  }
  
  markPhotoForDeletion(image: any, mark: boolean): void {
    this.service.markPhotoForDeletion(image, mark);
  }
  
  cancelUpload(): void {
    this.service.cancelUpload();
  }
}