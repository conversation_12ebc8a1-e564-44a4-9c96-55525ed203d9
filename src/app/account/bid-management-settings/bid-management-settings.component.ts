import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BidManagementSettingsService } from 'src/app/account/shared/data-access/bid-management-settings.service';
import { BidManagementSetting } from 'src/app/account/shared/interfaces/bid-management-settings.model';

@Component({
  selector: 'app-bid-management-settings',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './bid-management-settings.component.html',
  styleUrls: ['./bid-management-settings.component.css'],
})
export class BidManagementSettingsComponent implements OnInit {
  settingsForm!: FormGroup; // Main form group
  newContactForm!: FormGroup; // Separate form for adding new contacts
  changesMade = false; // Track unsaved changes
  editingIndex: number | null = null; // Track the index of the contact being edited
  isLoading = true; // Track loading state
  isSaving = false; // Track saving state

  private originalContactValue: { [index: number]: { contactName: string; contactEmail: string } } = {};
  private bidManagementSettingsService = inject(BidManagementSettingsService);
  private toastr = inject(ToastrService);
  private fb = inject(FormBuilder);

  private static readonly TOASTR_MESSAGES = {
    loadSettingsError: 'Failed to load settings. Please try again.',
    nameEmailValidationError: 'Name and proper email address required.',
    addContactSuccess: 'Contact added successfully. Don’t forget to save.',
    editInProgressError: 'Finish editing the current contact before editing another.',
    contactValidationError: 'Please correct errors before saving the contact.',
    applyContactSuccess: 'Contact changes applied. Be sure to save your changes.',
    removeContactConfirmation: 'Are you sure you want to remove this contact?',
    removeContactInfo: 'Contact removed. Don’t forget to save.',
    saveSettingsValidationError: 'Please correct errors in the form before saving.',
    saveSettingsSuccess: 'Settings saved successfully!',
    saveSettingsError: 'Failed to save settings. Please try again.',
  };

  constructor() {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadSettings();
  }

  private initializeForm(settings?: BidManagementSetting): void {
    const contacts = settings?.QAContacts || [];
    const contactArray = this.fb.array(
      contacts.map((contact) =>
        this.fb.group({
          contactName: [contact.ContactName, Validators.required],
          contactEmail: [contact.ContactEmail, [Validators.required, Validators.email]],
        })
      )
    );

    this.settingsForm = this.fb.group({
      newProjectImportContactInfo: [settings?.NewProjectImportContactInfo || false, Validators.required],
      qaContacts: contactArray,
    });

    this.newContactForm = this.fb.group({
      contactName: ['', Validators.required],
      contactEmail: ['', [Validators.required, Validators.email]],
    });
  }

  private loadSettings(): void {
    this.isLoading = true;
    this.bidManagementSettingsService.getSettings().subscribe({
      next: (settings) => {
        this.initializeForm(settings);
        this.isLoading = false;
      },
      error: () => {
        // this.toastr.error(BidManagementSettingsComponent.TOASTR_MESSAGES.loadSettingsError, 'Error');
        this.isLoading = false;
      }
    });
  }

  get qaContacts(): FormArray {
    return this.settingsForm.get('qaContacts') as FormArray;
  }

  cancelNewContact(): void {
    this.newContactForm.reset();
  }

  addQAContact(): void {
    if (this.newContactForm.invalid) {
      this.toastr.error(BidManagementSettingsComponent.TOASTR_MESSAGES.nameEmailValidationError, 'Validation Error');
      return;
    }

    const newEmail = this.newContactForm.value.contactEmail;

    // Check for duplicates
    const duplicate = this.qaContacts.controls.some(contact => contact.value.contactEmail === newEmail);
    if (duplicate) {
      this.toastr.error('This email address is already added.', 'Validation Error');
      return;
    }

    const newContact = this.fb.group({
      contactName: [this.newContactForm.value.contactName, Validators.required],
      contactEmail: [this.newContactForm.value.contactEmail, [Validators.required, Validators.email]],
    });

    this.qaContacts.push(newContact);
    this.newContactForm.reset();
    this.toastr.success(BidManagementSettingsComponent.TOASTR_MESSAGES.addContactSuccess, 'Success');
    this.changesMade = true;
  }

  editQAContact(index: number): void {
    if (this.editingIndex !== null && this.editingIndex !== index) {
      this.toastr.error(BidManagementSettingsComponent.TOASTR_MESSAGES.editInProgressError, 'Error');
      return;
    }

    this.originalContactValue[index] = { ...this.qaContacts.at(index).value };
    this.editingIndex = index;
  }

  applyQAContact(index: number): void {
    const contact = this.qaContacts.at(index);

    if (contact.invalid) {
      this.toastr.error(BidManagementSettingsComponent.TOASTR_MESSAGES.contactValidationError, 'Validation Error');
      contact.markAllAsTouched();
      return;
    }

    delete this.originalContactValue[index];
    this.editingIndex = null;
    this.toastr.success(BidManagementSettingsComponent.TOASTR_MESSAGES.applyContactSuccess, 'Success');
    this.changesMade = true;
  }

  cancelEditQAContact(index: number): void {
    if (this.editingIndex === index && this.originalContactValue[index]) {
      this.qaContacts.at(index).setValue(this.originalContactValue[index]); // Restore original value
      delete this.originalContactValue[index];
    }
    this.editingIndex = null;
  }

  removeQAContact(index: number): void {
    if (confirm(BidManagementSettingsComponent.TOASTR_MESSAGES.removeContactConfirmation)) {
      this.qaContacts.removeAt(index);
      this.editingIndex = null;
      this.toastr.info(BidManagementSettingsComponent.TOASTR_MESSAGES.removeContactInfo, 'Info');
      this.changesMade = true;
    }
  }

  saveSettings(): void {
    if (this.settingsForm.invalid) {
      this.toastr.error(BidManagementSettingsComponent.TOASTR_MESSAGES.saveSettingsValidationError, 'Validation Error');
      return;
    }

    this.isSaving = true;

    const updatedSettings: BidManagementSetting = {
      NewProjectImportContactInfo: this.settingsForm.value.newProjectImportContactInfo,
      QAContacts: this.qaContacts.value.map((contact: { contactName: string; contactEmail: string }) => ({
        ContactName: contact.contactName,
        ContactEmail: contact.contactEmail,
      })),
    };

    this.bidManagementSettingsService.addUpdateSettings(updatedSettings).subscribe({
      next: () => {
        this.toastr.success(BidManagementSettingsComponent.TOASTR_MESSAGES.saveSettingsSuccess, 'Success');
        this.changesMade = false;
        this.isSaving = false;
      },
      error: () => {
        this.toastr.error(BidManagementSettingsComponent.TOASTR_MESSAGES.saveSettingsError, 'Error');
        this.isSaving = false;
      },
    });
  }
}