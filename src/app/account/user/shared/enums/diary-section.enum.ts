/**
 * Enum representing the different sections of a diary
 * Used for role settings and visibility control
 */
export enum DiarySectionType {
  NOTES = 'Notes',
  SITE_CONDITIONS = 'Site Conditions',
  PHOTOS = 'Photos',
  TIME_SHEET = 'Time Sheet',
  WEATHER = 'Weather'
}

/**
 * Visibility options for diary sections
 */
export enum VisibilityType {
  SHOW = 'Show',
  HIDE = 'Hide'
}

/**
 * Array of all diary section types
 * Used for initialization and defaults
 */
export const ALL_DIARY_SECTIONS: DiarySectionType[] = [
  DiarySectionType.NOTES,
  DiarySectionType.SITE_CONDITIONS,
  DiarySectionType.PHOTOS,
  DiarySectionType.TIME_SHEET,
  DiarySectionType.WEATHER
];
