import { uuid } from "uuidv4";

export class PolicyAttributes{    
    Mappings: Array<PolicyAttributeMapping> = [];
}
export class PolicyAttributeMapping{
    constructor(name: string){
        this.Name = name;
    }
    Name: string;
    Mappings: Array<PolicyAttributeMap> = [];
}

export class PolicyAttributeMap{
    constructor(name: string){
        this.Name = name;
    }
    Name: string;
    PolicyId: string = uuid();
}