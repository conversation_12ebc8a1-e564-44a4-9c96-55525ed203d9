import { Injectable, EventEmitter } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { AccessEffects } from './access-effects';


@Injectable()
export class UserAccessGuard  {
  constructor(private accessService: AccessService, private router: Router) {}
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return new Observable<boolean>(obs => {
      var resource = route.data['resource'] as string;
      var action = route.data['action'] as string;
      this.accessService.CheckAccess(resource, action).subscribe(result => {
        if (result.Access === AccessEffects.Deny) {
          this.router.navigate(['/unauthorized']);
          obs.next(false);
        } else if (result.Access === AccessEffects.Allow) {
          obs.next(true);
        } else {
          this.router.navigate(['/unauthorized']);
          obs.next(false);
        }

        obs.complete();
      });
    });
  }
}
