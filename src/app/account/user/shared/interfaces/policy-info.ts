import { Policy } from "src/app/models/access/policy";
import { uuid } from "uuidv4";

export class PolicyInfo{
    PolicyId: string = uuid();
    Active:boolean = false;
    Description:string | null = null;
    Name: string | null = null;
    GroupName: string | null = null;
    IsLoading: boolean = false;
    Attributes: Array<AttributeInfo> = [];
    SelectedAttribute: AttributeInfo | null = null;

}

export class AttributeInfo{
    PolicyId: string = uuid();
    Name: string | null = null;
    Policy: Policy | null = null;
    PolicyGroup: string | null = null;
    PolicyGroupName: string | null = null;
}