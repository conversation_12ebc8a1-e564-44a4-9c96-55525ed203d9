import { uuid } from "uuidv4";

export interface DelegateInvitation {
	Id: string;
	AccountRequestedEmail?: string;
	AccountRequestingId?: string;
	AccountRequestedId?: string;
	EmailMessageId?: string;
	Role?: string;
	Status?: string;
	Title: string | null;
	DateRequested?: Date;
	DateReceived?: Date;
	DateCompleted?: Date;
	InvitationToken?: string;
	Properties?: Map<string, string>;
}

export interface DelegateInvitationRequest {
	Email?: string;
	Role?: string;
	ReturnUrl?: string;
	Product?: string;
	Title: string | null;
}

export interface DelegateInvitationResponse{
	Invitation: DelegateInvitation;
}