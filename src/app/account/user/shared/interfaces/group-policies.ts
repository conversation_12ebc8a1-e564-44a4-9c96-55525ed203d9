import { Policy } from "src/app/models/access/policy";
import { uuid } from "uuidv4";

export class GlobalPolicies{
    PolicyGroups: Array<GlobalPolicyGroup> = [];
}

export class GlobalPolicyGroup{
    constructor(name: string){
        this.Name = name;
    }
    Name: string;
    GroupName: string | null = null;
    Description: string | null = null;
    PolicyItems: Array<GlobalPolicyGroupItem> = [];
    SelectedPolicy: Policy | null = null;
}

export class GlobalPolicyGroupItem{
    constructor(name: string){
        this.Name = name;
    }
    Name:string;
    Id: string = uuid();
    Policy: Policy | null = null;
}