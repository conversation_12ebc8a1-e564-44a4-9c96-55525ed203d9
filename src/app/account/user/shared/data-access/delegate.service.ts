﻿import { computed, effect, inject, Injectable, signal } from '@angular/core';
import { Observable, of, Subject, Subscription, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { AccountDelegateStore, DelegateNavStore, DelegateUserNavInfo } from 'src/app/shared/interfaces/delegate';
import { Delegate, UserDelegateStore } from '../interfaces/delegate';
import { rxResource } from '@angular/core/rxjs-interop';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';



@Injectable({
  providedIn: 'root'
})
export class DelegateService {

  client = inject(HttpClient);
  accountService = inject(AccountService);
  newDelegate = new Subject<string>();
  newDelegate$ = this.newDelegate.asObservable();
  userDelegateStore = computed(() => this.delegateNavStoreResource.value());  
  delegateNavStoreAccountInfo = computed(() => this.navStoreUserInfoResource.value());
  delegateInfoLoading = computed(() => this.delegateNavStoreResource.isLoading());
  delegateAccountInfoLoading = computed(() => this.navStoreUserInfoResource.isLoading());
  userDelegateStoreSub: Subscription | null = null;
  initialize = signal<boolean>(false);

  private delegateNavStoreUserIds = signal<Array<string>>([]);
  delegateNavInfo = signal<DelegateNavStore | null>(null);

  constructor() {
    effect(() => {
      if(this.userDelegateStore()){
        this.delegateNavStoreUserIds.set(this.userDelegateStore()?.MyDelegations as Array<string>);
      }
    });

    effect(() => {
      if (this.userDelegateStore() && this.delegateNavStoreAccountInfo()) {
        var navStore = new DelegateNavStore();
        navStore.currentDelegateUserId = this.userDelegateStore()?.CurrentDelegation as string;
        navStore.accountsAccess = new Array<DelegateUserNavInfo>();

        for (let account of this.delegateNavStoreAccountInfo() as CivCastAccount[]) {
          navStore.accountsAccess.push(
            new DelegateUserNavInfo(account.CognitoUserId, account.Profile?.FirstName, account.Profile?.LastName, account.Profile?.Company?.Name)
          );
        }

        this.delegateNavInfo.set(navStore);
      }      
    });
  }

  private navStoreUserInfoResource = rxResource({
    request: () => this.delegateNavStoreUserIds(),
    loader: (request) => {
      if (request.request) {
        return this.accountService.GetAccounts(request.request);        
      }

      return of([]);
    }
  });

  delegateNavStoreResource = rxResource({
    request: () => this.initialize(),
    loader: (request) => {
      if (request.request) {
        return this.client.get<UserDelegateStore>(`${environment.services_root_endpoints.delegation}/delegation/user`);
      }

      return of(null);
    }
  });

  
  // public GetUserDelegateStore(){

  //   this.userDelegateStoreSub?.unsubscribe();

  //   this.userDelegateStoreSub = this.client.get<UserDelegateStore>(`${environment.services_root_endpoints.delegation}/delegation/user`).subscribe({
  //     next: (store) => {
  //       this.userDelegateStore.set(store);
  //     },
  //     error: (err) => {
  //       console.log(err);
  //     }
  //   });
  // }
  



  destroy(){  
    this.userDelegateStoreSub?.unsubscribe();
  }

  public AddDelegates(delegates: Array<Delegate>): Observable<any> {    
    return  this.client.put<any>(`${environment.services_root_endpoints.delegation}/delegation/account`, delegates);
  }

  public GetDelegates(): Observable<AccountDelegateStore> {
    return this.client.get<AccountDelegateStore>(`${environment.services_root_endpoints.delegation}/delegation/account`);
  }

  public RemoveDelegate(delegateId: string): Observable<any> {    
    return this.client.delete(`${environment.services_root_endpoints.delegation}/delegation/account?delegateId=${delegateId}`);
  }

  public RemoveCurrentDelegate(): Observable<any> {
    return this.client.delete(`${environment.services_root_endpoints.delegation}/delegation/user`);
  }

  public AccessDelegate(delegateId: string): Observable<Delegate> {
    var delegate = {
      UserId: delegateId
    };

    return this.client.put<Delegate>(`${environment.services_root_endpoints.delegation}/delegation/user`, delegate);
  }

  public UpdateCurrentUser(currentUser: string | null ){
    this.delegateNavStoreResource.update((store) => {
      if(store){
        store.CurrentDelegation = currentUser;
      }
      return store;
    });
  }


  public GetDelegate(delegateId:string): Observable<Delegate>{
    return this.client.get<Delegate>(`${environment.services_root_endpoints.delegation}/delegation/account/${delegateId}`);
  }

  public SaveDelegate(delegate: Delegate): Observable<Delegate>{
    return this.client.patch<Delegate>(`${environment.services_root_endpoints.delegation}/delegation/account?instruction=save`, delegate);
  }

  public ActivateDelegate(delegateId: string): Observable<any>{
    return this.client.patch<any>(`${environment.services_root_endpoints.delegation}/delegation/account?instruction=activate-delegate&delegateId=${delegateId}`, null);
  }
  public DeactivateDelegate(delegateId: string): Observable<any>{
    return this.client.patch<any>(`${environment.services_root_endpoints.delegation}/delegation/account?instruction=deactivate-delegate&delegateId=${delegateId}`, null);
  }

  public InviteAddDelegate(delegate: Delegate, token:string):Observable<Delegate>{
    var request = {
      DelegateInfo: delegate,
      InviteToken: token
    };

    return this.client.patch<Delegate>(`${environment.services_root_endpoints.delegation}/delegation/account/invite-add-delegate`, request);
  } 

  changeDelegation(){

  }





 
}
