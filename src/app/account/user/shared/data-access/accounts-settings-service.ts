import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AccountSettings } from "../interfaces/account-settings";
import { HttpClient } from "@angular/common/http";
import { HubService } from "src/app/shared/data-access/hub-service";
import { environment } from "src/environments/environment";
import { HubInfo } from "src/app/shared/interfaces/hub-info";


@Injectable({
	providedIn: 'root'
  })
export class AccountsSettingsService {

	constructor(private http: HttpClient, private hubService: HubService) { }
	getSettings(): Observable<AccountSettings> {
		return new Observable<AccountSettings>(obs => {

			const settings = this.getLocalSettings();

			if (settings) {
				obs.next(settings);
				obs.complete();
			} else {
				this.http.get<AccountSettings>(`${environment.services_root_endpoints.accounts_settings}/account-settings`).subscribe(
					{
						next: (result) => {
							this.setLocalSettings(result);
							obs.next(result);
							obs.complete();
						},
						error: (err) => {
							obs.error(err);
						},
						complete: () => {

						}
					});
			}

		});
	}

	setLocalSettings(accountSettings: AccountSettings) {		
		localStorage.setItem("account-settings", JSON.stringify(accountSettings));
	}

	getLocalSettings(): AccountSettings | null {
		const settings = localStorage.getItem("account-settings");
		let nSettings = null;
		if (settings) {
			nSettings = JSON.parse(settings) as AccountSettings;
			return nSettings;
		}

		return null;
	}
	update(accountSettings: AccountSettings): Observable<AccountSettings>  {
		return new Observable<AccountSettings>(obs => {
		this.http.post<AccountSettings>(`${environment.services_root_endpoints.accounts_settings}/account-settings`, accountSettings).subscribe(
			{
				next: (result) => {
					this.setLocalSettings(result);
					obs.next(result);
					obs.complete();
				},
				error: (err) => {
					obs.error(err);
				},
				complete: () => {

				}
			});
		});
	}

	isSettingHidden(id: string, accountSettings: AccountSettings): boolean {
		if (accountSettings) {
			var hasId = accountSettings.HideLinkIds.filter(x => x === id);
			if (hasId.length > 0) {
				return true;
			}
		}
		return false;
	}

	getMainDefaultHub(){
		return this.hubService.getDefaultHub();
	}

	getDefaultHub(): Observable<HubInfo> {
		return new Observable(obs => {
			this.getSettings().subscribe({
				next: (result) => {
					let defaultHub = null;
					if (result) {
						defaultHub = this.hubService.getHub(result.DefaultLinkId);
					} else {
						defaultHub = this.hubService.getDefaultHub();
					}
	
					obs.next(defaultHub);
					obs.complete();
				},
				error: (err) => {
					obs.error(err);
				}
			});
		});

	}
}	