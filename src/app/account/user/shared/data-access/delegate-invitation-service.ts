import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment";
import { DelegateInvitation, DelegateInvitationRequest, DelegateInvitationResponse } from "../interfaces/delegate-invitation";

@Injectable({
	providedIn: 'root'
})
export class DelegateInvitationService {

	constructor(private http: HttpClient) {

	}

	getInvitations(): Observable<Array<DelegateInvitation>> {
		return this.http.get<Array<DelegateInvitation>>(`${environment.services_root_endpoints.delegate_invitation}/invitations`);
	}

	getInvitation(id: string): Observable<DelegateInvitation> {
		return this.http.get<DelegateInvitation>(`${environment.services_root_endpoints.delegate_invitation}/invitations/${id}`);
	}

	getInvitationNoAuth(id: string): Observable<DelegateInvitation> {
		var headers = new HttpHeaders().set("x-auth","false")
		return this.http.get<DelegateInvitation>(`${environment.services_root_endpoints.delegate_invitation}/noauth/invitations/${id}`, {headers: headers});
	}

	resendInvitation(id: string, email: string, role: string, title: string, product: string, returnUrl: string): Observable<DelegateInvitation> {		
		var request: DelegateInvitationRequest = {
			Email: email,
			Role: role,
			Title: title,
			Product: product,
			ReturnUrl: returnUrl
		};

		return this.http.post<DelegateInvitation>(`${environment.services_root_endpoints.delegate_invitation}/invitations/${id}/resend`, request);
	}

	initializeInvitation(invitationId: string): Observable<DelegateInvitationResponse>{
		return this.http.post<DelegateInvitationResponse>(`${environment.services_root_endpoints.delegate_invitation}/invitations/${invitationId}/initialize`, null);
	}

	noAuthInitilization(invitationId: string){
		var headers = new HttpHeaders().set("x-auth","false")
		return this.http.post<DelegateInvitation>(`${environment.services_root_endpoints.delegate_invitation}/noauth/invitations/${invitationId}/initialize`, null, {headers: headers});
	}

	completeInvitation(invitationId: string): Observable<DelegateInvitationResponse>{
		return this.http.post<DelegateInvitationResponse>(`${environment.services_root_endpoints.delegate_invitation}/invitations/${invitationId}/complete`, null);
	}

	sendInvitation(email: string, role: string, title: string | null, product: string, returnUrl: string):Observable<DelegateInvitationResponse> {
		var request: DelegateInvitationRequest = {
			Email: email,
			Role: role,
			Title: title,
			Product: product,
			ReturnUrl: returnUrl
		};

		return this.http.post<DelegateInvitationResponse>(`${environment.services_root_endpoints.delegate_invitation}/invitations`, request);
	}

	deleteInvitation(invitationId: string){
		return this.http.delete(`${environment.services_root_endpoints.delegate_invitation}/invitations/${invitationId}`);
	}
}