import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { environment } from 'src/environments/environment';
// import { UserPolicyStore } from '../../../models/user-policy-store';
// import { Role, UserRoleStore } from '../../../models/user-role-store';
import { Observable, Subject } from 'rxjs';
import { uuid } from 'uuidv4';
// import { GlobalPolicies, GlobalPolicyGroup } from '../interfaces/group-policies';
// import { PolicyAttributeMapping } from '../interfaces/policy-attribute-mapping';
// import { PolicyInfo, AttributeInfo } from '../interfaces/policy-info';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { GlobalPolicies, GlobalPolicyGroup } from '../interfaces/group-policies';
import { Policy } from '../interfaces/policy';
import { PolicyAttributeMapping } from '../interfaces/policy-attribute-mapping';
import { PolicyInfo, AttributeInfo } from '../interfaces/policy-info';
import { UserPolicyStore } from '../interfaces/user-policy-store';
import { Role, UserRoleStore } from '../interfaces/user-role-store';
// import { Policy } from '../interfaces/policy';


@Injectable({
  providedIn: 'root'
})
export class UserAccessService {
   client = inject(HttpClient);
  accessService = inject(AccessService);
  constructor() { }

  public GetGlobalPolicies() : Observable<GlobalPolicies>  {
    const headers = new HttpHeaders({
      'ContentType': 'application/json',
      'Cache-Control': 'no-cache'
    });
    
    return this.client.get<GlobalPolicies>(environment.global_policy_url, { headers });     
  }
  public AddUserStorePolicy(delegateId:string, policy: Policy): Observable<any>{
    var request = {
      DelegateId: delegateId,
      PolicyInfo: policy
    };

    return this.client.patch(`${environment.services_root_endpoints.acl}/user-store/add-policy`,request);
  }

  public RemoveUserStorePolicy(delegateId:string, policyId: string): Observable<any>{
    var request = {
      DelegateId: delegateId,
      PolicyId: policyId
    };
    
    return this.client.patch(`${environment.services_root_endpoints.acl}/user-store/remove-policy`,request);
  }

  public AddPolicyToRole(roleId: string, policy: Policy){
    var request = {
      RoleId: roleId,
      Policy: policy
    }

    return this.client.patch(`${environment.services_root_endpoints.acl}/user-role-store/add-policy-to-role`,request);
  }

  public RemovePolicyFromRole(roleId:string, policyId: string){
    var request = {
      RoleId: roleId,
      PolicyId: policyId
    }

    return this.client.patch(`${environment.services_root_endpoints.acl}/user-role-store/remove-policy-from-role`,request);
  }

  public GetRole(roleId:string): Observable<Role>{
    return this.client.get<Role>(`${environment.services_root_endpoints.acl}/user-role-store/${roleId}`);
  }

  public AddRole(role:Role):Observable<Role>{
    var request = {
      Role: role
    }

    return this.client.patch<Role>(`${environment.services_root_endpoints.acl}/user-role-store/add-role`, request);
  }
  public RemoveRole(roleId:string): Observable<any>{
    var request = {
      RoleId: roleId
    };

    return this.client.patch<UserRoleStore>(`${environment.services_root_endpoints.acl}/user-role-store/remove-role`, request);
  }

  public SaveRole(role:Role): Observable<any>{
    var request = {
      Role: role
    };

    return this.client.put(`${environment.services_root_endpoints.acl}/user-role-store`, request);
  }

  public UpdateRole(roleId:string): Observable<any>{
    var request = {
      RoleId: roleId
    };

    return this.client.patch<UserRoleStore>(`${environment.services_root_endpoints.acl}/user-role-store/update-role`, request);
  }

  public GetPolicyData(userId: string): Observable<[UserPolicyStore, Array<PolicyInfo>]>{
    return new Observable(obs => {    
      this.accessService.GetUserStore(userId).subscribe(result => {      
        var userStore = result;
        var policyInfos = this.GetPolicyInfo(userStore.Policies).subscribe({
          next: (policyInfos) => {
            obs.next( [userStore, policyInfos]);
            obs.complete();
          }, 
          error: (err) => {
            obs.error(err);
          }
        });        
      });
    });
  }    

  public GetGlobalPoliciesByGroup(policyGroupName: string, name: string): Observable<GlobalPolicyGroup>{
    return new Observable<GlobalPolicyGroup>(obs => {
      this.GetGlobalPolicies().subscribe({
        next: (gPolicies) => {
          var data = gPolicies.PolicyGroups.filter(x => x.GroupName === policyGroupName && x.Name == name)

          if(data.length> 1){
            throw Error("Global policies has more than 1 group with the same naming conventions");      
          }
          obs.next(data[0]);
          obs.complete();
        },
        error: (err) => {
          obs.error(err);
        }
      });
    });
  }

    
    public GetPolicyInfo(policies: Array<Policy>): Observable<Array<PolicyInfo>>{
      var obs = new Subject<Array<PolicyInfo>>()
      this.GetGlobalPolicies().subscribe(result => {
        let policyInfos = new Array<PolicyInfo>();
        for (let policy of result.PolicyGroups) {

          var policyInfo = new PolicyInfo();
          policyInfo.Name = policy.Name;          
          policyInfo.GroupName = policy.GroupName;          
          policyInfo.Description = policy.Description;          
          policyInfo.Active = false;

          for(let policyItem of policy.PolicyItems){
            var attribute = new AttributeInfo();
            attribute.Name = policyItem.Name;
            attribute.Policy = policyItem.Policy;            
            attribute.PolicyId = (policyItem.Policy) ? policyItem.Policy.Id : uuid();
            attribute.PolicyGroup = policyInfo.GroupName; 
            attribute.PolicyGroupName = policyInfo.Name;         
            policyInfo.Attributes.push(attribute);          
          }

          var denyAttribute = new AttributeInfo();
          denyAttribute.PolicyId = "deny";
          denyAttribute.Name = "Deny";
          denyAttribute.PolicyGroup = policyInfo.GroupName;
          denyAttribute.PolicyGroupName = policyInfo.Name;
          denyAttribute.Policy = new Policy();
          denyAttribute.Policy.Id = "deny"
          policyInfo.Attributes.push(denyAttribute);
          
          if (policies && policies.length > 0) {
            for(let item of policyInfo.Attributes){
              if (policies.filter(x => x.Id ==  item.Policy?.Id).length > 0) {
                policyInfo.Active = true;
                policyInfo.SelectedAttribute = item;                
                break;
              }else{
                policyInfo.SelectedAttribute = denyAttribute;
              }
            }   

          }else{
            policyInfo.SelectedAttribute = denyAttribute;
          }
    
          policyInfos.push(policyInfo);
        }
        obs.next(policyInfos);
        obs.complete();
      });


      return obs.asObservable();
 
  }

  getPolicyAttributeMappings(): Observable<PolicyAttributeMapping>{
    const headers = new HttpHeaders({
      'ContentType': 'application/json'
    });
    return this.client.get<PolicyAttributeMapping>('./assets/data/policy-attribute-mapping.json', { headers });
  } 
}