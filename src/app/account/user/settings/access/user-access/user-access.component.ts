import { Component, OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { debounceTime, forkJoin, Observable } from 'rxjs';
import { AccountService } from 'src/app/shared/data-access/account-service';
// import { UserAccessService } from 'src/app/user/shared/data-access/user.access.service';
import { PolicyInfo } from 'src/app/account/user/shared/interfaces/policy-info';
import { UserPolicyStore } from 'src/app/account/user/shared/interfaces/user-policy-store';
import { Role, UserRoleStore } from 'src/app/account/user/shared/interfaces/user-role-store';
import { RoleInfo } from 'src/app/account/user/shared/interfaces/role-info';
import { CommonModule } from '@angular/common';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { UserAccessService } from '../../../shared/data-access/user.access.service';
import { PermissionsComponent } from 'src/app/account/roles/ui/permissions/permissions.component';

@Component({
    selector: 'user-access',
    templateUrl: './user-access.component.html',
    styleUrls: ['./user-access.component.css'],
    imports: [CommonModule, PermissionsComponent, FormsModule, ReactiveFormsModule]
})
export class UserAccessComponent implements OnInit {
  route = inject(ActivatedRoute);
  accountService = inject(AccountService);
  userAccessService = inject(UserAccessService);
  accessService = inject(AccessService);
  authService = inject(AuthService);
  toastrService = inject(ToastrService);
  delegateId: string | null = null;
  account: CivCastAccount | null = null;
  userPolicyStore: UserPolicyStore | null = null;
  userRoleStore: UserRoleStore | null = null;
  roles: Array<RoleInfo> = [];
  cRoles: Array<RoleInfo> | null = null;
  IsRolesLoading: boolean = false;
  IsPermissionsLoading: boolean = false;
  userId: string | null = null;
  searchRoles = new UntypedFormControl();
  policyInfos: Array<PolicyInfo> = [];
  constructor() { }

  async ngOnInit() {
    this.initialize();

    this.userId = await this.authService.getSubId();    
     
    this.searchRoles.valueChanges.pipe(debounceTime(400)).subscribe(value => {
      if(this.cRoles === null){
        this.cRoles = Object.assign([], this.roles);
      }

      var sValue = value as string;      
      this.roles = this.cRoles.filter(x => x.Role!.Name.toLowerCase().indexOf(sValue.toLowerCase()) > -1);
    });

  }

  initialize(){

      this.IsPermissionsLoading = true
      this.IsRolesLoading = true;

      const {delegateId} = this.route.snapshot.params
      this.delegateId = delegateId;

      if(this.delegateId){
        forkJoin([this.accountService.GetAccounts([this.delegateId]), this.userAccessService.GetPolicyData(this.delegateId), this.accessService.GetRoles()]).subscribe({
          next: (result) => {
            this.account = result[0][0];    
            this.policyInfos = result[1][1];      
            this.userPolicyStore = result[1][0];
            this.userRoleStore = result[2];
        
            this.setupRoleInfos(result[2].Roles, this.userPolicyStore.Roles);
            this.IsPermissionsLoading = false
            this.IsRolesLoading = false;
          },
          error: (err) => {
            console.log(err);
            this.IsPermissionsLoading = false
            this.IsRolesLoading = false;
          }
        });
      }
  }

  setupRoleInfos(roleStore: Array<Role>, userRoleIds: Array<string>){
    for(let role of roleStore){
      var roleInfo = new RoleInfo();
      roleInfo.Role = role;

      var hasRole = userRoleIds.filter(x => x === role.Id);
      if(hasRole.length > 0){
        roleInfo.Active = true;        
      }

      this.roles.push(roleInfo);
    }
  }

  addRoleToUser(role: RoleInfo){
  
    if(this.delegateId && role.Role){
      role.IsLoading = true;
      this.accessService.AddRoleUserStore(this.delegateId, role.Role.Id).subscribe({
        next: (result) => {
          role.Active = true;
          role.IsLoading = false;
        },
        error: (err) => {
          role.IsLoading = false;
        }
      });
    }      
  }

  removeRoleFromUser(role: RoleInfo){
    if(this.delegateId && role.Role){  
      role.IsLoading = true;
      this.accessService.RemoveRoleUserStore(this.delegateId, role.Role.Id).subscribe({
        next: (result) => {
          role.Active = false;
          role.IsLoading = false;
        },
        error: (err) => {
          role.IsLoading = false;
        }
      });     
 
    }  
  }

  addPolicy(policyInfo: PolicyInfo) {    
    policyInfo.IsLoading = true;
    
    if (this.delegateId && policyInfo.SelectedAttribute) {
      if (policyInfo.SelectedAttribute?.Policy) {
        this.userAccessService.AddUserStorePolicy(this.delegateId, policyInfo.SelectedAttribute.Policy).subscribe({
          next: (result) => {
            policyInfo.Active = true;
            policyInfo.IsLoading = false;
          },
          error: (err) => {
            policyInfo.IsLoading = false;
          }
        });
      }

    } else {
      this.toastrService.warning("Please select an access type");
    } 
      

  }

  removePolicy(policyInfo: PolicyInfo) {
    if(this.delegateId){
      policyInfo.IsLoading = true;
    
      if(policyInfo.SelectedAttribute?.Policy){
        this.userAccessService.RemoveUserStorePolicy(this.delegateId, policyInfo.SelectedAttribute.Policy.Id).subscribe({
          next: (result) => {
            policyInfo.Active = false;
            policyInfo.IsLoading = false;
          },
          error: (err) => {
            policyInfo.IsLoading = false;
          }
        });
      }
    }    
  }
}
