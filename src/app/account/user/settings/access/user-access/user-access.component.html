  @if(delegateId !== userId){
    <!--page-title-->
    <div class="row my-4">
      <div class="col">
        <h1 class="page-title">{{ account?.Profile?.FirstName }} {{ account?.Profile?.LastName }}</h1>
      </div>
    </div>
    <!--user-policies-title-->
      <div class="row mb-4">
        <div class="col">
          <div class="text-light bg-dark p-2">Roles</div>
        </div>
      </div>
      <!--user-policies-instructions-->
      <div class="row mb-4">
        <div class="col">
          <span>Roles attached to this user.</span>
        </div>
      </div>
      <!--policies-->
      <div class="row mb-4">
        <div class="col">        
          <!-- <cm-loading id="rolesLoading" [show]="IsRolesLoading"></cm-loading>   -->
          <ul class="list-group">
            <li class="list-group-item bg-light">
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text" id="basic-addon1">
                    <i class="fa fa-filter" aria-hidden="true"></i>
                  </span>
                </div>
                <input type="search" class="form-control" placeholder="filter by name" [formControl]="searchRoles" />
              </div>
            </li>
            @for (role of roles; track $index) {
              <li class="list-group-item">
                <!-- <cm-loading id="roleLoading" [show]="role.IsLoading"></cm-loading> -->
                <div class="row align-items-center">
                    <div class="col-12 col-md-11 mb-2 mb-md-0">
                      <div class="text-truncate">{{ role.Role?.Name }}</div>
                    </div>
                    <div class="col-12 col-md-1 d-flex justify-content-md-end">
                      @if(role.Active){
                        <button type="button" class="btn btn-success" (click)="removeRoleFromUser(role)">
                          <i class="fas fa-check text-white"></i>
                        </button>
                      }@else {
                        <button type="button" class="btn btn-secondary" (click)="addRoleToUser(role)">
                          <i class="fas fa-minus"></i>
                        </button>
                      }                  
                
                    </div>
                </div>
              </li>
            }
     
          </ul>
        </div>
      </div>   
  
    <!--user-permissions-title-->
    <div class="row mb-4">
      <div class="col">
        <div class="text-light bg-dark p-2">Permissions</div>
      </div>
    </div>
    <!--user-permissions-instructions-->
    <div class="row mb-4">
      <div class="col">
        <span>Overide roles with policies specific to this individual.</span>
      </div>
    </div>
    <!--permissions-->
    <div class="row mb-4">
      <div class="col-12">
        <!-- <cm-loading id="permissionsLoading" [show]="IsPermissionsLoading"></cm-loading> -->
        <access-permissions
            (add-policy)="addPolicy($event)"
            (remove-policy)="removePolicy($event)"        
            [policies]="policyInfos">
        </access-permissions>
      </div>
    </div>
  }@else {
    <div class="alert alert-danger text-center my-5 py-1">
      You are not allowed to manage your own security settings as a delegate.
    </div>
  }
