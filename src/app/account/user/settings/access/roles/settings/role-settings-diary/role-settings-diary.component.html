<!-- page header -->
<header class="bg-light p-3 p-lg-4">
  <div class="container">
    <!-- page title -->
    <h1 class="page-title fs-5 mb-0">Role Settings</h1>
  </div>
</header>
<!-- role settings -->
<section class="p-3 p-lg-4">
  <div class="container">
    <div class="card mb-3">
      <div class="card-body">
        <h2 class="card-title fs-6 page-title">Diary</h2>
        @if (settingsTableService.mainForm()) {
          <form [formGroup]="settingsTableService.mainForm()">
          @if (service.error()) {
            <div class="alert alert-danger">
              {{ service.error() }}
            </div>
          }
          @if (service.isLoading()) {
            <div class="skeleton-loader">
              Loading settings...
            </div>
          } @else {
            <app-settings-table 
              [formArray]="settingsTableService.formArray" 
              [isLoading]="settingsTableService.isLoading">
            </app-settings-table>
          }
          <div class="d-flex justify-content-end">
            <button type="button" 
                   (click)="saveSettings()" 
                   [disabled]="service.isSaving() || !settingsTableService.formValid() || !settingsTableService.isDirty()" 
                   class="btn btn-primary">
              Save
            </button>
          </div>
          </form>
        } @else {
          <div class="skeleton-loader">Initializing form...</div>
        }
      </div>
    </div>
  </div>
</section>
