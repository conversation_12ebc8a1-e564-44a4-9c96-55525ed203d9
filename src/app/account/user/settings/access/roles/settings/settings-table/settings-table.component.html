<table class="table align-middle">
  <thead>
    <tr>
      <th scope="col">Section</th>
      <th scope="col">Display</th>
    </tr>
  </thead>
  <tbody>
    @if (isLoading()) {
      <tr>
        <td colspan="2" class="text-center">
          <div class="skeleton-loader">Loading settings...</div>
        </td>
      </tr>
    } @else if (formArray() && formArray()?.controls.length > 0) {
      @for (section of sectionFormGroups(); track trackByFn($index, section)) {
        <tr [formGroup]="section">
          <td>{{ section.get('name')?.value }}</td>
          <td class="text-end">
            <select class="form-select visibility-dropdown" formControlName="visibility">
              <option [value]="VisibilityType.SHOW">{{ VisibilityType.SHOW }}</option>
              <option [value]="VisibilityType.HIDE">{{ VisibilityType.HIDE }}</option>
            </select>
          </td>
        </tr>
      }
    } @else {
      <tr>
        <td colspan="2" class="text-center">No settings available</td>
      </tr>
    }
  </tbody>
</table>
