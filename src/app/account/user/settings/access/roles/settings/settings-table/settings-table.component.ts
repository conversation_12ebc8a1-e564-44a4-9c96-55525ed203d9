import { Component, Input, signal, effect, Signal } from '@angular/core';
import { ReactiveFormsModule, FormGroup, AbstractControl, FormArray } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { VisibilityType } from 'src/app/account/user/shared/enums/diary-section.enum';

/*
 * Design Rationale: Use of @Input Signals
 *
 * Per preference of "no @Inputs unless necessary," this presentational component uses
 * @Input signals (formArray, isLoading) to maintain reusability and simplicity.
 * 
 * Benefits:
 * 1. Reusability: Decoupled from specific services (e.g., SettingsTableService),
 * allowing use with any compatible formArray/isLoading signals.
 * 2. Clear Data Flow: Explicitly defines minimal data needed for table rendering.
 * 3. Robust Reactivity: Signals ensure reactive updates, aligning with Angular 19 best practices.
 * 4. Simplicity: Avoids over-engineering vs. service injection for direct parent-child data.
 * Coupling to a service would reduce reusability and violate SRP by mixing presentation
 * with state logic.
 */
@Component({
  selector: 'app-settings-table',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './settings-table.component.html',
  styles: []
})
export class SettingsTableComponent {
  @Input({ required: true }) formArray!: Signal<FormArray | null>;
  @Input({ required: true }) isLoading!: Signal<boolean>;
  
  readonly VisibilityType = VisibilityType;  
  readonly sectionFormGroups = signal<FormGroup[]>([]);
  
  /**
   * Track function for @for to improve performance
   * @param index The index of the item
   * @param section The form group representing a section
   * @returns The section name as a unique identifier, or index if name is missing
   */
  trackByFn(index: number, section: FormGroup): string | number {
    return section.get('name')?.value ?? index;
  }
  
  constructor() {
    // Effect to reactively update section form groups whenever the form array changes
    effect(() => {
      const formArray = this.formArray();
      if (formArray) {
        // Cast controls to FormGroup[] - safe because SettingsTableService ensures proper structure
        this.sectionFormGroups.set(formArray.controls as FormGroup[]);
      } else {
        this.sectionFormGroups.set([]);
      }
    });
  }
}
