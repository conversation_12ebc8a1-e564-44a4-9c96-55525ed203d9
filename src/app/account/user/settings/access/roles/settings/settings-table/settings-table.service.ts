import { Injectable, signal, computed, effect } from '@angular/core';
import { Form<PERSON><PERSON>y, FormBuilder, FormGroup, AbstractControl, Validators } from '@angular/forms';
import { VisibilityType } from 'src/app/account/user/shared/enums/diary-section.enum';

/**
 * Manages the reactive form state (FormArray, validation, dirty status) specifically 
 * for the SettingsTableComponent. Provided at the parent component level to ensure 
 * state isolation. Separates complex form logic from the parent component (SoC) 
 * using signals for clean state management.
 */
@Injectable()
export class SettingsTableService {
  private readonly _sections = signal<SettingsSection[]>([]);
  readonly sections = this._sections.asReadonly();
  
  private readonly _mainForm = signal<FormGroup | null>(null);
  readonly mainForm = this._mainForm.asReadonly();
  
  private readonly _formArray = signal<FormArray | null>(null);
  readonly formArray = this._formArray.asReadonly();
  
  readonly formValid = computed(() => this._mainForm()?.valid ?? false);
  
  private readonly _isLoading = signal<boolean>(false);
  readonly isLoading = this._isLoading.asReadonly();
  
  private readonly _formValueVersion = signal<number>(0);

  private readonly _valueChanged = signal(0);
  
  // Computed dirty state by comparing current form values with initial sections
  readonly isDirty = computed(() => {
    // Include _formValueVersion in the dependency tracking to ensure recomputation
    this._formValueVersion();
    
    const formArray = this._formArray();
    const initialSections = this._sections();
    
    if (!formArray || initialSections.length === 0) {
      return false;
    }
    
    // Deep comparison using JSON.stringify
    return JSON.stringify(formArray.value) !== JSON.stringify(initialSections);
  });
  
  constructor(private fb: FormBuilder) {
    // Effect 1: Manages the valueChanges subscription lifecycle
    effect((onCleanup) => {
      const formArray = this._formArray();
      if (formArray) {
        // Subscription to valueChanges is used because Angular signals cannot
        // natively track FormArray value mutations. This is wrapped in an effect
        // with onCleanup to ensure declarative lifecycle management, minimizing
        // imperative code while leveraging Reactive Forms' reliable change detection.
        const sub = formArray.valueChanges.subscribe(() => {
          this._valueChanged.update(v => v + 1);
        });
        onCleanup(() => sub.unsubscribe());
      }
    });

    // Effect 2: Updates _formValueVersion based on the _valueChanged trigger
    effect(() => {
      this._valueChanged(); // Depend on trigger
      this._formValueVersion.update(v => v + 1); // Update signal for isDirty
    }, { allowSignalWrites: true });
  }
  
  /**
   * Initialize the main form with a sections FormArray
   * @param sections The sections to initialize with
   * @returns The created FormGroup
   */
  initializeForm(sections?: SettingsSection[]): FormGroup {
    // Create the main form
    const form = this.fb.group({
      sections: this.fb.array([])
    });
    
    this._mainForm.set(form);
    
    // Initialize form array if sections are provided
    if (sections && sections.length > 0) {
      this.initializeFormArray(sections);
    } else {
      this._sections.set([]); // Clear sections if none provided
      this._formArray.set(null);
      
      // Reset version when clearing form
      this._formValueVersion.set(0);
    }
    
    return form;
  }
  
  /**
   * Initialize the form array with sections
   * @param sections The sections to initialize with
   * @returns The created FormArray
   */
  initializeFormArray(sections: SettingsSection[]): FormArray {
    // Set the sections state (used as the initial state for dirty checking)
    this._sections.set([...sections]); // Create a copy to ensure reference equality works correctly
    
    // Create form groups for each section
    const formGroups = sections.map(section => 
      this.fb.group({
        name: [section.name],
        visibility: [section.visibility, Validators.required]
      })
    );
    
    // Create form array
    const formArray = this.fb.array(formGroups);
    
    this._formArray.set(formArray); // Update the form array signal
    
    // Reset version counter when initializing/re-initializing
    this._formValueVersion.set(0);
    
    // Update the form with the new form array
    if (this._mainForm()) {
      this._mainForm()?.setControl('sections', formArray);
    }
    
    return formArray;
  }
  
  /**
   * Get sections reflecting the CURRENT form state for saving/mapping.
   * @returns The updated sections based on current form values
   */
  getCurrentSections(): SettingsSection[] {
    const formArr = this._formArray();
    if (!formArr) {
      return [];
    }

    // Use value to get current values from the form controls
    return formArr.value as SettingsSection[];
  }

  /**
   * Set loading state
   * @param isLoading Whether the service is loading
   */
  setLoading(isLoading: boolean): void {
    this._isLoading.set(isLoading);
  }
  
  /**
   * Get current sections (returns the initial sections used for comparison)
   * @returns The initial sections
   */
  getSections(): SettingsSection[] {
    return this.sections(); 
  }
  
  /**
   * Cast AbstractControl to FormGroup
   * This is needed for the [formGroup] directive in the template
   * @param control The control to cast
   * @returns The control as a FormGroup
   */
  getFormGroup(control: AbstractControl): FormGroup {
    return control as FormGroup;
  }
  
  /**
   * Get sections in the format expected by the API, based on current form values
   * @param mapper A function to map SettingsSection to the desired output format
   * @returns The mapped sections reflecting current form state
   */
  getMappedSections<T>(mapper: (section: SettingsSection) => T): T[] {
    return this.getCurrentSections().map(mapper);
  }
  
  /**
   * Track function for ngFor to improve performance
   * @param index The index of the item
   * @param section The form group representing a section (might be AbstractControl)
   * @returns The section name as a unique identifier, or index if name/section is missing
   */
  trackByFn(index: number, section: AbstractControl | undefined | null): string | number {
    if (section instanceof FormGroup) {
      return section.get('name')?.value ?? index; 
    }
    return index;
  }
}

/**
 * Interface representing a settings section with a name and visibility setting
 */
export interface SettingsSection {
  name: string;
  visibility: VisibilityType | string;
}
