import { Component, OnInit, inject, effect, computed } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { RoleSettingsDiaryService, DiarySection } from 'src/app/account/shared/data-access/role-settings-diary-service';
import { CommonModule } from '@angular/common';
import { SettingsTableComponent } from '../settings-table/settings-table.component';
import { SettingsTableService, SettingsSection } from '../settings-table/settings-table.service';

@Component({
  selector: 'app-role-settings-diary',
  templateUrl: './role-settings-diary.component.html',
  styleUrls: ['./role-settings-diary.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SettingsTableComponent
  ],
  providers: [SettingsTableService]
})
export class RoleSettingsDiaryComponent implements OnInit {
  readonly service = inject(RoleSettingsDiaryService);
  readonly settingsTableService = inject(SettingsTableService);
  private readonly route = inject(ActivatedRoute);
  private readonly toastr = inject(ToastrService);
  
  roleId: string | null = null;
  
  // Computed signal for mapping diary sections to settings sections
  readonly mappedSettingsSections = computed(() => {
    const diarySections = this.service.diarySections();
    
    if (!diarySections || diarySections.length === 0) {
      return [];
    }
    
    return diarySections.map(section => ({
      name: section.Component,
      visibility: section.SettingValue
    }));
  });
  
  constructor() {
    effect(() => {
      const settingsSections = this.mappedSettingsSections();
      this.settingsTableService.setLoading(true);
      
      if (settingsSections?.length > 0) {
        this.settingsTableService.initializeFormArray(settingsSections);
      } else {
        this.settingsTableService.initializeForm([]);
      }
      
      this.settingsTableService.setLoading(false);
    });
  }

  ngOnInit(): void {
    this.roleId = this.route.snapshot.paramMap.get('roleId');
    
    this.settingsTableService.initializeForm([]);
    
    if (this.roleId && typeof this.roleId === 'string' && this.roleId.trim() !== '') {
      this.service.setRoleId(this.roleId);
    } else {
      this.toastr.error('Invalid role ID');
      this.service.setRoleId(null);
    }
  }

  /**
   * Save settings to the service
   * This method optimizes the save operation to prevent screen flickering
   * by directly updating the local state instead of reloading from the server
   */
  saveSettings(): void {
    if (!this.roleId || !this.settingsTableService.formValid()) return;
    
    const currentSectionsToSave = this.settingsTableService.getCurrentSections();
    
    // Map the form values to the API format
    const sectionsPayload: DiarySection[] = currentSectionsToSave.map((section: SettingsSection) => ({
      Component: section.name,
      SettingValue: section.visibility
    }));
    
    const payload = this.service.createSavePayload(this.roleId, sectionsPayload);
    
    // Set the form to loading state to prevent user interaction during save
    this.settingsTableService.setLoading(true);
    
    this.service.saveRoleSettings(this.roleId, payload).subscribe({
      next: () => {
        this.toastr.success('Settings saved successfully.');
        
        // Update the initial state in the settings table service to match the current state
        // This prevents the form from being marked as dirty after saving
        this.settingsTableService.initializeFormArray(currentSectionsToSave);
        
        this.settingsTableService.setLoading(false);
      },
      error: () => {
        this.toastr.error('Failed to save settings.');
        this.settingsTableService.setLoading(false);
      }
    });
  }

}
