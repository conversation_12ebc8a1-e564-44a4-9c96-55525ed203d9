import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Role } from 'src/app/account/user/shared/interfaces/user-role-store';
import { RoleSettingsDiaryComponent } from '../role-settings-diary/role-settings-diary.component';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-settings',
  imports: [CommonModule, RoleSettingsDiaryComponent],
  templateUrl: './role-settings.component.html',
  styleUrl: './role-settings.component.css'
})
export class RoleSettingsComponent {
  isSettingsLoading: boolean = false;
  isSaving: boolean = false;
  role: Role | null = null;
  aRouter: any;
  userAccessService: any;
  policies: any;
  private route = inject(ActivatedRoute);
  
  constructor() {}  

  ngOnInit() {
    this.initialize();
  }

  initialize(){

      this.isSettingsLoading = false;

      const roleId = this.route.snapshot.paramMap.get('roleId');

      if (!roleId) {
        console.error('Role ID is missing from the route');
        this.isSettingsLoading = false;
        return;
      }  
  }
}
