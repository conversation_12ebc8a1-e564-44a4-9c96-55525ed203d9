﻿<div class="container p-4 mb-4">
  <!-- header -->
  <header>
    <div class="d-flex justify-content-between">
      <h1 class="fs-5 mb-0">Users</h1>
      <div>
        @if(addDelegateUserAccess === 'Allow'){
        <a class="btn btn-primary text-white" type="button" routerLink="../../invitation/manager">+ Invite</a>
        }
      </div>
    </div>
    <p>Add users and assign them roles. Users must register first.</p>
  </header>
  <!-- search -->
  <section class="col-12 col-md-6 col-xl-4 mb-3">
    <div class="input-group">
      <span class="input-group-text" id="basic-addon1"><i class="fas fa-search"></i></span>
      <input type="search" class="form-control" [formControl]="search" placeholder="Search by name or title"
        aria-label="Username" aria-describedby="basic-addon1">
      <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
        aria-expanded="false"><i class="fa fa-sort" aria-hidden="true"></i></button>
      <ul class="dropdown-menu dropdown-menu-end">
        <li>
          <a class="dropdown-item" href="javascript:void(0)" (click)="sort('Title', false)">
            Sort by Title
            <i class="fa fa-caret-down me-1" aria-hidden="true"></i>
          </a>
        </li>
        <li>
          <a class="dropdown-item" href="javascript:void(0)" (click)="sort('Title', true)">
            Sort by Title
            <i class="fa fa-caret-up me-1" aria-hidden="true"></i>
          </a>
        </li>
      </ul>
    </div>
  </section>
  <!-- users -->
  <section>
    @if(loading || loadingAccess){
    <div class="mb-4">
      <app-list-skeleton></app-list-skeleton>
    </div>
    }@else{
    <ul class="list-group mb-4">
      @for (person of personInfos | filterByImpure: ['group']: 'active' | ccOrderBy: obSetting:isReversed; track
      $index) {
      <li class="list-group-item">
        <!-- <cm-loading [show]=person.isLoading></cm-loading> -->
        <div *ngIf="person.roleError" class="alert alert-danger">{{ person.roleError }}</div>
        <div class="row">
          <div class="col-12 col-lg-6 mb-2 mb-lg-0">
            <div class="fw-bold">
              {{ person.accountInfo?.Profile?.FirstName }} {{ person.accountInfo?.Profile?.LastName}}
            </div>
            <div class="text-secondary small">
              Title: {{ person.delegateInfo?.Title }}
            </div>
            <div class="text-secondary small">
              Username: {{ person.accountInfo?.Username }}
            </div>
          </div>
          <div class="col-12 col-lg-6 d-flex align-items-center justify-content-lg-end">
            @if (person.userId !== userId) {
            <div class="d-flex justify-content-end">
              @if(person.roleError){
              <button type="button" class="btn btn-outline-secondary me-3" (click)="clearAllRoles(person)"
                [disabled]="person.isLoading">
                <i class="fa fa-circle-notch fa-spin fa-1x me-2" *ngIf="person.isLoading"></i>
                Clear Roles
              </button>
              }

              @if(changeRoleAccess === 'Allow'){

              <select [(ngModel)]="person.roleId" class="form-select me-2" (change)="updateRole(person);"
                [disabled]="person.roleError || person.isLoading">

                <option value="">None</option>
                @for (role of userRoleStore?.Roles; track $index) {
                <option [ngValue]="role.Id">
                  {{role.Name}}
                </option>
                }
              </select>
              }

              <div class="dropdown">
                @if(editDelegateAccess === 'Allow'){
                <button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
                  data-bs-toggle="dropdown" aria-expanded="false" [disabled]="aUser?.userId === userId">
                  <i class="fa fa-pencil" aria-hidden="true"></i>
                </button>
                }

                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
                  <li>
                    <a class="dropdown-item" href="javascript:void(0)" (click)="openDelegateSettings(person)">Info</a>
                  </li>
                  <li>
                    <a class="dropdown-item" href="javascript:void(0)"
                      (click)="deactivateDelegate(person)">Deactivate</a>
                  </li>
                </ul>
              </div>
            </div>
            }

            @if(person.userId === userId && editDelegateAccess === 'Allow'){
            <div>
              You can not edit your own account.
            </div>
            }

          </div>
        </div>
      </li>
      }

    </ul>
    }
    @if(!loading && !loadingAccess){
    <button class="btn mb-3" type="button" (click)="showDeactivatedUsers = !showDeactivatedUsers"
      [ngClass]="{'btn-outline-danger': showDeactivatedUsers, 'btn-outline-dark': !showDeactivatedUsers}">
      Show Deactived Users
    </button>
    }
    @if(showDeactivatedUsers){
    <div id="deactivatedusers">
      <ul class="list-group">
        <li class="list-group-item bg-danger text-white">
          <div>Deactivated Users</div>
          <div class="small">These users do not have access to your account.</div>
        </li>
        <li class="list-group-item" *ngIf="personCountStatus.deactivatedCount <= 0">
          No deactivated users
        </li>
        @for (person of personInfos | filterByImpure: ['group']: 'deactivated' | ccOrderBy: obSetting:isReversed;
        track $index) {
        <li class="list-group-item">
          <!-- <cm-loading [show]=person.isLoading></cm-loading> -->
          <div class="row">
            <div class="col-12 col-lg-6 mb-2 mb-lg-0">
              <div class="fw-bold">
                {{ person.accountInfo?.Profile?.FirstName }} {{ person.accountInfo?.Profile?.LastName}}
              </div>
              <div class="text-secondary small">
                Title: {{ person.delegateInfo?.Title }}
              </div>
              <div class="text-secondary small">
                Username: {{ person.accountInfo?.Username }}
              </div>
            </div>
            <div class="col-12 col-lg-6 d-flex align-items-center justify-content-lg-end">
              <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton1"
                  data-bs-toggle="dropdown" aria-expanded="false" [disabled]="aUser?.userId === person.userId">
                  <i class="fa fa-pencil" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1" *ngIf="editDelegateAccess === 'Allow'">
                  <li>
                    <a class="dropdown-item" href="javascript:void(0)" (click)="activateDelegate(person)">
                      Activate
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item" href="javascript:void(0)" (click)="removeDelegate(person)">Remove</a>
                  </li>
                </ul>
              </div>
              @if(editDelegateAccess === 'Allow' && person.userId === userId){
              <div>
                You can not edit your own account.
              </div>
              }
            </div>
          </div>
        </li>
        }
      </ul>
    </div>
    }
  </section>
</div>