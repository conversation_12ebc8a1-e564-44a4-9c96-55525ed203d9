﻿import { Component, OnInit, inject } from '@angular/core';
import { Router, ActivatedRoute, RouterLink } from '@angular/router';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { orderBy } from 'lodash';
import { AccountDelegateStore, Delegate } from 'src/app/shared/interfaces/delegate';
// import { DelegateService } from 'src/app/user/shared/data-access/delegate.service';
import { UserService } from 'src/app/shared/data-access/user.service';
import { UserRoleStore } from 'src/app/models/user-role-store';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { debounceTime, forkJoin, Observable, Subject } from 'rxjs';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { CommonModule } from '@angular/common';
import { OrderPipe } from 'src/app/shared/utils/pipes/order-by-pipe';
import { AccountService } from '../../../../shared/data-access/account-service';
import { NgPipesModule } from 'ngx-pipes';
import { User, CMUser } from 'src/app/shared/interfaces/user';
import { AccountProfile, CivCastAccount, Company } from 'src/app/shared/interfaces/account-profile';
import { ListSkeletonComponent } from 'src/app/components/skeletons/list-skeleton/list-skeleton.component';
import { DelegateService } from '../../shared/data-access/delegate.service';

@Component({
    selector: 'project-settings-delegates',
    templateUrl: './delegate.settings.component.html',
    styleUrls: ['./delegate.settings.component.css'],
    imports: [CommonModule, FormsModule, ReactiveFormsModule, NgPipesModule, OrderPipe, RouterLink, ListSkeletonComponent]
})
export class DelegatesSettingsComponent implements OnInit {
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  authService = inject(AuthService);
  delegeteService = inject(DelegateService);
  userService = inject(UserService);
  toastr = inject(ToastrService);
  accessService = inject(AccessService);
  accountService = inject(AccountService);
  confirmService = inject(ConfirmService);
  user: CMUser | null = null;
  aUser: User | null = null;
  userId: string | null = null;
  obSetting: string = 'LastName';
  isReversed: boolean = false;
  search = new UntypedFormControl();
  accountDelegateStore: AccountDelegateStore | null = null;
  loading: boolean = false;
  loadingAccess: boolean = false;
  personInfos: Array<PersonInfo> = [];
  cPersonInfos: Array<PersonInfo> = [];
  userRoleStore: UserRoleStore | null = null;
  changeRoleAccess: string | null = null;
  editDelegateAccess: string | null = null;
  addDelegateUserAccess: string | null = null;
  showDeactivatedUsers: boolean = false;
  personCountStatus: any = {
    activeCount: 0,
    deactivatedCount: 0
  }

  private roleUpdateSubject = new Subject<{ person: any }>();
  constructor() { }

  ngOnInit() {
    this.authService.getSubId().then((subId) => {
      this.userId = subId;
    });

    this.getDelegates();
    this.getAccess();

    this.search.valueChanges.pipe(debounceTime(400)).subscribe({
      next: (value: string) => {
        let d = value.toLowerCase();

        this.personInfos = this.cPersonInfos.filter(
          x => x.accountInfo != null
            && x.accountInfo.Profile !== null
            && (x.accountInfo.Profile?.FirstName?.toLowerCase().indexOf(d) > -1 
            || x.accountInfo.Profile?.LastName?.toLowerCase().indexOf(d) > -1 
            || (x.delegateInfo && x.delegateInfo.Title?.toLowerCase().indexOf(d) > -1))
        );

        this.verifyGroupStatus(this.personInfos);
      },
      error: (err) => {
        console.log(err);
      }
    });

    this.roleUpdateSubject.pipe(debounceTime(300)).subscribe(({ person }) => {
      this.updateRole(person);
    });

    this.accessService.GetRoles().subscribe({
      next: (result) => {
        this.userRoleStore = result;
      },
      error: (err) => {
        this.toastr.error("An error occured gathering roles from this account");
      }
    });
  }

   updateRoleSelection(person: PersonInfo) {
      this.roleUpdateSubject.next({ person });
   }
  
  getAccess() {
    this.loadingAccess = true;

    forkJoin([this.accessService.CheckAccess("acl", "addroletouser"), this.accessService.CheckAccess("delegate-account", "savedelegate"), this.accessService.CheckAccess("delegate-account", "adddelegateuser")]).subscribe({
      next: (result) => {
        this.changeRoleAccess = result[0].Access;
        this.editDelegateAccess = result[1].Access;
        this.addDelegateUserAccess = result[2].Access;
        this.loadingAccess = false;
      },
      error: (err) => {
        this.loadingAccess = false;
      }
    });
  }

  getDelegates() {
    this.loading = true;
    this.accountDelegateStore = null;
    this.personInfos = [];
    this.delegeteService.GetDelegates().subscribe({
      next: (accountDelegateStore) => {
        this.accountDelegateStore = accountDelegateStore;

        var userIds = new Array<string>();

        for (let user of accountDelegateStore.Users) {
          var nUser = {} as PersonInfo;
          nUser.userId = user.UserId;

          if (user.Deactivated) {
            nUser.group = "deactivated";
          } else {
            nUser.group = "active";
          }

          nUser.delegateInfo = user;

          this.personInfos.push(nUser);

          userIds.push(user.UserId);
        }

        this.cPersonInfos = Object.assign([], this.personInfos);

        forkJoin({ accountInfos: this.accountService.GetAccounts(userIds), response: this.accessService.GetUserStores(userIds) }).subscribe({
          next: ({accountInfos, response}) => {
            for (let pUser of this.personInfos) {
              var account = accountInfos.find(x => x.CognitoUserId === pUser.userId);

              if (account) {
                pUser.accountInfo = account;
              } else {
                pUser.accountInfo = {} as CivCastAccount;
                pUser.userId = pUser.userId;
                pUser.accountInfo.Profile = {} as AccountProfile;
                pUser.accountInfo.Profile.FirstName = "Account";
                pUser.accountInfo.Profile.LastName = `Not Found (${pUser.userId})`;
                pUser.accountInfo.Profile.Username = pUser.userId;
                pUser.accountInfo.CognitoUserId = pUser.userId;
                pUser.accountInfo.Profile.Company = {} as Company;
                pUser.accountInfo.Profile.Company.Name = "Not Found";
                
                console.log(`user info not found ${pUser.userId}`);
              }

              var userStoreInfo = response.UserPolicyStores.find(x => x.UserId === pUser.userId)

              if (userStoreInfo) {
                if (userStoreInfo.Roles.length > 1) {
                  pUser.roleError = "User can only be assigned 1 role. There are too many roles on this user. Please clear roles and select a new role";
                } else if(userStoreInfo.Roles.length > 0){
                  pUser.originalRole = userStoreInfo.Roles[0];
                  pUser.roleId = userStoreInfo.Roles[0];
                }else{
                  pUser.originalRole = null;
                  pUser.roleId = null;
                }
              }
            }

            this.loading = false;
            this.verifyGroupStatus(this.personInfos);
          },
          error: (err) => {
            this.loading = false;
          }
        });
      },
      error: (err) => {
        this.loading = false;
      }
    });
  }

  clearAllRoles(person: PersonInfo) {

    if (person) {
      person.isLoading = true;
      this.accessService.GetUserStore(person.userId).subscribe({
        next: (userStore) => {
          var rolesToRemove = new Array<Observable<any>>();
          for (let role of userStore.Roles) {
            rolesToRemove.push(this.accessService.RemoveRoleUserStore(person.userId, role));
          }

          forkJoin(rolesToRemove).subscribe({
            next: (result) => {
              person.originalRole = null;
              person.roleId = null;
              person.isLoading = false;
              person.roleError = null;              
            },
            error: (err) => {
              console.log(err);
              person.isLoading = false;
            }
          });
        },
        error: (err) => {
          person.isLoading = true;
        }
      });
    }

  }

  updateRole(person: PersonInfo) {
    person.isLoading = true;

    if (person.roleId === "" && person.originalRole) {
      this.accessService.RemoveRoleUserStore(person.userId, person.originalRole).subscribe({
        next: (result) => {
          person.roleId = null;
          person.originalRole = null;
          person.isLoading = false;
        },
        error: (err) => {
          console.log(err);
          person.isLoading = false;
        }
      });      

    } else if (person.roleId && person.originalRole) {
        this.accessService.RemoveRoleUserStore(person.userId, person.originalRole).subscribe({
          next: (result) => {
            if (person.roleId) {
              this.accessService.AddRoleUserStore(person.userId, person.roleId).subscribe({
                next: (result) => {
                  person.originalRole = person.roleId;
                  person.isLoading = false;
                },
                error: (err) => {
                  person.isLoading = false;
                }
              });
            }
          },
          error: (err) => {
            console.log(err);
            person.isLoading = false;
          }
        });
     
    }else{
      if (person.userId && person.roleId && !person.originalRole) {
        this.accessService.AddRoleUserStore(person.userId, person.roleId).subscribe({
          next: (result) => {
            person.originalRole = person.roleId;
            person.isLoading = false;
          },
          error: (err) => {
            console.log(err);
            person.isLoading = false;
          }
        });
      }else{
        this.toastr.error("There was a condition in which we could not update the role. Please contact support");
        person.isLoading = false;
      }      
    }
  }

  verifyGroupStatus(personInfo: Array<PersonInfo>) {
    if (personInfo) {
      this.personCountStatus.activeCount = personInfo.filter(x => x.group === 'active').length;
      this.personCountStatus.deactivatedCount = personInfo.filter(x => x.group === 'deactivated').length;
    }

    this.personInfos = orderBy([...this.personInfos], item => item.accountInfo?.Profile.LastName, ['asc']);
  }

  open() {
    this.router.navigate(['invitation', "manager"], { relativeTo: this.aRoute })
  }

  openDelegateSettings(person: PersonInfo) {
    this.router.navigate([person.userId], { relativeTo: this.aRoute });
  }

  activateDelegate(person: PersonInfo) {   
    person.isLoading = true;

    this.delegeteService.ActivateDelegate(person.userId).subscribe({
      next: (result) => {
        person.group = "active";
        this.verifyGroupStatus(this.personInfos);
        person.isLoading = false;
      },
      error: (err) => {
        console.log(err);
        person.isLoading = false;
      }
    });

  }

  deactivateDelegate(person: PersonInfo) {
    
    person.isLoading = true;
    this.delegeteService.DeactivateDelegate(person.userId).subscribe({
      next: (result) => {
        person.group = "deactivated";
        person.originalRole = null;
        person.roleId = null;
        this.verifyGroupStatus(this.personInfos);
        this.showDeactivatedUsers = true;
        person.isLoading = false;
      },
      error: (err) => {
        person.isLoading = false;
      }
    });

    if (person.originalRole) {
      this.accessService.RemoveRoleUserStore(person.userId, person.originalRole).subscribe({
        next: (result) => {

        },
        error: (err) => {

        }
      });
    }
  }

  editPermissions(person: PersonInfo) {
    this.router.navigate(['access', 'manage', 'user-store', person.userId], { relativeTo: this.aRoute.parent });
  }

  removeCurrentDelegate() {
    this.delegeteService.RemoveCurrentDelegate().subscribe({
      next: (response) => {
        if(this.user){
          this.user.CurrentDelegation = null;
          this.userService.cmUser.next(null);
        }
 
      }, error: (err) => {

      }
    });
  }

  removeDelegate(person: PersonInfo) {

    this.confirmService.open("Deleting a user is not recommend. You will lose the ability to filter on this user when you create reports. Are you sure you want to continue?").result.then(async value => {

      try {
        person["isLoading"] = true;
        await this.delegeteService.RemoveDelegate(person.userId).toPromise();

        var nIdx = this.personInfos.indexOf(person);

        this.personInfos.splice(nIdx, 1);
        this.verifyGroupStatus(this.personInfos);

        this.toastr.success(`Sucessfully removed`);
      } catch (error) {

      } finally {
        person["isLoading"] = false;
      }
    });
  }

  sort(name: string, isReversed: boolean) {
    this.obSetting = name;
    this.isReversed = isReversed;
  }
}

export interface PersonInfo {
  delegateInfo: Delegate | null;
  accountInfo: CivCastAccount | null;
  originalRole: string | null;
  roleId: string | null;
  userId: string;
  group: string | null;
  isLoading: boolean;
  roleError: string | null;
}
