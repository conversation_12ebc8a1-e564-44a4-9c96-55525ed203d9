<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">Landing Page Settings</h1>
	<!-- landing page settings -->
	<section class="mb-3">
		<!-- select default hub -->
		<div class="col-md-6 mb-4">
			<label for="" class="form-label">Redirect me to this page when I log in.</label>
			<select [(ngModel)]="accountSettings.DefaultLinkId" class="form-select">
				@for (hub of hubs; track $index) {
				<option [ngValue]="hub.Id">
					{{ hub.Name }}
				</option>
				}
			</select>
		</div>
		<!-- select the hubs you want to see -->
		<div>
			<label for="" class="form-label">Select the hubs you want to see.</label>
			<ul class="list-group">
				@for (hub of hubs; track $index) {
				<li class="list-group-item">
					<div class="row align-items-center">
						<div class="col-12 col-md-8 mb-2 mb-md-0">
							<div>{{ hub.Name }}</div>
							<div class="text-muted small"> {{ hub.Description }}</div>
						</div>
						<div class="col-12 col-md-4">
							<div>
								<select class="form-select" (change)="hideShowHub(hub)"
									aria-label="Default select example">
									<option [selected]="!hub.IsHidden">Show</option>
									<option [selected]="hub.IsHidden">Hide</option>
								</select>
							</div>
						</div>
					</div>
				</li>
				}
			</ul>
		</div>
	</section>
	<!-- footer -->
	<footer class="d-flex justify-content-end">
		<button class="btn btn-primary" (click)="saveAccountSettings()" [disabled]="isSaving">
			@if(isSaving){
			<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
			}
			Save
		</button>
	</footer>
</div>