import { Component, OnInit, inject } from '@angular/core';
import { HubService } from 'src/app/shared/data-access/hub-service';
import { cloneDeep} from 'lodash';
import { ToastrService } from 'ngx-toastr';
// import { AccountSettings } from '../../../account/user/shared/interfaces/account-settings';
import { AccountsSettingsService } from '../../shared/data-access/accounts-settings-service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HubRoots } from 'src/app/shared/interfaces/hub-root-constants';
import { HubInfo } from 'src/app/shared/interfaces/hub-info';
import { AccountSettings } from '../../shared/interfaces/account-settings';

@Component({
    selector: 'app-account-settings',
    templateUrl: './account-settings.component.html',
    styleUrls: ['./account-settings.component.css'],
    imports: [CommonModule, ReactiveFormsModule, FormsModule]
})
export class AccountSettingsComponent implements OnInit {
  accountSettingsService = inject(AccountsSettingsService);
  toastrService = inject(ToastrService);
  hubService = inject(HubService);
  accountSettings: AccountSettings = {
    Id: "",
    UserId: "",
    DefaultLinkId: HubRoots.default_hub_id,
    HideLinkIds: new Array<string>()
  };
  hubs:Array<HubInfo> = [];
  isSaving:boolean = false;
  constructor(){}
  ngOnInit(): void {
    var hubInfo = this.hubService.getHubs()
    this.hubs = cloneDeep(hubInfo);   

    this.accountSettingsService.getSettings().subscribe(
      {
        next: (result) => {
          if(result){
            this.accountSettings = result;
          }
        },error: (err) => {
          console.log(err);
        }, complete: () => {
      
        }
      }); 
  }


  hideShowHub(hub: any){
    hub.IsHidden = !hub.IsHidden;
  }
  saveAccountSettings(){
    this.isSaving = true;
    this.accountSettings.HideLinkIds = [];

    for(let hub of this.hubs){
        if(hub.IsHidden){
          this.accountSettings.HideLinkIds.push(hub.Id);
        }
    }

    var defaultHidden = this.accountSettingsService.isSettingHidden(this.accountSettings.DefaultLinkId, this.accountSettings);

    this.accountSettingsService.update(this.accountSettings).subscribe({
      next: (result) => {
        this.hubService.hubUpdate.next(result);
        this.accountSettings = result;
        this.toastrService.success("Account settings has been saved!");
        this.isSaving = false;
      },error: (err) => {
        this.isSaving = false;
        this.toastrService.error(`Account settings issue ${err.Message}`);
      }
    });
  }

}