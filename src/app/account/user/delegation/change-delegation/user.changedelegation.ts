﻿import { Component, OnInit, Input, Ng<PERSON>one, inject } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { DelegateLocalStorageService } from 'src/app/shared/data-access/delegate-localstorage-service';
// import { DelegateService } from '../../../../user/shared/data-access/delegate.service';
// import { AccountsSettingsService } from 'src/app/user/shared/data-access/accounts-settings-service';
import { AccountService } from '../../../../shared/data-access/account-service';
import { CommonModule } from '@angular/common';
import { NavService } from 'src/app/shared/data-access/nav.service';
import { delay, forkJoin, mergeMap } from 'rxjs';
import { AccountsSettingsService } from '../../shared/data-access/accounts-settings-service';
import { DelegateService } from '../../shared/data-access/delegate.service';
import { EmployeeService } from 'src/app/construction/shared/data-access/employee.service';
import { ConstructionProjectsService } from 'src/app/construction/shared/data-access/projects.service';

@Component({
    selector: 'user-changedelegation',
    templateUrl: './user.changedelegation.html',
    styleUrls: ["./user-delegation.css"],
    imports: [CommonModule]
})
export class UserChangeDelegationComponent implements OnInit {
  private router = inject(Router);
  aRoute = inject(ActivatedRoute);
  accountService = inject(AccountService);
  navService = inject(NavService);
  delegateService = inject(DelegateService);
  accountsSettingsService = inject(AccountsSettingsService);
  zone = inject(NgZone);
  delegateLocalService = inject(DelegateLocalStorageService);
  employeeService = inject(EmployeeService);
  projectsService = inject(ConstructionProjectsService);
  message: string = "";
  delay: number = 1500;

  constructor(
 ) { }

  ngOnInit() {   

    var userId = this.aRoute.snapshot.queryParams['did'];
    if (userId) {
      if (userId === "root") {
        this.removeCurrentDelegate();
      } else {
        this.accessDelegate(userId);
      }
    } else {
      this.message = "No Delegate Id Found";
    } 
  }

  accessDelegate(userId: string) {
    this.message = "Securely Requesting Access to Delegate";
    this.delegateLocalService.ClearDelegationIdFromClientStore()
    this.accountService.RemoveAccountIdentity();
    this.delegateService.AccessDelegate(userId).pipe(delay(this.delay), mergeMap(() => this.delegateLocalService.CreateAndGetDelegatedId())).subscribe({
      next: (result) => {
        this.message = "Routing you to your default page";
        this.clearGlobalServices();
        this.accountsSettingsService.getDefaultHub().pipe(delay(this.delay)).subscribe({
          next: (result) => {
            if(result?.Route){
              this.router.navigate([result.Route]);
            }else{
              this.router.navigate(["/"]);
            }
          },
          error: (err) => {
            console.log(err);
            this.navService.resetNav.next(true);
          }
        });
      },error: (err) => {
        console.log(err);
        this.navService.resetNav.next(true);
      }
 
    });
  }

  removeCurrentDelegate() {
    this.message = "Securely Removing Delegation";
    this.delegateLocalService.ClearDelegationIdFromClientStore();
    this.accountService.RemoveAccountIdentity();
    this.delegateService.RemoveCurrentDelegate().pipe(delay(this.delay), mergeMap(() => this.delegateLocalService.CreateAndGetDelegatedId())).subscribe({
      next: (result) => {
        this.message = "Routing you to your default page";
        this.clearGlobalServices();
        this.accountsSettingsService.getDefaultHub().pipe(delay(this.delay)).subscribe({
          next: (result) => {
            if(result?.Route){
              this.router.navigate([result.Route]);
            }else{
              this.router.navigate(["/"]);
            }
          },
          error: (err) => {
            console.log(err);
            this.navService.resetNav.next(true);
          }
        });
      },
      error: (err) => {
        console.log(err);
        this.navService.resetNav.next(true);
      }
    });
  }

  clearGlobalServices(){
    this.employeeService.employeeResource.set(null);
    this.employeeService.employeeId.set(null);
    this.employeeService.employeeStoreResource.set(null);
    this.employeeService.executeEmployeeStore.set(false);
    this.projectsService.initialize.set(false);
    this.delegateService.initialize.set(false);
    this.delegateService.delegateNavStoreResource.set(null);
  }

}
