﻿<div class="row my-4">
  <!--page title-->
  <div class="col-12">
    <h1 class="page-title fs-4">Add Users</h1>
  </div>
  <!--search users-->
  <div class="col-12 mb-2">
    <div class="input-group">
      <span class="input-group-text" id="basic-addon1">
        <i class="fa fa-search" aria-hidden="true"></i>
      </span>
      <input type="text" class="form-control" autofocus [(ngModel)]="search" (keyup.enter)="searchUsers()"
        placeholder="Search by username or company" aria-describedby="basic-addon1" />
      <button class="btn btn-outline-secondary" type="button" (click)="searchUsers()">Search</button>
      <button class="btn btn-primary" type="button" (click)="addDelegates()">Save</button>
    </div>
  </div>
  <!--list of users -->
  <div class="col-12">
    <ul class="list-group">
      <!--loader -->
      @if(isLoading){
        <li class="list-group-item" style="height: 200px;">
          <cm-loading id="delegateLoader" [show]="true"></cm-loading>
        </li>  
      }@else {
        <!--users -->
        @for (person of accounts; track $index) {
          <li class="list-group-item">
            <div class="row">
              <div class="col-12 col-md-8 flex-column">
                <div class="fw-bold">{{ person.Profile.FirstName}} {{ person.Profile.LastName}}</div>
                <div class="small text-muted">Company: {{ person.Profile.Company.Name }}</div>
                <div class="small text-muted">Username: {{ person.Username }}</div>
              </div>
              <div class="col-12 col-md-4 d-flex justify-content-end align-items-center">
                @if(!person.IsOnList && person.CognitoUserId){
                  <button type="button" class="btn btn-primary" [class.active]="person.IsActive"
                    (click)="checkDelegate(person)">
                    @if(person.IsActive){
                      <span class="me-2">Selected</span>
                      <i class="fa fa-check"></i>
                    }@else {
                      <span>Select User</span>
                    }
                  </button>
                }

                @if(!person.CognitoUserId){
                  <span class="badge bg-warning text-dark">Incomplete Registration</span>
                }
          
                @if(person.IsOnList){
                  <span class="badge bg-info text-dark">Already a User</span>
                }
           
              </div>
            </div>
          </li>
        }
     
      }

    </ul>
  </div>
</div>