﻿import { Component, Input, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
// import { DelegateService } from '../../../../user/shared/data-access/delegate.service';
import { AccountService } from '../../../../shared/data-access/account-service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Delegate } from '../../shared/interfaces/delegate';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';
import { DelegateService } from '../../shared/data-access/delegate.service';

@Component({
  selector: 'add-delegate-component',
  templateUrl: './add-delegate-component.html',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, CMLoaderModule]
})
export class AddDelegateComponent implements OnInit {
  delegateService = inject(DelegateService);
  router = inject(Router);
  aRouter = inject(ActivatedRoute);
  accountService = inject(AccountService);    
  toastr = inject(ToastrService);
  closeResult: string | null = null;
  accounts: Array<CivCastAccount> = [];  
  search: string | null = null;
  isLoading: boolean = false;
  //currentPermissions: UserApi.Permission[] = [];
  @Input() currentDelegates: Delegate[] = [];

  constructor(
 ) {}

  ngOnInit() {
    this.delegateService.GetDelegates().subscribe(result => {
      this.currentDelegates = result.Users;
    });
  }

  searchUsers() {
    let search = { Search: this.search };
    this.isLoading = true;
    this.accountService.FindAccounts(search).subscribe(accounts => {
      for (var d of accounts) {
        var f = this.currentDelegates.filter(x => x.UserId == d.CognitoUserId);
        d.IsActive = false;
        if (f.length > 0) {
          d.IsOnList = true;      
        }
      }

      this.accounts = accounts;
      this.isLoading = false;
    }, err => {
      this.isLoading = false;
    });
  }

  addDelegates() {

    this.isLoading = true

    var aDelegates: Array<Delegate> = [];

    for (let user of this.accounts) {
      if (user.IsActive) {
        var d = {
          UserId: user.CognitoUserId
        } as Delegate;                 

        aDelegates.push(d);
      }
    }
    if(aDelegates.length > 0){
      this.delegateService.AddDelegates(aDelegates).subscribe({
        next: (response) => {
          this.router.navigate(["people"], {relativeTo: this.aRouter.parent});
          this.isLoading = false;
        },
        error: (err) => {
          this.isLoading = false;
        }
      });      
    }else{
      this.toastr.warning("No new people to add. Please select at least 1 person to add");
    }

  }  

  checkDelegate(delegate: any) {
    if (delegate.IsActive == undefined || delegate.IsActive == null) {
      delegate['IsActive'] = true;
    } else {
      if (delegate.IsActive) {
        delegate.IsActive = false;
      } else {
        delegate.IsActive = true;
      }
    }
  }
}
