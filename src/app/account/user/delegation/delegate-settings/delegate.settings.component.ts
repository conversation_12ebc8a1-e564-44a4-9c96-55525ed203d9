﻿import { Component, OnInit, Input, inject} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserProfile } from 'src/app/models/user-profile';
// import { DelegateService } from 'src/app/user/shared/data-access/delegate.service';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { AccountService } from '../../../../shared/data-access/account-service';
import { CommonModule } from '@angular/common';
import { Delegate } from '../../shared/interfaces/delegate';
import { FormsModule } from '@angular/forms';
import { CMLoaderModule } from 'src/app/modules/cm-loader/cm-loader-module';
import { DelegateService } from '../../shared/data-access/delegate.service';



@Component({
    selector: 'delegate-settings',
    templateUrl: './delegate.settings.component.html',
    imports: [CommonModule, FormsModule, CMLoaderModule]
})
export class DelegateUserSettingsComponent implements OnInit {
  @Input() delegateUser: Delegate | null = null;   
  route = inject(ActivatedRoute);
  router = inject(Router);
  delegateService = inject(DelegateService);
  toastrService = inject(ToastrService);
  accountService = inject(AccountService);
  userProfle: UserProfile | null = null;
  loading: boolean = true;

  constructor(

  ) { }

  ngOnInit() {
    var delegateId = this.route.snapshot.params['delegateId'];
    this.getDelegate(delegateId);
  }

  getDelegate(delegateId: string) {
 
    this.loading = true;
    forkJoin([this.delegateService.GetDelegate(delegateId), this.accountService.GetProfile(delegateId)]).subscribe({
      next: (result) => {
        this.delegateUser = result[0];
        this.userProfle = result[1];
        this.loading = false;
      }, error: (err) => {
        this.loading = false;
      }
    }); 
  }

  saveDelegate() {
    if(this.delegateUser){
      this.delegateService.SaveDelegate(this.delegateUser).subscribe({
        next: (result) => {
          this.toastrService.success('Settings Saved');
          this.router.navigate(["people"], {relativeTo: this.route.parent});
        },
        error: (err) => {

        }
      });
    }
  }
}
