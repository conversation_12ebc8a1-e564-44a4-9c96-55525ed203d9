﻿<div class="container mt-3">
  <div class="row">
    <!--page-title-->
    <div class="col-12 mb-2">
      <h1 class="page-title fs-4"> {{ userProfle?.FirstName }} {{ userProfle?.LastName }}</h1>
    </div>
    @if(loading){
      <cm-loading [show]="loading"></cm-loading>
    }@else{
      @if(delegateUser){
    
          <div class="col-12" >
            <div class="card">
              <div class="card-header d-flex align-items-center justify-content-between">
                <div>User Info</div>
                <button type="button" class="btn btn-primary" (click)="saveDelegate()">Save</button>
              </div>
              <div class="card-body">
                <div class="form-floating">
                  <input type="text" class="form-control" [(ngModel)]="delegateUser.Title" />
                  <label for="floatingInput">Title</label>
                </div>
              </div>
            </div>
          </div>
      }@else {
        <div class="alert alert-warning">
          Delegate User not Found
        </div>
      }
    }
  </div>
</div>





<!-- <ng-template #userProfile></ng-template> -->