import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DelegateUnauthorizedComponent } from './delegate-unauthorized.component';

describe('DelegateUnauthorizedComponent', () => {
  let component: DelegateUnauthorizedComponent;
  let fixture: ComponentFixture<DelegateUnauthorizedComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ DelegateUnauthorizedComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DelegateUnauthorizedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
