import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DelegateLocalStorageService } from 'src/app/shared/data-access/delegate-localstorage-service';

@Component({
    selector: 'app-delegate-unauthorized',
    templateUrl: './delegate-unauthorized.component.html',
    styleUrls: ['./delegate-unauthorized.component.css'],
    standalone: false
})
export class DelegateUnauthorizedComponent implements OnInit {

  constructor(
    private delegationService: DelegateLocalStorageService, 
    private router: Router) { }

  ngOnInit() {
  }

  async refresh(){
    this.delegationService.ClearDelegationIdFromClientStore()
    await this.delegationService.CreateAndGetDelegatedId().toPromise();
    this.router.navigate(['/user', 'change-delegation']);  
    // location.reload();    
  }
}
