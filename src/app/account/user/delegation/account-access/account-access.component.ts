import { Component, effect, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
// import { UserDelegateStore } from '../../../shared/interfaces/delegate';
// import { DelegateService } from '../../shared/data-access/delegate.service';
import { forkJoin } from 'rxjs';
import { AccountService } from 'src/app/shared/data-access/account-service';
import { CivCastAccount } from 'src/app/shared/interfaces/account-profile';
import { InnerListSkeletonComponent } from 'src/app/components/skeletons/inner-list-skeleton/inner-list-skeleton.component';
import { UserDelegateStore } from 'src/app/shared/interfaces/delegate';
import { DelegateService } from '../../shared/data-access/delegate.service';

@Component({
    selector: 'app-account-access',
    templateUrl: './account-access.component.html',
    styleUrls: ['./account-access.component.css'],
    imports: [InnerListSkeletonComponent]
})
export class AccountAccessComponent implements OnInit {

  //delegateStore: UserDelegateStore | null = null;  
  accounts: Array<CivCastAccount> = [];
  isLoading: boolean = false;

  delegateService = inject(DelegateService);

  delegateStore = this.delegateService.userDelegateStore;  

  delegateStoreEffect = effect(() => {
    if(this.delegateStore()){
      this.getDelegationInfo(this.delegateStore() as UserDelegateStore);
    }
  });

  constructor(        
     private router: Router,
     private aRoute: ActivatedRoute,
     private toastrService: ToastrService,
     private accountsService: AccountService
     ) {
        this.delegateService.initialize.set(true);

     }

  ngOnInit() {
    //this.delegateService.GetUserDelegateStore();
  }

  getDelegationInfo(delegateStore: UserDelegateStore) {
     
      if(delegateStore){
        this.isLoading = true;

         this.accountsService.GetAccounts(delegateStore.MyDelegations).subscribe({
          next: (accounts) => {                        
            this.accounts = accounts;
            this.isLoading = false;
          }, error: (err) => {
            this.isLoading = false;
          }
        });  
      }
  }

  removeCurrentDelegate() {
    if(this.delegateStore()){
      this.delegateService.UpdateCurrentUser(null);      
      this.router.navigate(['change-delegation'], { queryParams: { did: "root" }, skipLocationChange: true, relativeTo: this.aRoute.parent });
    }

  }

  async accessDelegate(account: CivCastAccount) { 
    if(this.delegateStore()){
      this.delegateService.UpdateCurrentUser(account.CognitoUserId);
      this.router.navigate(['change-delegation'], { queryParams: { did: account.CognitoUserId }, skipLocationChange: true, relativeTo: this.aRoute.parent  });
    }   
  }
}
