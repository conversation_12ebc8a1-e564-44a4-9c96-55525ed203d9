<div class="container mt-3">
	<div class="row">
		<!--page-title-->
		<div class="col-12">
			<h1 class="page-title fs-4">Switch Accounts</h1>
		</div>
		<!--instructions-->
		<div class="col-12 mb-4">
			<span>This is a list of all the accounts you have access to. Click the "Access" button to enter an
				account.</span>
		</div>
		@if(isLoading){
			<app-inner-list-skeleton></app-inner-list-skeleton>
		}@else {
		<!--accounts i can access-->
			<ul class="list-group">
				<li class="list-group-item">
					<div class="row align-items-center">
						<div class="col-12 col-md-10 mb-2 mb-md-0">
							<div class="text-truncate">My Account</div>
						</div>
						<div class="col-12 col-md-2 d-flex justify-content-md-end">
							@if(!delegateStore?.CurrentDelegation){
								<span class="badge bg-success" *ngIf="">Current
									Account</span>
								<button type="button" class="btn btn-outline-secondary" (click)="removeCurrentDelegate()"
									*ngIf="delegateStore?.CurrentDelegation">
									Access
								</button>
							}						
						</div>
					</div>
				</li>
				@for (info of accounts; track $index) {
					<li class="list-group-item">
						<div class="row align-items-center">
							<div class="col-12 col-md-10 mb-2 mb-md-0">
								<div class="text-truncate">{{ info?.Profile?.Company?.Name }}</div>
							</div>
							<div class="col-12 col-md-2 d-flex justify-content-md-end">
								@if(delegateStore?.CurrentDelegation === info.CognitoUserId){
									<span class="badge bg-success">Current Account</span>
								}@else {
									<button type="button" class="btn btn-outline-secondary" (click)="accessDelegate(info)">
										Access
									</button>
								}						
							
							</div>
						</div>
					</li>
				}
			
			</ul>
		}
	</div>
</div>