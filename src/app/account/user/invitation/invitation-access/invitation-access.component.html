<div class="mt-4">

    <!-- starting invitation process -->
    @if(!isComplete && isLoading && isLoggedIn){

        <div class="d-flex flex-column align-items-center">
            <div class="mb-3">
                <i class="fas fa-user fa-2x me-3"></i>
                <i class="fas fa-circle-notch fa-spin fa-2x me-3"></i>
                <i class="fas fa-users fa-2x"></i>
            </div>
            <div>
               Connecting you to your team...
            </div>
        </div>
    }@else if(isLoading && !isLoggedIn){
        <div class="text-center">
            <div style="margin-top:100px">
                <i class="fas fa-spinner-third fa-fw fa-spin fa-8x"></i>      
            </div>	
         </div>
    }


    @if(error){
    <!-- error -->
    <div class="alert alert-danger">
        {{error }}
    </div>
    }

    @if(success){
        <div class="alert alert-success">
            {{success }}
        </div>
    }


    @if(!isLoggedIn && !isLoading && !isComplete){
    <!-- main -->
        <app-login-required>
            <div class="text-center mb-4">Your team is waiting on you to join! </div>
        </app-login-required>>
    }

</div>