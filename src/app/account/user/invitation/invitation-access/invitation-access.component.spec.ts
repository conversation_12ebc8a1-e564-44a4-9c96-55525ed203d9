import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InvitationAccessComponent } from './invitation-access.component';

describe('InvitationComponent', () => {
  let component: InvitationAccessComponent;
  let fixture: ComponentFixture<InvitationAccessComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InvitationAccessComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InvitationAccessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
