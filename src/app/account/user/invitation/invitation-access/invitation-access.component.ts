import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { 
  getCurrentUser, 
  fetchUserAttributes 
} from 'aws-amplify/auth';
import { signInWithRedirect } from 'aws-amplify/auth';
import { ToastrService } from 'ngx-toastr';
import { inject } from '@angular/core';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { DelegateInvitationService } from '../../shared/data-access/delegate-invitation-service';
import { LoginRequiredComponent } from 'src/app/components/login-required/login-required.component';


@Component({
    selector: 'app-invitation',
    templateUrl: './invitation-access.component.html',
    styleUrls: ['./invitation-access.component.css'],
    providers: [DatePipe],
    imports: [CommonModule, LoginRequiredComponent]
})
export class InvitationAccessComponent implements OnInit {

  authService = inject(AuthService);
  invitationService = inject(DelegateInvitationService);
  toastrService = inject(ToastrService);
  datePipe = inject(DatePipe);
  aRoute = inject(ActivatedRoute);
  error: string | null = null;
  success: string | null = null;
  isComplete: boolean = false;
  invitationId: string;
  isLoggedIn: boolean = false;
  accountInviter: any;
  isLoading: boolean = true;

  constructor() {

    const { invitationId } = this.aRoute.snapshot.queryParams;

    this.invitationId = invitationId;

    if (this.invitationId) {
      sessionStorage.setItem("invitationId", this.invitationId);
    }
  }

  ngOnInit() {
    if (!this.invitationId) {
      this.error = "Invitation Id Is Missing";
    } else {
      this.invitationService.getInvitationNoAuth(this.invitationId).subscribe({
        next: (invitationInfo) => {
          if (invitationInfo) {
            if (invitationInfo.Status === "COMPLETE") {
              var d = this.datePipe.transform(invitationInfo.DateCompleted, 'short')
              this.success = `This invitation was accepted on ${d}`
              this.isComplete = true;
              this.isLoading = false;
              return;
            }
            else if (invitationInfo.Status === "SENT") {
              this.invitationService.noAuthInitilization(this.invitationId).subscribe({
                next: (invResponse) => {
                  if (invResponse) {
                    let response = invResponse;

                    if (response.Status === "PENDING") {
                      getCurrentUser().then(result => {
                        if (result) {
                          this.isLoggedIn = true;
                          this.invitationService.completeInvitation(this.invitationId).subscribe({
                            next: (result) => {
                              if (result.Invitation.Status === "COMPLETE") {
                                this.toastrService.success("You have completed the invitation process");
                                location.replace("/");
                              } else if (result.Invitation.Status === "ERROR") {
                                this.error = "There was an issue completing your invitation. You can try to relog or call support";
                              }
                              this.isLoading = false;
                            },
                            error: (err) => {
                              this.error = "There was an issue completing your invitation. You can try to relog or call support";
                              this.isLoading = false;
                            }
                          });
                        } else {
                          this.isLoading = false;
                        }
                      }, err => {
                        signInWithRedirect().then((user) => {
                          console.log("ERROR-SigninWithRedirect", user);
                        });
                        
                        this.isLoading = false;
                      });
                    }
                  }
                },
                error: (err) => {
                  this.error = "Error occurred";
                }
              });
            } else if (invitationInfo.Status === "ERROR") {
              this.error = `There was an issue with this invitation.`;
              this.isLoading = false  
            }else{
              this.isLoading = false            
            }
          }
        },
        error: (err) => {
          this.error = "Error occurred";
        }
      });
    }
  }
}
