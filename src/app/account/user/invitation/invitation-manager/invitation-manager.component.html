<!-- page header -->
<header class="bg-light p-3 p-lg-4">
	<div class="container">
		<!-- page title -->
		<h1 class="page-title fs-5">Invite Users</h1>
		<p>Type in the email of the user you want to invite and click the "Invite" button. We will send an
			invite email.</p>
		<!-- invitation form -->
		<form #inviteForm="ngForm" (ngSubmit)="addInvitation()">
			<div class="row mb-3">
				<div class="col-12 col-md-4 mb-1 mb-md-0">
					<div class="form-floating">
						<input type="email" placeholder="Email" name="email" id="email" [(ngModel)]="email"
							class="form-control" required email />
						<label for="email">Email</label>
					</div>
				</div>
				<div class="col-12 col-md-3 mb-1 mb-md-0">
					<div class="form-floating">
						<input name="title" placeholder="Title" id="title" type="text" class="form-control"
							[(ngModel)]="title" />
						<label for="title">Title (Optional)</label>
					</div>
				</div>
				<div class="col-12 col-md-3 mb-1 mb-md-0">
					<div class="form-floating">
						<select class="form-select me-2" name="roleInfo" [(ngModel)]="role">
							@for (role of userRoleStore?.Roles; track $index) {
							<option [ngValue]="role.Id">{{role.Name}}
							</option>
							}
						</select>
						<label for="roleInfo">Role (Optional)</label>
					</div>
				</div>
				<div class="col-12 col-md-2 d-flex justify-content-end">
					<div>
						<button class="btn btn-primary"
							[disabled]="!inviteForm.form.valid || isInviting">Invite</button>
					</div>
				</div>
			</div>
		</form>
	</div>
</header>
<!-- invite user -->
<section class="p-3 p-lg-4">
	<div class="container">
		@if(isLoading){
		<div class="placeholder-glow">
			<ul class="list-group mt-4">
				<li class="list-group-item bg-light fw-bold">
					<span class="placeholder col-12"></span>
				</li>
				<li class="list-group-item" *ngFor="let item of [1,2,3,4]">
					<div class="row align-items-center">
						<div class="col-12 col-lg-4">
							<div class="fw-bold">
								<span class="placeholder col-8"></span>
							</div>
							<div>
								<span class="placeholder col-8"></span>
							</div>
						</div>
						<div class="col-12 col-lg-4">
							<div class="small text-muted">
								<span class="placeholder col-8"></span>
							</div>
							<div class="small text-muted">
								<span class="placeholder col-8"></span>
							</div>
						</div>
						<div class="col-12 col-lg-1">
							<span class="placeholder col-8"></span>
						</div>
						<div class="col-12 col-lg-3 d-flex justify-content-end">
							<span class="placeholder col-8" style="height: 50px;"></span>
						</div>
					</div>
				</li>
			</ul>
		</div>
		}@else {
		<!-- invitation list -->
		<div class="card mb-3">
			<div class="card-body">
				<h2 class="card-title fs-6 page-title">Invitation List</h2>
				<ul class="list-group">
					@for (invitation of invitations | orderByImpure: '-DateRequested'; track $index) {
					<li class="list-group-item" [ngClass]="{'list-group-item-danger': invitation.Status === 'ERROR'}">
						<div class="row align-items-center">
							<div class="col-12 col-lg-4">
								<div class="fw-bold">{{ invitation.DateRequested | date:'short'}}</div>
								<div>{{ invitation.AccountRequestedEmail}}</div>
							</div>
							<div class="col-12 col-lg-4">
								<div class="small text-muted">Title: {{ invitation.Title }}</div>
								<div class="small text-muted">Role: {{ getRoleName(invitation) }}</div>
							</div>
							<div class="col-12 col-lg-1">
								<div class="badge bg-success">
									<ng-container [ngSwitch]="invitation.Status">
										<span *ngSwitchCase="'Complete'">Invitation Accepted</span>
										<span *ngSwitchCase="'SENT'">Invitation Sent</span>
										<span *ngSwitchDefault>{{invitation.Status || 'NA'}}</span>
									</ng-container>
								</div>
								<div class="small text-muted" *ngIf="invitation.DateCompleted">
									{{ invitation.DateCompleted | date:'short'}}
								</div>
							</div>
							<div class="col-12 col-lg-3 d-flex justify-content-end">

								<div class="btn-group">
									<!--TODO: [disabled]="invitation.isDeleting"-->
									<button class="btn btn-outline-secondary" (click)="resendInvitation(invitation)"
										*ngIf="invitation.Status !== 'ERROR' && invitation.Status !== 'COMPLETE'">
										<!-- <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="invitation.isResending"></i> 	 -->
										Resend
									</button>
									<!--TODO: [disabled]="invitation.isDeleting"-->
									<button class="btn btn-danger" (click)="deleteInvitation(invitation)">
										<!-- <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="invitation.isDeleting"></i>  -->
										Remove
									</button>
								</div>
							</div>
						</div>
					</li>
					}

				</ul>
			</div>
		</div>
		}
	</div>
</section>