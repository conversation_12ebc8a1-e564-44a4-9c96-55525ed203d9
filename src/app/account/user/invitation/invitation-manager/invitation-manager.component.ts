import { Component, OnInit, inject } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { UserRoleStore } from 'src/app/models/user-role-store';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
import { environment } from 'src/environments/environment';
import { CommonModule } from '@angular/common';
import { DelegateInvitationService } from '../../shared/data-access/delegate-invitation-service';
import { DelegateInvitation } from '../../shared/interfaces/delegate-invitation';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgPipesModule } from 'ngx-pipes';

@Component({
    selector: 'app-invitation-manager',
    templateUrl: './invitation-manager.component.html',
    styleUrls: ['./invitation-manager.component.css'],
    imports: [CommonModule, FormsModule, ReactiveFormsModule, NgPipesModule]
})
export class InvitationManagerComponent implements OnInit {
  delegateInvitationService = inject(DelegateInvitationService);
  private toastr = inject(ToastrService);
  private confirmService = inject(ConfirmService);
  private accessService = inject(AccessService);
  invitations: Array<DelegateInvitation> = [];
  email:string | null = null;
  role:string | null = null;
  title:string | null = null;
  isLoading:boolean = false;
  isInviting: boolean = false;
  userRoleStore: UserRoleStore | null = null;
  constructor() { }

  ngOnInit() {
    this.initialize();
  }

  initialize(){
    this.isLoading = true;
    forkJoin([this.delegateInvitationService.getInvitations(), this.accessService.GetRoles()]).subscribe({
      next: (result) => {
        this.invitations = result[0];
        this.userRoleStore = result[1];
  
        this.isLoading = false;
      },
      error: (err) => {
        this.isLoading = false;
        this.toastr.error("An error occured gathering roles from this account");
        console.log(err);
      }
    });
  }

  addInvitation(){      
    if(this.email && this.role){
      
      this.isInviting = true;

      this.delegateInvitationService.sendInvitation(this.email, this.role, this.title, environment.DelegateInvitation.Product, environment.DelegateInvitation.ReturnUrl).subscribe({
        next: (invitation) => {
          this.invitations.push(invitation.Invitation);
          this.toastr.success("Invitation Sent");
          this.isInviting = false;
        },
        error: (err) => {
          this.initialize();
          this.isInviting = false;
        }
      });   
    } 
  }

  deleteInvitation(invitation: DelegateInvitation){
    this.confirmService.open("Are you sure you want to remove this invitation?").result.then(async value => {
      // try {      
     
          //invitation["isDeleting"] = true;
      this.delegateInvitationService.deleteInvitation(invitation.Id).subscribe({
        next: (result) => {
          var idx = this.invitations.indexOf(invitation);

          this.invitations.splice(idx, 1);
    
          this.toastr.success("Invitation Removed");
        },
        error: (err) => {
          console.log(err);
        }
      });
   
        
      // } catch (error) {
  
      // }finally{
      //   // invitation["isDeleting"] = false;      
      // }
    });

  }

  getRoleName(invitation:DelegateInvitation): string{

    if(invitation.Role && this.userRoleStore){
      var hasRole = this.userRoleStore.Roles.filter(x => x.Id === invitation.Role);
      if(hasRole.length > 0){
        return hasRole[0].Name
      }else{
        return "Not Found";
      }
    }else{
      return "";
    }



  }
  resendInvitation(invitation: DelegateInvitation){
        
    if(confirm("Are you sure you want to resend this invitation?")){
      // invitation["isResending"] = true;
      if(invitation.AccountRequestedId && invitation.Role && invitation.Title){
        this.delegateInvitationService.resendInvitation(invitation.Id, invitation.AccountRequestedId, invitation.Role, invitation.Title, environment.DelegateInvitation.Product, environment.DelegateInvitation.ReturnUrl).subscribe({
          next: (result) => {
            this.toastr.success("Invitation Sent");
            // invitation["isResending"] = false;      
          }, 
          error: (err) => {
            console.log(err);
          }
        });
      }


    }
  }

}
