import { DatePipe } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Amplify } from 'aws-amplify';
import { signInWithRedirect } from 'aws-amplify/auth';
import { DelegateInvitationService } from '../../shared/data-access/delegate-invitation-service';

@Component({
    selector: 'app-user-invitation',
    templateUrl: './user-invitation.component.html',
    styleUrls: ['./user-invitation.component.css'],
    providers: [DatePipe],
    standalone: false
})
export class UserInvitationComponent implements OnInit {
  private aRoute = inject(ActivatedRoute);
  private datePipe = inject(DatePipe);
  private invitationService = inject(DelegateInvitationService);
  error: string = "";
  constructor(){}
  ngOnInit() {
    const {invitationId} = this.aRoute.snapshot.params;    

    if(invitationId){
      this.invitationService.getInvitation(invitationId).subscribe({
        next: (invitationInfo) => {
          if(invitationInfo){
            if(invitationInfo.Status === "COMPLETE"){
              var d = this.datePipe.transform(invitationInfo.DateCompleted, 'short')
              this.error = `This invitation was accepted on ${d}`
              return;
            }
          }
  
          this.invitationService.initializeInvitation(invitationId).subscribe({
            next: (response) => {
              if(response){
                signInWithRedirect().then((user) => {
                  console.log("SigninWithRedirect", user);
                });
              }
            },
            error: (err) => {
              console.log(err);
            }
          });
        },
        error: (err) => {
          this.error = "An error occured trying to setup your invitation.";
        }
      });
    }
  }
}