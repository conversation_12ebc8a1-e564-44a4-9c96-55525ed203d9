<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">E-Bid Settings</h1>
	<!-- disclaimer -->
	<section class="card mb-4">
		<div class="card-body">
			<h2 class="card-title fs-6">Standard Disclaimer</h2>
			<p>When setting up an e-bid, you can require bidders to acknowledge your
				disclaimer before submitting their bid. To save time, enter your standard disclaimer here, and it
				will be automatically
				included in all future e-bids, eliminating the need to re-enter it for each project.</p>
			@if(isLoading()){
			<div class="placeholder-glow">
				<div class="placeholder col-12" style="height: 300px"></div>
			</div>
			}@else{
			<quill-editor [ngModel]="disclaimer()" [modules]="quillModules" format="html"
				[disabled]="isLoading() || isSaving()" style="height: 250px; width: 100%;"
				(onContentChanged)="onDisclaimerChange($event)">
			</quill-editor>
			}
		</div>
	</section>
	<!-- standard mandatory documents -->
	<section class="card mb-4">
		<div class="card-body">
			<h2 class="card-title fs-6">Standard Mandatory Documents</h2>
			<p>When setting up an e-bid, you can require bidders to upload specific,
				"Mandatory Documents." To save time, create a list of frequently required documents here and quickly
				add them to any
				new bid setup.</p>
			@if(isLoading()){
			<div class="placeholder-glow">
				<div class="placeholder col-12" style="height: 300px"></div>
			</div>
			}@else {
			<!-- add document form -->
			<form (submit)="addDocument()">
				<!-- document name -->
				<div class="mb-3">
					<label for="documentName" class="form-label">Document Name</label>
					<input type="text" class="form-control" id="documentName" placeholder="Example: Bid Bond"
						[(ngModel)]="newDocumentName" name="documentName" required />
				</div>
				<!-- document notes -->
				<div class="mb-3">
					<label for="documentNotes" class="form-label">Document Notes</label>
					<textarea class="form-control" id="documentNotes" rows="3"
						placeholder="Example: Please upload a signed copy of your bid bond."
						[(ngModel)]="newDocumentNotes" name="documentNotes"></textarea>
				</div>
				<!-- add button -->
				<div class="d-flex justify-content-end">
					<button type="submit" class="btn btn-outline-dark" [disabled]="!newDocumentName.trim()">Add</button>
				</div>
			</form>
			<!-- document list -->
			<table class="table">
				<thead>
					<tr>
						<th scope="col">Name</th>
						<th scope="col">Description</th>
						<th scope="col"></th>
					</tr>
				</thead>
				<tbody>
					@for (doc of documents(); track $index) {
					<tr>
						<td class="align-middle">
							{{ doc.DocumentName }}
						</td>
						<td class="align-middle text-muted">
							{{ doc.DocumentNotes }}
						</td>
						<td class="align-middle text-end">
							<button type="button" class="btn btn-outline-danger"
								(click)="removeDocument(doc)">Delete</button>
						</td>
					</tr>
					}
				</tbody>
			</table>
			}
		</div>
	</section>
	<!-- page footer -->
	<footer class="d-flex justify-content-end">
		@if(isDirty()){
		<span class="text-danger mx-4">
			<i class="fas fa-exclamation-triangle fa-1x mx-2"></i>
			Changes Made. Not Saved.
		</span>
		}
		<button type="button" class="btn btn-primary" (click)="saveSettings()"
			[disabled]="isSaving() || isLoading() || !isDirty()">
			@if(isSaving()){
			<i class="spinner-border spinner-border-sm"></i>
			}
			Save
		</button>
	</footer>
</div>