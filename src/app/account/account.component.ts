import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { environment } from 'src/environments/environment';
import { DelegateMenuComponent } from '../components/delegate-menu/delegate-menu.component';

@Component({
    selector: 'app-account',
    imports: [RouterLink, CommonModule, DelegateMenuComponent],
    templateUrl: './account.component.html',
    styleUrl: './account.component.css'
})
export class AccountComponent {
  public readonly ENVIROMENT_SETTING: typeof environment = environment;
}
