<div class="container p-4 mb-4">
	<!--  header -->
	<h1 class="fs-5 mb-3">Work Day Reasons (Employees)</h1>
	<p>Customize your diary's work day options. Easily rearrange options with a simple
		drag-and-drop.</p>
	<!-- add work day option -->
	<section class="col-12 col-md-6 col-xl-4 mb-3">
		<div class="input-group">
			<input type="text" class="form-control" [(ngModel)]="option" placeholder="Add Work Day Option" />
			<button class="btn btn-outline-dark" (click)="addTimeReason()">Add</button>
		</div>
	</section>
	<!-- work day options -->
	<section>
		<ul class="list-group" cdkDropList (cdkDropListDropped)="dropOption($event)">
			@for (dayOption of timeCardComponent()?.WorkDayOptions; track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between" cdkDrag>
				<div style="cursor: grab;">
					<i class="drag-handle fas fa-grip-vertical text-secondary fa-lg me-3" aria-hidden="true"></i>
				</div>
				<div class="input-group">
					<input class="form-control" type="text" [(ngModel)]="timeCardComponent()?.WorkDayOptions[$index]"
						(blur)="optionInputBoxChange(dayOption, $index)" [readonly]="dayOption === 'Work Day'" />
					@if(dayOption !== 'Work Day') {
					<button type="button" class="btn btn-outline-secondary" (click)="removeOption($index)">
						<i class="fas fa-trash-alt"></i>
					</button>
					}
				</div>
			</li>
			}@empty{
			<li class="list-group-item">
				None yet.
			</li>
			}
		</ul>
	</section>
</div>