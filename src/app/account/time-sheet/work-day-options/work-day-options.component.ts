import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TimeCardComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { tap, switchMap } from 'rxjs';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';

@Component({
    selector: 'app-work-day-options',
    imports: [CommonModule, FormsModule, CdkDropList, CdkDrag],
    templateUrl: './work-day-options.component.html',
    styleUrl: './work-day-options.component.css'
})
export class ProjectGlobalSettingWorkDayOptionsComponent {
  dailyLogComponentService = inject(ConstructionDailyLogService);
  timeCardComponent = this.dailyLogComponentService.timeCardComponent;
  componentIdentifier = signal<DailyLogComponentIdentifiers>(DailyLogComponentIdentifiers.TIMECARD);
  option: string = '';

  previousOptions: Array<string> = [];
  gatherConditions = toSignal(toObservable(this.timeCardComponent).pipe(tap({
    next: (component) => {
      if(component){
        this.previousOptions = [...component.WorkDayOptions]
      }
    }
  })));

  private getComponent = toSignal(toObservable(this.componentIdentifier).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((identifier) => this.dailyLogComponentService.getSubComponent(identifier)),
    tap((identifier) => {
      this.isLoading.set(false);
    })
  ));
  isLoading = signal<boolean>(false);  
  
  removeOption(idx:number) {
    let component = this.timeCardComponent() as TimeCardComponent;
    component.WorkDayOptions.splice(idx, 1);
    this.dailyLogComponentService.updateSubComponent(component).subscribe();
  }

  addTimeReason() {
    if(this.option){ 
      let component = this.timeCardComponent() as TimeCardComponent;

      if(!component.WorkDayOptions){
        component.WorkDayOptions = new Array<string>();
      }
      
      if(!component.WorkDayOptions.find(x => x.toLocaleLowerCase() === this.option.toLocaleLowerCase())){
        component.WorkDayOptions.push(this.option);
        this.dailyLogComponentService.updateSubComponent(component).subscribe();
      }     
    }    
  }

  optionInputBoxChange(condition: string, idx: number) {
    let component = this.timeCardComponent() as TimeCardComponent;

    if(!this.previousOptions.find(x => x.toLocaleLowerCase() === condition.toLocaleLowerCase())){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }else{
      component.WorkDayOptions[idx] = this.previousOptions[idx];
    }    
  }

  dropOption(event: CdkDragDrop<string[]>) {
    let component = this.timeCardComponent() as TimeCardComponent;
    moveItemInArray(component.WorkDayOptions, event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }
    
  }

}
