<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="page-title fs-5">Equipment Work Day Reasons (Equipment)</h1>
	<p>Customize your equipment's work day options. Easily rearrange options with a simple
		drag-and-drop.</p>
	<!-- add work day option -->
	<section class="col-12 col-md-6 col-xl-4 mb-3">
		<div class="input-group">
			<input type="text" class="form-control" [(ngModel)]="option" placeholder="Add Equipment Work Day Option" />
			<button class="btn btn-outline-dark" (click)="addTimeReason()">Add</button>
		</div>
	</section>
	<!-- equipment work day options -->
	<section>
		<!--TODO: Fix Sortable   dnd-sortable-container [sortableData]="timeCardComponent?.EquipmentDayOptions"-->
		<!--equipment options-->
		<ul class="list-group" cdkDropList (cdkDropListDropped)="dropOption($event)">
			@for (dayOption of timeCardComponent()?.EquipmentDayOptions; track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between" cdkDrag>
				<div dnd-sortable-handle style="cursor: grab;">
					<i class="drag-handle fas fa-grip-vertical text-secondary fa-lg me-3" aria-hidden="true"></i>
				</div>
				<div class="input-group">
					<input class="form-control" type="text"
						[(ngModel)]="timeCardComponent()?.EquipmentDayOptions[$index]"
						(blur)="optionInputBoxChange(dayOption, $index)"
						[readonly]="timeCardComponent()?.EquipmentDayOptions[$index] === 'Work Day'" />
					@if(dayOption !== 'Work Day') {
					<button type="button" class="btn btn-outline-secondary" (click)="removeOption($index)">
						<i class="fas fa-trash-alt"></i>
					</button>
					}
				</div>
			</li>
			}@empty {
			<li class="list-group-item">
				<div class="alert alert-info m-0" role="alert">
					No equipment work day options.
				</div>
			</li>
			}


		</ul>
	</section>
</div>