import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { tap, switchMap } from 'rxjs';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { TimeCardComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';

@Component({
    selector: 'app-equipment-day-options',
    imports: [CommonModule, FormsModule, CdkDropList, CdkDrag],
    templateUrl: './equipment-day-options.component.html',
    styleUrl: './equipment-day-options.component.css'
})
export class ProjectGlobalSettingsEquipmentDayOptionsComponent {
  dailyLogComponentService = inject(ConstructionDailyLogService);
  timeCardComponent = this.dailyLogComponentService.timeCardComponent;
  componentIdentifier = signal<DailyLogComponentIdentifiers>(DailyLogComponentIdentifiers.TIMECARD);
  option: string = '';
  previousOptions: Array<string> = [];
  gatherOptions = toSignal(toObservable(this.timeCardComponent).pipe(tap({
    next: (component) => {
      if(component){
        this.previousOptions = [...component.EquipmentDayOptions]
      }
    }
  })));

  private getComponent = toSignal(toObservable(this.componentIdentifier).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((identifier) => this.dailyLogComponentService.getSubComponent(identifier)),
    tap((identifier) => {
      this.isLoading.set(false);
    })
  ));
  isLoading = signal<boolean>(false);  
  
  removeOption(idx:number) {
    let component = this.timeCardComponent() as TimeCardComponent;
    component.EquipmentDayOptions.splice(idx, 1);
    this.dailyLogComponentService.updateSubComponent(component).subscribe();
  }

  addTimeReason() {
    if(this.option){ 
      let component = this.timeCardComponent() as TimeCardComponent;

      if(!component.EquipmentDayOptions){
        component.EquipmentDayOptions = new Array<string>();
      }
      
      if(!component.EquipmentDayOptions.find(x => x.toLocaleLowerCase() === this.option.toLocaleLowerCase())){
        component.EquipmentDayOptions.push(this.option);
        this.dailyLogComponentService.updateSubComponent(component).subscribe();
      }     
    }    
  }

  optionInputBoxChange(condition: string, idx: number) {
    let component = this.timeCardComponent() as TimeCardComponent;

    if(!this.previousOptions.find(x => x.toLocaleLowerCase() === condition.toLocaleLowerCase())){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }else{
      component.EquipmentDayOptions[idx] = this.previousOptions[idx];
    }    
  }

  dropOption(event: CdkDragDrop<string[]>) {
    let component = this.timeCardComponent() as TimeCardComponent;
    moveItemInArray(component.EquipmentDayOptions, event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }
    
  }

}
