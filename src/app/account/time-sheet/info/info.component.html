<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">Project Info Fields</h1>
	<p>Hide, show, and add info fields. Easily rearrange options with a simple
		drag-and-drop.</p>
	<!-- project info fields -->
	<section>
		<!-- dnd-sortable-container [sortableData]="infoComponent?.Properties" TODO: fix-->
		<ul class="list-group" cdkDropList (cdkDropListDropped)="drop($event)">
			<li class="list-group-item d-flex justify-content-end">
				<div class="btn-group" role="group" aria-label="Basic example">
					<button class="btn btn-outline-dark" (click)="add()" placement="bottom"
						ngbTooltip="Add Property">Add</button>
					<button class="btn btn-outline-dark" (click)="saveComponent()">Save</button>
				</div>
			</li>
			<!--  dnd-sortable
		  [sortableIndex]="i"
		  (dragend)="tempDragEnd()"  TODO: Fix-->
			@if(isLoading()){
			<div class="col-12">
				<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
					<div class="placeholder-glow">
						<div class="row">
							<div class="col-12">
								<span class="placeholder col-12" style="height:20px;"></span>
							</div>
						</div>
						<div class="row">
							<div class="col-4">
								<span class="placeholder col-12"></span>
							</div>
						</div>
					</div>
				</li>
			</div>
			}@else{
			@for (item of infoComponent()?.Properties; track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between" style="cursor: grab;" cdkDrag>
				<div class="d-flex flex-row">
					<div>
						<i class="drag-handle fas fa-grip-vertical text-secondary fa-lg me-3" aria-hidden="true"></i>
					</div>
					<div>
						{{ item.Name }}
					</div>
				</div>
				<div>
					<button type="button" class="btn me-1" (click)="hide(item)"
						[ngClass]="{ 'btn-outline-secondary': item.IsHidden, 'btn-dark': !item.IsHidden }">
						<i class="fal"
							[ngClass]="{ 'fa-square': item.IsHidden, 'fa-check-square': !item.IsHidden }"></i>
					</button>
					<button type="button" class="btn btn-outline-dark me-1" (click)="edit(item)" *ngIf="!item.IsLocked">
						<i class="fa fa-pencil" aria-hidden="true"></i>
					</button>
					<button type="button" class="btn btn-outline-secondary" (click)="remove(item)"
						*ngIf="!item.IsLocked">
						<i class="fa fa-trash" aria-hidden="true"></i>
					</button>
				</div>
			</li>
			}@empty {
			<li class="list-group-item">
				None yet.
			</li>
			}

			}
		</ul>
	</section>
</div>