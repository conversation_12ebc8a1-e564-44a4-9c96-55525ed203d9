<!-- page header -->
<header class="bg-light p-3 p-lg-4">
	<div class="container">
		<!-- page title -->
		<h1 class="page-title fs-5 mb-0">{{mode | titlecase}} Property</h1>
	</div>
</header>
<!-- add property -->
<section class="p-3 p-lg-4">
	<div class="container">
		@if(isLoading()){
		<div class="text-center">
			<div style="margin-top:100px">
				<i class="fas fa-spinner-third fa-fw fa-spin fa-8x"></i>
			</div>
		</div>
		}@else {
		<!-- form -->
		<div class="loading-wrapper">
			<form [formGroup]="propertyForm" (ngSubmit)="save()">
				<!-- name -->
				<div class="form-floating mb-3">
					<input type="text" class="form-control" name="name" id="name" formControlName="Name" />
					<label for="floatingInput">Name</label>
				</div>
				<!-- type -->
				<div class="mb-3">
					<div class="mb-1"><label>Type</label></div>
					<select class="form-select" name="type" id="formOptions" formControlName="ValueType">
						@for (option of formOptions(); track $index) {
						<option [ngValue]="option.ValueType">
							{{ option.Name }}
						</option>
						}
					</select>
				</div>
				<div class="d-flex justify-content-end">
					<button type="submit" class="btn btn-primary" [disabled]="!propertyForm.valid">
						@if(isSaving()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						Save
					</button>
				</div>
			</form>
		</div>
		}
	</div>
</section>