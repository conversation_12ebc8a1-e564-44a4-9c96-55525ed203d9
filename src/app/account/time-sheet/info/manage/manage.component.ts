import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, FormBuilder, ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { ConstructionInfoService } from 'src/app/construction/shared/data-access/info.service';
import { uuid } from 'uuidv4';
import { InfoItem, InfoValueTypes } from 'src/app/construction/shared/interfaces/info';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { of, switchMap, tap } from 'rxjs';

@Component({
    selector: 'app-manage',
    imports: [CommonModule, FormsModule, ReactiveFormsModule],
    templateUrl: './manage.component.html',
    styleUrl: './manage.component.css'
})
export class ManageInfoComponent {
  aRouter = inject(ActivatedRoute);
  router = inject(Router);
  isLoading = signal<boolean>(false);
  isSaving = signal<boolean>(false);
  pageTitle = signal<string>('Property Manager');
  formBuilder = inject(FormBuilder);
  infoService = inject(ConstructionInfoService);
  infoItem = signal<InfoItem | null>(null);
  mode: string | null = null;
  propertyForm = this.formBuilder.group({
    Name: new FormControl('', Validators.required),
    ValueType: new FormControl('', Validators.required)
  });
  formOptions = signal<Array<{Name:string,ValueType:InfoValueTypes}>>([
      {Name:'Text',ValueType: InfoValueTypes.String},
      {Name:'Date',ValueType:InfoValueTypes.Date},
      {Name:'Number',ValueType:InfoValueTypes.Decimal},
      {Name:'Check',ValueType:InfoValueTypes.Boolean},
      {Name:'Currency',ValueType:InfoValueTypes.Currency}
  ]);

  itemId = signal<string>('');  

  private getItem = toSignal(toObservable(this.itemId).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((item) => item ? this.infoService.getItem(item) : of('')),
    tap((item) => {
      if(item){
        this.propertyForm.patchValue({
          Name: item.Name,
          ValueType: item.ValueType,
        });

        this.infoItem.set(item);
      }
      this.isLoading.set(false);
    }),  
  ));

  componentToSave = signal<InfoItem | null>(null);
  saveComponentFeature = toSignal(toObservable(this.componentToSave).pipe(
    tap((component) => {
      this.isSaving.set(true);
    }),
    switchMap((component) => {
      
      if(component && component.PropertyId && component.Name){
        if( this.mode === 'add'){           
          return this.infoService.addItem(component);  
        }else if(this.mode === 'edit'){        
          return this.infoService.updateItem(component)  
        }else{
          return of(null);
        }      
      }else{
        return of(null);
      }
    }),
    tap((result) => {
      this.isSaving.set(false);
      if(result){
        this.router.navigate(['info'], { relativeTo: this.aRouter.parent });
      }      
    })
  ));

  constructor(){

    this.aRouter.paramMap.subscribe({
      next: (params) => {
        this.mode = params.get('manage');
        const id = params.get('id');
        if( this.mode ){
          const capitalized =  this.mode .charAt(0).toUpperCase() +  this.mode.slice(1)
          this.pageTitle.set(`${capitalized} Property`);
        }

        if(id){
          this.itemId.set(id);
        }
      }
      });

      
  }


  save(){    
    const {Name, ValueType} = this.propertyForm.value

    let infoItem = {...this.infoItem()}
    infoItem.Name = Name ?? '';
    infoItem.ValueType = ValueType ? parseInt(ValueType) as InfoValueTypes : InfoValueTypes.String;
    
    if(!infoItem.PropertyId){
        infoItem.PropertyId = uuid();
    }      

    this.componentToSave.set(infoItem as InfoItem);    
  }
}