import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';

@Component({
  selector: 'app-time-sheet',
  imports: [RouterOutlet],
  templateUrl: './time-sheet.component.html',
  styleUrl: './time-sheet.component.css'
})
export class TimeSheetComponent {
  userProjectComponentsService = inject(UserProjectComponentsService);
  constructor() { 

    this.userProjectComponentsService.getUserComponentsAllCached().subscribe();
  }

}
