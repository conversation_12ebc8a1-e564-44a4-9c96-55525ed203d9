<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">Starter Team</h1>
	<p>Select users you want automatically added to new projects.</p>
	<!-- starter-team -->
	<section>
		<!-- <app-list-skeleton *ngIf="!loaded"></app-list-skeleton> -->
		@if(isLoading()){
		<ul class="list-group">
			<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
				<div class="placeholder-glow">
					<div class="row">
						<div class="col-6">
							<span class="placeholder col-12" style="height:20px;"></span>
						</div>
					</div>
					<div class="row">
						<div class="col-4">
							<span class="placeholder col-12"></span>
						</div>
					</div>
				</div>
			</li>
		</ul>
		}@else {
		<!--team-->
		<ul class="list-group">
			@if(teamPermissions()?.allowTeamUpdates === 'Allow'){
			<li class="list-group-item d-flex justify-content-end">
				<div class="btn-group" role="group" aria-label="Basic example">
					<button type="button" class="btn btn-outline-dark" (click)="addAll()">
						@if(isAddingAll()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						Add All
					</button>
					<button type="button" class="btn btn-outline-dark" (click)="removeAll()">
						@if(isRemovingAll()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						Remove All
					</button>
				</div>
			</li>
			}
			@for (member of teamMemberInfo(); track $index) {
			<!--*ngFor="let member of teamMemberInfo | orderBy:'AccountInfo.Profile.LastName'; let i = index"-->
			<li class="list-group-item">
				<div class="d-flex justify-content-between align-items-center">
					<div>
						<div class="fw-bold">{{ member.AccountInfo?.Profile?.FirstName }} {{
							member.AccountInfo?.Profile?.LastName
							}}</div>
						<div class="small text-secondary">{{ member.DelegateInfo?.Title }}</div>
					</div>
					@if(teamPermissions()?.allowTeamUpdates === 'Allow'){
					<div>
						@if(member.IsActive){
						@if(member.IsLoading){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}@else {
						<button type="button" class="btn btn-primary" (click)="removeTeamMember(member)">
							<i class="fal fa-check-square fa-lg text-white"></i>
						</button>
						}
						}@else {
						@if(member.IsLoading){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}@else {
						<button type="button" class="btn btn-outline-dark" (click)="addTeamMember(member)">
							<i class="fal fa-square fa-lg"></i>
						</button>
						}

						}
					</div>
					}
				</div>
			</li>
			}@empty {
			<li class="list-group-item">
				<div class="alert alert-info m-0" role="alert">
					You must <a href="javascript:void(0)" [routerLink]="['/account', 'users', 'settings', 'people']">add
						users</a> to create a starter team.
				</div>
			</li>
			}
		</ul>
		}
	</section>
</div>