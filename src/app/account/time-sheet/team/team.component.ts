import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { sign } from 'crypto';
import { RouterLink } from '@angular/router';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';
import { ProjectComponentIdentifiers } from 'src/app/construction/shared/interfaces/project-components';
import { TeamComponentInfo, TeamMember, TeamMemberComponentInfo, TeamMemberInfo, TeamUserProjectComponentInfo } from 'src/app/construction/shared/interfaces/team';
// import { DelegateService } from 'src/app/user/shared/data-access/delegate.service';
import { forkJoin } from 'rxjs';
import { ConstructionTeamsService } from 'src/app/construction/shared/data-access/teams.service';
import { DelegateService } from 'src/app/account/user/shared/data-access/delegate.service';

@Component({
    selector: 'app-team',
    imports: [CommonModule, RouterLink],
    templateUrl: './team.component.html',
    styleUrl: './team.component.css'
})
export class ProjectGlobalSettingsTeamComponent {
  userProjectComponentsService = inject(UserProjectComponentsService);
  delegateService = inject(DelegateService);
  teamService = inject(ConstructionTeamsService);
  access = signal<string>('');
  isLoading = signal<boolean>(false);
  isAddingAll = signal<boolean>(false);
  isRemovingAll = signal<boolean>(false);
  teamComponentInfo = signal<TeamComponentInfo>({} as TeamComponentInfo);
  teamMemberInfo = this.teamService.userTeamMemberInfo;
  teamPermissions = this.teamService.teamPermissions;
  constructor() {
    this.access.set('allow');
    this.isLoading.set(true);
    this.teamService.getTeamPermissions().subscribe();
    this.isLoading.set(true);
    this.teamService.getUserTeamComponentSettingsWithMemberInfo().subscribe({
      next: (result) => {
        this.isLoading.set(false);
      },
      error: (err) => {
        console.log(err);
        this.isLoading.set(false);
      },
    });
  }

  addAll() {
    let teamMembers = new Array<TeamMember>();
    this.isAddingAll.set(true);

    this.updateAll(true);

    let teamMemberInfo = this.teamMemberInfo() as Array<TeamMemberInfo>;

    for (let tmInfo of teamMemberInfo.filter(x => x.IsActive === false)) {
      var teamMember = {} as TeamMember;
      teamMember.IsActive = true;
      teamMember.UserId = tmInfo.DelegateInfo?.UserId as string;
      teamMembers.push(teamMember);
    }


    this.teamService.AddTeamMembersSettings(teamMembers).subscribe({
      next: (result) => {
        this.updateAll(false, true);
        this.isAddingAll.set(false);
      },
      error: (err) => {
        console.log(err);
        this.updateAll(false);
        this.isAddingAll.set(false);
      }
    });
  }

  private updateAll(isLoading: boolean, isActive: boolean | null = null) {
    this.teamMemberInfo.update((teamMembers) => {
      if (teamMembers) {
        return teamMembers.map((teamMemberInfo) => {
          teamMemberInfo.IsLoading = isLoading;
          if(isActive !== null) {
            teamMemberInfo.IsActive = isActive;
          }
          return teamMemberInfo;
        });
      } else {
        return teamMembers;
      }
    });
  }

  private updateOne(delegateUserId: string, isLoading: boolean, isActive: boolean | null = null) {
    this.teamMemberInfo.update((teamMembers) => {
      if (teamMembers) {
        return teamMembers.map((teamMemberInfo) => {
          if (teamMemberInfo.DelegateInfo?.UserId === delegateUserId) {
            teamMemberInfo.IsLoading = isLoading;
            
            if(isActive !== null) {
              teamMemberInfo.IsActive = isActive;
            }
          }
          return teamMemberInfo;
        });
      } else {
        return teamMembers;
      }
    });
  }
  

  removeAll() {
    this.isRemovingAll.set(true);

   this.updateAll(true);

    let teamMemberInfo = this.teamMemberInfo() as Array<TeamMemberInfo>;

    let teamMembers = new Array<string>();

    for (let tmInfo of teamMemberInfo.filter(x => x.IsActive === true)) {
      if (tmInfo.DelegateInfo?.UserId) {
        teamMembers.push(tmInfo.DelegateInfo?.UserId);
      }
    }

    if (teamMembers.length > 0) {
      this.teamService.RemoveTeamMembersSettings(teamMembers).subscribe({
        next: (result) => {
          this.updateAll(false, false);
          this.isRemovingAll.set(false);
        },
        error: (err) => {
          console.log(err);
          this.isRemovingAll.set(false);
          this.updateAll(false);
        }
      });
    } else {
      this.isRemovingAll.set(false);
    }
  }

  removeTeamMember(teamMember: TeamMemberInfo) {
    this.updateOne(teamMember.DelegateInfo?.UserId as string, true);

    this.teamService.RemoveTeamMembersSettings([teamMember.DelegateInfo?.UserId as string]).subscribe({
      next: (result) => {
        this.updateOne(teamMember.DelegateInfo?.UserId as string, false,false);
      },
      error: (err) => {
        console.log(err);
        this.updateOne(teamMember.DelegateInfo?.UserId as string, false);
      }
    });
  }
  addTeamMember(teamMember: TeamMemberInfo) {
    this.updateOne(teamMember.DelegateInfo?.UserId as string, true);

    var teamMemberInfo = {} as TeamMember;
    teamMemberInfo.IsActive = true;
    teamMemberInfo.UserId = teamMember.DelegateInfo?.UserId as string;

    this.teamService.AddTeamMembersSettings([teamMemberInfo]).subscribe({
      next: (result) => {
        this.updateOne(teamMember.DelegateInfo?.UserId as string, false,true);
      },
      error: (err) => {
        console.log(err);
        this.updateOne(teamMember.DelegateInfo?.UserId as string, false);
      }
    });
  }
}
