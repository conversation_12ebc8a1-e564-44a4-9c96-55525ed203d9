import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { Observable, concat, concatMap, forkJoin, of, switchMap, tap } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import {CdkDragDrop, CdkDropList, CdkDrag, moveItemInArray} from '@angular/cdk/drag-drop';
import { UserProjectComponent } from 'src/app/construction/shared/interfaces/dashboard';

@Component({
    selector: 'app-project-global-settings-tools',
    imports: [CommonModule, CdkDropList, CdkDrag],
    templateUrl: './tools.component.html',
    styleUrl: './tools.component.css'
})
export class ProjectGlobalSettingsToolsComponent {
  isLoading = signal<boolean>(false);  
  toastrService = inject(ToastrService);
  userProjectComponentService = inject(UserProjectComponentsService);
  allComponents = this.userProjectComponentService.cachedUserAllComponents  
  runComponents = signal<boolean>(true);
  addComponentsFeature = signal<ComponentActiveRequest | null>(null);
  reorderComponents = signal<Array<string>>([]);
  
  private reorderComponentsReadOnly = toSignal(toObservable(this.reorderComponents).pipe(
       switchMap(request => request.length > 0 ? this.userProjectComponentService.reorderComponents(request) : of(null)),
       tap(result => {        
        if(this.reorderComponents().length > 0){
          this.toastrService.success("Order saved");
        }
       })
     ));


  private readOnlyAddComponentsFeature = toSignal(toObservable(this.addComponentsFeature).pipe(
    tap((request) => {
      if(request){
        for(let comp of request.Components){
          this.setComponentLoading(comp.ComponentIdentifier, true);
        }
      }

    }),
    switchMap(request =>{
      if(!request){
        return of(null);
      }
      let compIds = new Array<string>();

      for(let comp of request.Components){
        compIds.push(comp.ComponentIdentifier);
      }

      return new Observable<ComponentActiveRequest>(obs => {
        if(request.IsActive){
          this.userProjectComponentService.addComponentListFromStore(compIds).subscribe({
            next: (result) => {
              this.allComponents().filter(x => compIds.includes(x.ComponentIdentifier)).forEach(x => x.IsActive = true);
              this.userProjectComponentService.updateCache([...this.allComponents()]);
              obs.next(request);
              obs.complete();
            },
            error: (err) => {
              obs.error(err);
            }
          });
        }else{
          this.userProjectComponentService.removeComponentListFromStore(compIds).subscribe({
            next: (result) => {
              this.allComponents().filter(x => compIds.includes(x.ComponentIdentifier)).forEach(x => x.IsActive = false);
              this.userProjectComponentService.updateCache([...this.allComponents()]);
              obs.next(request);
              obs.complete();
            },
            error: (err) => {
              obs.error(err);
            }
          });
        }
      });
   
    }),
    tap((request) => {
      if(request){
        for(let comp of request.Components){
          this.setComponentLoading(comp.ComponentIdentifier, false, request.IsActive);
        }
      }
  
    }),
  ));


  addAll() {  
    var componentsToAdd = new Array<UserProjectComponent>();

    for(let component of this.allComponents().filter(x => x.IsActive === false && x.AllowDeletion === true)){
      componentsToAdd.push(component);
    }

    this.addComponentsFeature.set({Components: componentsToAdd, IsActive: true});
  }


  removeAll() {
    var componentsToRemove = new Array<UserProjectComponent>();

    for(let component of this.allComponents().filter(x => x.IsActive === true && x.AllowDeletion === true)){
      componentsToRemove.push(component);
    }

    this.addComponentsFeature.set({Components: componentsToRemove, IsActive: false});
  }

  setComponentLoading(componentIdentifier:string, isLoading: boolean, isActive:boolean | null = null) {
    this.allComponents.update(components => {
      components.map(x => {
        if(x.ComponentIdentifier === componentIdentifier){
          x.isLoading = isLoading;
          if(isActive !== null){
            x.IsActive = isActive;
          }
          
        }        
      });
      return [...components];
    });
  }

  addComponent(component: UserProjectComponent) {
      this.addComponentsFeature.set({Components: [component], IsActive: true});
  }

  removeComponent(component: UserProjectComponent) {    
    this.addComponentsFeature.set({Components: [component], IsActive: false});
  }


  saveOrder() {
    let orderIds = this.allComponents().map(x => x.ComponentIdentifier);
    this.reorderComponents.set(orderIds);
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.allComponents(), event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.saveOrder();
    }
    
  }
  
}


export interface ComponentActiveRequest{
  IsActive: boolean, Components: Array<UserProjectComponent>
};






  // private readOnlyAddComponentsFeature = toSignal(toObservable(this.addComponentsFeature).pipe(
  //   switchMap(componentActiveRequest =>{
  //     for(let comp of componentActiveRequest.Components){
  //       this.setComponentLoading(comp.ComponentIdentifier, componentActiveRequest.IsActive, true);
  //     }

  //     let addObs = new Array<Observable<UserProjectComponent>>();

  //     for(let comp of componentActiveRequest.Components){
  //       addObs.push(this.userProjectComponentService.addComponentToStoreById(comp.ComponentIdentifier, componentActiveRequest.IsActive));
  //     }

  //     return concat(...addObs);
  //   }),
  //   concatMap((result: UserProjectComponent) => {
  //     this.allComponents.update(components => {
  //       components.map(x => {
  //         if (x.ComponentIdentifier === result.ComponentIdentifier) {     
  //           x.IsActive = result.IsActive;       
  //           x.isLoading = false;
  //         }
  //       })
  //       return [...components];
  //     });
  //     return of(result);
  //   })
  // ));
