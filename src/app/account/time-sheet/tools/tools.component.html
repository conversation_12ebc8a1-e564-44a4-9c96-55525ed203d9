<div class="container p-4 mb-4">
	<!--  header -->
	<h1 class="fs-5 mb-3">Project Tools Settings</h1>
	<p>Customize your project menu. Easily rearrange options with a simple
		drag-and-drop.</p>
	<!-- sidebar  -->
	<section>
		<!--TODO: fix dnd-sortable-container [sortableData]="allComponents"-->
		@if(isLoading()){
		<ul class="list-group">
			<li class="list-group-item bg-light" style="height: 50px"></li>
			<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
				<div class="placeholder-glow">
					<div class="row">
						<div class="col-12">
							<span class="placeholder col-12" style="height:20px;"></span>
						</div>
					</div>
					<div class="row">
						<div class="col-4">
							<span class="placeholder col-12"></span>
						</div>
					</div>
				</div>
			</li>
		</ul>
		}@else {
		<div cdkDropList (cdkDropListDropped)="drop($event)">
			<ul class="list-group">
				<li class="list-group-item d-flex justify-content-end">
					<div class="btn-group" role="group" aria-label="Basic example">
						<button type="button" class="btn btn-outline-dark" (click)="addAll()">Add All</button>
						<button type="button" class="btn btn-outline-dark" (click)="removeAll()">Remove
							All</button>
					</div>
				</li>
				@for (component of allComponents(); track $index) {
				<li class="list-group-item d-flex align-items-center justify-content-between" style="cursor: grab;"
					cdkDrag>
					<div class="d-flex flex-row">
						<div>
							<i class="drag-handle fas fa-grip-vertical text-secondary fa-lg me-3"
								[style.color]="(component.IsActive) ? 'black' : 'red'" aria-hidden="true"></i>
						</div>
						<div>
							{{component.Name}}
						</div>
					</div>
					@if(component.isLoading){
					<i class="fas fa-circle-notch fa-spin fa-2x"></i>
					}@else{
					<div>
						<button type="button" class="btn btn-dark" (click)="removeComponent(component)"
							*ngIf="component.IsActive && component.AllowDeletion">
							<i class="fal fa-check-square fa-lg text-white"></i>
						</button>
						<button type="button" class="btn btn-outline-dark" (click)="addComponent(component)"
							*ngIf="!component.IsActive && component.AllowDeletion">
							<i class="fal fa-square fa-lg"></i>
						</button>
						<button type="button" class="btn btn-dark"
							*ngIf="component.IsActive && !component.AllowDeletion" [disabled]="true">
							<i class="fas fa-check-square fa-lg"></i>
						</button>
					</div>
					}
				</li>
				}@empty {
				<li class="list-group-item">
					<div class="alert alert-info m-0" role="alert">
						None yet.
					</div>
				</li>
				}

			</ul>
		</div>
		}
	</section>
</div>