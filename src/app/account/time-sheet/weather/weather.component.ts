import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { of, switchMap, tap } from 'rxjs';
import { WeatherComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { ToastrService } from 'ngx-toastr';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';

@Component({
    selector: 'app-weather',
    imports: [CommonModule, FormsModule, ReactiveFormsModule, CdkDropList, CdkDrag],
    templateUrl: './weather.component.html',
    styleUrl: './weather.component.css'
})
export class ProjectGlobalSettingsWeatherComponent {
  formBuilder = inject(FormBuilder);
  toastrService = inject(ToastrService);
  skyName: string = '';
  tempName: string = '';
  dailyLogComponentService = inject(ConstructionDailyLogService);
  isLoading = signal<boolean>(false);
  isSaving = signal<boolean>(false);
  weatherComponent = this.dailyLogComponentService.weatherComponent;
  componentIdentifier = signal<DailyLogComponentIdentifiers>(DailyLogComponentIdentifiers.WEATHER);
  previousSkys: Array<string> = [];
  previousTemps: Array<string> =[];
  gatherSkys = toSignal(toObservable(this.weatherComponent).pipe(tap({
    next: (weatherComponent) => {
      if(weatherComponent){
        this.previousSkys = [...weatherComponent.Skys]
        this.previousTemps = [...weatherComponent.Temperatures];
      }
    }
  })));

  private getWeatherComponent = toSignal(toObservable(this.componentIdentifier).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((identifier) => this.dailyLogComponentService.getSubComponent(identifier)),
    tap((weatherComponent) => {
      this.isLoading.set(false);
    })
  ));


  removeSky(idx:number) {
    let component = this.weatherComponent() as WeatherComponent;
    component.Skys.splice(idx, 1);
    this.dailyLogComponentService.updateSubComponent(component).subscribe();
  }

  addSky() {
    if(this.skyName){ 
      let component = this.weatherComponent() as WeatherComponent;

      if(!component.Skys){
        component.Skys = new Array<string>();
      }
      
      if(!component.Skys.find(x => x.toLocaleLowerCase() === this.skyName.toLocaleLowerCase())){
        component.Skys.push(this.skyName);
        this.dailyLogComponentService.updateSubComponent(component).subscribe();
      }     
    }    
  }

  removeTemp(idx: number) {
    let component = this.weatherComponent() as WeatherComponent;  
    component.Temperatures.splice(idx, 1);   
    this.dailyLogComponentService.updateSubComponent(component).subscribe();
  }

  addTemperature() { 
    if(this.tempName){ 
      let component = this.weatherComponent() as WeatherComponent;

      if(!component.Temperatures){
        component.Temperatures = new Array<string>();
      }
      
      if(!component.Temperatures.find(x => x.toLocaleLowerCase() === this.tempName.toLocaleLowerCase())){
        component.Temperatures.push(this.tempName);
        this.dailyLogComponentService.updateSubComponent(component).subscribe();
      }     
    }  
  }
  skyInputBoxChange(sky: string, idx: number) {
    let component = this.weatherComponent() as WeatherComponent;
    if(!this.previousSkys.find(x => x.toLocaleLowerCase() === sky.toLocaleLowerCase())){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }else{
      component.Skys[idx] = this.previousSkys[idx];
    }
    
  }

  temperatureInputBoxChange(temperature: string, idx: number) {
    let component = this.weatherComponent() as WeatherComponent;

    if(!this.previousTemps.find(x => x.toLocaleLowerCase() === temperature.toLocaleLowerCase())){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }else{
      component.Temperatures[idx] = this.previousTemps[idx];
    }    
  }

  dropSky(event: CdkDragDrop<string[]>) {
    let component = this.weatherComponent() as WeatherComponent;
    moveItemInArray(component.Skys, event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }
    
  }

  dropTemp(event: CdkDragDrop<string[]>) {
    let component = this.weatherComponent() as WeatherComponent;
    moveItemInArray(component.Temperatures, event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }
    
  }

}
