import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProjectGlobalSettingsWeatherComponent } from './weather.component';

describe('WeatherComponent', () => {
  let component: ProjectGlobalSettingsWeatherComponent;
  let fixture: ComponentFixture<ProjectGlobalSettingsWeatherComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProjectGlobalSettingsWeatherComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ProjectGlobalSettingsWeatherComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
