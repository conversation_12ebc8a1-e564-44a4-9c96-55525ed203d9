<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">Weather Settings</h1>
	<p>Customize your diary's weather settings. Easily rearrange options with a simple
		drag-and-drop.</p>
	<!-- temp and weather -->
	<section>
		<div class="row">
			<!-- temperature -->
			<div class="col-12 col-lg-6">
				<div class="card mb-3">
					<div class="card-body">
						<h2 class="card-title fs-6">Temperature</h2>
						<div class="input-group mb-2">
							<input type="text" class="form-control" [(ngModel)]="tempName"
								placeholder="Add Temperature" />
							<button class="btn btn-outline-dark" (click)="addTemperature()">Add</button>
						</div>
						<!--dnd-sortable-container [sortableData]="weatherComponent?.Temperatures"
			[dropZones]="['temperature']" TODO: fix-->
						<ul class="list-group" cdkDropList (cdkDropListDropped)="dropTemp($event)">
							<!-- dnd-sortable [sortableIndex]="i"
			  (onDropSuccess)="tempDragEnd()" TODO: Fix-->
							@for (temperature of weatherComponent()?.Temperatures; track $index) {
							<li class="list-group-item d-flex align-items-center justify-content-between" cdkDrag>
								<div dnd-sortable-handle style="cursor: grab;">
									<i class="drag-handle fas fa-grip-vertical text-secondary fa-lg me-3"
										aria-hidden="true"></i>
								</div>
								<div class="input-group">
									<input class="form-control" type="text"
										[(ngModel)]="weatherComponent()?.Temperatures[$index]"
										(blur)="temperatureInputBoxChange(temperature, $index)" />
									<button type="button" class="btn btn-outline-secondary"
										(click)="removeTemp($index)">
										<i class="fas fa-trash-alt"></i>
									</button>
								</div>
							</li>
							}@empty {
							<li class="list-group-item">
								<div class="alert alert-info m-0" role="alert">
									No temperatures.
								</div>
							</li>
							}
						</ul>
					</div>
				</div>
			</div>
			<!-- sky conditions -->
			<div class="col-12 col-lg-6">
				<div class="card mb-3">
					<div class="card-body">
						<h2 class="card-title fs-6">Sky Conditions</h2>
						<!-- dnd-sortable-container [sortableData]="weatherComponent?.Skys" [dropZones]="['skys']" TODO: fix-->
						<div class="input-group mb-2">
							<input type="text" class="form-control" [(ngModel)]="skyName"
								placeholder="Add Sky Condition" />
							<button class="btn btn-outline-dark" (click)="addSky()">Add</button>
						</div>
						<ul class="list-group" cdkDropList (cdkDropListDropped)="dropSky($event)">
							<!-- dnd-sortable [sortableIndex]="i" (onDropSuccess)="tempDragEnd()" TODO: fix-->
							@for (sky of weatherComponent()?.Skys; track $index) {
							<li class="list-group-item d-flex align-items-center justify-content-between" cdkDrag>
								<div dnd-sortable-handle style="cursor: grab;">
									<i class="drag-handle fas fa-grip-vertical text-secondary fa-lg me-3"
										aria-hidden="true"></i>
								</div>
								<div class="input-group">
									<input class="form-control" type="text"
										[(ngModel)]="weatherComponent()?.Skys[$index]"
										(blur)="skyInputBoxChange(sky, $index)" />
									<button type="button" class="btn btn-outline-secondary" (click)="removeSky($index)">
										<i class="fas fa-trash-alt"></i>
									</button>
								</div>
							</li>
							}@empty{
							<li class="list-group-item">
								<div class="alert alert-info m-0" role="alert">
									No sky conditions.
								</div>
							</li>
							}
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>
</div>