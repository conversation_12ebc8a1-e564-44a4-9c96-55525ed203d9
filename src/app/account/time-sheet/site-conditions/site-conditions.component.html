<div class="container p-4 mb-4">
	<!--  header -->
	<h1 class="page-title fs-5">Site Conditions Settings</h1>
	<p>Customize your diary's site conditions. Easily rearrange options with a simple
		drag-and-drop.</p>
	<!-- search -->
	<section class="col-12 col-md-6 col-xl-4 mb-3">
		<div class="input-group">
			<input type="text" class="form-control" [(ngModel)]="condition" placeholder="Add site condition" />
			<button class="btn btn-outline-dark" (click)="addCondition()">Add</button>
		</div>
	</section>
	<!-- site conditions -->
	<section>
		<ul class="list-group" cdkDropList (cdkDropListDropped)="dropCondition($event)">
			@for (item of siteConditionsComponent()?.Conditions; track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between" cdkDrag>
				<div dnd-sortable-handle style="cursor: grab;">
					<i class="fas fa-grip-vertical a-lg text-secondary me-3 drag-handle" aria-hidden="true"></i>
				</div>
				<div class="input-group">
					<input class="form-control" type="text" [(ngModel)]="siteConditionsComponent()?.Conditions[$index]"
						(blur)="conditionInputBoxChange(item, $index)" />
					<button type="button" class="btn btn-outline-secondary" (click)="removeCondition($index)">
						<i class="fas fa-trash-alt"></i>
					</button>
				</div>
			</li>
			}@empty {
			<li class="list-group-item">
				<div class="alert alert-info m-0" role="alert">
					No site conditions.
				</div>
			</li>
			}
		</ul>
	</section>
</div>