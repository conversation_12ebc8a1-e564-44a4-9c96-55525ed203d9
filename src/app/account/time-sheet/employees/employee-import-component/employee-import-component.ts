﻿import { Component, Input, OnInit, inject } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable, catchError, forkJoin, of, switchMap, tap, throwError} from 'rxjs';
import { newGuid } from 'src/app/models/utilities';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { EmployeeImportService } from 'src/app/construction/shared/data-access/employee-import.service';
import { Employee, EmployeeImportInfo, EmployeeImportRequest, EmployeeImportResponse, ImportSettings } from 'src/app/construction/shared/interfaces/employee'
import { CommonModule } from '@angular/common';
import { CivCastAWSUploader } from 'src/app/shared/ui/uploader/civcast.aws.uploader.component';
import { FormsModule } from '@angular/forms';
import { EmployeeService, EmployeeStoreStatus } from 'src/app/construction/shared/data-access/employee.service';

@Component({
  selector: 'employee-import-component',
  standalone: true,
  imports: [CommonModule, CivCastAWSUploader, FormsModule],
  templateUrl: './employee-import-component.html'
})
export class EmployeeImportComponent implements OnInit {
  //public uploader: FileUploader;
  employeeImportService = inject(EmployeeImportService);
  employeeService = inject(EmployeeService);
  router = inject(Router);
  aRouter = inject(ActivatedRoute);
  toastrService = inject(ToastrService);
  lambdaAwsService = inject(LambdaAWSService);
  public responseInfo = {
    IsPartial: false,
    Rule: 0,
    SaveSettings: true
  };

  maxStep = 5;
  minStep = 1;
  currentStep = 0;
  settingOption = true;
  letterArray: string[] = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
  isError = false;
  updatingEmployees = false;
  employeeInfo: any;
  employeeImportRequest: EmployeeImportRequest | null = null;
  employeeImportResponse: EmployeeImportResponse | null = null;
  sampleEmployee: Employee | null = null;
  importInfo: EmployeeImportInfo | null = null;
  saveSettings: boolean = false;
  hasSettings: boolean = false;
  isLoading: boolean = false;
  importSettings: ImportSettings | null = null;
  currentEmployees = this.employeeService.employeeStore;

  ngOnInit(): void {
    this.isLoading = true;

    this.employeeService.executeEmployeeStore.set(true);

    this.employeeImportService.getSettings().subscribe(
      {
        next: (result: any) => {
          this.importSettings = result;
          this.isLoading = false;
        },
        error: (err: any) => {
          this.isLoading = false;
        }
      });
      
  }

  setSelectedFiles(files: FileList) {
      this.isLoading = true;

      let file = files[0];

      this.setFileKey(file).pipe(
        switchMap((key: string) => 
            this.setupImportResponse(this.SetupImportRequest(key))
          ), 
          tap((data) => {
            if (this.hasSettings) {
              this.gatherEmployees().subscribe({
                next: (value: any) => {
                  this.currentStep = 4;
                  this.isLoading = false;
                },
                error: (err: any) => {
                  this.toastrService.error("Issue setting the selected files");
                  this.isLoading = false;
                }
              });              
            } else {
              if (this.employeeImportResponse) {
                if (this.employeeImportResponse.Worksheets.length > 0) {
                  if (this.employeeImportResponse.Worksheets[0] === environment.EmployeeImport.WorksheetName) {
                    this.gatherEmployees().subscribe({
                      next: (value: any) => {
                        this.currentStep = 4;
                        this.isLoading = false;
                      },
                      error: (err: any) => {
                        this.toastrService.error("Issue setting the selected files");
                        this.isLoading = false;
                      }
                    });                    
                  } else {
                    if (this.employeeImportResponse.IsPartial) {
                      this.currentStep = 1;
                    } else {
                      this.currentStep = 3;
                    }
                  }
                }
              }
            }
          }),
          catchError((err) => 
            of(this.isLoading = false, this.toastrService.error("Issue setting the selected files"))
          )
        ).subscribe(); 

  }

  setupImportResponse(request: EmployeeImportRequest) : Observable<EmployeeImportResponse> {      

    if(!request){
      return throwError(() => new Error("No request"));
    }else{
      return new Observable(obs => {
        this.isLoading = true;

        if(request.Key){
          this.employeeImportService.import(request).subscribe({
            next: (employeeImportResponse: EmployeeImportResponse) => {
              this.employeeImportResponse = employeeImportResponse;
              this.isLoading = false;
              obs.next({...employeeImportResponse});
              obs.complete();
            },
            error: (err: any) => {
              this.isLoading = false;
              obs.error(err);
            }
          })
        }else{
          obs.error("No Key Found in request");
        }

      });
    }

  }

  error(event: any){
    console.log(event);
    this.toastrService.error("Upload Issue");
  }

  setFileKey(file: File): Observable<string> {
    return new Observable(obs => {
      const guid = newGuid()
      var key = `employee-imports/${guid}`;
  
      this.lambdaAwsService.uploadFilePresignedUrl(key, file).subscribe({
        next: (value: any) => {
          obs.next(key);
          obs.complete();
        }, error: (err: any) => {
          obs.error(err);
        }
      });       
    });

  }

  goToNextStep() { 
      this.isLoading = true;

      if (this.currentStep == this.maxStep) {
        console.error("max step reached");
      } else {
        this.currentStep++;
      }

      if (this.currentStep == 2) {
        if(this.employeeImportRequest){
          this.employeeImportService.getSampleEmployee(this.employeeImportRequest).subscribe({
            next: (sampleEmployee) => {
              this.sampleEmployee = sampleEmployee;  
            },
            error: (err: any) =>{
              console.log(err);           
            }
          });
        }
      } else if (this.currentStep == 3) {
      } else if (this.currentStep == 4) {
        this.gatherEmployees().subscribe();
      }
  }

  SetupImportRequest(key: string) : EmployeeImportRequest{
      if(!key){
        throw new Error("No key to setup import request");
      }

      this.employeeImportRequest = new EmployeeImportRequest();
      this.employeeImportRequest.CurrentEmployees = this.currentEmployees()?.Employees || [];
      this.employeeImportRequest.Bucket = environment.EmployeeImport.Bucket;
      this.employeeImportRequest.Key = key;
  
      if (this.importSettings) {
        if (this.importSettings.FirstName && this.importSettings.LastName && this.importSettings.Classification && this.importSettings.EmployeeId) {
          this.employeeImportRequest.FirstName = this.importSettings.FirstName;
          this.employeeImportRequest.LastName = this.importSettings.LastName;
          this.employeeImportRequest.Classification = this.importSettings.Classification;
          this.employeeImportRequest.WorkSheetName = this.importSettings.WorkSheetName;
          this.employeeImportRequest.EmployeeId = this.importSettings.EmployeeId;
          this.hasSettings = true;
        }
      }
  
      if (!this.hasSettings) {
        this.employeeImportRequest.EmployeeId = {
          Cell: "A",
          Name: ""
        };
  
        this.employeeImportRequest.FirstName = {
          Cell: "B",
          Name: ""
        };
        this.employeeImportRequest.LastName = {
          Cell: "C",
          Name: ""
        };
        this.employeeImportRequest.Classification = {
          Cell: "D",
          Name: ""
        };
  
      }
  
      return {...this.employeeImportRequest};


  }

  saveImportSettings(): Observable<any>{

    if(!this.employeeImportRequest){
      return throwError(() => new Error("No request found"));
    }else{
      let importSettings = new ImportSettings();
      importSettings.FirstName = this.employeeImportRequest.FirstName;
      importSettings.LastName = this.employeeImportRequest.LastName;
      importSettings.Classification = this.employeeImportRequest.Classification;
      importSettings.WorkSheetName = this.employeeImportRequest.WorkSheetName;
      importSettings.EmployeeId = this.employeeImportRequest.EmployeeId;
  
      return this.employeeImportService.saveSettings(importSettings);
    }
 
  }

  gatherEmployees(): Observable<any> {
    if(!this.employeeImportRequest){
      return throwError(() => new Error("No request found in order to gather employees"));
    }else{
      return new Observable<any>(obs => {      
        if(this.employeeImportRequest){
          if (this.saveSettings) {
            this.saveImportSettings().subscribe();
          } 
  
          this.employeeImportRequest.CurrentEmployees = this.currentEmployees()?.Employees || [];
          this.employeeImportService.gatherEmployees(this.employeeImportRequest).subscribe({
            next: (employeeInfo: EmployeeImportInfo) => {
              this.importInfo = employeeInfo;
  
              if (this.importInfo.EmployeeValidation && this.importInfo.NewEmployees) {
                if (this.importInfo.EmployeeValidation.length === 0 && this.importInfo.NewEmployees.length > 0) {
                  this.saveEmployees();                  
                }
              }
              obs.next(true);
              obs.complete();
            },
            error: (err: any) => {
              obs.error(err);
            }
          });
        }else{
          obs.error("No request found in order to gather employees");
        }

      });
    }



  }

  saveEmployees() {
  
      this.isLoading = true;
      let empList = new Array<Employee>();

      if(this.importInfo){
        if(!this.importInfo.EmployeeValidation){
          this.importInfo.EmployeeValidation = [];
        }

        for (let emp of this.importInfo.EmployeeValidation) {
          empList.push(emp.Employee);
        }
      }    

      this.employeeService.SaveEmployeeList(empList, true, this.aRouter?.parent);
  }

  removeSettings() {
    
      this.isLoading = true;
      this.employeeImportService.deleteSettings().subscribe({
        next: (value: any) => {
          this.isLoading = false;    
          this.importSettings = null;
        }
      });
  }


  fixData(employeeInfo: any) {
    if (employeeInfo['IsVerify']) {
      employeeInfo.IsVerify = !employeeInfo.IsVerify;
    } else {
      employeeInfo['IsVerify'] = true;
    }
  }

  async goToPreviousStep() {
    if (this.currentStep == this.minStep) {
    } else {
      this.currentStep--;
    }
  }
}
