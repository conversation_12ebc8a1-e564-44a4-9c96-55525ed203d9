﻿import { Component, Input, OnInit, effect, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Employee } from 'src/app/construction/shared/interfaces/employee';
import { EmployeeService } from 'src/app/construction/shared/data-access/employee.service';

@Component({
  selector: 'employee-manage-component',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './employee-manage-component.html',
  styleUrls:['./employee-manage-component.css']
})
export class EmployeeManageComponent implements OnInit {
  employeeService = inject(EmployeeService);
  private aRoute = inject(ActivatedRoute);
  employee = this.employeeService.employee;
  @Input() formType: string = "default";

  isLoading = this.employeeService.isEmployeeLoading;
  isSaving = this.employeeService.isSavingEmployee;
  error = this.employeeService.error  
  pageTitle: string = "page";
  employeeId: string  | null = null;
  employeeFormGroup = new FormGroup({
    _t: new FormControl<string[]>(['BaseEntity', 'Employee'], Validators.required),    
    FirstName: new FormControl<string>('', Validators.required),
    LastName: new FormControl<string>('', Validators.required),
    Classification: new FormControl<string | null>(null),
    InternalId: new FormControl<string | null>(null),
    CustomId: new FormControl<string | null>(null, Validators.required),
    IsActive: new FormControl<boolean>(true)
    
  }); // Assuming you have a form group in your service

  constructor() { 
    effect(() => {
      if(this.employee()){
        this.employeeFormGroup.patchValue({
          FirstName: this.employee()?.FirstName || '',
          LastName: this.employee()?.LastName || '',
          Classification: this.employee()?.Classification || null,
          InternalId: this.employee()?.InternalId || null,
          CustomId: this.employee()?.CustomId || null,
          IsActive: this.employee()?.IsActive || true
        }, {emitEvent: false});
      }
  });
  }

  ngOnInit(): void {
    const {formType, pageTitle} = this.aRoute.snapshot.data;    

    this.formType = formType;
    this.pageTitle = pageTitle; 

    if(this.formType == "edit"){
      const {employeeId} = this.aRoute.snapshot.params;
      this.employeeId = employeeId;
      if(this.employeeId){
        this.employeeService.employeeId.set(employeeId);
      }
    }else{
      this.employeeService.employeeId.set(null);
      this.employeeService.employeeResource.set(null);
    }


  }

  saveEmployee() {
    this.isSaving.set(true);
    var employee = this.employeeFormGroup.value as Employee;
    employee._t = ['BaseEntity', 'Employee'];

    try {
      if (this.formType == 'add') {
        employee.IsActive = true;
        this.employeeService.AddEmployee(employee, true, this.aRoute.parent); 
      } else if (this.formType == 'edit') {                
        this.employeeService.SaveEmployee(employee, true, this.aRoute.parent);        
      }
      
    } catch (error) {
      console.error(error);
    }
  }
}
