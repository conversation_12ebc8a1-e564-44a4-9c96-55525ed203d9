import { Component, inject, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { debounceTime } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/construction/shared/data-access/employee.service';
import { Employee } from 'src/app/construction/shared/interfaces/employee';
import { DelegateLocalStorageService } from 'src/app/shared/data-access/delegate-localstorage-service';

@Component({
    selector: 'app-employees',
    imports: [CommonModule, FormsModule, ReactiveFormsModule],
    templateUrl: './employees.component.html',
    styleUrl: './employees.component.css'
})
export class EmployeesComponent {
  aRouter = inject(ActivatedRoute);
  delegateStorageService = inject(DelegateLocalStorageService);
  employeeService = inject(EmployeeService);
  toastrService = inject(ToastrService);
  router = inject(Router);
  templateUrl = "";
  loading = this.employeeService.isLoading;
  view = "list";
  status = "active";
  inputPlaceholder = "";
  search = new UntypedFormControl();
  employees: Employee[] = [];
  nEmployees = computed(() => this.employeeService.employeeStore()); //Employee[] = [];
  constructor() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe(value => {
      const v = value.toLowerCase().trim();
      this.employees = this.nEmployees()?.Employees.filter(
        item =>
          item.FirstName && item.FirstName.toLowerCase().indexOf(v) > -1 ||
          item.LastName && item.LastName.toLowerCase().indexOf(v) > -1 ||
          (item.FirstName + ' ' + item.LastName).toLowerCase().indexOf(v) > -1 ||
          // || item.ForemanId.toLowerCase().indexOf(v) > -1
          item.CustomId.toLowerCase().indexOf(v) > -1
      ) as Employee[];
    });

    this.templateUrl = environment.excel_template_locations.employee;
    this.gatherPermissions();
    this.gatherAll(this.status);
    this.employeeService.executeEmployeeStore.set(true); // Trigger initial load of employee store

    effect(() => {
      this.employees = this.employeeService.employeeStore()?.Employees || [];
    });

  
  }

  gatherPermissions() {
    // try {
    //   forkJoin([this.accessService.CheckAccess("employees", "updateemployee"), this.accessService.CheckAccess("employees", "addemployee")]).subscribe({
    //     next: (result) => {
    //       this.updateEmployeeAccess = result[0].Access;
    //       this.addEmployeeAccess = result[1].Access;
    //     },
    //     error: (err) => {

    //     }
    //   })

    // } catch (error) {

    // } finally {

    // }

  }

  gatherAll(status: any) {
    if (this.view === 'list') {
      this.inputPlaceholder = 'filter by name, number';
    } else if (this.view === 'crew') {
      this.inputPlaceholder = 'filter foreman by name, number';
    }

    this.searchEmployee(status);
  }

  searchEmployee(status: any) {

    this.status = status;
    this.employeeService.status.set(status);
  }

  setView(view: string) {
    this.view = view;
    this.gatherAll(this.status);
  }

  importEmployees() {
    this.router.navigate(["import"], { relativeTo: this.aRouter });
  }

  addEmployee() {
    this.router.navigate(["add"], { relativeTo: this.aRouter });
  }

  editEmployee(emp: Employee) {
    this.router.navigate(["edit", emp.InternalId], { relativeTo: this.aRouter });
  }

  deactivate(idx: number, emp: Employee) {
    emp.IsActive = false;
    this.employeeService.SaveEmployee(emp); 
  }

   activate(idx: number, emp: Employee) {
    //this.loading.set(false); 
    emp.IsActive = true;
    this.employeeService.SaveEmployee(emp);
  }
}
