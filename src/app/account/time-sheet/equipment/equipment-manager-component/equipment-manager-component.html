﻿<div class="container p-4 mb-4">
  <!-- header -->
  <h1 class="fs-5 mb-3">{{pageTitle}}</h1>
<!-- add/edit employee -->
  <section>
    <!-- <div class="placeholder-glow" *ngIf="isLoading">
    <div class="mb-3" *ngFor="let item of [1,2,3,4]">
      <span class="placeholder col-12" style="height:60px;"></span>
    </div>
  </div> -->
    <div>
      <form #manageEquipmentForm="ngForm" (ngSubmit)="saveItem()">
        <div class="mb-3">
          <!-- number -->
          <div class="form-floating mb-3">
            <input class="form-control" type="text" name="number" [(ngModel)]="equipmentItem.Number"
              [disabled]="isSaving()" id="number" required />
            <label for="floatingInput">Number</label>
          </div>
          <!-- make -->
          <div class="form-floating mb-3">
            <input class="form-control" type="text" name="make" [(ngModel)]="equipmentItem.Make" id="make"
              [disabled]="isSaving()" required />
            <label for="floatingInput">Make</label>
          </div>
          <!-- model -->
          <div class="form-floating mb-3">
            <input class="form-control" type="text" name="model" [(ngModel)]="equipmentItem.Model" id="model"
              [disabled]="isSaving()" required />
            <label for="floatingInput">Model</label>
          </div>
          <!-- notes -->
          <div class="form-floating mb-3">
            <textarea class="form-control" name="notes" [(ngModel)]="equipmentItem.Notes" id="notes"
              [disabled]="isSaving()"></textarea>
            <label for="floatingInput">Notes</label>
          </div>
        </div>
        <!-- save button -->
        <div class="d-flex justify-content-end">
          <button type="submit" class="btn btn-primary"
            [disabled]="!manageEquipmentForm.form.valid || isSaving()">
            <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isSaving()"></i>
            Save
          </button>
        </div>
      </form>
    </div>
  </section>
</div>