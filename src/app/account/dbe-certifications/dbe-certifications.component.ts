import { CommonModule } from '@angular/common';
import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DbeCertificationsService } from 'src/app/account/shared/data-access/dbe-certifications-service';
import { LambdaAWSService, PresignedUrlResponse } from 'src/app/shared/data-access/lambdaaws.service';
import { DBECertificationAdd, DocumentInfo } from 'src/app/account/shared/interfaces/dbe-certifications.model';
import { environment } from 'src/environments/environment';
import { v4 as uuidv4 } from 'uuid';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { tap } from 'rxjs/operators';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule],
  selector: 'app-dbe-certifications',
  templateUrl: './dbe-certifications.component.html',
  styleUrls: ['./dbe-certifications.component.css']
})
export class DbeCertificationsComponent implements OnInit, OnDestroy {
 
  dbeCertificationsService = inject(DbeCertificationsService);
  staticCertifications = this.dbeCertificationsService.staticCertifications;
  userCertifications = this.dbeCertificationsService.userCertifications;
  checkedCertifications: Set<string> = new Set<string>();
  uploadProgress: { [key: string]: number } = {};
  isLoading = this.dbeCertificationsService.isLoading;
  isSaving = false; // Track saving state
  
  lambdaAWSService = inject(LambdaAWSService);

  constructor(){
    this.dbeCertificationsService.executeUserDBECert.set(true);
  }

  staticCertificationSignal = toSignal(toObservable(this.staticCertifications).pipe(
    tap((staticCerts) => {
      if (staticCerts) {
        this.loadUserCertifications();
      }
    })
  ));

  userCertificationSignal = toSignal(toObservable(this.userCertifications).pipe(
    tap((userCerts) => {
      if (userCerts) {
        this.checkedCertifications = new Set(userCerts.map(cert => cert.DBECertId));
        for (const cert of userCerts) {
          delete this.uploadProgress[cert.DBECertId];
        }
      }
    })
  ));

  ngOnInit(): void {

  }

  ngOnDestroy(): void {
    this.dbeCertificationsService.destroy();
  }  

  onFileSelected(certificationId: string, event: Event): void {
    const fileInput = event.target as HTMLInputElement;
    const file = fileInput.files?.[0];
    if (file) {
        this.uploadFile(certificationId, file);
        fileInput.value = ''; 
    }
}

  private fetchPresignedUrlForDocument(doc: DocumentInfo): void {
    this.lambdaAWSService.getDownloadPresignedUrl(doc.S3Key, doc.FileName).subscribe({
      next: (presignedUrl: PresignedUrlResponse) => {
        document.location.href = presignedUrl.PresignedUrl;
      },
      error: (err: any) => console.error('Error retrieving presigned URL:', err)
    });
  }

  private uploadFile(certificationId: string, file: File): void {
    const encodedFileName = encodeURIComponent(uuidv4() + '_' + file.name);
    const s3Key = `${environment.DBECertificationDocuments.S3KeyRoot}/${encodedFileName}`;
  
    this.isSaving = true;
    this.lambdaAWSService.getUploadPresignedUrl(s3Key, file.type).subscribe({
      next: (presignedUrl: PresignedUrlResponse) => {
        this.lambdaAWSService.uploadFileWithSignedURLWithProgress(presignedUrl.PresignedUrl, file).subscribe({
          next: (progress: string) => {
            const progressValue = parseInt(progress, 10);
            if (!isNaN(progressValue)) {
              this.uploadProgress[certificationId] = progressValue;
            }
          },
          complete: () => {
            this.uploadProgress[certificationId] = 100;
            this.saveDocumentMetadata(certificationId, file.name, s3Key, file.type, file.size);
            this.isSaving = false;
          },
          error: (err: any) => {
            console.error('Upload error:', err);
            delete this.uploadProgress[certificationId];
            this.isSaving = false;
          }
        });
      },
      error: (err: any) => {
        console.error('Error in uploadFile:', err);
        this.isSaving = false;
      }
    });
  }

  private saveDocumentMetadata(certificationId: string, fileName: string, s3Key: string, contentType: string, fileSize: number): void {
    const docInfo: DocumentInfo = {
      DocumentId: uuidv4(),
      BucketName: environment.DBECertificationDocuments.Bucket,
      ContentType: contentType,
      FileName: fileName,
      FileSize: fileSize,
      S3Key: s3Key
    };

    const certIndex = this.userCertifications()?.findIndex(cert => cert.DBECertId === certificationId);
    if (certIndex !== -1) {
      this.dbeCertificationsService.updateDBECertDocument(certificationId, docInfo);     
    }
  }

  private loadUserCertifications(): void {
    //this.dbeCertificationsService.getDBECerts();
  }

  isChecked(certificationId: string): boolean {
    return this.checkedCertifications.has(certificationId);
  }

  onCertificationChange(certificationId: string, checked: boolean): void {
    if (checked) {
      this.addCertification(certificationId);
    } else {
      this.removeCertification(certificationId);
    }
  }

  private addCertification(certificationId: string): void {
    const certification: DBECertificationAdd = { DBECertId: certificationId, DBECertDocuments: [] };
    this.dbeCertificationsService.addDBECert(certification);
  }

  private removeCertification(certificationId: string): void {
    this.dbeCertificationsService.deleteDBECert(certificationId);
  }

  getDocuments(certificationId: string): DocumentInfo[] {
    const certification = this.userCertifications()?.find(cert => cert.DBECertId === certificationId);
    return certification ? certification.DBECertDocuments : [];
  }

  confirmDeleteDocument(certificationId: string, document: DocumentInfo): void {
    if (confirm("Are you sure you want to delete this document?")) {
      this.deleteDocument(certificationId, document);
    }
  }

  private deleteDocument(certificationId: string, document: DocumentInfo): void {
    const certIndex = this.userCertifications()?.findIndex(cert => cert.DBECertId === certificationId);

    if (certIndex !== -1) {
      this.dbeCertificationsService.deleteDBECertDocument(certificationId, document.DocumentId);
    }
  }
}