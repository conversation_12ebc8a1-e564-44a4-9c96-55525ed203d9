import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { AddendumNotificationsService } from 'src/app/account/shared/data-access/addendum-notifications.service';
import { AddendumNotification } from 'src/app/account/shared/interfaces/addendum-notifications.model';

@Component({
  selector: 'app-addendum-notifications',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './addendum-notifications.component.html',
  styleUrls: ['./addendum-notifications.component.css'],
})
export class AddendumNotificationsComponent implements OnInit {
  settingsForm!: FormGroup; // Main form group
  newContactForm!: FormGroup; // Separate form for adding new contacts
  changesMade = false; // Track unsaved changes
  editingIndex: number | null = null; // Track the index of the contact being edited
  isLoading = true; // Track loading state
  isSaving = false; // Track saving state

  private originalContactValue: { [index: number]: { contactName: string; contactEmail: string } } = {};
  private addendumNotificationsService = inject(AddendumNotificationsService);
  private toastr = inject(ToastrService);
  private fb = inject(FormBuilder);

  private static readonly TOASTR_MESSAGES = {
    loadSettingsError: 'Failed to load settings. Please try again.',
    nameEmailValidationError: 'Name and proper email address required.',
    addContactSuccess: 'Contact added successfully. Don’t forget to save.',
    editInProgressError: 'Finish editing the current contact before editing another.',
    contactValidationError: 'Please correct errors before saving the contact.',
    applyContactSuccess: 'Contact changes applied. Be sure to save your changes.',
    removeContactConfirmation: 'Are you sure you want to remove this contact?',
    removeContactInfo: 'Contact removed. Don’t forget to save.',
    saveSettingsValidationError: 'Please correct errors in the form before saving.',
    saveSettingsSuccess: 'Settings saved successfully!',
    saveSettingsError: 'Failed to save settings. Please try again.',
  };

  constructor() {
    this.initializeForm(); 
  }

  ngOnInit(): void {
    this.loadSettings(); 
  }

  private initializeForm(addendumNotifications?: AddendumNotification): void {
    const contacts = addendumNotifications?.Contacts || []; 
    const contactArray = this.fb.array(
      contacts.map((contact) =>
        this.fb.group({
          contactName: [contact.ContactName, Validators.required],
          contactEmail: [contact.ContactEmail, [Validators.required, Validators.email]],
        })
      )
    );

    this.settingsForm = this.fb.group({
      sendAddendumNotifications: [addendumNotifications?.SendAddendumNotifications || false, Validators.required],
      contacts: contactArray,
    });

    this.newContactForm = this.fb.group({
      contactName: ['', Validators.required],
      contactEmail: ['', [Validators.required, Validators.email]],
    });
  }

  private loadSettings(): void {
    this.isLoading = true;
    this.addendumNotificationsService.getSettings().subscribe({
      next: (settings) => {
        this.initializeForm(settings);
        this.isLoading = false;
      },
      error: () => {
        this.toastr.error(AddendumNotificationsComponent.TOASTR_MESSAGES.loadSettingsError, 'Error');
        this.isLoading = false;
      }
    });
  }

  get contacts(): FormArray {
    return this.settingsForm.get('contacts') as FormArray;
  }

  cancelNewContact(): void {
    this.newContactForm.reset();
  }

  addContact(): void {
    if (this.newContactForm.invalid) {
      this.toastr.error(AddendumNotificationsComponent.TOASTR_MESSAGES.nameEmailValidationError, 'Validation Error');
      return;
    }

    const newEmail = this.newContactForm.value.contactEmail;

    // Check for duplicates
    const duplicate = this.contacts.controls.some(contact => contact.value.contactEmail === newEmail);
    if (duplicate) {
        this.toastr.error('This email address is already added.', 'Validation Error');
        return;
    }
    
    const newContact = this.fb.group({
      contactName: [this.newContactForm.value.contactName, Validators.required],
      contactEmail: [this.newContactForm.value.contactEmail, [Validators.required, Validators.email]],
    });

    this.contacts.push(newContact);
    this.newContactForm.reset();
    this.toastr.success(AddendumNotificationsComponent.TOASTR_MESSAGES.addContactSuccess, 'Success');
    this.changesMade = true;
  }

  editContact(index: number): void {
    if (this.editingIndex !== null && this.editingIndex !== index) {
      this.toastr.error(AddendumNotificationsComponent.TOASTR_MESSAGES.editInProgressError, 'Error');
      return;
    }

    this.originalContactValue[index] = { ...this.contacts.at(index).value };
    this.editingIndex = index;
  }

  applyContact(index: number): void {
    const contact = this.contacts.at(index);

    if (contact.invalid) {
      this.toastr.error(AddendumNotificationsComponent.TOASTR_MESSAGES.contactValidationError, 'Validation Error');
      contact.markAllAsTouched();
      return;
    }

    delete this.originalContactValue[index];
    this.editingIndex = null;
    this.toastr.success(AddendumNotificationsComponent.TOASTR_MESSAGES.applyContactSuccess, 'Success');
    this.changesMade = true;
  }

  cancelEditContact(index: number): void {
    if (this.editingIndex === index && this.originalContactValue[index]) {
      this.contacts.at(index).setValue(this.originalContactValue[index]); // Restore original value
      delete this.originalContactValue[index];
    }
    this.editingIndex = null;
  }

  removeContact(index: number): void {
    if (confirm(AddendumNotificationsComponent.TOASTR_MESSAGES.removeContactConfirmation)) {
      this.contacts.removeAt(index);
      this.editingIndex = null;
      this.toastr.info(AddendumNotificationsComponent.TOASTR_MESSAGES.removeContactInfo, 'Info');
      this.changesMade = true;
    }
  }

  saveSettings(): void {
    if (this.settingsForm.invalid) {
      this.toastr.error(AddendumNotificationsComponent.TOASTR_MESSAGES.saveSettingsValidationError, 'Validation Error');
      return;
    }

    this.isSaving = true;

    const updatedAddendumNotifications: AddendumNotification = {
      SendAddendumNotifications: this.settingsForm.value.sendAddendumNotifications,
      Contacts: this.contacts.value.map((contact: { contactName: string; contactEmail: string }) => ({
        ContactName: contact.contactName,
        ContactEmail: contact.contactEmail,
      })),
    };

    this.addendumNotificationsService.addUpdateSettings(updatedAddendumNotifications).subscribe({
      next: () => {
        this.toastr.success(AddendumNotificationsComponent.TOASTR_MESSAGES.saveSettingsSuccess, 'Success');
        this.changesMade = false;
        this.isSaving = false;
      },
      error: () => {
        this.toastr.error(AddendumNotificationsComponent.TOASTR_MESSAGES.saveSettingsError, 'Error');
        this.isSaving = false;
      },
    });
  }
}