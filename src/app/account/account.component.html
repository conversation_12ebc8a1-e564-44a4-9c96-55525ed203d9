<!-- header -->
<header class="bg-light p-3 p-lg-4">
    <div class="container d-flex justify-content-between align-items-center">
        <!-- page title -->
        <h1 class="page-title fs-5 mb-0">Account</h1>
        <!-- switch accounts -->
        <app-delegate-menu></app-delegate-menu>
    </div>
</header>
<!-- navs -->
<section class="p-3 p-lg-4">
    <div class="container">
        <div class="row">
            <!-- bid management -->
            <div class="col-12 col-md-6 col-xl-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <header class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
                                style="height: 40px; width: 40px;"><i class="fal fa-megaphone"></i></div>
                            <h2 class="card-title fs-6 mb-0 page-title">Bid Management</h2>
                        </header>
                        <nav>
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link custom-link active" routerLink="bid-management-settings">New
                                        Project Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="ebid-settings">E-Bid Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="link-sharing">Project Link Sharing</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- bid opportunities -->
            <div class="col-12 col-md-6 col-xl-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <header class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
                                style="height: 40px; width: 40px;"><i class="fal fa-search"></i></div>
                            <h2 class="card-title fs-6 mb-0 page-title">Bid Opportunities</h2>
                        </header>
                        <nav>
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="bid-alerts">Bid Alerts</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="general-settings">Privacy
                                        Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="addendum-notifications">Addendum Alert
                                        Emails</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="download-history"
                                        [queryParamsHandling]="'preserve'" skipLocationChange="true">Download
                                        History</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="bid-history">Bid History ✏️</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- onsite -->
            <div class="col-12 col-md-6 col-xl-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <header class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
                                style="height: 40px; width: 40px;"><i class="fal fa-hard-hat"></i></div>
                            <h2 class="card-title fs-6 mb-0 page-title">OnSite</h2>
                        </header>
                        <nav>
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="approvals">Approve Time Sheets</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="reports">Reports</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/employees">Employees</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/equipment">Equipment</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/mechanics-cost-codes">Mechanics Cost Codes</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/team">Starter Team</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/diary">Diary Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/weather">Weather Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/site-conditions">Site
                                        Conditions Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/work-day-options">Work Day
                                        Reasons (Employees)</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/equipment-day-options"> Work
                                        Day Reasons (Equipment)</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/tools">Project Tools
                                        Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="time-sheet/info">Project Info Fields</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- users -->
            <div class="col-12 col-md-6 col-xl-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <header class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
                                style="height: 40px; width: 40px;"><i class="fal fa-users"></i></div>
                            <h2 class="card-title fs-6 mb-0 page-title">Users</h2>
                        </header>
                        <nav>
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="users">Users</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="roles">Roles</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <!-- profile -->
            <div class="col-12 col-md-6 col-xl-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <header class="d-flex align-items-center mb-3">
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3"
                                style="height: 40px; width: 40px;"><i class="fal fa-user"></i></div>
                            <h2 class="card-title fs-6 mb-0 page-title">Profile</h2>
                        </header>
                        <nav>
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a class="nav-link custom-link"
                                        [attr.href]="ENVIROMENT_SETTING.id_endpoints.edit_profile">Edit
                                        Profile</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="default-page">Landing Page Settings</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="dbe-certifications">DBE
                                        Certifications</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link" routerLink="billing">Billing ✏️</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link"
                                        [attr.href]="ENVIROMENT_SETTING.id_endpoints.change_username">Change
                                        Username</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link custom-link"
                                        [attr.href]="ENVIROMENT_SETTING.id_endpoints.change_password">Change
                                        Password</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- footer -->
<footer class="px-3 px-lg-4 mb-4">
    <div class="container d-flex justify-content-end">
        <button type="button" class="btn btn-outline-dark me-2" routerLink="/signout">Sign Out</button>
        <!--  <a [attr.href]="ENVIROMENT_SETTING.id_endpoints.delete_account" role="button"
            class="btn btn-outline-danger">Delete Account</a> -->
    </div>
</footer>