import { Component, ElementRef, OnInit, ViewChild, effect, inject, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { environment } from 'src/environments/environment';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedLink } from 'src/app/bid-adv/shared/interfaces/link-sharing';
import { VanityIdComponent } from '../general-settings/vanity-id/vanity-id.component';
import { GeneralSettingsService } from '../shared/data-access/general-settings.service';
import { GeneralSettingsConstants } from '../shared/interfaces/general-settings-model';

@Component({
    selector: 'app-adv-link-sharing',
    templateUrl: './link-sharing.component.html',
    styleUrls: ['./link-sharing.component.css'],
    standalone: true,
    imports: [CommonModule,ReactiveFormsModule, FormsModule, VanityIdComponent]
})
export class AdvLinkSharingComponent implements OnInit {
  authService = inject(AuthService);
  generalSettingsService = inject(GeneralSettingsService);
  toastrService = inject(ToastrService);
  links: Array<SharedLink> = [];
  emailLink = signal<string>("");
  webPageLink = signal<string>("");
  generalSettingsForm = this.generalSettingsService.generalSettingsForm;
  vanityErrorMessage = this.generalSettingsService.errorMessage;
  vanityIdMaxLength = GeneralSettingsConstants.MaxVanityIdLength;
  isSaving = this.generalSettingsService.isSaving;
  isLoggedIn = this.authService.isLoggedIn;

  isLoading = signal<boolean>(true);
  constructor(){
    this.generalSettingsService.initialized.set(true);

    effect(() => {

      if(this.authService.subId() && this.generalSettingsService.initialized()){        
        if(this.generalSettingsService.generalSettings() && this.generalSettingsService.generalSettings()?.VanityId){
          this.emailLink.set(`${environment.BaseSiteUrl}/publishers/${this.generalSettingsService.generalSettings()?.VanityId}`);
        }else{
          this.emailLink.set(`${environment.BaseSiteUrl}/publishers/${this.authService.subId()}`);
        }
        
        this.webPageLink.set(`<a href='${this.emailLink()}'>Bid Opportunities</a>`);

        this.isLoading.set(false);

      }
    })
  }
  ngOnInit(): void {

  }

  copyInput(value:string){
    navigator.clipboard.writeText(value);
    this.toastrService.success("Copied!");
  }

  updateVanityId(){
    this.generalSettingsService.updateVanityIdOnly();
  }

}
