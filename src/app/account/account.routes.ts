import { Route } from '@angular/router';
import {check<PERSON><PERSON><PERSON><PERSON>equired } from '../shared/utils/guards/activations'
import { AccountComponent } from './account.component';
import { UserAccessGuard } from '../shared/utils/guards/user-access-guard';
import { BidOpsDocumentService } from '../bid-opportunities/shared/data-access/bid-ops-document.service';
import { DownloadHistoryService } from '../bid-opportunities/shared/data-access/download-history.service';

export const ACCOUNT_ROUTES: Route[] = [
	{
		path: '',
			
		component: AccountComponent
	},
	{
		path: 'approvals',
		canActivate: [UserAccessGuard],
		data: { permissionGroup: 'global', resource: 'route-access', action: 'access-user-approval' },
		loadComponent: () => import('../construction/approvals/approvals.component').then(c => c.ApprovalsComponent)
	},
	{
		path: 'mechanics-approvals',
		canActivate: [UserAccessGuard],
		data: { permissionGroup: 'global', resource: 'route-access', action: 'access-mechanics-approvals' },
		loadComponent: () => import('../construction/approvals-mechanics/approvals-mechanics.component').then(c => c.ApprovalsMechanicsComponent)
	},
	{
		path: 'reports',
		loadComponent: () => import('../construction/reports/reports.component').then(c => c.ReportsComponent)
	},
	{
		path: 'download-history',		
		providers: [BidOpsDocumentService, DownloadHistoryService],
		loadComponent: () => import('./download-history/download-history.component').then(m => m.DownloadHistoryComponent)
	}, 
	{
		path: 'billing',
		loadComponent: () => import('./billing/billing.component').then(m => m.BillingComponent)
	}, 
	{
		path: 'dbe-certifications',
		loadComponent: () => import('./dbe-certifications/dbe-certifications.component').then(m => m.DbeCertificationsComponent)
	  } ,
	{
		path: 'bid-management-settings',
		loadComponent: () => import('./bid-management-settings/bid-management-settings.component').then(m => m.BidManagementSettingsComponent)
	}, 
	{
		path: 'bid-alerts',
		loadComponent: () => import('./bid-alerts/bid-alerts.component').then(m => m.BidAlertsComponent)
	}, 
	{
		path: 'bid-history',
		loadComponent: () => import('./bid-history/bid-history.component').then(m => m.BidHistoryComponent)
	}, 
	{
		path: 'ebid-settings',
		loadComponent: () => import('./ebid-settings/ebid-settings.component').then(m => m.EbidSettingsComponent)
	}, 
	{
		path: 'addendum-notifications',
		loadComponent: () => import('./addendum-notifications/addendum-notifications.component').then(m => m.AddendumNotificationsComponent)
	}, 
	{
		path: 'link-sharing',
		loadComponent: () => import('./link-sharing/link-sharing.component').then(m => m.AdvLinkSharingComponent)
	},
	{
		path: 'general-settings',
		loadComponent: () => import('./general-settings/general-settings.component').then(m => m.GeneralSettingsComponent),
	},
	{
		path: 'roles',
		canActivate: [UserAccessGuard],
		data:{ resource: 'route-access', action: 'access-user-roles' },
		loadComponent: () => import('./roles/roles.component').then(m => m.RolesComponent)
	},
	{
		path: 'roles/settings/:roleId',
		canActivate: [UserAccessGuard],
		data: { resource: 'route-access', action: 'access-user-roles-byId' },
		loadComponent: () => import('./user/settings/access/roles/settings/role-settings/role-settings.component').then(m => m.RoleSettingsComponent)
	},
		{
		path: 'default-page',
		canActivate: [UserAccessGuard],
		data: { resource: 'route-access', action: 'access-user-settings-account' },
		loadComponent: () => import('./user/settings/account/account-settings.component').then(m => m.AccountSettingsComponent)
	},
	{
	
		path: 'time-sheet',
		loadComponent: () => import('./time-sheet/time-sheet.component').then(m => m.TimeSheetComponent),
		children: [
			{ path : '' , redirectTo : 'tools' , pathMatch : 'full' },
			{
				path: 'diary',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-dailylog' },
				loadComponent: () => import('./time-sheet/diary/diary.component').then(c => c.ProjectGlobalSettingDiaryComponent)
			},
			{
				path: 'mechanics-cost-codes',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-mechanics' },
				loadChildren: () => import('./mechanics-cost-codes/mechanics-cost-codes.routes').then(m => m.mechanicsCostCodesRoutes)
			},
			{
				path: 'tools',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-tools' },
				loadComponent: () => import('./time-sheet/tools/tools.component').then(c => c.ProjectGlobalSettingsToolsComponent)
			},
			{
				path: 'equipment-day-options',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-dailylog' },
				loadComponent: () => import('./time-sheet/equipment-day-options/equipment-day-options.component').then(c => c.ProjectGlobalSettingsEquipmentDayOptionsComponent)
			},
			{
				path: 'work-day-options',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-dailylog' },
				loadComponent: () => import('./time-sheet/work-day-options/work-day-options.component').then(c => c.ProjectGlobalSettingWorkDayOptionsComponent)
			},
			{
				path: 'info',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-info' },
				loadComponent: () => import('./time-sheet/info/info.component').then(c => c.ProjectGlobalSettingsInfoComponent)
			},
			{
				path: 'info/:manage',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-info' },
				loadComponent: () => import('./time-sheet/info/manage/manage.component').then(c => c.ManageInfoComponent)
			},
			{
				path: 'info/:manage/:id',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-info' },
				loadComponent: () => import('./time-sheet/info/manage/manage.component').then(c => c.ManageInfoComponent)
			},
			{
				path: 'team',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-team' },
				loadComponent: () => import('./time-sheet/team/team.component').then(c => c.ProjectGlobalSettingsTeamComponent)
			},
			{
				path: 'weather',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-dailylog' },
				loadComponent: () => import('./time-sheet/weather/weather.component').then(c => c.ProjectGlobalSettingsWeatherComponent)
			},
			{
				path: 'site-conditions',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-settings-dailylog' },
				loadComponent: () => import('./time-sheet/site-conditions/site-conditions.component').then(c => c.ProjectGlobalSettingSiteConditionsComponent)
			},
			{
				path: 'employees',
			 	canActivate: [UserAccessGuard],
			 	data: { resource: 'route-access', action: 'access-user-employees' },
				loadComponent: () => import('./time-sheet/employees/employees.component').then(m => m.EmployeesComponent)
			},
			{
				path: 'employees/import',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-employees' },
				loadComponent: () => import('./time-sheet/employees/employee-import-component/employee-import-component').then(m => m.EmployeeImportComponent)
			},
			{
				path: 'employees/add',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-employees', formType: 'add', pageTitle: "Add Employee" },
				loadComponent: () => import('./time-sheet/employees/employee-manage-component/employee-manage-component').then(m => m.EmployeeManageComponent)
			},
			{
				path: 'employees/edit/:employeeId',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-employees', formType: 'edit', pageTitle: "Edit Employee"  },
				loadComponent: () => import('./time-sheet/employees/employee-manage-component/employee-manage-component').then(m => m.EmployeeManageComponent)
			},
			{
				path: 'equipment',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-equipment' },
				loadComponent: () => import('./time-sheet/equipment/equipment.component').then(m => m.EquipmentComponent)
			},
			{
				path: 'equipment/import',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-equipment' },
				loadComponent: () => import('./time-sheet/equipment/equipment-import-component/equipment-import-component').then(m => m.EquipmentImportComponent)
			},
			{
				path: 'equipment/add',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-equipment', state: 'add', pageTitle: "Add Equipment"  },
				loadComponent: () => import('./time-sheet/equipment/equipment-manager-component/equipment-manager-component').then(m => m.EquipmentManagerComponent)
			},
			{
				path: 'equipment/edit/:equipmentId',
				canActivate: [UserAccessGuard],
				data: { resource: 'route-access', action: 'access-user-equipment', state: 'edit', pageTitle: "Edit Equipment" },
				loadComponent: () => import('./time-sheet/equipment/equipment-manager-component/equipment-manager-component').then(m => m.EquipmentManagerComponent)
			}
		]
	},
	{
		path: 'roles/access',
		children: [
			{
				path:'',    
				loadComponent: () => import('./user/settings/access/access.component').then(m => m.AccessComponent),
				children: [     
				  {
					path: 'manage/user-store/:delegateId',
					loadComponent: () => import('./user/settings/access/user-access/user-access.component').then(m => m.UserAccessComponent) 
				  },      
				  {
					path: 'roles',
					canActivate: [UserAccessGuard],
					data:{ resource: 'route-access', action: 'access-user-roles' },
					loadComponent: () => import('./roles/roles.component').then(m => m.RolesComponent) 
				  },
				  {
					path: 'add',
					canActivate: [UserAccessGuard],
					data:{ resource: 'route-access', action: 'access-user-roles', pageTitle: "Add Role", mode: "add" },
					loadComponent: () => import('./roles/editor/role-editor.component').then(m => m.RoleEditorComponent) 
				  },
				  {
					path: 'edit/:roleId',
					canActivate: [UserAccessGuard],
					data:{ resource: 'route-access', action: 'access-user-roles', pageTitle: "Edit Role", mode: "edit" },
					loadComponent: () => import('./roles/manager/role-manager.component').then(m => m.RoleManagerComponent) 
				  },
				  {
					path: 'manage/:roleId',
					canActivate: [UserAccessGuard],
					data:{ resource: 'route-access', action: 'access-user-roles-byId' },
					loadComponent: () => import('./roles/manager/role-manager.component').then(m => m.RoleManagerComponent) 
				  }
				]
			  }
		]
	},
	{
		path: 'users',
		loadComponent: () => import('./user/user.component').then(m => m.UserComponent),
		children: [		
			{path : '' , redirectTo : 'settings' , pathMatch : 'full'},
			{
				path: 'delegation',				
				children: [				
					{
						path: "switch-account",
						loadComponent: () => import('./user/delegation/account-access/account-access.component').then(m => m.AccountAccessComponent)
					},
					{
						path: 'change-delegation',
						loadComponent: () => import('./user/delegation/change-delegation/user.changedelegation').then(m => m.UserChangeDelegationComponent)
					},
					{
						path: ':delegateId',
						canActivate: [UserAccessGuard],
						data: { resource: 'route-access', action: 'access-user-delegates-byid' },
						loadChildren: () => import('./user/delegation/delegate-settings/delegate.settings.component').then(m => m.DelegateUserSettingsComponent)
					}
				]
			},
			{
				path: 'invitation',
				loadComponent: () => import('./user/invitation/invitation.component').then(m => m.InvitationComponent),
				children: [	
					{
						path: '',
						redirectTo: 'manager',
						pathMatch: 'full'
					},
					{
						path: 'manager',
						canActivate: [UserAccessGuard],
						data: { permissionGroup: 'global', action: "invitation-manager", resource: "route-access" },
						loadComponent: () => import('./user/invitation/invitation-manager/invitation-manager.component').then(m => m.InvitationManagerComponent)
					}
				]
			},
			{
				path: 'settings',
				loadComponent: () => import('./user/settings/settings.component').then(m => m.SettingsComponent),
				children: [
					{
						path: '',
						redirectTo: 'people',
						pathMatch: 'full'
					},				
					{
						path: 'people',
						canActivate: [UserAccessGuard],
						data: { resource: 'route-access', action: 'access-user-delegates' },
						loadComponent: () => import('./user/settings/delegate-settings/delegate.settings.component').then(m => m.DelegatesSettingsComponent)
					},
					{
						path: 'people/:delegateId',
						canActivate: [UserAccessGuard],
						data: { resource: 'route-access', action: 'access-user-delegates' },
						loadComponent: () => import('./user/delegation/delegate-settings/delegate.settings.component').then(m => m.DelegateUserSettingsComponent)
					},
					{
						path: 'access',
						children: [
							{
								path:'',    
								loadComponent: () => import('./user/settings/access/access.component').then(m => m.AccessComponent),
								children: [     
								  {
									path: 'manage/user-store/:delegateId',
									loadComponent: () => import('./user/settings/access/user-access/user-access.component').then(m => m.UserAccessComponent) 
								  }
								//   {
								// 	path: 'roles',
								// 	canActivate: [UserAccessGuard],
								// 	data:{ resource: 'route-access', action: 'access-user-roles' },
								// 	loadComponent: () => import('./user/settings/access/roles/roles.component').then(m => m.RolesComponent) 
								//   },
								//   {
								// 	path: 'roles/add',
								// 	canActivate: [UserAccessGuard],
								// 	data:{ resource: 'route-access', action: 'access-user-roles', pageTitle: "Add Role", mode: "add" },
								// 	loadComponent: () => import('./user/settings/access/roles/editor/role-editor.component').then(m => m.RoleEditorComponent) 
								//   }								  
						
								]
							  }
						]
					}
				]
			}
		]
	}	
];
