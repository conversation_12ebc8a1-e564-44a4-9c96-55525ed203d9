import { Component, OnInit, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { MechanicsCostCodesService } from '../../construction/shared/data-access/mechanics-cost-codes.service';
import { MechanicsCostCodesListComponent } from './ui/mechanics-cost-codes-list/mechanics-cost-codes-list.component';

import { environment } from 'src/environments/environment';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-mechanics-cost-codes',
  standalone: true,
  imports: [CommonModule, RouterLink, MechanicsCostCodesListComponent],
  templateUrl: './mechanics-cost-codes.component.html',
  styleUrls: ['./mechanics-cost-codes.component.css']
})
export class MechanicsCostCodesComponent implements OnInit {

  mechanicsService = inject(MechanicsCostCodesService);
  router = inject(Router);
  toaster = inject(ToastrService);

  isLoading = signal<boolean>(false);
  searchText = signal<string>('');
  access = signal<string>("Allow"); // User-based access, always allow for now
  templateUrl: string = environment.excel_template_locations.cost_code; // Match project cost codes

  // Computed signals following existing pattern
  totalHours = computed<number>(() => {
    return this.mechanicsService.filteredCostCodes()?.reduce((previousValue, currentValue) => {
      return previousValue += (currentValue.Hours) ? currentValue.Hours : 0;
    }, 0) || 0;
  });

  constructor() {
    // Initialize search functionality following existing pattern
    // Search will be handled in template similar to existing cost codes
  }

  ngOnInit(): void {
    // Load initial data
    this.mechanicsService.reload();
  }

  search(value: string) {
    this.searchText.set(value);
    // Filter will be handled by computed signal in service
    this.mechanicsService.setSearchFilter(value);
  }

  deleteCostCode(costCodeId: string) {
    // Update loading state for specific cost code
    this.mechanicsService.setLoadingState(costCodeId, true);

    this.mechanicsService.deleteCostCode(costCodeId).then(() => {
      this.toaster.success('Cost code deleted successfully');
    }).catch((err) => {
      console.log(err);
      this.toaster.error("Error deleting cost code");
      this.mechanicsService.setLoadingState(costCodeId, false);
    });
  }
}