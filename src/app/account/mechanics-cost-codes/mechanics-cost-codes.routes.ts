import { Routes } from '@angular/router';

export const mechanicsCostCodesRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./mechanics-cost-codes.component').then(c => c.MechanicsCostCodesComponent)
  },
  {
    path: 'add',
    data: { mode: 'add' },
    loadComponent: () => import('./mechanics-cost-code/mechanics-cost-code.component').then(c => c.MechanicsCostCodeComponent)
  },
  {
    path: 'edit/:costcodeId',
    data: { mode: 'edit' },
    loadComponent: () => import('./mechanics-cost-code/mechanics-cost-code.component').then(c => c.MechanicsCostCodeComponent)
  },
  {
    path: 'import',
    loadComponent: () => import('./mechanics-cost-code-import/mechanics-cost-code-import.component').then(c => c.MechanicsCostCodeImportComponent)
  }
];
