# Mechanics Cost Codes Feature

## 📋 Overview

The Mechanics Cost Codes feature provides user-based cost code management for mechanics, allowing them to create, edit, and manage their personal cost codes for time and equipment tracking. This implementation follows the existing project-based cost codes visual patterns while using modern Angular 19 architecture.

## 🏗️ Architecture & Component Structure

This feature follows the established project patterns found in `src/app/construction/project/cost-codes/` to ensure consistency across the application.

### 📁 Folder Structure

```
src/app/account/mechanics-cost-codes/
├── mechanics-cost-codes.component.ts          # Main list page (Smart Component)
├── mechanics-cost-codes.component.html
├── mechanics-cost-codes.component.css
├── mechanics-cost-codes.routes.ts             # Feature routing configuration
├── mechanics-cost-code/                       # Individual form (singular)
│   ├── mechanics-cost-code.component.ts       # Add/Edit form component
│   ├── mechanics-cost-code.component.html
│   └── mechanics-cost-code.component.css
└── ui/                                        # Reusable UI components
    └── mechanics-cost-codes-list/              # Table display (plural)
        ├── mechanics-cost-codes-list.component.ts
        ├── mechanics-cost-codes-list.component.html
        └── mechanics-cost-codes-list.component.css
```

## 🎯 Component Responsibilities

### 1. `MechanicsCostCodesComponent` (Smart Component)
**Location**: `./mechanics-cost-codes.component.ts`

**Purpose**: Main page controller and orchestrator

**Responsibilities**:
- Manages page-level state (loading, search, access control)
- Handles user interactions (search input, delete operations)
- Coordinates with `MechanicsCostCodesService` for data operations
- Provides data to child components via inputs
- Manages navigation and routing

**Key Features**:
- Search functionality with debounced input
- Total hours calculation
- Access control management
- Template URL for Excel downloads
- Integration with toast notifications

### 2. `MechanicsCostCodeComponent` (Form Component)
**Location**: `./mechanics-cost-code/mechanics-cost-code.component.ts`

**Purpose**: Individual cost code form for create/edit operations

**Responsibilities**:
- Handles add/edit form logic
- Form validation and submission
- Route parameter processing (add vs edit mode)
- Backend API integration for CRUD operations
- Loading state management during save operations

**Key Features**:
- Reactive forms with validation
- Dynamic page titles based on mode
- Phase/CostCode field synchronization for backend compatibility
- Error handling and user feedback

### 3. `MechanicsCostCodesListComponent` (Dumb/UI Component)
**Location**: `./ui/mechanics-cost-codes-list/mechanics-cost-codes-list.component.ts`

**Purpose**: Pure presentation component for displaying cost codes table

**Responsibilities**:
- Renders cost codes in table format
- Handles row-level actions (edit, delete)
- Displays loading states for individual items
- Provides content projection slots for filters and totals

**Key Features**:
- Input-driven data display
- Event emission for user actions
- Responsive table design
- Loading indicators per row
- Content projection for flexibility

### 4. `mechanicsCostCodesRoutes` (Routing Configuration)
**Location**: `./mechanics-cost-codes.routes.ts`

**Purpose**: Defines navigation structure for the feature

**Responsibilities**:
- Route definitions for list, add, and edit views
- Lazy loading configuration
- Route data passing (mode: 'add'/'edit')
- Integration with parent routing structure

## 🎨 Design Patterns

### Smart vs Dumb Components
- **Smart Components**: Handle business logic, state management, and service integration
- **Dumb Components**: Focus purely on presentation and user interaction

### Component Communication
- **Parent → Child**: Data flows down via `@Input()` signals
- **Child → Parent**: Events flow up via `@Output()` signals
- **Service Layer**: Centralized state management via `MechanicsCostCodesService`

### Modern Angular Patterns
- **Signals**: Reactive state management
- **Standalone Components**: No NgModules required
- **Dependency Injection**: Using `inject()` function
- **Declarative Control Flow**: `@if`, `@for`, `@empty`
- **rxResource**: Efficient data fetching and caching

## 🔄 Data Flow

```
User Interaction
       ↓
MechanicsCostCodesComponent (Smart)
       ↓
MechanicsCostCodesService
       ↓
Backend API (CM.Mechanics.Serverless)
       ↓
Database (MongoDB)
```

## 📝 Naming Conventions

### Why These Names?

1. **`mechanics-cost-codes`** (plural): Main feature container, handles multiple cost codes
2. **`mechanics-cost-code`** (singular): Individual cost code operations (add/edit one)
3. **`mechanics-cost-codes-list`** (plural): Displays list of multiple cost codes
4. **`mechanics-cost-codes.routes`**: Feature-level routing (matches parent folder name)

This follows the established pattern from `cost-codes/cost-code/cost-codes-list` in the project.

## 🎯 Consistency with Project Standards

### Matches Existing Patterns
- **Visual Design**: Identical to `src/app/construction/project/cost-codes/`
- **Folder Structure**: Mirrors project cost codes organization
- **Component Architecture**: Same smart/dumb component separation
- **Routing Strategy**: Consistent lazy loading and route data patterns
- **Service Integration**: Follows established service patterns

### Modern Angular 19 Compliance
- ✅ Signal-based state management
- ✅ Standalone components
- ✅ rxResource for data fetching
- ✅ Reactive forms
- ✅ Proper dependency injection
- ✅ Clean separation of concerns

## 🔗 Integration Points

### Backend Integration
- **Service**: `MechanicsCostCodesService`
- **API**: `CM.Mechanics.Serverless` Lambda
- **Endpoints**: `/cost-codes` with full CRUD operations

### Parent Application
- **Routes**: Integrated via `account.routes.ts`
- **Navigation**: Accessible from OnSite menu
- **Permissions**: Protected by `UserAccessGuard`

## 📚 Related Documentation

- [Business Requirements](../../../documentation/mechanics_feature/1-Business_Requirements.md)
- [Backend Technical Specification](../../../documentation/mechanics_feature/2-Backend_Technical_Specification.md)
- [Frontend PRD](../../../documentation/mechanics_feature/4-Frontend_PRD.md)
- [Master Todo List](../../../documentation/mechanics_feature/5-Master_Todo_List.md)
