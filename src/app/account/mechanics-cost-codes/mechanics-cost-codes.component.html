<!--page-title-->
<header class="mb-3 pt-3">
	<h3 class="page-title fs-6">Mechanics Cost Codes</h3>
	<p>Add, update, and delete your personal cost codes for tracking time and equipment usage.</p>
</header>
@if(isLoading()){
<div class="col-12 placeholder-glow">
	<ul class="list-group">
		<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
			<div>
				<div class="row">
					<div class="col-10">
						<span class="placeholder col-12"></span>
					</div>
				</div>
				<div class="row">
					<div class="col-3">
						<span class="placeholder col-12"></span>
					</div>
				</div>
			</div>
		</li>
	</ul>
</div>
}@else {
<app-mechanics-cost-codes-list [costCodes]="mechanicsService.filteredCostCodes()" [totalHours]="totalHours()"
	(deleteCostCode)="deleteCostCode($event)">
	<div class="input-group" filter>
		<span class="input-group-text" id="basic-addon1">
			<i class="fas fa-search" aria-hidden="true"></i>
		</span>
		<input type="search" class="form-control" placeholder="Search" (input)="search($event.target.value)"
			aria-describedby="basic-addon1" />
		@if (access() == 'Allow') {
		<button class="btn btn-outline-dark dropdown-toggle" type="button" data-bs-toggle="dropdown"
			aria-expanded="false">Add Cost Codes</button>
		}
		<ul class="dropdown-menu dropdown-menu-end">
			<li><a class="dropdown-item" routerLink="add">Add</a></li>
			<li><a class="dropdown-item" routerLink="import">Import</a></li>
			<li>
				<hr class="dropdown-divider">
			</li>
			<li><a class="dropdown-item" [href]="templateUrl" target="_self">Download Template</a></li>
		</ul>
	</div>
	<div totalHours>
		{{ totalHours() | number: '1.2-2' }}
	</div>
</app-mechanics-cost-codes-list>
}


