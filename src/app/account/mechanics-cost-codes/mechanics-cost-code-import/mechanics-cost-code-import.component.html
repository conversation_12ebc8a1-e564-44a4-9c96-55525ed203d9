<header class="mt-4">
  <h3 class="page-title fs-6">Import Mechanics Cost Codes</h3>
</header>
<div class="mb-3">
    <p>Choose a file to import your personal cost codes. Make sure it follows our Excel template format.</p>
    <div class="mb-3 d-flex align-items-start gap-3">
      <button class="btn btn-outline-secondary" type="button" (click)="goBack()" style="height: 45px;">
        <i class="fas fa-arrow-left me-2"></i>Back
      </button>
      <div class="flex-grow-1">
        <civcast-aws-uploader
          [maxFiles]="1"
          (uploadError)="errorInfo($event)"
          (selectedFiles)="setSelectedFiles($event)"
          [allowDragDrop]="false"
          [allowFolderFiles]="false"
          [accept-info]="''"
          *ngIf="!isLoading">
        </civcast-aws-uploader>
      </div>
    </div>
    <div class="progress" *ngIf="isLoading">
      <div class="progress-bar progress-bar-striped progress-bar-animated" 
           role="progressbar"
           [ngStyle]="{ 'width': '100%' }">
      </div>
    </div>
  </div>
