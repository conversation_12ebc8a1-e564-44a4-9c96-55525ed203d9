import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { CivCastAWSUploader } from 'src/app/shared/ui/uploader/civcast.aws.uploader.component';
import { newGuid } from 'src/app/models/utilities';
import { environment } from 'src/environments/environment';
import { MechanicsCostCodesService } from '../../../construction/shared/data-access/mechanics-cost-codes.service';
import { ImportCostCodesResponse } from '../../../construction/shared/interfaces/mechanics';

@Component({
  selector: 'app-mechanics-cost-code-import',
  templateUrl: './mechanics-cost-code-import.component.html',
  imports: [CommonModule, CivCastAWSUploader],
  styleUrls: ['./mechanics-cost-code-import.component.css']
})
export class MechanicsCostCodeImportComponent implements OnInit {
  isLoading = false;
  uploadTotal = 0;

  constructor(
    private authService: AuthService,
    private mechanicsCostCodesService: MechanicsCostCodesService,
    private toastrService: ToastrService,
    private aRoute: ActivatedRoute,
    private lambdaAWSService: LambdaAWSService,
    private router: Router
  ) {}

  ngOnInit() {}

  setSelectedFiles(files: FileList) {
    const file = files[0];
    this.uploadCostCodeFile(file);
  }

  async uploadCostCodeFile(file: File) {
    this.isLoading = true;
    const key = `cost-codes-imports/${newGuid()}`;

    this.lambdaAWSService.uploadFilePresignedUrl(key, file).subscribe({
      next: async (result) => {
        try {
          // Import cost codes from uploaded file using service-centric approach
          const importResponse = await this.mechanicsCostCodesService.importCostCodes(
            key, 
            environment.CostCodes.Bucket
          );

          // Handle detailed import response with comprehensive feedback
          this.handleImportResponse(importResponse);
          this.isLoading = false;
          this.router.navigate(['../'], { relativeTo: this.aRoute });
        } catch (error) {
          console.error('Import failed:', error);
          this.toastrService.error('Import failed. Please check your file format and try again.');
          this.isLoading = false;
        }
      },
      error: (err) => {
        console.error('Upload failed:', err);
        this.toastrService.error('Upload failed. Please try again.');
        this.isLoading = false;
      }
    });
  }

  errorInfo(error: any) {
    this.toastrService.error('Upload error: ' + error);
  }

  goBack() {
    this.router.navigate(['../'], { relativeTo: this.aRoute });
  }

  /**
   * Handle import response with detailed feedback
   * Follows service-centric approach - UI only handles feedback presentation
   */
  private handleImportResponse(response: ImportCostCodesResponse): void {
    if (response.SuccessCount > 0 && response.ErrorCount === 0) {
      // Complete success
      this.toastrService.success(
        `Successfully imported ${response.SuccessCount} cost code(s) from ${response.TotalRowsProcessed} row(s)`
      );
    } else if (response.SuccessCount > 0 && response.ErrorCount > 0) {
      // Partial success - show both success and errors
      this.toastrService.warning(
        `Partially successful: ${response.SuccessCount} imported, ${response.ErrorCount} failed from ${response.TotalRowsProcessed} row(s)`
      );
      
      // Show specific error details
      if (response.Errors && response.Errors.length > 0) {
        const errorSummary = response.Errors.slice(0, 3).join('\n'); // Show first 3 errors
        const moreErrors = response.Errors.length > 3 ? `\n... and ${response.Errors.length - 3} more errors` : '';
        this.toastrService.error(`Import errors:\n${errorSummary}${moreErrors}`, 'Import Issues', {
          timeOut: 10000 // Longer timeout for error details
        });
      }
    } else if (response.SuccessCount === 0 && response.ErrorCount > 0) {
      // Complete failure
      this.toastrService.error(
        `Import failed: ${response.ErrorCount} errors found in ${response.TotalRowsProcessed} row(s)`
      );
      
      // Show specific error details
      if (response.Errors && response.Errors.length > 0) {
        const errorSummary = response.Errors.slice(0, 3).join('\n');
        const moreErrors = response.Errors.length > 3 ? `\n... and ${response.Errors.length - 3} more errors` : '';
        this.toastrService.error(`Import errors:\n${errorSummary}${moreErrors}`, 'Import Failed', {
          timeOut: 15000 // Longer timeout for critical errors
        });
      }
    } else {
      // No rows processed
      this.toastrService.warning('No cost codes were found to import. Please check your file format.');
    }
  }
}
