<div class="mb-3">
	<ng-content select="[filter]"></ng-content>
</div>
<div class="table-responsive">
	<table class="table table-bordered">
		<thead>
			<tr>
				<th scope="col">Code</th>
				<th scope="col">Description</th>
				<th scope="col" class="text-end">Hours</th>
				<th scope="col" class="text-end">Units</th>
				<th scope="col">UM</th>
				<th scope="col" class="text-end"></th>
			</tr>
		</thead>
		<tbody>
			@for(costCode of costCodes(); track costCode.CostCodeId){
			<tr>
				<td>
					<span>{{ costCode.Phase }}</span>
				</td>
				<td>
					<span>{{ costCode.Description }}</span>
				</td>
				<td class="text-end">
					<span>{{ costCode.Hours | number: '1.2-2' }}</span>
				</td>
				<td class="text-end">
					<span>{{ costCode.Units | number: '1.3-3' }} </span>
				</td>
				<td>
					<span>{{ costCode.UnitOfMeasure }}</span>
				</td>
				<td class="text-end" *ngIf="access === 'Allow'">
					@if(costCode.IsLoading){
					<i class="fas fa-circle-notch fa-spin fa-2x mx-2"></i>
					}@else {
					<div class="dropdown">
						<button class="btn btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton1"
							data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fa fa-pencil" aria-hidden="true"></i>

						</button>
						<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">

							<li><a class="dropdown-item" [routerLink]="['edit', costCode.CostCodeId]">Edit</a></li>
							<li>
								<a class="dropdown-item" href="javascript:void(0)"
									(click)="deleteCostCodeEvent(costCode)">Delete</a>
							</li>
						</ul>
					</div>
					}
				</td>
			</tr>
			}
			@empty {
			<tr>
				<td colspan="6">
					<div class="alert alert-info" role="alert">
						No cost codes.
					</div>
				</td>
			</tr>
			}
			<tr>
				<td></td>
				<td></td>
				<td class="text-end">
					<span class="fw-bold">Total Hrs.</span>
					<span><ng-content select="[totalHours]"></ng-content></span>
				</td>
				<td></td>
				<td></td>
				<td></td>
			</tr>
		</tbody>
	</table>
</div>
