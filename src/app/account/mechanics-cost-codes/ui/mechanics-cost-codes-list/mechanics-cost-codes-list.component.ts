import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MechanicsCostCodeComponent as MechanicsCostCodeInterface } from '../../../../construction/shared/interfaces/mechanics';

@Component({
  selector: 'app-mechanics-cost-codes-list',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './mechanics-cost-codes-list.component.html',
  styleUrls: ['./mechanics-cost-codes-list.component.css']
})
export class MechanicsCostCodesListComponent {
  costCodes = input<MechanicsCostCodeInterface[]>([]);
  totalHours = input<number>(0);
  access = 'Allow'; // Match project cost codes pattern

  deleteCostCode = output<string>();

  deleteCostCodeEvent(costCode: MechanicsCostCodeInterface) {
    this.deleteCostCode.emit(costCode.CostCodeId);
  }
}
