import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { BsModalService, ModalModule } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { MechanicsCostCodesComponent } from './mechanics-cost-codes.component';
import { MechanicsCostCodesService } from '../../construction/shared/data-access/mechanics-cost-codes.service';

describe('MechanicsCostCodesComponent', () => {
  let component: MechanicsCostCodesComponent;
  let fixture: ComponentFixture<MechanicsCostCodesComponent>;
  let mockMechanicsService: jasmine.SpyObj<MechanicsCostCodesService>;
  let mockToastr: jasmine.SpyObj<ToastrService>;
  let mockModalService: jasmine.SpyObj<BsModalService>;

  beforeEach(async () => {
    mockMechanicsService = jasmine.createSpyObj('MechanicsCostCodesService', [
      'createCostCode',
      'updateCostCode',
      'deleteCostCode'
    ], {
      costCodes: jasmine.createSpy().and.returnValue([]),
      isLoading: jasmine.createSpy().and.returnValue(false),
      activeCostCodes: jasmine.createSpy().and.returnValue([])
    });

    mockToastr = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    mockModalService = jasmine.createSpyObj('BsModalService', ['show']);

    await TestBed.configureTestingModule({
      imports: [
        MechanicsCostCodesComponent,
        ReactiveFormsModule,
        ModalModule.forRoot()
      ],
      providers: [
        { provide: MechanicsCostCodesService, useValue: mockMechanicsService },
        { provide: ToastrService, useValue: mockToastr },
        { provide: BsModalService, useValue: mockModalService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(MechanicsCostCodesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.costCodeForm.get('costCode')?.value).toBe('');
    expect(component.costCodeForm.get('phase')?.value).toBe('1');
    expect(component.costCodeForm.get('description')?.value).toBe('');
    expect(component.costCodeForm.get('hours')?.value).toBe(1);
    expect(component.costCodeForm.get('units')?.value).toBe(1);
    expect(component.costCodeForm.get('unitOfMeasure')?.value).toBe('hour');
  });

  it('should validate required fields', () => {
    const form = component.costCodeForm;
    
    // Mark fields as touched to trigger validation
    form.get('costCode')?.markAsTouched();
    form.get('phase')?.markAsTouched();
    form.get('description')?.markAsTouched();
    form.get('hours')?.markAsTouched();
    form.get('units')?.markAsTouched();
    
    expect(form.get('costCode')?.hasError('required')).toBeTruthy();
    expect(form.get('phase')?.hasError('required')).toBeTruthy();
    expect(form.get('description')?.hasError('required')).toBeTruthy();
    expect(form.get('hours')?.hasError('required')).toBeTruthy();
    expect(form.get('units')?.hasError('required')).toBeTruthy();
  });

  it('should open create modal with default form values', () => {
    const template = {} as any;
    component.openCreateModal(template);
    
    expect(component.isEditMode()).toBeFalsy();
    expect(component.selectedCostCode()).toBeNull();
    expect(component.costCodeForm.get('phase')?.value).toBe('1');
    expect(component.costCodeForm.get('hours')?.value).toBe(1);
    expect(component.costCodeForm.get('units')?.value).toBe(1);
    expect(component.costCodeForm.get('unitOfMeasure')?.value).toBe('hour');
  });

  it('should show delete confirmation', () => {
    const costCodeId = 'test-id';
    component.confirmDelete(costCodeId);
    
    expect(component.showDeleteConfirm()).toBeTruthy();
    expect(component.deleteTargetId()).toBe(costCodeId);
  });

  it('should cancel delete confirmation', () => {
    component.confirmDelete('test-id');
    component.cancelDelete();
    
    expect(component.showDeleteConfirm()).toBeFalsy();
    expect(component.deleteTargetId()).toBe('');
  });
});