import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MechanicsCostCodesService } from '../../../construction/shared/data-access/mechanics-cost-codes.service';
import { MechanicsCostCodeComponent as MechanicsCostCodeInterface } from '../../../construction/shared/interfaces/mechanics';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-mechanics-cost-code',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './mechanics-cost-code.component.html',
  styleUrls: ['./mechanics-cost-code.component.css']
})
export class MechanicsCostCodeComponent implements OnInit {
  
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  mechanicsService = inject(MechanicsCostCodesService);
  toastr = inject(ToastrService);
  
  mode: string = 'none';
  isLoading = signal<boolean>(false);
  isSaving = signal<boolean>(false);
  costCodeId: string | null = null;
  pageTitle: string = "";

  costCodeFormGroup: FormGroup = new FormGroup({
    Phase: new FormControl('', [Validators.required]),
    CostCode: new FormControl('', [Validators.required]), // Backend requires both Phase and CostCode
    Description: new FormControl('', [Validators.required]),
    Hours: new FormControl(1, [Validators.required, Validators.min(0.1)]),
    Units: new FormControl(1, [Validators.required, Validators.min(0.1)]),
    UnitOfMeasure: new FormControl('hour', [Validators.required])
  });

  constructor() {
    var snap = this.aRoute.snapshot;
    const { mode } = snap.data;
    this.mode = mode;

    if(this.mode === "edit"){
      this.pageTitle = "Edit Cost Code";
    }else if(this.mode === "add"){
      this.pageTitle = "Add Cost Code";
    }else{
      this.pageTitle = "Cost Code";
    }
    this.costCodeId = snap.paramMap.get("costcodeId");
  }

  // Sync CostCode field with Phase field for backend compatibility
  syncCostCode(event: any): void {
    const phaseValue = event.target.value;
    this.costCodeFormGroup.patchValue({ CostCode: phaseValue });
  }

  ngOnInit(): void {
    if (this.costCodeId && this.mode === "edit") {
      this.isLoading.set(true);
      this.mechanicsService.getCostCode(this.costCodeId).then((costCode) => {
        this.costCodeFormGroup.patchValue({
          Phase: costCode.Phase,
          CostCode: costCode.CostCode,
          Description: costCode.Description,
          Hours: costCode.Hours,
          Units: costCode.Units,
          UnitOfMeasure: costCode.UnitOfMeasure
        });
        this.isLoading.set(false);
      }).catch((err) => {
        this.isLoading.set(false);
        this.toastr.error('Error loading cost code');
      });
    }
  }

  saveCostCode() {
    if (this.costCodeFormGroup.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isSaving.set(true);
    const formValue = this.costCodeFormGroup.value as MechanicsCostCodeInterface;

    if (this.mode === "edit" && this.costCodeId) {
      formValue.CostCodeId = this.costCodeId;
      
      this.mechanicsService.updateCostCode(formValue).then(() => {
        this.isSaving.set(false);
        this.toastr.success('Cost code updated successfully');
        this.router.navigate(['../..'], { relativeTo: this.aRoute });
      }).catch((err) => {
        this.isSaving.set(false);
        this.toastr.error('Error updating cost code');
      });
    } else if (this.mode === "add") {
      this.mechanicsService.createCostCode(formValue).then(() => {
        this.isSaving.set(false);
        this.toastr.success('Cost code created successfully');
        this.router.navigate(['..'], { relativeTo: this.aRoute });
      }).catch((err) => {
        this.isSaving.set(false);
        this.toastr.error('Error creating cost code');
      });
    } else {
      this.isSaving.set(false);
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.costCodeFormGroup.controls).forEach(key => {
      const control = this.costCodeFormGroup.get(key);
      control?.markAsTouched();
    });
  }

  goBack() {
    this.router.navigate(['../'], { relativeTo: this.aRoute });
  }
}
