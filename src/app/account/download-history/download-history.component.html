<div class="container p-4 mb-4">
    <!-- header -->
    <h1 class="fs-5 mb-3">Download History</h1>
    <!-- search -->
    <section class="col-12 col-md-6 col-xl-4 mb-3">
        <div class="input-group">
            <span class="input-group-text" id="basic-addon1">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" [(ngModel)]="search" (ngModelChange)="searchUpdate.next($event)"
                placeholder="Search by project or document name" />
        </div>
    </section>
    <!-- download history -->
    <section class="card p-3 mb-3">
        <table class="table">
            <thead>
                <tr class="d-none d-lg-table-row">
                    <th scope="col">
                        <a class="fw-bold text-dark text-nowrap me-1" (click)="sort('DateCreated')"
                            routerLink="">Download
                            Date</a>
                        <app-sort-icon [CurrentSortBy]="downloadHistoryRequest.SortBy" SortBy="DateCreated"
                            [CurrentSortOrder]="downloadHistoryRequest.SortOrder"></app-sort-icon>
                    </th>
                    <th scope="col">
                        <a class="fw-bold text-dark text-nowrap me-1" (click)="sort('ProjectTitle')"
                            routerLink="">Project</a>
                        <app-sort-icon [CurrentSortBy]="downloadHistoryRequest.SortBy" SortBy="ProjectTitle"
                            [CurrentSortOrder]="downloadHistoryRequest.SortOrder"></app-sort-icon>
                    </th>
                    <th scope="col">
                        <span class="fw-bold text-dark text-nowrap">Doc Name</span>
                    </th>
                    <th scope="col">
                        <a class="fw-bold text-dark text-nowrap me-1" (click)="sort('FileType')" routerLink="">Type</a>
                        <app-sort-icon [CurrentSortBy]="downloadHistoryRequest.SortBy" SortBy="FileType"
                            [CurrentSortOrder]="downloadHistoryRequest.SortOrder"></app-sort-icon>
                    </th>
                    <th scope="col">
                        <span class="fw-bold text-dark text-nowrap"></span>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if(isUserDownloadHistoryLoading()){
                <tr>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                    <td class="placeholder-glow">
                        <span class="placeholder w-100"></span>
                    </td>
                </tr>
                }@else {
                @for(doc of userDownloadHistory()?.DownloadHistory; track $index){
                <tr>
                    <td class="d-lg-none">
                        <div class="fw-bold mb-1">{{ doc.DateCreated | date: 'M/dd/yyyy'}}</div>
                        <div>
                            <a [routerLink]="['../projects', doc.ProjectId]" class="text-decoration-none">{{
                                doc.ProjectTitle }}</a>
                        </div>
                        <div class="mb-1">{{ doc.FileType }}</div>
                        <div class="mb-1">{{ doc.FileTitle }}</div>
                        <div>
                            <button class="btn btn-outline-dark" (click)="downloadFile(doc);"
                                [disabled]="doc.IsDownloading">
                                @if(doc.IsDownloading){
                                <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
                                }
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </td>
                    <td class="d-none d-lg-table-cell align-middle">
                        {{ doc.DateCreated | date: 'M/dd/yyyy'}}
                    </td>
                    <td class="d-none d-lg-table-cell align-middle">
                        <a [routerLink]="['../projects', doc.ProjectId]" class="text-decoration-none">{{
                            doc.ProjectTitle }}</a>
                    </td>
                    <td class="d-none d-lg-table-cell align-middle">
                        {{ doc.FileType }}
                    </td>
                    <td class="d-none d-lg-table-cell align-middle">
                        {{ doc.FileTitle }}
                    </td>
                    <td class="d-none d-lg-table-cell align-middle text-end">
                        <button class="btn btn-outline-dark" (click)="downloadFile(doc);"
                            [disabled]="doc.IsDownloading">
                            @if(doc.IsDownloading){
                            <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
                            }
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                </tr>
                }@empty {
                <tr>
                    <td colspan="5">
                        <div class="alert alert-info m-0" role="alert">
                            You haven't downloaded any documents yet.
                        </div>
                    </td>
                </tr>
                }
                }
            </tbody>
        </table>
    </section>
    <!-- footer -->
    <footer class="d-flex justify-content-end" ng-if="Downloads">
        @if(userDownloadHistory()?.Total > 0 && userDownloadHistory()?.Total >
        downloadHistoryRequest.Limit){
        <ngb-pagination [(page)]="downloadHistoryRequest.Page" [pageSize]="downloadHistoryRequest.Limit"
            [collectionSize]="userDownloadHistory().Total" (pageChange)="setPage($event)" />
        }
    </footer>
</div>