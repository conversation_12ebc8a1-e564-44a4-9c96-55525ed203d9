import { Component, ElementRef, OnInit, ViewChild, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { debounceTime, forkJoin } from 'rxjs';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { AccessService } from 'src/app/shared/data-access/access-service';
// import { UserAccessService } from 'src/app/user/shared/data-access/user.access.service';
import { CommonModule } from '@angular/common';
import { NgPipesModule } from 'ngx-pipes';
import { ListSkeletonComponent } from 'src/app/components/skeletons/list-skeleton/list-skeleton.component';
import { Role, UserRoleStore } from 'src/app/models/user-role-store';
import { UserAccessService } from '../user/shared/data-access/user.access.service';

@Component({
    selector: 'app-roles',
    templateUrl: './roles.component.html',
    styleUrls: ['./roles.component.css'],
    imports: [CommonModule, FormsModule, ReactiveFormsModule, NgPipesModule, RouterLink, ListSkeletonComponent]
})
export class RolesComponent implements OnInit {
  @ViewChild('roleNameInput') inputOne: ElementRef | null = null;
  userAccessService = inject(UserAccessService);
  accessService = inject(AccessService);
  aRoute = inject(ActivatedRoute);
  router = inject(Router);
  confirmService = inject(ConfirmService);
  toastrService = inject(ToastrService);
  userRoleStore: UserRoleStore = new UserRoleStore();
  roles: Array<Role> = [];  
  isLoading: boolean = false;
  cUserRoleStore: UserRoleStore | null = null;
  hasDeleteRoleAccess: string | null = null;
  hasAddRoleAccess: string | null = null;
  searchRole = new UntypedFormControl();
  constructor() {

    this.searchRole.valueChanges.pipe(debounceTime(400)).subscribe(value => {
      if (this.cUserRoleStore === null) {
        this.cUserRoleStore = Object.assign([], this.userRoleStore);
      }

      var sValue = value as string;
      this.roles = this.cUserRoleStore.Roles.filter(x => x.Name.toLowerCase().indexOf(sValue.toLowerCase()) > -1);
    });

  }

  ngOnInit() {
      this.getRoles();
      this.checkAccess();
  }

  getRoles(){
   
    this.isLoading = true;
    this.accessService.GetRoles().subscribe({
      next: (userRoleStore) => {
        this.userRoleStore = userRoleStore;
        this.isLoading = false;
        this.roles = this.userRoleStore.Roles;
        this.cUserRoleStore = Object.assign([], this.userRoleStore);
      },
      error: (err) => {
        console.log(err);
        this.isLoading = false;
      }
    });



  }

  checkAccess() {
    forkJoin({aclAddRole: this.accessService.CheckAccess("acl", "addrole"), removeRole: this.accessService.CheckAccess("acl", "removerole")}).subscribe(result => {
      this.hasAddRoleAccess = result.aclAddRole.Access;
      this.hasDeleteRoleAccess = result.removeRole.Access;
    });
  }

  async removeRole(role: Role) {
    this.confirmService.open("Are you sure you want to remove this role?").result.then(async value => {
 
        this.isLoading = true;
        this.userAccessService.RemoveRole(role.Id).subscribe({
          next: (result) => {
            let idx = this.roles.findIndex(x => x.Id === role.Id);
            if (idx >= 0) {
              this.roles.splice(idx, 1);
            }
            this.toastrService.success("Role Removed");     
            this.isLoading = false;       
          },
          error: (err) => {
            console.log(err);
            this.isLoading = false;
          }
        });  
    });
  
  }

  dragEnd(){
    
  }

  navigateToRoleSettings(role: Role) {
    this.router.navigate([`/account/roles/settings/${role.Id}`]);
  }  
}
