<ul class="list-group mb-3">
    <li class="list-group-item bg-light">
        <div class="input-group">
            <span class="input-group-text" id="basic-addon1">
                <i class="fa fa-filter" aria-hidden="true"></i>
            </span>
            <input type="search" class="form-control" placeholder="filter by name" [formControl]="searchPolicy" />
        </div>
    </li>
    <ng-container *ngFor="let group of policies | groupBy: 'GroupName' | values">
        <li class="list-group-item bg-light fw-bold">
            {{group[0].GroupName}}
        </li>
        <li class="list-group-item" *ngFor="let info of group | orderBy: 'Name'; let i = index">
            <cm-loading [show]="info.IsLoading"></cm-loading>
            <div class="row align-items-center">
                <div class="col-12 col-md-8 col-lg-9 col-xl-10 mb-2 mb-md-0">
                    <div class="text-truncate">{{ info.Name }}</div>
                    <div class="text-truncate text-secondary small">{{info.Description}}</div>
                </div>
                <div class="col-12 col-md-4 col-lg-3 col-xl-2 mb-2 mb-md-0">
                    <select name="effectSelection{{i}}" id="effectSelection{{i}}" class="form-select"
                        (ngModelChange)="attributeChanged($event)" [(ngModel)]="info.SelectedAttribute">
                        <option *ngFor="let attribute of info.Attributes" [ngValue]="attribute">{{ attribute.Name }}
                        </option>
                    </select>
                </div>
            </div>
        </li>
    </ng-container>
</ul>