import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { NgPipesModule } from 'ngx-pipes';

import { debounceTime } from 'rxjs';
import { UserAccessService } from 'src/app/account/user/shared/data-access/user.access.service';
import { AttributeInfo, PolicyInfo } from 'src/app/account/user/shared/interfaces/policy-info';

@Component({
    selector: 'access-permissions',
    templateUrl: './permissions.component.html',
    styleUrls: ['./permissions.component.css'],
    imports: [CommonModule, NgPipesModule, FormsModule, ReactiveFormsModule]
})
export class PermissionsComponent implements OnInit {
  private userAccessService = inject(UserAccessService);
  searchPolicy = new UntypedFormControl();
  @Input() policies: Array<PolicyInfo> = [];
  @Output("add-policy") addPolicyEvent = new EventEmitter<PolicyInfo>();
  @Output("remove-policy") removePolicyEvent = new EventEmitter<PolicyInfo>();  
  @Output("selected-policy") selectedPolicyEvent = new EventEmitter<AttributeInfo>();  
  cPolicyInfos: Array<PolicyInfo> | null = null;
  view: number = 0;
  effects = ["Allow","Deny","ReadOnly"];
  constructor() { }
  async setupData(){
 
  }

  async ngOnInit() {
    this.searchPolicy.valueChanges.pipe(debounceTime(400)).subscribe(value => {
      if(this.cPolicyInfos === null){
        this.cPolicyInfos = Object.assign([], this.policies);
      }

      var sValue = value as string;
      this.policies = this.cPolicyInfos.filter(x => x.Name != null && x.Name.toLowerCase().indexOf(sValue.toLowerCase()) > -1);
    });
  }

  addPolicy(policyInfo: PolicyInfo) {
    policyInfo.IsLoading = true;    
    this.addPolicyEvent.next(policyInfo);
  }

  removePolicy(policyInfo: PolicyInfo) {
    policyInfo.IsLoading = true;
    this.removePolicyEvent.next(policyInfo);
  }
  setAclEffects(effect: string){
      console.log(effect);
  }

  attributeChanged(att: AttributeInfo){
    this.selectedPolicyEvent.next(att);    
  }
}