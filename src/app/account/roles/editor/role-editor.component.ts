import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Role } from 'src/app/account/user/shared/interfaces/user-role-store';
import { UserAccessService } from '../../user/shared/data-access/user.access.service';

@Component({
    selector: 'app-role-editor',
    templateUrl: './role-editor.component.html',
    styleUrls: ['./role-editor.component.css'],
    imports: [CommonModule, FormsModule]
})
export class RoleEditorComponent implements OnInit {
  private toastrService = inject(ToastrService);
    userAccessService = inject(UserAccessService); 
    router = inject(Router);
    aRoute = inject(ActivatedRoute);
  role: Role = {} as Role;
  isLoading: boolean = false;
  mode: string | null= null;
  roleId: string | null = null;
  pageTitle: string | null = null;
  constructor() {
      const {mode, roleId, pageTitle} = this.aRoute.snapshot.data;
      this.mode = mode;
      this.roleId = roleId;
      this.pageTitle = pageTitle;

     }

  ngOnInit() {

    if(this.roleId){

    } 
  }

  async saveRole(){
    if(this.mode == "add"){
      this.createRole();
    }
  }

  createRole(){
    if(this.role.Name === ""){
      this.toastrService.error("Role Name is required");
      return;
    }
    this.isLoading = true;

    this.userAccessService.AddRole(this.role).subscribe({
      next: (nRole) => {
        this.router.navigate(["manage", nRole.Id], {relativeTo: this.aRoute.parent});
        this.isLoading = false;
      },
      error: (err) => {
        console.log(err);
        this.isLoading = false;
      }
    });  
  }
}
