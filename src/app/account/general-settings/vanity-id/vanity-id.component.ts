
import { Component, Input, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormControl } from '@angular/forms';

@Component({
  selector: 'app-vanity-id-input',
  templateUrl: './vanity-id.component.html',
  styleUrl: './vanity-id.component.css',
  standalone: true,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => VanityIdComponent),
      multi: true
    }
  ]
})
export class VanityIdComponent implements ControlValueAccessor {
  @Input() maxLength: number = 50; // Default max length
  @Input() formControl: FormControl | null = null;

  value: string = '';
  isDisabled: boolean = false;

  onChange = (value: string) => {};
  onTouched = () => {};

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  onInputChange(event: Event): void {
    const input = (event.target as HTMLInputElement).value;
    this.value = input;
    this.onChange(input);
  }
}