
	<textarea
	  type="text"
	  class="form-control"
	  [value]="value"
	  [attr.maxlength]="maxLength"
	  [disabled]="isDisabled"
	  (input)="onInputChange($event)"
	  (blur)="onTouched()"
	></textarea>
	@if(formControl?.invalid){
		<small class="text-danger">
			@if(formControl?.hasError('maxlength')){
				<div>
					Vanity ID cannot exceed the maximum length. ({{value.length | number}} / {{ maxLength }} characters)
				</div>
			}@else {
				Invalid
			}
		
		</small>
	}
