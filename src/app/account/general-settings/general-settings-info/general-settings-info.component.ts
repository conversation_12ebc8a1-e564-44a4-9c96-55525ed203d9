import { Component, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { GeneralSettingsService } from '../../shared/data-access/general-settings.service';
import { GeneralSettingsConstants } from '../../shared/interfaces/general-settings-model';
import { CommonModule } from '@angular/common';
import { VanityIdComponent } from '../vanity-id/vanity-id.component';

@Component({
  selector: 'app-general-settings-info',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './general-settings-info.component.html',
  styleUrl: './general-settings-info.component.css'
})
export class GeneralSettingsInfoComponent {
  generalSettingsService = inject(GeneralSettingsService);

  generalSettingsForm = this.generalSettingsService.generalSettingsForm;
  isLoading = this.generalSettingsService.isLoading;
  isSaving = this.generalSettingsService.isSaving;
  errorMessage = this.generalSettingsService.errorMessage;

  vanityIdMaxLength = GeneralSettingsConstants.MaxVanityIdLength;
  updateGeneralSettings() {
    this.generalSettingsService.updateGeneralSettings();
  }
}
