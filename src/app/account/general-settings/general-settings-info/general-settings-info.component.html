<form [formGroup]="generalSettingsForm" (ngSubmit)="updateGeneralSettings()">
	<section class="card mb-3">
			<div class="card-body">
				<div class="form-check">
					<input class="form-check-input" type="checkbox" id="hideonplanholders" name="hideonplanholders"
						formControlName="HideInfoOnPlanholders">
					<label class="form-check-label" for="hideonplanholders">
						Hide my contact info on plan holders lists.
					</label>
				</div>
			</div>
	</section>
	<!-- page footer -->
	<footer class="d-flex justify-content-end">
		<button type="submit" class="btn btn-primary" [disabled]="isSaving()">
			@if (isSaving()) {
			<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
			} Save
		</button>
	</footer>
</form>