import { Component, inject, OnInit } from '@angular/core';
import { GeneralSettingsService } from '../shared/data-access/general-settings.service';
import { GeneralSettingsInfoComponent } from "./general-settings-info/general-settings-info.component";

@Component({
  selector: 'app-general-settings',
  imports: [GeneralSettingsInfoComponent,GeneralSettingsInfoComponent],
  templateUrl: './general-settings.component.html',
  styleUrl: './general-settings.component.css'
})
export class GeneralSettingsComponent implements OnInit {
  ngOnInit(): void {
    this.generalSettingsService.initialized.set(true);
  }

  generalSettingsService = inject(GeneralSettingsService);
  isLoading = this.generalSettingsService.isLoading;
}
