import { HttpClient } from "@angular/common/http";
import { computed, inject, Injectable, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { environment } from "src/environments/environment";
import { ProjectClickStream } from "../interfaces/click-streams.model";

@Injectable({
  providedIn: 'root',
})
export class ClickStreamService {
	http = inject(HttpClient);

	foundProjectsFromClickStream = computed(() => this.projectClickStreamResource.value());
	projectIds = signal<Array<string> | null>(null);

	projectClickStreamResource = rxResource({
		request: () => this.projectIds(),
		loader: (request) => {
			if (request.request) {
				const projectIdsStr = request.request.join(',') 
				return this.http.get<Array<string>>(`${environment.services_root_endpoints.clickStream}/?clickStreamType=1&projectIds=${projectIdsStr}`);
			}

			return of(null);
		}
	});

	addProjectClick(projectId: string){
		const clickStream = new ProjectClickStream(projectId);
		this.http.post(`${environment.services_root_endpoints.clickStream}`, clickStream).subscribe((response) => {
			
			this.projectClickStreamResource.update((projects) => {
				if(projects){
					return [...projects, projectId];
				}

				return [projectId] as Array<string>;				
			});
		});

	}
}