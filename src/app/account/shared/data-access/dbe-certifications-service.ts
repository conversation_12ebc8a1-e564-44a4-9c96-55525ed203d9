import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { computed, Injectable, signal } from '@angular/core';
import { Observable, EMPTY, throwError, Subscription, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { DBECertification, DBECertificationAdd, DBECertificationStatic, DocumentInfo } from '../interfaces/dbe-certifications.model';
import { rxResource } from '@angular/core/rxjs-interop';
import { sign } from 'crypto';

@Injectable({
  providedIn: 'root',
})
export class DbeCertificationsService {
  userCertifications = computed(() => this.userCertsResource.value());
  staticCertifications = computed(() => this.staticCertificationsResource.value());
  staticCertificationSubscription: Subscription | null = null;
  dbeCertificationUpdateSubscription: Subscription | null = null;
  userIds = signal<string[]>([]);  
  initializeStaticCertifications = signal<boolean>(false);
  dbeByUserIds = computed(() => this.dbeByUserIdsResource.value());
  userId = signal<string | null>(null); // Initialize userId signal
  userDBECerts = computed(() => this.dbeByUserIdResource.value());
  executeUserDBECert = signal<boolean>(false);
  isLoading = computed(() => this.userCertsResource.isLoading() 
  || this.staticCertificationsResource.isLoading() 
  || this.userCertsResource.isLoading() 
  || this.dbeByUserIdsResource.isLoading() 
  || this.dbeByUserIdResource.isLoading());

  constructor(private client: HttpClient) {

   }

  destroy(){
    this.staticCertificationSubscription?.unsubscribe();
    this.dbeCertificationUpdateSubscription?.unsubscribe();
    
  }

  staticCertificationsResource = rxResource({
    request: () => true,
    loader: (request) => {
      if (request.request) {
        return this.client.get<Array<DBECertificationStatic>>('assets/data/dbe-certifications.json');
      }
      return of(null);
    }
  });

  userCertsResource = rxResource({
    request: () => this.executeUserDBECert(),
    loader: (request) => {
      if (request.request) {
        return this.client.get<Array<DBECertification>>(`${environment.services_root_endpoints.dbeCert}/user`);
      }

      return of(null);
    }
  })

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }



	dbeByUserIdsResource = rxResource({
		request: () => this.userIds(),
		loader: (request) => {
      if(request.request && request.request.length > 0) {
        const body = { userIds: request.request };
        return this.client.post<Array<DBECertification>>(`${environment.services_root_endpoints.dbeCert}/users`, body)
      }

			return of(null);
		}
	});

  dbeByUserIdResource = rxResource({
    request: () => this.userId(),
    loader: (request) => {      
      if(request.request && request.request.length > 0) {
        const body = { userIds: [request.request] };
        return this.client.post<Array<DBECertification>>(`${environment.services_root_endpoints.dbeCert}/users`, body)
      }

      return of(null);
    }
  });



  getDBECertsByUserIds(userIds: string[]): Observable<Array<DBECertification>> {
    const body = { userIds: userIds };
    return this.client.post<Array<DBECertification>>(
      `${environment.services_root_endpoints.dbeCert}/users`, 
      body
    ).pipe(
      catchError(this.handleError)
    );
  }

  addDBECert(certification: DBECertificationAdd) {
    const completeCertification: DBECertificationAdd = {
      ...certification,
    };
  
    this.client.post<DBECertification>(`${environment.services_root_endpoints.dbeCert}/user`, completeCertification).pipe(
      catchError(this.handleError)
    ).subscribe({
      next: (cert: DBECertification) => {
        this.userCertsResource.update((certs) => {
          if (!certs) {
            return undefined;
          }
          return [...certs, cert];
        });
      }
    });
  }

  updateDBECertDocument(certificationId: string, updatedDocument: DocumentInfo) {
    this.dbeCertificationUpdateSubscription?.unsubscribe();
    const updateRequest = { DBECertDocument: updatedDocument }; // Wrap in DBECertDocument key
  
    this.dbeCertificationUpdateSubscription = this.client
      .patch<DBECertification>(
        `${environment.services_root_endpoints.dbeCert}/user/${certificationId}/document`, 
        updateRequest
      ).pipe(
        tap(() => console.log('Certification documents updated successfully')),
        catchError(this.handleError)
      ).subscribe({
        next: (cert: DBECertification) => {
          this.userCertsResource.update((certs) => {
            if (!certs) {
              return null;
            }
  
            const idx = certs.findIndex((c) => c.DBECertId === cert.DBECertId);
            if (idx === -1) {
              certs = [...certs, cert]; 
            }
  
            certs.splice(idx, 1, cert);
            return certs;
          });
        }
      });
  }

  deleteDBECertDocument(certificationId: string, documentId: string): void {
    const deleteUrl = `${environment.services_root_endpoints.dbeCert}/user/${certificationId}/document/${documentId}`;

    this.client.delete<DBECertification>(deleteUrl).pipe(
      catchError(this.handleError)
    ).subscribe({
      next: (cert: DBECertification) => {
        this.userCertsResource.update((certs) => {
          if (!certs) {
            return null;
          }
  
          const idx = certs.findIndex((c) => c.DBECertId === cert.DBECertId);
          if (idx !== -1) {
            const updatedCert = { ...certs[idx] };
            updatedCert.DBECertDocuments = updatedCert.DBECertDocuments.filter(
              (doc) => doc.DocumentId !== documentId
            );
            certs.splice(idx, 1, updatedCert);
          }
          return certs;
        });
      },
      error: (err: any) => console.error('Error deleting document:', err)
    });
  }  

  deleteDBECert(certificationId: string): void {
    this.client.delete<void>(`${environment.services_root_endpoints.dbeCert}/user/${certificationId}`).pipe(
      catchError(this.handleError)
    ).subscribe({
      next: () => {
        this.userCertsResource.update((certs) => {
          if (!certs) {
            return null;
          }

          // Create a new array excluding the deleted certification
          return certs.filter(cert => cert.DBECertId !== certificationId);
        });
      }
    });
}
}