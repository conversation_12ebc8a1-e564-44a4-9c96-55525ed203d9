import { Injectable, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { BidNotificationSetting } from '../interfaces/bid-notification-settings.model';

@Injectable({
  providedIn: 'root',
})
export class BidNotificationSettingsService {
  private readonly apiUrl = environment.services_root_endpoints.bid_notification_settings;
  
  bidNotificationSettings = signal<BidNotificationSetting | null>(null);

  constructor(private client: HttpClient) { }

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  upsertSettings(settings: BidNotificationSetting): Observable<BidNotificationSetting> {
    return this.client.post<BidNotificationSetting>(this.apiUrl, settings).pipe(
      tap((updatedSettings) => {
        this.bidNotificationSettings.set(updatedSettings);
      }),
      catchError(this.handleError)
    );
  }

  getSettings(): Observable<BidNotificationSetting> {
    return this.client.get<BidNotificationSetting>(this.apiUrl).pipe(
      tap((settings) => this.bidNotificationSettings.set(settings)),
      catchError(this.handleError)
    );
  }
}
