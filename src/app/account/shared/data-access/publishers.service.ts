import { Injectable, computed, inject, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Publisher, PublisherProject } from 'src/app/account/shared/interfaces/publishers.model';
import { rxResource } from '@angular/core/rxjs-interop';

@Injectable({
  providedIn: 'root',
})
export class PublishersService {
  private readonly apiUrl = environment.services_root_endpoints.publishers;
  private http = inject(HttpClient);

  publishers = computed(() => this.publishersResource.value());
  publisherProjects = signal<PublisherProject[]>([]);
  publisher = computed(() => this.publisherByIdentifierResource.value());
  isLoading = computed(() => this.publishersResource.isLoading() || this.publisherByIdentifierResource.isLoading());
  
  publisherName = computed(() => this.publisher()?.PublisherName || 'NA');
  initializePublishers = signal<boolean | null>(null);
  publisherId = signal<string | null>(null);
  publisherProfileName = signal<string | null>(null);
  publisherVanityName = signal<string | null>(null);
  searchText = signal<string>('');   
  sortColumn = signal<string>('');
  sortDirection = signal<'asc' | 'desc'>('asc');

  filteredProjects = computed(() => {
    const searchText = this.searchText().toLowerCase();
    const sortColumn = this.sortColumn();
    const sortDirection = this.sortDirection();
    let projects = this.publisher()?.PublisherProjects || [];

    projects = projects.filter((project) =>
      project.ProjectTitle.toLowerCase().includes(searchText)
    );

    if (sortColumn) {
      projects = [...projects].sort((a, b) => {
        const valueA = (a as any)[sortColumn];
        const valueB = (b as any)[sortColumn];

        if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
        if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return projects;
  });

  publishersResource = rxResource({
		request: () => this.initializePublishers(),
		loader: (request) => {
			if (request.request) {
        return this.http.get<Publisher[]>(this.apiUrl + '/');
			}

			return of(null);
		}
	});

  publisherByIdentifierResource = rxResource({
    request: () => ({
      publisherId: this.publisherId(),
      publisherProfileName: this.publisherProfileName(),
      publisherVanityName: this.publisherVanityName()
    }),
    loader: (request) => {

        if(request.request.publisherProfileName){
          const url = `${this.apiUrl}/name/${encodeURIComponent(request.request.publisherProfileName)}`;
          return this.http.get<Publisher>(url);
        }else if(request.request.publisherId){
          const url = `${this.apiUrl}/id/${request.request.publisherId}`;
          return this.http.get<Publisher>(url);
        }else if(request.request.publisherVanityName){
          const url = `${this.apiUrl}/vanity/${request.request.publisherVanityName}`;
          return this.http.get<Publisher>(url);
        }     
      

      return of(null);
    }
  });

  private handleError(error: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    if (error instanceof HttpErrorResponse) {
      errorMessage = `Error ${error.status}: ${error.message}`;
    }

    console.error('Error details:', {
      message: errorMessage,
      error: error instanceof HttpErrorResponse ? error.error : null,
    });

    return throwError(() => new Error(errorMessage));
  }

  // getPublishers(): Observable<Publisher[]> {
  //   if (this.publishers().length > 0) {
  //     return of(this.publishers());
  //   }
    
  //   this.isLoading.set(true);
  //   return this.http.get<Publisher[]>(this.apiUrl + '/').pipe(
  //     tap((publishers) => {
  //       this.publishers.update(() => publishers);
  //       this.isLoading.set(false);
  //     }),
  //     catchError((error) => {
  //       this.isLoading.set(false);
  //       return this.handleError(error);
  //     })
  //   );
  // }

  // getPublisherProjectsByProjectId(projectId: string): Observable<Publisher> {
  //   const url = `${this.apiUrl}/projects/${projectId}`;
  //   this.isLoading.set(true);

  //   return this.http.get<Publisher>(url).pipe(
  //       tap((publisher) => {
  //           this.publisherProjects.set(publisher.PublisherProjects || []);
  //           this.isLoading.set(false);
  //       }),
  //       catchError((error) => {
  //           this.isLoading.set(false);
  //           return this.handleError(error);
  //       })
  //   );


  // getPublisherProjectsByName(publisherName: string): Observable<PublisherProject[]> {
  //   const url = `${this.apiUrl}/publishers/${encodeURIComponent(publisherName)}`;
  //   this.isLoading.set(true);

  //   return this.http.get<Publisher>(url).pipe(
  //     map((publisher) => publisher.PublisherProjects || []),
  //     tap((projects) => {
  //       this.publisherProjects.set(projects); 
  //       this.isLoading.set(false);
  //     }),
  //     catchError((error) => {
  //       this.isLoading.set(false);
  //       return this.handleError(error);
  //     })
  //   );
  // }
}