import { Injectable, inject, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Favorite } from 'src/app/account/shared/interfaces/favorites.model';

@Injectable({
  providedIn: 'root',
})
export class FavoritesService {
  private readonly apiUrl = environment.services_root_endpoints.favorites;
  private http = inject(HttpClient);

  favorites = signal<Favorite[]>([]);

  private handleError(error: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    if (error instanceof HttpErrorResponse) {
      errorMessage = `Error ${error.status}: ${error.message}`;
    }

	console.error('Error details:', {
		message: errorMessage,
		error: error instanceof HttpErrorResponse ? error.error : null,
	  });

    return throwError(() => new Error(errorMessage));
  }

  getFavorites(): Observable<Favorite[]> {
	return this.http.get<Favorite[]>(this.apiUrl).pipe(
	  catchError((error: HttpErrorResponse) => {
		if (error.status === 404) {
		  console.warn('No favorites found for the current user.');
		  return new Observable<Favorite[]>((observer) => {
			observer.next([]); 
			observer.complete();
		  });
		}
		return this.handleError(error); 
	  })
	);
  }  
  

  addFavorite(projectId: string): Observable<Favorite> {
	console.log('Adding favorite:', projectId);
  
	const payload = { ProjectId: projectId }; 
  
	return this.http.post<Favorite>(this.apiUrl, payload).pipe(
	  tap((newFavorite) => {
		this.favorites.update((favorites) => [...favorites, newFavorite]);
	  }),
	  catchError(this.handleError)
	);
  }
  
  removeFavorite(projectId: string): Observable<void> {
	if (!projectId) {
		throw new Error('Project ID is missing.');
	  }

    const url = `${this.apiUrl}/${projectId}`;
    return this.http.delete<void>(url).pipe(
      tap(() => {
        this.favorites.update((favorites) => favorites.filter(fav => fav.ProjectId !== projectId));
      }),
      catchError(this.handleError)
    );
  }
}