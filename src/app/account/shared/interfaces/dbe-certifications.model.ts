import { Datetime } from "aws-sdk/clients/costoptimizationhub";

// DBECertification model now includes UserId
export interface DBECertification {
	DBECertId: string;
	UserId: string; // Directly associates the certification with a user
	CreatedAt: string; // Optional, depending on usage on the frontend
	DBECertDocuments: DocumentInfo[]; // List of documents, each with a direct URL
}

// DocumentInfo model, representing each document with a direct URL
export interface DocumentInfo {
	DocumentId: string;      // Unique identifier for the document
    BucketName: string;      // Name of the S3 bucket
    ContentType: string;     // MIME type of the document
    FileName: string;        // Original filename, for display
    FileSize: number;        // Size in bytes
    S3Key: string;           // Key or path within the S3 bucket
}

export interface DBECertificationAdd {
	DBECertId: string;
	DBECertDocuments: DocumentInfo[]; // List of documents, each with a direct URL
}

export interface DBECertificationStatic {
	DBECertDocuments: any;
	DBECertId: string;
	Abbreviation: string;
	Name: string;
}

