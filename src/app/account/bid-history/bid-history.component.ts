import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OnlineEBidHistoryService } from '../shared/data-access/ebid-history.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-bid-history',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './bid-history.component.html',
  styleUrls: ['./bid-history.component.css'],
})
export class BidHistoryComponent implements OnInit {
  private eBidHistoryService = inject(OnlineEBidHistoryService);
  private router = inject(Router);

  bidHistory = this.eBidHistoryService.eBidHistory;
  isLoading = this.eBidHistoryService.isLoading;

  placeholders = Array.from({ length: 10 }, (_, index) => index);

  ngOnInit(): void {
    this.eBidHistoryService.loadEBidHistory();
  }

  navigateToProject(projectId: string): void {
    this.router.navigate(['/bid-opportunities/projects', projectId, 'summary']);
  }
}
