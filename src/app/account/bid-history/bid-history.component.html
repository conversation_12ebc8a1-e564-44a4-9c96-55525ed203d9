<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">Bid History</h1>
	<!-- bid history -->
	<section>
		<ul class="list-group">
			<!-- Loading Skeleton -->
			@if (isLoading()) {
			@for (placeholder of placeholders; track placeholder) {
			<li class="list-group-item">
				<div class="row align-items-center placeholder-glow">
					<div class="col-12 col-lg-5">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-3">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-2">
						<span class="placeholder col-12"></span>
					</div>
					<div class="col-12 col-lg-2">
						<span class="placeholder col-12"></span>
					</div>
				</div>
			</li>
			}
			}
			<!-- Actual Data -->
			@else {
			@if (bidHistory().length === 0) {
			<li class="list-group-item">
				<div class="alert alert-info m-0" role="alert">
					No bid history found.
				</div>
			</li>
			}
			@for (bid of bidHistory(); track bid.ProjectId) {
			<li class="list-group-item row-hover">
				<a href="javascript:void(0)" class="text-decoration-none text-dark"
					(click)="navigateToProject(bid.ProjectId)">
					<div class="row">
						<div class="col-12 col-lg-5">
							{{ bid.ProjectTitle }}
						</div>
						<div class="col-12 col-lg-3">{{ bid.BidDetailsBidDate }}</div>
						<div class="col-12 col-lg-2">
							@if (bid.OpenedAt) {
							<span class="badge text-bg-secondary">Open</span>
							} @else {
							<span class="badge text-bg-secondary">Closed</span>
							}
						</div>
						<div class="col-12 col-lg-2">
							@if (bid.SubmittedAt) {
							<span class="badge text-bg-success">Submitted</span>
							} @else {
							<span class="badge text-bg-danger">Not Submitted</span>
							}
						</div>
					</div>
				</a>
			</li>
			}
			}
		</ul>
	</section>
</div>