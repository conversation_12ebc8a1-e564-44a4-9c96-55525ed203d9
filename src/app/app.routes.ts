import { NgModule } from '@angular/core';
import { Routes, RouterModule, Route } from '@angular/router';
import { DelegateUnauthorizedComponent } from './account/user/delegation/delegate-unauthorized/delegate-unauthorized.component';
import { PageNotFoundComponent } from './components/page-not-found/page-not-found.component';
import { HubRoots } from './shared/interfaces/hub-root-constants';
import { HomeComponent } from './components/home/<USER>';
import { AuthGuard } from './shared/utils/guards/auth.guard';
import {checkLoginRequired } from './shared/utils/guards/activations';
import { PublisherNameResolver } from './shared/utils/resolves/publisher-name-resolve';
export const APP_ROUTES: Route[] = [
  {
    path: '',
    component: HomeComponent
  },
  {
    path: 'account',
    canActivate: [checkLoginRequired],  
    loadChildren: () => import('./account/account.routes').then(m => m.ACCOUNT_ROUTES)
  },
  {
    path: 'bid-forecast',
    loadComponent: () => import('./bid-forecast/bid-forecast.component').then(m => m.BidForecastComponent)
  },
  {
    path: 'help-center',
    loadComponent: () => import('./help-center/help-center.component').then(m => m.HelpCenterComponent)
  },
  // {
  //   path: 'user',
  //   loadChildren: () => import('./user/user.routes').then(m => m.USER_ROUTES)
  // },
  {
    path: 'auth',
    loadChildren: () => import('./authentication/authentication.routes').then(m => m.AUTHENTICATION_ROUTES)
  },
  {
    path: 'site-map',
    loadComponent: () => import('./components/site-map/site-map.component').then(m => m.SiteMapComponent)
  },
  {
		path: 'checklogin',
		loadComponent: () => import('./authentication/check-login/check-login.component').then(m => m.CheckLoginComponent)
	},
	{
		path: 'login',
		loadComponent: () => import('./authentication/login/login.component').then(m => m.LoginComponent)
	},
	{
		path: 'logout',
		loadComponent: () => import('./authentication/signout/signout.component').then(m => m.SignoutComponent)
	},
	{
		path: 'signin',
		loadComponent: () => import('./authentication/signin/signin.component').then(m => m.SigninComponent)
	},
	{
		path: 'signout',
		loadComponent: () => import('./authentication/signout/signout.component').then(m => m.SignoutComponent)
	},
	{ 
		path: 'unauthorized', 
		loadComponent: () => import('./authentication/unauthorized/auth.unauthorized.component').then(m => m.UnauthorizedComponent)
	},
  {
    path: 'invitation',
    loadComponent: () => import('./account/user/invitation/invitation-access/invitation-access.component').then(m => m.InvitationAccessComponent)
  },
  {
    path: 'invitation/setup/:invitationId',
    canActivate: [checkLoginRequired],
    loadComponent: () => import('./account/user/invitation/user-invitation/user-invitation.component').then(m => m.UserInvitationComponent)
  },
  {
    path: 'publishers/:vanityName',
    loadComponent: () => import('./publishers-vanity/publishers-vanity.component').then(m => m.PublishersVanityComponent),    
    resolve: {
      publishername: PublisherNameResolver, // Attach the resolver
    }
  },
  {
    path: 'login-required',
    loadComponent: () => import('./components/login-required/login-required.component').then(m => m.LoginRequiredComponent)
  },
  {
    path: HubRoots.advertisement.route,  
    canActivate: [checkLoginRequired],  
    loadChildren: () => import('./bid-adv/bid-adv.routes').then(m => m.BIDS_ADV_ROUTES)
  },  
  {
    path: HubRoots.bid_opportunities.route,
    loadChildren: () => import('./bid-opportunities/bid-opportunity.routes').then(m => m.BID_OPPORTUNITIES_ROUTES)
  },  
  {
    path: HubRoots.construction.route,
    canActivate: [checkLoginRequired],
    loadChildren: () => import('./construction/construction.routes').then(m => m.CONSTRUCTION_ROUTES)
  }, 
  { path: 'delegate-unauthorized', component: DelegateUnauthorizedComponent },
  { path: '**', component: PageNotFoundComponent }
];
