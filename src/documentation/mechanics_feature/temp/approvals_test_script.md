# Mechanics Approval Workflow - Manual Test Script

**Document ID**: Mechanics_Approvals_Manual_Test_Script  
**Date**: January 10, 2025  
**Version**: 1.0  
**Purpose**: Complete step-by-step manual testing guide for mechanics approval workflow

---

## **🎯 TEST OVERVIEW**

This script validates the complete mechanics approval workflow from timesheet creation through final payroll approval, ensuring 100% parity with project-based approval patterns.

### **Test Scope**
- ✅ End-to-end approval workflow (L1→L2→L3→L4→L5→Payroll→Final)
- ✅ Individual and bulk approval operations
- ✅ Permission-based access control
- ✅ Detail component functionality
- ✅ Error handling and edge cases
- ✅ UI/UX consistency with project-based approvals

---

## **👥 TEST USER REQUIREMENTS**

### **Required Test Users**
1. **Admin User** - Full system access, all approval levels
2. **L1 Approver** - Level 1 approval permissions only
3. **L3 Approver** - Level 1-3 approval permissions
4. **L5 Approver** - Level 1-5 approval permissions  
5. **Payroll User** - Payroll (Level 999) approval permissions
6. **Mechanic Employee** - Basic user with mechanics diary access
7. **Non-Mechanics User** - User without mechanics permissions

### **Permission Setup Verification**
- Ensure users have `access-mechanics-approvals` permission as needed
- Verify approval level assignments in user profiles
- Confirm `access-mechanics-diary` permission for diary creation

---

## **📋 PRE-TEST SETUP**

### **Step 1: Verify System State**
1. Navigate to `/construction/mechanics-diary`
2. Verify page loads without errors
3. Check that navigation shows "Mechanics Diary" link (permission-based)
4. Confirm today's date is displayed correctly

### **Step 2: Create Test Timesheet Data**
**Login as: Mechanic Employee**

1. Navigate to `/construction/mechanics-diary`
2. Select current date
3. Add timesheet entries:
   - Equipment: Select any available equipment
   - Cost Code: Select mechanics cost code
   - Hours: Enter 8.0 hours
4. Click "Save" to persist timesheet
5. **CRITICAL**: Submit the timesheet to create approval record
6. Note the employee name and date for later reference

### **Step 3: Verify Approval Creation**
**Login as: Admin User**

1. Navigate to `/account/mechanics-approvals` 
2. Verify new approval appears in list
3. Confirm approval shows:
   - Employee name matches timesheet creator
   - Date matches timesheet date
   - Total hours = 8.0
   - Status = "Pending"
   - Current Level = 1 (L1)

---

## **🔄 MAIN APPROVAL WORKFLOW TEST**

### **TEST CASE 1: L1 APPROVAL PROCESS**
**Login as: L1 Approver**

#### **1.1 Access Verification**
1. Navigate to `/account/mechanics-approvals`
2. **VERIFY**: Page loads successfully
3. **VERIFY**: Can see pending approvals requiring L1
4. **VERIFY**: Cannot see actions for higher-level approvals

#### **1.2 Individual Approval - Detail View**
1. Click on test timesheet row to expand detail
2. **VERIFY**: `MechanicsTimesheetDetailComponent` loads
3. **VERIFY**: Shows employee information correctly
4. **VERIFY**: Displays approval status with L1-L5 + Payroll indicators
5. **VERIFY**: L1 shows pending (clock icon), others show pending
6. **VERIFY**: "Approve" button is visible and enabled
7. **VERIFY**: "Unapprove" button is hidden (no prior approvals)
8. Click "Approve" button
9. **VERIFY**: Loading spinner appears during request
10. **VERIFY**: Success toast message: "Timesheet approved successfully"
11. **VERIFY**: L1 indicator changes to green checkmark with approver name
12. **VERIFY**: Current level advances to L2
13. **VERIFY**: "Approve" button becomes hidden
14. **VERIFY**: "Unapprove" button becomes visible

#### **1.3 Unapprove Functionality**
1. Click "Unapprove" button in detail view
2. **VERIFY**: Confirmation dialog appears: "Are you sure you want to unapprove this timesheet?"
3. Click "Confirm"
4. **VERIFY**: Loading spinner appears
5. **VERIFY**: Success toast: "Timesheet unapproved successfully"
6. **VERIFY**: L1 indicator returns to pending (clock icon)
7. **VERIFY**: Current level returns to L1
8. **VERIFY**: "Approve" button becomes visible again
9. **VERIFY**: "Unapprove" button becomes hidden

#### **1.4 Re-approve for Workflow Continuation**
1. Click "Approve" button again to continue workflow
2. **VERIFY**: L1 approval completes successfully

**EXPECTED RESULT**: ✅ L1 approval workflow functions correctly

---

### **TEST CASE 2: BULK APPROVAL OPERATIONS**
**Login as: L3 Approver**

#### **2.1 Bulk Selection Interface**
1. Navigate to `/account/mechanics-approvals`
2. **VERIFY**: Checkboxes appear in table rows
3. **VERIFY**: "Select All" checkbox in header
4. **VERIFY**: Bulk action buttons area visible

#### **2.2 Bulk Approve Operation**
1. Select checkbox for test timesheet (now at L2)
2. Select additional pending L2 approvals if available
3. **VERIFY**: Selected count updates correctly
4. Click "Bulk Approve" button
5. **VERIFY**: Confirmation dialog shows selected count
6. Click "Confirm"
7. **VERIFY**: Loading state shown during bulk operation
8. **VERIFY**: Success toast with approval count
9. **VERIFY**: Selected timesheets advance to L3
10. **VERIFY**: Selection clears after operation

#### **2.3 Select All Functionality**
1. Click "Select All" checkbox in header
2. **VERIFY**: All visible timesheets become selected
3. **VERIFY**: Select All checkbox shows checked state
4. Click "Select All" again to deselect
5. **VERIFY**: All selections clear
6. **VERIFY**: Select All checkbox shows unchecked state

**EXPECTED RESULT**: ✅ Bulk operations work correctly with proper state management

---

### **TEST CASE 3: APPROVAL LEVEL PROGRESSION**
**Continue with same timesheet through all levels**

#### **3.1 L3 → L4 Progression**
**Login as: L5 Approver**

1. Navigate to `/account/mechanics-approvals`
2. Find test timesheet (should be at L3)
3. Expand detail view
4. **VERIFY**: L1, L2, L3 show approved with approver names
5. **VERIFY**: L4, L5, Payroll show pending
6. Click "Approve" to advance to L4
7. **VERIFY**: L4 shows approved, advances to L5

#### **3.2 L4 → L5 Progression**
1. Click "Approve" again for L5
2. **VERIFY**: L5 shows approved
3. **VERIFY**: Current level advances to Payroll (999)
4. **VERIFY**: Payroll indicator shows pending

#### **3.3 Payroll → Final Approval**
**Login as: Payroll User**

1. Navigate to `/account/mechanics-approvals`
2. Find test timesheet (should be at Payroll level)
3. Expand detail view
4. **VERIFY**: All L1-L5 show approved
5. **VERIFY**: Payroll shows pending
6. **VERIFY**: "Approve" button available for payroll user
7. Click "Approve" for final payroll approval
8. **VERIFY**: Payroll shows approved
9. **VERIFY**: `FinalApprovalDate` is set (timesheet fully approved)
10. **VERIFY**: No further approval actions available
11. **VERIFY**: Status changes to "Approved"

**EXPECTED RESULT**: ✅ Complete L1→L2→L3→L4→L5→Payroll→Final progression works correctly

---

## **🔍 DETAILED FEATURE TESTING**

### **TEST CASE 4: FILTERING AND SEARCH**
**Login as: Admin User**

#### **4.1 Date Range Filtering**
1. Navigate to `/account/mechanics-approvals`
2. Set start date to yesterday
3. Set end date to tomorrow
4. **VERIFY**: Results include test timesheet
5. Set start date to next week
6. **VERIFY**: No results shown (future date range)
7. Reset date filters

#### **4.2 Employee Name Search**
1. Enter partial employee name in search box
2. **VERIFY**: Results filter to matching employees
3. Enter non-existent name
4. **VERIFY**: "No approvals found" message
5. Clear search

#### **4.3 Status Filtering**
1. Select "Pending" status filter
2. **VERIFY**: Only pending approvals shown
3. Select "Approved" status filter
4. **VERIFY**: Only approved approvals shown
5. Select "All" status filter
6. **VERIFY**: All approvals shown

#### **4.4 Approval Level Filtering**
1. Select "L1" from level dropdown
2. **VERIFY**: Only L1 pending approvals shown
3. Select "Payroll" from level dropdown
4. **VERIFY**: Only payroll-level approvals shown
5. Reset to "All Levels"

**EXPECTED RESULT**: ✅ All filtering options work correctly

---

### **TEST CASE 5: PERMISSION-BASED ACCESS CONTROL**
**Test access restrictions**

#### **5.1 Non-Mechanics User Access**
**Login as: Non-Mechanics User**

1. Navigate to `/account/mechanics-approvals`
2. **VERIFY**: Access denied or page not found
3. Check main navigation
4. **VERIFY**: "Mechanics Diary" link not visible in construction nav

#### **5.2 Level-Based Approval Restrictions**
**Login as: L1 Approver**

1. Navigate to `/account/mechanics-approvals`
2. Find timesheet at L3+ level
3. Expand detail view
4. **VERIFY**: "Approve" button not available for L3+ timesheets
5. **VERIFY**: Cannot approve above user's permission level

#### **5.3 Unapproval Restrictions**
**Login as: L1 Approver**

1. Find timesheet approved at L3+ level
2. Expand detail view
3. **VERIFY**: "Unapprove" button not available for higher-level approvals
4. **VERIFY**: Can only unapprove at or below user's level

**EXPECTED RESULT**: ✅ Permission system correctly restricts access

---

## **🚨 ERROR HANDLING TESTING**

### **TEST CASE 6: ERROR SCENARIOS**

#### **6.1 Network Error Handling**
1. Disconnect network/WiFi temporarily
2. Attempt to approve a timesheet
3. **VERIFY**: Error toast appears: "Error approving timesheet"
4. **VERIFY**: Loading state clears
5. **VERIFY**: UI remains functional after error
6. Reconnect network

#### **6.2 Invalid Approval Level**
**Login as: L1 Approver**

1. Try to approve timesheet already at L5 (if any)
2. **VERIFY**: Error message about insufficient permission level
3. **VERIFY**: UI handles error gracefully

#### **6.3 Already Approved Timesheet**
1. Try to approve timesheet that was just approved by another user
2. **VERIFY**: Error message about timesheet state
3. **VERIFY**: Page refreshes or updates state appropriately

**EXPECTED RESULT**: ✅ Error handling is robust and user-friendly

---

## **📱 UI/UX CONSISTENCY TESTING**

### **TEST CASE 7: VISUAL AND INTERACTION CONSISTENCY**

#### **7.1 Component Visual Consistency**
1. Compare mechanics approvals page with project-based approvals
2. **VERIFY**: Similar layout, styling, and visual hierarchy
3. **VERIFY**: Consistent button styles and positioning
4. **VERIFY**: Matching color scheme and typography
5. **VERIFY**: Similar loading states and animations

#### **7.2 Interaction Pattern Consistency**
1. **VERIFY**: Click-to-expand detail works same as project-based
2. **VERIFY**: Bulk selection behavior matches project-based
3. **VERIFY**: Filter interface operates identically
4. **VERIFY**: Error messages use same toast system
5. **VERIFY**: Confirmation dialogs use same modal system

#### **7.3 Responsive Design**
1. Test on different screen sizes (desktop, tablet, mobile)
2. **VERIFY**: Table scrolls horizontally on small screens
3. **VERIFY**: Bulk action buttons remain accessible
4. **VERIFY**: Detail component displays properly on mobile

**EXPECTED RESULT**: ✅ UI/UX is consistent with project-based approvals

---

## **📊 PERFORMANCE TESTING**

### **TEST CASE 8: LOAD AND PERFORMANCE**

#### **8.1 Large Dataset Handling**
1. Navigate to approval page with 50+ records
2. **VERIFY**: Page loads within 3 seconds
3. **VERIFY**: Scrolling is smooth
4. **VERIFY**: Filtering updates quickly

#### **8.2 Bulk Operation Performance**
1. Select 20+ timesheets for bulk approval
2. **VERIFY**: Selection updates smoothly
3. **VERIFY**: Bulk approval completes within 10 seconds
4. **VERIFY**: UI remains responsive during operation

**EXPECTED RESULT**: ✅ Performance is acceptable for production use

---

## **✅ TEST COMPLETION CHECKLIST**

### **Core Workflow Tests**
- [ ] L1 individual approval/unapproval works
- [ ] L2-L5 progression works correctly
- [ ] Payroll final approval works
- [ ] Bulk approve/unapprove operations work
- [ ] Detail component displays correctly
- [ ] Approval history tracking works

### **Access Control Tests**
- [ ] Permission-based page access works
- [ ] Level-based approval restrictions enforced
- [ ] Non-mechanics users properly blocked
- [ ] Navigation visibility based on permissions

### **UI/UX Tests**
- [ ] Visual consistency with project-based approvals
- [ ] Responsive design works on all devices
- [ ] Loading states and error handling consistent
- [ ] Filtering and search functionality complete

### **Integration Tests**
- [ ] Service methods return correct Observable patterns
- [ ] Backend API endpoints respond correctly
- [ ] State management updates properly
- [ ] Error scenarios handled gracefully

---

## **🎯 SUCCESS CRITERIA**

**PASS CRITERIA**: All test cases must pass with no critical or high-severity issues.

**ACCEPTABLE RESULTS**:
- ✅ 100% of core approval workflow functionality works
- ✅ 100% of permission-based access control works
- ✅ 95%+ UI/UX consistency with project-based approvals
- ✅ No critical errors or broken workflows
- ✅ Performance is acceptable for production use

**PRODUCTION READINESS**: System is ready for production deployment when all tests pass.

---

## **📝 TEST EXECUTION LOG**

**Test Date**: ___________  
**Tester**: ___________  
**Environment**: ___________  

### **Results Summary**
- **Passed Tests**: _____ / _____
- **Failed Tests**: _____ / _____
- **Critical Issues**: _____
- **Minor Issues**: _____

### **Issues Found**
| Test Case | Issue Description | Severity | Status |
|-----------|------------------|----------|---------|
| | | | |
| | | | |

### **Overall Assessment**
**Production Ready**: [ ] Yes [ ] No  
**Comments**: _________________________________

---

**Document Control**
- **Created**: January 10, 2025
- **Version**: 1.0
- **Classification**: Test Documentation
- **Related**: 7-Approval_Workflow_Validation_Plan.md, 8-Validation_Results_Summary.md