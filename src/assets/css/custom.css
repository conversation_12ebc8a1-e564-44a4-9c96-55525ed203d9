﻿/* body {
    font-size: .95rem;
    height: 100%;
}

html {
    height: 100%;
}

a {
    color: #4380c6;
    text-decoration: none;
}

.cursor-pointer {
    cursor: pointer;
}




.theme-color-two-background {
    background-color: #5cb85c;
}

.theme-color-three-background {
    background-color: #84acda;
}



.theme-color-two-btn {
    background-color: #5cb85c;
    border-color: #5cb85c;
    color: #FFF;
}

    .theme-color-two-btn:hover {
        background-color: #449D44;
        border-color: #449D44;
    }

.theme-color-three-btn {
    background-color: #84acda;
    border-color: #84acda;
    color: #FFF;
}

    .theme-color-three-btn:hover {
        background-color: #6a9ad2;
        border-color: #6a9ad2;
    }

.theme-color-warning-btn {
    background-color: #d9534f;
    border-color: #d9534f;
    color: #FFF;
}

    .theme-color-warning-btn:hover {
        background-color: #c9302c;
        border-color: #c9302c;
        color: #FFF;
    }


.header-border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.text-lighter {
    opacity: 0.7;
}

.dnd-sortable-drag {
    border: #428bca dashed 2px;
    padding: 5px;
}

.hugeModal > .modal-dialog {
    max-width: 90% !important;
    width: 90% !important;
}

 */