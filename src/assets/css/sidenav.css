﻿ /* The side navigation menu */
.sidenav {
    position: fixed; /* Stay in place */
    z-index: 1; /* Stay on top */
    left: 0;
    background-color: yellow; /* Black*/
    overflow-x: hidden; /* Disable horizontal scroll */
    padding-top: 15px; /* Place content 60px from the top */
    transition: 0.5s; /* 0.5 second transition effect to slide in the sidenav */
}


/* The navigation menu links */
.sidenav a {
    text-decoration: none;
    color: #88898c;
    display: block;
    transition: 0.3s
}


/* When you mouse over the navigation links, change their color */
.sidenav a:hover, .offcanvas a:focus{
    color: #FFF;
}



/* Style page content - use this if you want to push the page content to the right when you open the side navigation */
#main {
    transition: margin-left .5s;
}

/* On smaller screens, where height is less than 450px, change the style of the sidenav (less padding and a smaller font size) */
@media screen and (max-height: 450px) {
    .sidenav {padding-top: 15px;}
}

.sidenav-icon {
    margin-right: 10px;
}