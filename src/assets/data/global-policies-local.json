{"PolicyGroups": [{"GroupName": "Project Permissions", "Name": "Report - Cost Code Qty", "Description": "Allows the user to run the cost code report.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_cost_code_qty_report_allow", "Name": "Project Cost Code Quantity Report Allow", "Description": "Allows delegates to run the project cost code quantity report", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["report-costcodeqty"]}, {"Effect": "Allow", "Actions": ["access-project-reports"], "Resources": ["route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Cost Codes", "Description": "Allows the user to add, update, remove cost codes.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_component_costcodes_allow", "Name": "Project Cost Codes Allow", "Description": "Allows full access to manage cost codes", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-costcodes"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["fullaccess"], "Resources": ["project-components-costcodes"]}, {"Effect": "Allow", "Actions": ["replacecomponent"], "Resources": ["project-components"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "project_component_costcodes_readonly", "Name": "Project Cost Codes Readonly", "Description": "ALlows only the view of cost codes", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-costcodes"], "Resources": ["route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Files", "Description": "Allows users to upload, download and manage files.", "PolicyItems": [{"Name": "Add, Download, and Delete Files", "Policy": {"Id": "project_component_files_allow", "Name": "Project Files Allow", "Description": "Allows full access to manage files", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-files"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["*"], "Resources": ["project-components-files"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components"]}]}, "DataItems": null}}, {"Name": "Download Only", "Policy": {"Id": "project_component_files_download_only", "Name": "Project Files Download Only", "Description": "Allows the download of files", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-files"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getfolders", "getfiles", "downloadfile"], "Resources": ["project-components-files"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Project Info", "Description": "Allows the user to update project information.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_component_info_allow", "Name": "Project History Allow", "Description": "Allows full access to view project history", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-info"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["access-project-info-update", "replacecomponent"], "Resources": ["project-components", "projectcomponents-store"]}, {"Effect": "Allow", "Actions": ["*"], "Resources": ["project-components-info"]}, {"Effect": "Allow", "Actions": ["updateproject"], "Resources": ["projects"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "project_component_info_deny", "Name": "Project History Allow", "Description": "<PERSON><PERSON>'s access to view project history", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-info"], "Resources": ["route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Punch List", "Description": "Allows the user to add, update, remove punch list.", "PolicyItems": [{"Name": "Manage Punch List (Add, Edit, Delete)", "Policy": {"Id": "project_component_punchlist_allow", "Name": "Project Punch List Allow", "Description": "Allows full access to manage punch list", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-punch-list"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["*"], "Resources": ["project-components-punchlist"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components"]}]}, "DataItems": null}}, {"Name": "Work on Punch List (Check off Tasks)", "Policy": {"Id": "project_component_punchlist_tasks", "Name": "Project Punch List Allow", "Description": "Allows full access to manage punch list", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-punch-list"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getpunchlists", "getpunchlisttask", "markpunchlisttask"], "Resources": ["project-components-punchlist"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "project_component_punchlist_readonly", "Name": "Project Punch List Read Only", "Description": "Allows readonly access to manage punch list", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-punch-list"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getpunchlists", "getpunchlisttask", "getpunchlistcomponent"], "Resources": ["project-components-punchlist"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Project Settings", "Description": "Allows the user access to project settings.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_settings_allow", "Name": "Project Settings Allow", "Description": "Allows the management of project settings", "Version": "1", "Acl": {"Id": "26XlpDaO", "Statements": [{"Effect": "Allow", "Actions": ["replacecomponent", "updatecomponents"], "Resources": ["project-components"]}, {"Effect": "Allow", "Actions": ["access-project-settings"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["replacecomponent", "replacecomponents", "addcomponent", "removecomponent", "set<PERSON><PERSON><PERSON>"], "Resources": ["projectcomponents-store"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["user-project-components"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Team", "Description": "Allows the user to manage the project team.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_component_team_allow", "Name": "Project Edit Teams Allow", "Description": "Allows full access to manage project teams", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-team"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["addteammember", "removeteammember"], "Resources": ["project-components-team"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["replacecomponent"], "Resources": ["projectcomponents-store"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "project_component_team_readonly", "Name": "Project Cost Codes Readonly", "Description": "ALlows only the view of cost codes", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-team"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Diaries", "Description": "Allows the user to create and submit diarys.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_component_dailylog_submit_allow", "Name": "Project Submit Diary Allow", "Description": "Allows ", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-dailylog"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["*"], "Resources": ["projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components-projectusercomponents"]}, {"Effect": "Allow", "Actions": ["insertapproval", "getapproval", "removeapproval"], "Resources": ["approval"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Photos", "Description": "Allows the user to view photos.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_component_photos_allow", "Name": "Project Photos Allow", "Description": "Allows full access to view project photos", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-photos"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components-projectusercomponents"]}, {"Effect": "Allow", "Actions": ["getphotos"], "Resources": ["projectusercomponents-components-photos"]}, {"Effect": "Allow", "Actions": ["getdailylogsbyproject"], "Resources": ["projectusercomponents-dailyLog"]}]}, "DataItems": null}}]}, {"GroupName": "Project Permissions", "Name": "Report - Diary", "Description": "Allows the user to run the diary report.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "project_dailylog_report_allow", "Name": "Project Diary Report Allow", "Description": "Allows delegates to run the project diary report", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["reports", "report-dailylog", "report-dailylog-project"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["getapproval"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["getdailylogsbyproject", "getuserprojectdailylogs"], "Resources": ["projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["access-project-reports"], "Resources": ["route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Report - Payroll (Timesheets)", "Description": "Allows the user to run the global (i.e. all projects) payroll report. ", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "global_payroll_report_allow", "Name": "Payroll Report Allow", "Description": "Allows users to run the payroll report", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["report-payroll"]}, {"Effect": "Allow", "Actions": ["getapprovals"], "Resources": ["approval"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Report - Project List", "Description": "Allows the user to run the global (i.e. all projects) project list report.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "global_projectlist_report_allow", "Name": "Project List Report Allow", "Description": "Allows users to run the project list report", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["report-projectlist"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Report - Users, Roles, and Permissions", "Description": "Allows the user to run the global (i.e. all projects) users, roles, and permissions report.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "global_roles_report_allow", "Name": "Project List Report Allow", "Description": "Allows users to run the project list report", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["report-roles"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Access - Roles", "Description": "Allows the user to add, update, and remove roles.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "acl_allow", "Name": "ACL Allow", "Description": "Allows full access to manage user roles/permissions", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["acl"]}, {"Effect": "Allow", "Actions": ["access-user-delegate-access", "access-user-roles", "access-user-roles-byId"], "Resources": ["route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "acl_readonly", "Name": "ACL Readonly", "Description": "View only user roles/permissions", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["getstore"], "Resources": ["acl"]}, {"Effect": "Allow", "Actions": ["access-user-delegate-access", "access-user-roles", "access-user-roles-byId"], "Resources": ["route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "New Project", "Description": "Allows the user to add a new project.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "addprojectfull", "Name": "Add Project", "Description": "Allows user to add projects", "Version": "1", "Acl": {"Id": "cyH0nXmO", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["execute", "getcomponents", "updatecomponents", "replacecomponent"], "Resources": ["project-components"]}, {"Effect": "Allow", "Actions": ["getmanystores", "getcomponent", "getstore", "insertstore", "addcomponent", "replacecomponent"], "Resources": ["projectcomponents-store"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["user-project-components"]}, {"Effect": "Allow", "Actions": ["addteammember"], "Resources": ["project-components-team"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Access - Users", "Description": "Allows the user to add, update, and deactivate users.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "delegate_account_allow", "Name": "People full access", "Description": "Allows full access to manage account delegates", "Version": "1", "Acl": {"Id": "hwGpWQ0A", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["findaccounts"], "Resources": ["account"]}, {"Effect": "Allow", "Actions": ["getuserrolestore", "getuserstores", "getuserstore", "addroletouser", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Resources": ["acl"]}, {"Effect": "Allow", "Actions": ["access-user-delegates", "access-user-delegates-byid", "invitation-manager"], "Resources": ["route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "delegate_account_readonly", "Name": "Delegate Account <PERSON>", "Description": "Allow readonly access to accounts delegates", "Version": "1", "Acl": {"Id": "TUbo8yGE", "Statements": [{"Effect": "Allow", "Actions": ["access-delegates", "getstore", "getuserstores"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["getuserrolestore", "getuserstores"], "Resources": ["acl"]}, {"Effect": "Allow", "Actions": ["access-user-delegates", "access-user-delegates-byid"], "Resources": ["route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Timesheet - Delete Diary", "Description": "Allows the user to delete a diary from the approvals page.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "approvals_approval_delete_allow", "Name": "Project Info", "Description": "Allows access to delete approvals and diarys", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-approval", "getapprovals", "removeapproval", "access-delete-approval"], "Resources": ["approval", "route-access"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components-projectusercomponents"]}, {"Effect": "Allow", "Actions": ["getdailylogbyid", "getdailylog", "removedailylog"], "Resources": ["projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Timesheet - Update Qty/Notes", "Description": "Allows the user to update quantities and notes on the approvals page after the timesheet has been locked.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "approvals_edit_qty_notes_allow", "Name": "Project Info", "Description": "Allows full access to manage account delegates.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-approval", "getapprovals"], "Resources": ["approval", "route-access"]}, {"Effect": "Allow", "Actions": ["approval-edit-qty-notes"], "Resources": ["approval-edit-info"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["project-components-projectusercomponents"]}, {"Effect": "Allow", "Actions": ["getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Employees", "Description": "Allows the user to add, update, and deactivate employees.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "employee_allow", "Name": "Employee Allow", "Description": "Allows full access to manage employees", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["access-user-employees"], "Resources": ["route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "employee_readonly", "Name": "Employee Read Only", "Description": "Allow only views of employees", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-employees", "getstore"], "Resources": ["employees", "route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Equipment", "Description": "Allows the user to add, update, and deactivate equipment.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "equipment_allow", "Name": "Equipment", "Description": "Gives access to manage employees", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["access-user-equipment"], "Resources": ["route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "equipment_readonly", "Name": "Equipment Read Only", "Description": "Allow readonly equipment", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-equipment", "getstore"], "Resources": ["equipment", "route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Open/Close Projects", "Description": "Allows the user to open and close projects.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "global_openclose_allow", "Name": "Open/Close Allow", "Description": "Allow users full access to the open/close projects", "Version": "1", "Acl": {"Id": "LhlycGp1", "Statements": [{"Effect": "Allow", "Actions": ["access-project-settings", "access-user-closed-projects", "access-project-settings-close"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["set<PERSON><PERSON><PERSON>"], "Resources": ["projects"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Timesheet - Approval Level", "Description": "Sets the approval level for the user.", "PolicyItems": [{"Name": "Level 1", "Policy": {"Id": "approvals_lvl1", "Name": "Time Sheet Level 1", "Description": "Allow users to approve time sheet at level 1.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["project-components-projectusercomponents", "projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["access-user-approval"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 2", "Policy": {"Id": "approvals_lvl2", "Name": "Time Sheet Approval Level 2", "Description": "Allow users to approve time sheet at level 2.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["project-components-projectusercomponents", "projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["access-user-approval"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 3", "Policy": {"Id": "approvals_lvl3", "Name": "Time Sheet Approval Level 3", "Description": "Allow users to approve time sheet at level 3.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["project-components-projectusercomponents", "projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["access-user-approval"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 4", "Policy": {"Id": "approvals_lvl4", "Name": "Time Sheet Approval Level 4", "Description": "Allow users to approve time sheet at level 4.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["project-components-projectusercomponents", "projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["access-user-approval"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 5", "Policy": {"Id": "approvals_lvl5", "Name": "Time Sheet Approval Level 5", "Description": "Allow users to approve time sheet at level 5.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["project-components-projectusercomponents", "projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["access-user-approval"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Payroll", "Policy": {"Id": "approvals_payroll", "Name": "Time Sheet Payroll Approval", "Description": "Allow users to approve time sheet at payroll.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["project-components-projectusercomponents", "projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getprojectsbyid"], "Resources": ["projects"]}, {"Effect": "Allow", "Actions": ["access-user-approval"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Team", "Description": "Allows the user to manage the default project team for new projects.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "global_team_allow", "Name": "Team Allow", "Description": "Allow users to manage teams for new projects.", "Version": "1", "Acl": {"Id": "PhlycHp1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-team"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["allow-team-updates"], "Resources": ["user-settings-team-checks"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["getstore", "getcomponent", "replacecomponent"], "Resources": ["user-project-components"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["user-project-component-service"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "global_team_readonly", "Name": "Team Readonly", "Description": "Allow users to view teams for new projects.", "Version": "1", "Acl": {"Id": "PhlycHp1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-team"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["user-project-components"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Report - Diary", "Description": "Allows the user to run the global (i.e. all projects) diary report.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "global_dailylog_report_allow", "Name": "Project Diary Report Allow", "Description": "Allows delegates to run the project diary report", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["reports", "report-dailylog-global", "report-dailylog"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["delegate-account"]}, {"Effect": "Allow", "Actions": ["getdailylogsbyproject", "getuserprojectdailylogs"], "Resources": ["projectusercomponents-dailyLog"]}, {"Effect": "Allow", "Actions": ["getapproval"], "Resources": ["approval"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Mechanic Approvals", "Description": "Allows the user to access and manage mechanic timesheet approvals.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "mechanics_approvals_allow", "Name": "Mechanics Approvals - Allow", "Description": "Allows access to mechanics approvals functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-mechanics-approvals", "getapprovals", "insertapproval", "updateapproval", "deleteapproval", "getapproval<PERSON>id", "approvedailylog", "unapprovedailylog", "bulkapprove", "bulkunapprove", "execute"], "Resources": ["mechanics-approvals", "route-access"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["mechanics-service"]}, {"Effect": "Allow", "Actions": ["getdailylogbyid", "getdailylog"], "Resources": ["useractivitycomponents-mechanics"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON>", "Policy": {"Id": "mechanics_approvals_deny", "Name": "Mechanics Approvals - <PERSON><PERSON>", "Description": "Denies access to mechanics approvals functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "<PERSON><PERSON>", "Actions": ["access-mechanics-approvals", "getapprovals", "insertapproval", "updateapproval", "deleteapproval", "getapproval<PERSON>id", "approvedailylog", "unapprovedailylog", "bulkapprove", "bulkunapprove", "execute"], "Resources": ["mechanics-approvals", "route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "mechanics_approvals_readonly", "Name": "Mechanics Approvals - <PERSON><PERSON><PERSON>", "Description": "Allows read only access to mechanics approvals functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-mechanics-approvals", "getapprovals"], "Resources": ["mechanic-approval", "route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Mechanic Cost Codes", "Description": "Allows the user to add, update, and delete mechanic cost codes.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "mechanics_costcodes_allow", "Name": "Mechanics Cost Codes - Allow", "Description": "Allows full access to mechanics cost codes functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-mechanics", "getcostcodes", "insertcostcodes", "updatecostcodes", "deletecostcodes", "getcostcodebyid", "importcostcodes", "execute"], "Resources": ["usercomponents-mechanics", "route-access"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["mechanics-service"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON>", "Policy": {"Id": "mechanics_costcodes_deny", "Name": "Mechanics Cost Codes - <PERSON><PERSON>", "Description": "Denies access to mechanics cost codes functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "<PERSON><PERSON>", "Actions": ["access-user-mechanics", "getcostcodes", "insertcostcodes", "updatecostcodes", "deletecostcodes", "getcostcodebyid", "importcostcodes", "execute"], "Resources": ["usercomponents-mechanics", "route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "mechanics_costcodes_readonly", "Name": "Mechanics Cost Codes - <PERSON><PERSON><PERSON>", "Description": "Allows read only access to mechanics cost codes functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-user-mechanics", "getcostcodes", "getcostcodebyid"], "Resources": ["usercomponents-mechanics", "route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Mechanic Diary", "Description": "Allows the user to access and manage mechanic diary entries.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "mechanics_dailylog_allow", "Name": "Mechanics Daily Log - Allow", "Description": "Allows full access to mechanics daily log functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-mechanics-diary", "getdailylog", "insertdailylog", "savedailylog", "updatedailylog", "<PERSON><PERSON><PERSON><PERSON>", "removedailylog", "getdailylogbyid", "getdailylogbydate", "getdailylogs", "getdailylogsbymonth", "getdailylogsbydaterange", "getlatestdailylog", "copylastentry", "setnowork", "execute"], "Resources": ["useractivitycomponents-mechanics", "route-access"]}, {"Effect": "Allow", "Actions": ["execute"], "Resources": ["mechanics-service"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON>", "Policy": {"Id": "mechanics_dailylog_deny", "Name": "Mechanics Daily Log - Deny", "Description": "Denies access to mechanics daily log functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "<PERSON><PERSON>", "Actions": ["access-mechanics-diary", "getdailylog", "insertdailylog", "savedailylog", "updatedailylog", "<PERSON><PERSON><PERSON><PERSON>", "removedailylog", "getdailylogbyid", "getdailylogbydate", "getdailylogs", "getdailylogsbymonth", "getdailylogsbydaterange", "getlatestdailylog", "copylastentry", "setnowork", "execute"], "Resources": ["useractivitycomponents-mechanics", "route-access"]}]}, "DataItems": null}}, {"Name": "<PERSON><PERSON><PERSON>", "Policy": {"Id": "mechanics_dailylog_readonly", "Name": "Mechanics Daily Log - Readonly", "Description": "Allows read only access to mechanics daily log functionality", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["access-mechanics-diary", "getstore"], "Resources": ["user-activity-components", "route-access"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Mechanic Timesheet - Approval Level", "Description": "Allows the user to approve mechanic timesheets at various levels.", "PolicyItems": [{"Name": "Level 1", "Policy": {"Id": "mechanics_approvals_lvl1", "Name": "Mechanic Timesheet Level 1", "Description": "Allow users to approve mechanic timesheets at level 1.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["mechanic-approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["user-activity-components", "usercomponents-mechanics"]}, {"Effect": "Allow", "Actions": ["access-mechanics-approvals"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 2", "Policy": {"Id": "mechanics_approvals_lvl2", "Name": "Mechanic Timesheet Level 2", "Description": "Allow users to approve mechanic timesheets at level 2.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["mechanic-approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["user-activity-components", "usercomponents-mechanics"]}, {"Effect": "Allow", "Actions": ["access-mechanics-approvals"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment", "employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 3", "Policy": {"Id": "mechanics_approvals_lvl3", "Name": "Mechanic Timesheet Level 3", "Description": "Allow users to approve mechanic timesheets at level 3.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["mechanic-approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["user-activity-components", "usercomponents-mechanics"]}, {"Effect": "Allow", "Actions": ["access-mechanics-approvals"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment", "employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 4", "Policy": {"Id": "mechanics_approvals_lvl4", "Name": "Mechanic Timesheet Level 4", "Description": "Allow users to approve mechanic timesheets at level 4.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["mechanic-approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["user-activity-components", "usercomponents-mechanics"]}, {"Effect": "Allow", "Actions": ["access-mechanics-approvals"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment", "employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Level 5", "Policy": {"Id": "mechanics_approvals_lvl5", "Name": "Mechanic Timesheet Level 5", "Description": "Allow users to approve mechanic timesheets at level 5.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["mechanic-approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["user-activity-components", "usercomponents-mechanics"]}, {"Effect": "Allow", "Actions": ["access-mechanics-approvals"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment", "employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}, {"Name": "Payroll", "Policy": {"Id": "mechanics_approvals_payroll", "Name": "Mechanic Timesheet Payroll", "Description": "Allow users to approve mechanic timesheets for payroll processing.", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["mechanic-approval"]}, {"Effect": "Allow", "Actions": ["execute", "getdailylogbyid", "getdailylog", "savedailylog"], "Resources": ["user-activity-components", "usercomponents-mechanics"]}, {"Effect": "Allow", "Actions": ["access-mechanics-approvals"], "Resources": ["route-access"]}, {"Effect": "Allow", "Actions": ["getstore"], "Resources": ["equipment", "employees"]}, {"Effect": "Allow", "Actions": ["getuserrolestore"], "Resources": ["acl"]}]}, "DataItems": null}}]}, {"GroupName": "Admin/Global Permissions", "Name": "Project Settings", "Description": "Allows the user to control default settings for projects.", "PolicyItems": [{"Name": "Allow", "Policy": {"Id": "default_project_settings_allow", "Name": "Default Project Settings Allow", "Description": "Allows the management of project default settings", "Version": "1", "Acl": {"Id": "1", "Statements": [{"Effect": "Allow", "Actions": ["*"], "Resources": ["user-project-component-service", "user-project-components", "user-project-components-info"]}, {"Effect": "Allow", "Actions": ["access-user-project-settings", "access-user-settings-tools", "access-user-settings-info", "access-user-settings-dailylog"], "Resources": ["route-access"]}]}, "DataItems": null}}]}]}