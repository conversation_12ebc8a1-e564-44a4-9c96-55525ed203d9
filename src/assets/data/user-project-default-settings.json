{"_t": "UserProjectComponentStore", "Components": [{"_t": "InfoComponent", "ComponentIdentifier": "Info", "IsActive": true, "AllowDeletion": true, "Properties": [{"PropertyId": "955e8193-6d6d-4f9f-9033-eb0deeff139a", "Name": "Bid Date", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 4}, {"PropertyId": "b8e40aa4-6b33-4514-9166-92a1517249d8", "Name": "Customer", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "1c48b421-82b9-4336-a6ca-b1b34bb060b1", "Name": "Owner", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "6c3b72a4-423b-4218-8b87-b36875617947", "Name": "Engineer", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "83a1876e-e86d-426d-84a2-a4e368d0bc2c", "Name": "County", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "302866e7-36d4-4546-bd81-943a5a4ed4d3", "Name": "Location", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "07198f20-75fb-4051-9d14-b33a37cd12b8", "Name": "Control Number", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "1230ccd7-bab5-4d0a-a786-623854bf64f9", "Name": "Project No.", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "09424df9-0c8c-405a-8bbb-3c3dca00c01d", "Name": "Contract No.", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "3f074b3c-7ba0-4e14-8d56-1e20bfa37c4c", "Name": "Original Contract Amt.", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 5}, {"PropertyId": "ef7b90f2-a8ca-4dee-bded-bf9a6e59ca32", "Name": "Certified Payroll Req'd", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 2}, {"PropertyId": "6e71cff2-98b0-4417-8dd0-2f31c9560890", "Name": "Minority Goals", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "06e66996-fba3-4210-b3e8-801b849eac1a", "Name": "NTP Date", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 4}, {"PropertyId": "6f1fc298-dfde-4911-b9eb-ed3bcb411b68", "Name": "Act. Start Date", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 4}, {"PropertyId": "8018100d-87fe-49de-98f9-7c92fcf12510", "Name": "Allowable Time", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "c4a5161e-8883-4b3e-9f5e-e4406ae279f0", "Name": "Bond Number", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "9831f277-92f1-4b61-b579-a48bfb8613d0", "Name": "Project Manager", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "355c6f1a-bdb6-4b86-9f36-77bb45083043", "Name": "Superintendent", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "582f6d3e-f2ba-48ce-bc4a-dd8b1e0a0394", "Name": "Certified Payroll Contact Info", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "e0efcb01-e694-471a-b2e4-f247dba0cd6e", "Name": "Liquidated damages", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 5}, {"PropertyId": "8b78b83c-2aa6-44e2-ae8a-52b4fde4aa5d", "Name": "Current Contract Amount", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 5}, {"PropertyId": "5b422b9d-3c38-4c01-a8be-7bafb535b66d", "Name": "Bond Classification", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "9d7613fe-6b5e-4885-add8-dba38f990dd1", "Name": "Project Description", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}, {"PropertyId": "ccc13a95-ba9b-4bc3-81ba-f5e6a6ba1dfb", "Name": "Substantial Completion Date", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 4}, {"PropertyId": "cfa89c71-b409-4b6c-b51d-e96a16fd4f20", "Name": "Final Contract Amount", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 5}, {"PropertyId": "b149bea4-8247-43ab-9e9b-dfd21cc00d10", "Name": "Other Insurance Policies", "Value": null, "IsLocked": true, "IsHidden": false, "ValueType": 0}]}, {"_t": "TeamComponent", "ComponentIdentifier": "Team", "IsActive": true, "Name": "Team", "AllowDeletion": true, "TeamMembers": []}, {"_t": "DailyLogComponent", "ComponentIdentifier": "DailyLog", "IsActive": true, "Name": "Diary", "AllowDeletion": true, "Components": [{"_t": "NotesInfoComponent", "Name": "Notes", "ComponentIdentifier": "Notes", "IsActive": true, "AllowDeletion": true}, {"_t": "SiteConditionInfoComponent", "Name": "Site Conditions", "ComponentIdentifier": "SiteConditions", "IsActive": true, "AllowDeletion": true, "Conditions": ["Normal", "Wet-Workable", "Wet-Not Workable", "Very Dry", "Site Inaccessible", "Too Wet to Work"]}, {"_t": "PhotosInfoComponent", "Name": "Photos", "ComponentIdentifier": "Photos", "IsActive": true, "AllowDeletion": true}, {"_t": "TimeCardInfoComponent", "Name": "Time Sheet", "ComponentIdentifier": "TimeCard", "IsActive": true, "AllowDeletion": true, "WorkDayOptions": ["Work Day", "Work On Other Crew", "PTO/Vacation", "Rain Out", "Sick", "No Show/No Call", "Terminated/Quit"], "EquipmentDayOptions": ["Too Wet to Work", "Called Off Rent", "<PERSON>b'd In", "Mob'd Out"], "CostCodes": null}, {"_t": "WeatherInfoComponent", "Name": "Weather", "ComponentIdentifier": "Weather", "IsActive": true, "AllowDeletion": true, "Temperatures": ["Hot (90+)", "Warm (75-90)", "<PERSON> (60-75)", "Cold (32-45)", "Below Freezing"], "Skys": ["<PERSON>", "Partly Sunny/Cloudy", "Overcast", "Very Windy", "Rain", "Heavy Rain", "Rained Out", "<PERSON>l", "Freezing Rain/Sleet"]}]}, {"_t": "SettingsComponent", "CreateDate": null, "LastUpdated": null, "ComponentIdentifier": "Settings", "AllowDeletion": false, "Name": "Settings", "IsActive": true}, {"_t": "ReportComponent", "CreateDate": null, "LastUpdated": null, "ComponentIdentifier": "Reports", "AllowDeletion": true, "Name": "Reports", "IsActive": true}, {"_t": "PhotosComponent", "CreateDate": null, "LastUpdated": null, "ComponentIdentifier": "Photos", "AllowDeletion": true, "Name": "Photos", "IsActive": true, "Photos": null}, {"_t": "CostCodeComponent", "CreateDate": null, "LastUpdated": null, "ComponentIdentifier": "CostCodes", "AllowDeletion": true, "Name": "Cost Codes", "IsActive": true}]}