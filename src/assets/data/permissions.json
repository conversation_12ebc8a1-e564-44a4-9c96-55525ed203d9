{"permissions": {"global": [{"_t": [], "permissionId": "global::approvals", "name": "Approvals", "description": "Allows access to review and approve time cards", "group": "Global", "subgroup": "Approvals", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::approvals", "name": "Approvals", "description": "Denies access to approve time cards", "group": "Global", "subgroup": "Approvals", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::approvals::dailylog::edit", "name": "Approvals - Diary - Edit", "description": "Allows users to edit diary", "group": "Global", "subgroup": "ApprovalsEditDailyLog", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::approvals::dailylog::edit::timecard::qty", "name": "Approvals - Diary - Edit - Time Card - Qty", "description": "Allows users to edit diary time card qty", "group": "Global", "subgroup": "ApprovalsEditDailyLogTimeCardQty", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::approvals::dailylog::edit::notes", "name": "Approvals - Diary - Edit - Notes", "description": "Allows users to edit diary notes", "group": "Global", "subgroup": "ApprovalsEditDailyLogNotes", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::pstools::hsa", "name": "Settings - <PERSON><PERSON> Hide/Show/Arrange", "description": "Denies users to add/remove tools from the sidebar.", "group": "Global", "subgroup": "PSToolsHSA", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::pstools::hsa", "name": "Settings - <PERSON><PERSON> Hide/Show/Arrange", "description": "Allows users to add/remove tools from the sidebar. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "PSToolsHSA", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::psinfo::hsa", "name": "Settings - Info Hide/Show/Arrange", "description": "Denies users to customize the Project Info page by hiding/showing fields, and add/remove new fields.", "group": "Global", "subgroup": "PSInfoHSA", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::psinfo::hsa", "name": "Settings - Info Hide/Show/Arrange", "description": "Allows users to customize the Project Info page by hiding/showing fields, and add/remove new fields. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "PSInfoHSA", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::hsa", "name": "Settings - Diary - Hide/Show/Arrange", "description": "Denies users to add/remove sections from the diary", "group": "Global", "subgroup": "PSDailyLogHSA", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::hsa", "name": "Settings - Diary - Hide/Show/Arrange", "description": "Allows users to add/remove sections from the diary. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "PSDailyLogHSA", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::weather", "name": "Settings - Diary - Weather", "description": "Denies users to customize the weather component on the diary.", "group": "Global", "subgroup": "PSDailyLogWeather", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::weather", "name": "Settings - Diary - Weather", "description": "Allows users to customize the weather component on the diary. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "PSDailyLogWeather", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::sc", "name": "Settings - Diary - Site Conditions", "description": "Denies ausers to customize the site conditions component on the diary.", "group": "Global", "subgroup": "PSDailyLogSC", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::sc", "name": "Settings - Diary - Site Conditions", "description": "Allows users to customize the site conditions component on the diary. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "PSDailyLogSC", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::psdailylog::equipmentdayoptions", "name": "Settings - Diary- Time Card - Equipment Day Options", "description": "Allows users to customize the equipment day options for the time card equipment tracking. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "PSDailyLogEQDO", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::employees", "name": "Employees", "description": "Denies users from adding, editing, and deactivating employees.", "group": "Global", "subgroup": "Employees", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::employees", "name": "Employees", "description": "Allows users to add, edit, and deactivate employees.", "group": "Global", "subgroup": "Employees", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::employees", "name": "Employees", "description": "Allows user to view the employees but not make any changes.", "group": "Global", "subgroup": "Employees", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::equipment", "name": "Equipment", "description": "Denies access to setting up Equipment", "group": "Global", "subgroup": "Equipment", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::equipment", "name": "Equipment", "description": "Allows users to add, edit, and deactivate employees.", "group": "Global", "subgroup": "Equipment", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::equipment", "name": "Equipment", "description": "Allows read only access to setting up Equipment", "group": "Global", "subgroup": "Equipment", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::activity", "name": "Account History", "description": "Denies access to the Account History page.", "group": "Global", "subgroup": "Activity", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::activity", "name": "Account History", "description": "Allows user to access the Account History page.", "group": "Global", "subgroup": "Activity", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::activity", "name": "Account History", "description": "Allows user to have read only access to the Account History page.", "group": "Global", "subgroup": "Activity", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::team", "name": "Team", "description": "Denies users to add/remove people from the team.", "group": "Global", "subgroup": "Team", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::team", "name": "Team", "description": "Allows users to add/remove people from the team. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Global", "subgroup": "Team", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::team", "name": "Team", "description": "Allow users read only access to the Team page.", "group": "Global", "subgroup": "Team", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::managepolicies", "name": "Security Policies", "description": "Denies users to add/remove security policies and add/remove permissions on security policies.", "group": "Global", "subgroup": "ManagePolicies", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::managepolicies", "name": "Security Policies", "description": "Allows users to add/remove security policies and add/remove permissions on security policies", "group": "Global", "subgroup": "ManagePolicies", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::manageuserpermission", "name": "Manage User Permissions", "description": "Denies access to manage a users permissions", "group": "Global", "subgroup": "ManagePermission", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::manageuserpermission", "name": "Manage User Permissions", "description": "Allows access to manage a users permissions", "group": "Global", "subgroup": "ManagePermission", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::managedelegates", "name": "People", "description": "Denies users to add/remove people to the account and edit their polices and permissions.", "group": "Global", "subgroup": "ManageDelegates", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::managedelegates", "name": "People", "description": "Allows users to add/remove people to the account, and edit their polices and permissions.", "group": "Global", "subgroup": "ManageDelegates", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::managedelegates", "name": "People", "description": "Allows users read only access to People page.", "group": "Global", "subgroup": "ManageDelegates", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::timecard", "name": "Report - Time Card", "description": "Denies users from running global time card report.", "group": "Global", "subgroup": "TimeCardReport", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::timecard", "name": "Report - Time Card", "description": "Allows users to run global time card report.", "group": "Global", "subgroup": "TimeCardReport", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::report::dailylog", "name": "Report - Diary", "description": "Allows users to run diary report.", "group": "Global", "subgroup": "DailyLogReport", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::report::projectlist", "name": "Report - Project", "description": "Allows users to run Project report.", "group": "Global", "subgroup": "ProjectListReport", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::addproject", "name": "Add Project", "description": "Denies user from adding a new project", "group": "Global", "subgroup": "AddProject", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::addproject", "name": "Add Project", "description": "Allows users to to add a new project", "group": "Global", "subgroup": "AddProject", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::approvals::approval1", "name": "Time Card - Approver Level 1", "description": "User is allowed to approve diaries at level 1 and lower", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl1", "Value": 1}]}, {"_t": [], "permissionId": "global::approvals::approval2", "name": "Time Card - Approver Level 2", "description": "User is allowed to approve diaries at level 2 and lower", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl2", "Value": 2}]}, {"_t": [], "permissionId": "global::approvals::approval3", "name": "Time Card - Approver Level 3", "description": "User is allowed to approve diaries at level 3 and lower", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl3", "Value": 3}]}, {"_t": [], "permissionId": "global::approvals::approval4", "name": "Time Card - Approver Level 4", "description": "User is allowed to approve diaries at level 4 and lower", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl4", "Value": 4}]}, {"_t": [], "permissionId": "global::approvals::approval5", "name": "Time Card - Approver Level 5", "description": "User is allowed to approve diaries at level 5 and lower", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl5", "Value": 5}]}, {"_t": [], "permissionId": "global::approvals::payroll", "name": "Time Card - Payroll Approve", "description": "Allows users to set an approval to final.  This permission will be allow a user to finalize any approval", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "payroll", "Value": 999}]}, {"_t": [], "permissionId": "global::approvals::payroll", "name": "Time Card - Payroll Deny", "description": "Denies a the users payroll permission.  This is usually used to override a policy that has payroll permission", "group": "Global", "subgroup": "ApproverLevel", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::costcodes", "name": "Mechanic Cost Codes", "description": "Allows users to add, edit, and delete mechanic cost codes.", "group": "Global", "subgroup": "MechanicCostCodes", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::costcodes", "name": "Mechanic Cost Codes", "description": "Denies users access to mechanic cost codes management.", "group": "Global", "subgroup": "MechanicCostCodes", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::costcodes", "name": "Mechanic Cost Codes", "description": "Allows users read only access to mechanic cost codes.", "group": "Global", "subgroup": "MechanicCostCodes", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::diary", "name": "Mechanic Diary", "description": "Allows users to access and manage mechanic diary entries.", "group": "Global", "subgroup": "MechanicDiary", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::diary", "name": "Mechanic Diary", "description": "Denies users access to mechanic diary functionality.", "group": "Global", "subgroup": "MechanicDiary", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::diary", "name": "Mechanic Diary", "description": "Allows users read only access to mechanic diary entries.", "group": "Global", "subgroup": "MechanicDiary", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::approvals", "name": "Mechanic Approvals", "description": "Allows users to access and manage mechanic timesheet approvals.", "group": "Global", "subgroup": "MechanicApprovals", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::approvals", "name": "Mechanic Approvals", "description": "Denies users access to mechanic approvals functionality.", "group": "Global", "subgroup": "MechanicApprovals", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::approvals", "name": "Mechanic Approvals", "description": "Allows users read only access to mechanic approvals.", "group": "Global", "subgroup": "MechanicApprovals", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "global::mechanic::approvals::approval1", "name": "Mechanic Timesheet - Approver Level 1", "description": "User is allowed to approve mechanic timesheets at level 1 and lower", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl1", "Value": 1}]}, {"_t": [], "permissionId": "global::mechanic::approvals::approval2", "name": "Mechanic Timesheet - Approver Level 2", "description": "User is allowed to approve mechanic timesheets at level 2 and lower", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl2", "Value": 2}]}, {"_t": [], "permissionId": "global::mechanic::approvals::approval3", "name": "Mechanic Timesheet - Approver Level 3", "description": "User is allowed to approve mechanic timesheets at level 3 and lower", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl3", "Value": 3}]}, {"_t": [], "permissionId": "global::mechanic::approvals::approval4", "name": "Mechanic Timesheet - Approver Level 4", "description": "User is allowed to approve mechanic timesheets at level 4 and lower", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl4", "Value": 4}]}, {"_t": [], "permissionId": "global::mechanic::approvals::approval5", "name": "Mechanic Timesheet - Approver Level 5", "description": "User is allowed to approve mechanic timesheets at level 5 and lower", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "lvl5", "Value": 5}]}, {"_t": [], "permissionId": "global::mechanic::approvals::payroll", "name": "Mechanic Timesheet - Payroll Approve", "description": "Allows users to set a mechanic approval to final. This permission will allow a user to finalize any mechanic approval", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": [{"_t": ["IntegerDataType"], "TypeId": "Integer", "DataTypeId": "payroll", "Value": 999}]}, {"_t": [], "permissionId": "global::mechanic::approvals::payroll", "name": "Mechanic Timesheet - <PERSON><PERSON> Deny", "description": "Denies the users mechanic payroll permission. This is usually used to override a policy that has mechanic payroll permission", "group": "Global", "subgroup": "MechanicApproverLevel", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}], "project": [{"_t": [], "permissionId": "project::pstools::hsa", "name": "Settings - <PERSON><PERSON> Hide/Show/Arrange", "description": "Denies users to add/remove tools from the sidebar.", "group": "Project", "subgroup": "PSToolsHSA", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::pstools::hsa", "name": "Settings - <PERSON><PERSON> Hide/Show/Arrange", "description": "Allows users to add/remove tools from the sidebar. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Project", "subgroup": "PSToolsHSA", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::psinfo::hsa", "name": "Settings - Info Hide/Show/Arrange", "description": "Denies users to customize the Project Info page by hiding/showing fields, and add/remove new fields.", "group": "Project", "subgroup": "PSInfoHSA", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::psinfo::hsa", "name": "Settings - Info Hide/Show/Arrange", "description": "Allows users to customize the Project Info page by hiding/showing fields, and add/remove new fields. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Project", "subgroup": "PSInfoHSA", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::hsa", "name": "Settings - Diary- Hide/Show/Arrange", "description": "Denies users to add/remove sections from the diary", "group": "Project", "subgroup": "PSDailyLogHSA", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::hsa", "name": "Settings - Diary - Hide/Show/Arrange", "description": "Allows users to add/remove sections from the diary. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Project", "subgroup": "PSDailyLogHSA", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::weather", "name": "Settings - Diary - Weather", "description": "Denies users to customize the weather component on the diary.", "group": "Project", "subgroup": "PSDailyLogWeather", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::weather", "name": "Settings - Diary - Weather", "description": "Allows users to customize the weather component on the diary. (These global settings will be applied to all new projects. They can be edited at the project level.)", "group": "Project", "subgroup": "PSDailyLogWeather", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::sc", "name": "Settings - Diary - Site Conditions", "description": "Denies ausers to customize the site conditions component on the diary.", "group": "Project", "subgroup": "PSDailyLogSC", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::sc", "name": "Settings - Diary - Site Conditions", "description": "Allows users to customize the site conditions component on the diary.", "group": "Project", "subgroup": "PSDailyLogSC", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::costcodes", "name": "Settings - Diary - Cost Codes", "description": "Denies users the ability to add, edit, remove cost codes.", "group": "Project", "subgroup": "CostCodes", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::costcodes", "name": "Settings - Diary - Cost Codes", "description": "Allows users to add, edit, remove cost codes.", "group": "Project", "subgroup": "CostCodes", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::costcodes", "name": "Settings - Diary - Cost Codes", "description": "Allows users read only access to the Cost Code page.", "group": "Project", "subgroup": "CostCodes", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "project::psdailylog::equipmentdayoptions", "name": "Settings - Diary - Time Card - Equipment Day Options", "description": "Allows users to customize the equipment day options for the time card equipment tracking.", "group": "Project", "subgroup": "PSDailyLogEQDO", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::team", "name": "Team", "description": "Denies users the ability to add/remove people from the team and add/remove their project specific permissions. ", "group": "Project", "subgroup": "Team", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::team", "name": "Team", "description": "Allows users to add/remove people from the team and add/remove their project specific permissions. (These setting apply on a per project basis.)", "group": "Project", "subgroup": "Team", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::team", "name": "Team", "description": "Allow users read only access to the Team page.", "group": "Project", "subgroup": "Team", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "project::info", "name": "Info", "description": "Denies users the ability to input/delete project info", "group": "Project", "subgroup": "Info", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::info", "name": "Info", "description": "Allows users to input/delete project info.", "group": "Project", "subgroup": "Info", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::info", "name": "Info", "description": "Allows users read only access to the Info page.", "group": "Project", "subgroup": "Info", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "project::activity", "name": "Project History", "description": "Denies users the ability to access the Project History page.", "group": "Project", "subgroup": "Activity", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::activity", "name": "Project History", "description": "Allow users to access the Project History page.", "group": "Project", "subgroup": "Activity", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::activity", "name": "Project History", "description": "Allows read only access to the Project History page.", "group": "Project", "subgroup": "Activity", "accessLevel": {"_t": ["ReadOnlyAccessLevel"], "accessLevelId": "readonly", "name": "Read Only"}, "metaData": null}, {"_t": [], "permissionId": "project::dailylog", "name": "Diary", "description": "Denies users the ability to fill out/save/submit the diary form.", "group": "Project", "subgroup": "DailyLog", "accessLevel": {"_t": [], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::dailylog", "name": "Diary", "description": "Allows users to fill out/save/submit the diary form.", "group": "Project", "subgroup": "DailyLog", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::reports::rundailylog", "name": "Report - Diary", "description": "Denies the ability to run the diary report", "group": "Project", "subgroup": "RunDailyLogReport", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::reports::rundailylog", "name": "Report - Diary", "description": "Allows access to run the diary report", "group": "Project", "subgroup": "RunDailyLogReport", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::reports::runcostcodeqty", "name": "Report - Cost Code Quantity", "description": "Allows access to run the cost code quantity report", "group": "Project", "subgroup": "RunCostCodeQuantity", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}, {"_t": [], "permissionId": "project::photos", "name": "Photos", "description": "Denies a user from viewing all photos on the project", "group": "Project", "subgroup": "Photos", "accessLevel": {"_t": ["DenyAccessLevel"], "accessLevelId": "denyaccess", "name": "<PERSON><PERSON>"}, "metaData": null}, {"_t": [], "permissionId": "project::photos", "name": "Photos", "description": "Allows a user to view all photos in the project.", "group": "Project", "subgroup": "Photos", "accessLevel": {"_t": ["FullAccessLevel"], "accessLevelId": "fullaccess", "name": "Full"}, "metaData": null}]}}