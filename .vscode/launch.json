{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "name": "Launch Edge",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:5000",
            "webRoot": "${workspaceFolder}"
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Chrome against localhost",
            "url": "http://localhost:5000",
            "webRoot": "${workspaceFolder}"
        },
        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Brave against localhost",
            "url": "http://localhost:5000",
            "webRoot": "${workspaceFolder}",
            "runtimeExecutable": "C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe"
        }
    ]
}